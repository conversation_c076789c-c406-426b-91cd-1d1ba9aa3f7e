<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty name="APP_NAME" scope="context" source="spring.application.name"/>
    <springProperty name="LOG_FILE" scope="context" source="logging.file" defaultValue="../logs/${APP_NAME}"/>
    <springProperty name="LOG_POINT_FILE" scope="context" source="logging.file" defaultValue="../logs/point"/>
    <springProperty name="LOG_ACCESS_FILE" scope="context" source="logging.file" defaultValue="../logs/access"/>
    <springProperty name="LOG_NPM_FILE" scope="context" source="logging.file" defaultValue="../logs/npm"/>
    <springProperty name="LOG_MAX_FILE_SIZE" scope="context" source="logback.filesize" defaultValue="50MB"/>
    <springProperty name="LOG_FILE_MAX_DAY" scope="context" source="logback.filemaxday" defaultValue="90"/>
    <springProperty name="ServerIP" scope="context" source="spring.cloud.client.ip-address" defaultValue="0.0.0.0"/>
    <springProperty name="ServerPort" scope="context" source="server.port" defaultValue="0000"/>

    <property name="LOG_LEVEL" value="${log.level:-info}"/>

    <!-- 彩色日志 -->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />

    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="[${ServerPort}] %clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%level){blue} %clr(${PID}){magenta} %clr([%thread]){orange} %clr(%logger{5}){cyan} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}" />
    <property name="CONSOLE_LOG_PATTERN_NO_COLOR" value="[${ServerPort}] %d{yyyy-MM-dd HH:mm:ss.SSS} %level ${PID} [%thread] %logger{5} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}" />

    <!-- 控制台日志 -->
    <appender name="StdoutAppender" class="ch.qos.logback.core.ConsoleAppender">
        <withJansi>true</withJansi>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <!-- 按照每天生成常规日志文件 -->
    <appender name="FileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}/${APP_NAME}.log</file>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN_NO_COLOR}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 基于时间的分包策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}/${APP_NAME}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--保留时间,单位:天-->
            <maxHistory>${LOG_FILE_MAX_DAY}</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${LOG_MAX_FILE_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>${LOG_LEVEL}</level>
        </filter>
    </appender>

    <appender name="FileAppenderDebug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}/${APP_NAME}.debug.log</file>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN_NO_COLOR}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 基于时间的分包策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}/${APP_NAME}.%d{yyyy-MM-dd}.%i.debug.log</fileNamePattern>
            <!--保留时间,单位:天-->
            <maxHistory>${LOG_FILE_MAX_DAY}</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${LOG_MAX_FILE_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>

  <logger name="io.netty" level="warn"/>
  <logger name="org.jboss" level="info"/>
  <logger name="org.infinispan" level="info"/>
  <logger name="org.jgroups" level="info"/>
  <logger name="org.hibernate" level="info"/>
  <logger name="org.mybatis" level="info"/>
  <logger name="com.alibaba" level="info"/>
  <logger name="org.springframework" level="info"/>
  <logger name="org.apache.ibatis" level="info"/>
  <logger name="com.baomidou" level="info"/>
  <logger name="liquibase" level="info"/>
  <logger name="io.vertx" level="info"/>
  <logger name="io.lettuce" level="error"/>
  <logger name="springfox" level="info"/>
  
  <logger name="com.nti56.nlink" level="${log.level:-info}"/>

    <root level="${LOG_LEVEL}">
        <appender-ref ref="StdoutAppender"/>
        <appender-ref ref="FileAppender"/>
        <appender-ref ref="FileAppenderDebug"/>
    </root>
</configuration>
