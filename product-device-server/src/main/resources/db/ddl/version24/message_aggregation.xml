<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="createTable" author="grc">
        <createTable tableName="message_aggregation">
            <column name="id" type="bigint" >
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="content" type="varchar(1024)" remarks="模板内容"/>
            <column name="title" type="varchar(255)" remarks="模板标题"/>
            <column name="aggregation_id" type="bigint" remarks="消息聚合id"/>
            <column name="tenant_id" type="bigint" remarks="工程id"/>
            <column name="creator" type="varchar(90)" remarks="创建人名称"/>
            <column name="creator_id" type="bigint" remarks="创建人id"/>
            <column name="create_Time" type="datetime" remarks="创建时间"/>
            <column name="updator" type="varchar(90)" remarks="更新人名称"/>
            <column name="updator_id" type="bigint" remarks="更新人id"/>
            <column name="update_time" type="datetime" remarks="更新时间"/>
        </createTable>
    </changeSet>
    <changeSet id="addIndex" author="grc">
        <sql>
            create index aggregation_id_index on message_aggregation(aggregation_id);
        </sql>
    </changeSet>
</databaseChangeLog>
