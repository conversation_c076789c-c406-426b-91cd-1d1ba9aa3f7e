<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="createTable" author="guosheng">
        <createTable tableName="notify_channel">
            <column name="id" type="bigint" >
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="name" type="varchar(64)" remarks="渠道名称"/>
            <column name="description" type="varchar(1024)" remarks="描述"/>
            <column name="notify_type" type="tinyint" remarks="通知类型 0邮件 1短信"/>
            <column name="params" type="varchar(512)" remarks="渠道参数"/>
            <column name="engineering_id" type="bigint" remarks="工程id"/>
            <column name="space_id" type="bigint" remarks="空间id"/>
            <column name="module_id" type="bigint" remarks="模板id"/>
            <column name="creator" type="varchar(90)" remarks="创建人名称"/>
            <column name="creator_id" type="bigint" remarks="创建人id"/>
            <column name="create_Time" type="datetime" remarks="创建时间"/>
            <column name="updator" type="varchar(90)" remarks="更新人名称"/>
            <column name="updator_id" type="bigint" remarks="更新人id"/>
            <column name="update_time" type="datetime" remarks="更新时间"/>
            <column name="version" type="int" remarks="版本号" defaultValueNumeric="1"/>
            <column name="deleted" type="int" remarks="是否删除" defaultValueNumeric="0"/>
        </createTable>
    </changeSet>
    <changeSet id="20220510172117" author="guosheng">
        <sql>
            ALTER TABLE notify_channel ADD tenant_id bigint  COMMENT '租户id' ;
        </sql>
    </changeSet>
    <changeSet id="20220726172117" author="guosheng">
        <sql>
            ALTER TABLE notify_channel MODIFY params varchar(1024)  COMMENT '渠道参数' ;
        </sql>
    </changeSet>

    <changeSet id="20240902153801" author="zhoulc">
        <sql>
            ALTER TABLE `notify_channel`
                MODIFY COLUMN `version` int NULL DEFAULT 1 COMMENT '版本号' AFTER `update_time`,
                MODIFY COLUMN `deleted` int NULL DEFAULT 0 COMMENT '是否删除' AFTER `version`;
        </sql>
    </changeSet>

</databaseChangeLog>
