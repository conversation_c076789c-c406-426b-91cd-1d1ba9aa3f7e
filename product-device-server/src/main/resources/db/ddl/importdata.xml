<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <preConditions >
        <dbms type="mysql"/>
    </preConditions>
    <changeSet author="test" id="sqlFile" runOrder="last" failOnError="false" runOnChange="true">
        <preConditions onFail="CONTINUE">
            <sqlCheck expectedResult="0">select count(*) from DATABASECHANGELOG where id='sqlFile'</sqlCheck>
        </preConditions>
        <sqlFile
                encoding="UTF-8"
                path="export.sql"
                relativeToChangelogFile="false"
                splitStatements="true"
                stripComments="true"/>
    </changeSet>


</databaseChangeLog>
