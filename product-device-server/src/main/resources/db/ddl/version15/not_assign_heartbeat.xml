<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
  
  <changeSet author="linql" id="20230420125178">
    <sql>
      CREATE TABLE `not_assign_gateway` (
          `id` bigint NOT NULL,
          `imei` varchar(20) COMMENT '上报imei号',
          `adminPort` int NOT NULL COMMENT '上报管理端口',
          `host` VARCHAR(32)  COMMENT '上报ip|host',
          `port` int NOT NULL COMMENT '上报端口',
          `tenant_id` bigint DEFAULT NULL COMMENT '上报租户id',
          `edge_gateway_id` bigint DEFAULT NULL COMMENT '上报网关id',
          `creator_id` bigint DEFAULT NULL COMMENT '创建的用户id',
          `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建的用户姓名',
          `updator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改的用户姓名',
          `updator_id` bigint DEFAULT NULL COMMENT '最后一次修改的用户id',
          `create_time` datetime DEFAULT NULL COMMENT '创建数据时间',
          `update_time` datetime DEFAULT NULL COMMENT '更新数据时间',
          PRIMARY KEY (`id`) USING BTREE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='未分配网关';
    </sql>
  </changeSet>
  
  <changeSet author="linql" id="20230423125278">
    <sql>
      ALTER TABLE `not_assign_gateway` CHANGE COLUMN `adminPort` `admin_port` int NOT NULL COMMENT '上报管理端口' AFTER `imei`;
    </sql>
  </changeSet>
    
</databaseChangeLog>
