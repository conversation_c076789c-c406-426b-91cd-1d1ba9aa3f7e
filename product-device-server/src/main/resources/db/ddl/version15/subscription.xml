<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="20230424165100" author="liuyiran">
        <addColumn tableName="subscription">
            <column name="out_type"
                    type="TINYINT(1)"
                    remarks="数据传出类型，0-回调资源，1-设备服务，默认0"
                    defaultValue="0"
                    afterColumn="model_type">
                <constraints nullable="false"/>
            </column>
            <column name="target_device"
                    remarks="调用服务时保存目标设备ID，如果是调用自身则为0"
                    type="BIGINT"
                    afterColumn="model_type"
            >
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <sql>
            UPDATE subscription SET out_type = 0;
        </sql>
    </changeSet>

    <changeSet id="20230424165200" author="liuyiran">
        <addColumn tableName="subscription">
            <column name="target_service" remarks="服务名称" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20230424175200" author="liuyiran">
        <addColumn tableName="subscription">
            <column name="receivers" remarks="接收账号，用逗号隔开" type="VARCHAR(255)" afterColumn="model_type">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20230425175200" author="liuyiran">
        <dropNotNullConstraint tableName="subscription" columnName="callback_id" columnDataType="BIGINT" remarks="回调ID"/>
    </changeSet>

</databaseChangeLog>
