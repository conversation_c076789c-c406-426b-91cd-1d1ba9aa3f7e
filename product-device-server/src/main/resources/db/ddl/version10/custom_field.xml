<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun" id="1673514430">
        <sql>
            ALTER TABLE custom_field ADD COLUMN check_type TINYINT COMMENT '校验类型' AFTER field_type;
        </sql>
    </changeSet>
    
    <changeSet author="sushangqun" id="1675318753">
        <sql>
            ALTER TABLE custom_field ADD COLUMN dynamic_length_config JSON COMMENT '动态长度参数配置' AFTER check_type;
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1675318767">
        <sql>
            ALTER TABLE custom_field ADD COLUMN field_transform JSON COMMENT '字段内容转换' AFTER dynamic_length_config;
        </sql>
    </changeSet>
    
    <changeSet author="sushangqun" id="1675411679">
        <sql>
            ALTER TABLE custom_field ADD COLUMN end_byte INT COMMENT '结束位置' AFTER start_bit;
        </sql>
    </changeSet>
    
    
    <changeSet author="sushangqun" id="1675411680">
        <sql>
            ALTER TABLE custom_field ADD COLUMN end_bit INT COMMENT '结束位置索引' AFTER end_byte;
        </sql>
    </changeSet>

</databaseChangeLog>
