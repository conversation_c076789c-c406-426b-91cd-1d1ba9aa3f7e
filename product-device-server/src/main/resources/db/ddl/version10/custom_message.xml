<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="sushangqun" id="1674976461">
        <sql>
            ALTER TABLE custom_message ADD COLUMN direction TINYINT NOT NULL DEFAULT 1 COMMENT '方向，1-发送和接收，2-发送，3-接收' AFTER descript;
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1675041262">
        <sql>
            ALTER TABLE custom_message ADD COLUMN 
                auto_response TINYINT NOT NULL DEFAULT 0 
                COMMENT '自动响应，0-关闭，1-开启' 
                AFTER direction;
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1675041330">
        <sql>
            ALTER TABLE custom_message ADD COLUMN 
                auto_response_message_name VARCHAR(255)
                COMMENT '自动响应消息名' 
                AFTER auto_response;
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1675041805">
        <sql>
            ALTER TABLE custom_message ADD COLUMN 
                auto_response_config JSON
                COMMENT '自动响应配置' 
                AFTER auto_response_message_name;
        </sql>
    </changeSet>
</databaseChangeLog>
