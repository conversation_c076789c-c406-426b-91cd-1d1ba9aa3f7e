<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="20230201145100" author="liuyiran">
        <addColumn tableName="subscription">
            <column name="send_one_by_one"
                    type="TINYINT(1)"
                    remarks="是否逐个发送，0-否，1-是，默认0"
                    defaultValue="0"
                    afterColumn="model_type">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20230208145101" author="linql">
        <addColumn tableName="subscription">
            <column name="events" remarks="订阅事件，用“,”隔开" type="VARCHAR(512)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20230209145102" author="linql">
        <modifyDataType tableName="subscription" columnName="properties" newDataType="TEXT"/>
    </changeSet>

    <changeSet id="20230210145103" author="linql">
        <modifyDataType tableName="subscription" columnName="events" newDataType="TEXT"/>
    </changeSet>

    <changeSet id="2023021415196" author="wxd">
        <sql>
            UPDATE subscription SET data_conversion_code = REPLACE(data_conversion_code,'eventData.','event.eventData.');
        </sql>
    </changeSet>

    <changeSet id="20230216145100" author="liuyiran">
        <dropNotNullConstraint tableName="subscription" columnName="send_one_by_one" columnDataType="TINYINT(1)"/>
    </changeSet>
</databaseChangeLog>
