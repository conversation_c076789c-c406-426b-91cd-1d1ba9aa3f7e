<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun" id="1652065396">
        <addColumn tableName="device">
            <column name="runtime_metadata"  
                type="JSON" 
                remarks="运行时元数据"
                afterColumn="descript" >  
                <constraints nullable="true" />  
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="liuyiran" id="1652344657">
        <addColumn tableName="device">
            <column name="sync_status"
                    type="tinyint(1)"
                    remarks="同步状态:0-未同步，1-已同步"
                    defaultValue="1"
                    afterColumn="runtime_metadata" >
                <constraints nullable="false" />
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="liuyiran" id="1652758617">
        <addColumn tableName="device">
            <column name="tenant_id"
                    type="bigint"
                    remarks="租户Id"
                    defaultValue="1"
                    afterColumn="ENGINEERING_ID" >
                <constraints nullable="false" />
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="liuyiran" id="1652923772">
        <addColumn tableName="device">
            <column name="last_sync_time"
                    remarks="最后同步时间"
                    type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="activation_time"
                    remarks="激活时间"
                    type="datetime">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
