<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="guosheng" id="20220505145952">
        <sql>
            ALTER TABLE edge_gateway ADD type tinyint  COMMENT '网关类型 虚拟网关/网关设备' ;
            ALTER TABLE edge_gateway ADD status tinyint  COMMENT '状态 1启用 0停用 2未激活' ;
            ALTER TABLE edge_gateway ADD sync_time datetime  COMMENT '同步时间' ;
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1652067664">
        <dropColumn tableName="edge_gateway">
            <column name="model"></column>
        </dropColumn>
    </changeSet>


    <changeSet id="20220513172117" author="guosheng">
        <sql>
            ALTER TABLE edge_gateway ADD tenant_id bigint  COMMENT '租户id' ;
        </sql>
    </changeSet>

    <changeSet id="20220513172118" author="guosheng">
        <sql>
            ALTER TABLE edge_gateway ADD sync_status tinyint(1) NOT NULL DEFAULT '0' COMMENT '同步状态:0-未同步，1-已同步' ;
        </sql>
    </changeSet>

    <changeSet id="1653276145" author="sushangqun">
        <sql>
            ALTER TABLE edge_gateway MODIFY status tinyint(1) NOT NULL DEFAULT '2' COMMENT '状态 1启用 0停用 2未激活' ;
        </sql>
    </changeSet>

</databaseChangeLog>
