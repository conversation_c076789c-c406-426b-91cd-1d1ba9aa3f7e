<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">


    <changeSet id="20220513172117" author="guosheng">
        <sql>
            ALTER TABLE channel ADD tenant_id bigint  COMMENT '租户id' ;
        </sql>
    </changeSet>

    <changeSet id="20220513172118" author="guosheng">
        <sql>
            ALTER TABLE channel_param ADD tenant_id bigint  COMMENT '租户id' ;
        </sql>
    </changeSet>

    <changeSet id="20220513172119" author="guosheng">
        <sql>
            ALTER TABLE channel ADD md5_proofread varchar(32)  COMMENT 'md5校对' ;
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1652948876">
        <addColumn tableName="channel" >
            <column name="runtime_info"  
                type="JSON" 
                remarks="通道运行时信息"
                afterColumn="edge_gateway_id" >  
                <constraints nullable="true" />  
            </column>
        </addColumn> 
    </changeSet>
    
</databaseChangeLog>
