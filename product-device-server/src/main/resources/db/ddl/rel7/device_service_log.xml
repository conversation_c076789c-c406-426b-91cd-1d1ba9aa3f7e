<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="liuyiran" id="20220810102949">
        <sql>
            CREATE TABLE device_service_log (
            id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
            device_id BIGINT COMMENT '设备id',
            service_id BIGINT COMMENT '服务id',
            service_name VARCHAR(255) NOT NULL   COMMENT '服务名称' ,
            device_name VARCHAR(255) COMMENT '设备名称' ,
            call_type INT NOT NULL COMMENT '执行者',
            call_success TINYINT NOT NULL COMMENT '调用成功:0-(失败) 1-(成功)',
            input_data JSON  COMMENT '输入参数' ,
            output_data TEXT  COMMENT '结果' ,
            CREATOR_ID BIGINT COMMENT '创建人ID' ,
            CREATOR VARCHAR(90)  COMMENT '创建人' ,
            CREATE_TIME DATETIME  COMMENT '创建时间' ,
            TENANT_ID BIGINT  COMMENT '租户id',
            PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备服务日志表';
        </sql>
    </changeSet>

    <changeSet author="liuyiran" id="20220810112548739">
        <createIndex tableName="device_service_log" indexName="tenant_device_service">
            <column name="TENANT_ID"/>
            <column name="device_id"/>
            <column name="service_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>