<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun" id="1650356705">
        <dropColumn tableName="compute_task">
            <column name="gather_task_id"></column>
        </dropColumn>
        <addColumn tableName="compute_task">
            <column name="device_id" 
                remarks="所属设备id"
                type="BIGINT"
                afterColumn="name" 
            >
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="compute_task">
            <column name="event_name" 
                remarks="所属事件名" 
                type="VARCHAR(128)"
                afterColumn="name" 
            >
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="sushangqun" id="1650941420">
        <dropColumn tableName="compute_task">
            <column name="version"></column>
        </dropColumn>
    </changeSet>
    <changeSet author="sushangqun" id="1650941501">
        <sql>
            ALTER TABLE compute_task ADD COLUMN ENGINEERING_ID bigint COMMENT '工程id';
            ALTER TABLE compute_task ADD COLUMN SPACE_ID bigint COMMENT '空间id';
            ALTER TABLE compute_task ADD COLUMN MODULE_ID bigint COMMENT '模块id';
            ALTER TABLE compute_task ADD COLUMN CREATOR varchar(90) DEFAULT NULL COMMENT '创建人名称';
            ALTER TABLE compute_task ADD COLUMN CREATOR_ID bigint unsigned DEFAULT NULL COMMENT '创建人id';
            ALTER TABLE compute_task ADD COLUMN UPDATOR_ID bigint unsigned DEFAULT NULL COMMENT '修改人id';
            ALTER TABLE compute_task ADD COLUMN UPDATOR varchar(90) DEFAULT NULL COMMENT '修改人名称';
            ALTER TABLE compute_task ADD COLUMN UPDATE_TIME datetime DEFAULT NULL COMMENT '修改时间';
            ALTER TABLE compute_task ADD COLUMN VERSION int DEFAULT '1' COMMENT '版本号';
            ALTER TABLE compute_task ADD COLUMN DELETED int DEFAULT '0' COMMENT '是否删除';
        </sql>
    </changeSet>
    
</databaseChangeLog>