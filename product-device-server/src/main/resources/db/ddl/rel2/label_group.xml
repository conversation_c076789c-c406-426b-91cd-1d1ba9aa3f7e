<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun" id="1650346227">
        <addColumn tableName="label_group" >
            <column name="gather_task"  
                type="JSON" 
                remarks="采集任务内容"
                afterColumn="strategy_id" >  
                <constraints nullable="true" />  
            </column>
        </addColumn> 
    </changeSet>

    <changeSet id="20220424102042" author="guosheng">
        <sql>
            ALTER TABLE label_group ADD COLUMN ENGINEERING_ID bigint COMMENT '工程id';
            ALTER TABLE label_group ADD COLUMN SPACE_ID bigint COMMENT '空间id';
            ALTER TABLE label_group ADD COLUMN MODULE_ID bigint COMMENT '模块id';
            ALTER TABLE label_group ADD COLUMN CREATOR varchar(90) DEFAULT NULL COMMENT '创建人名称';
            ALTER TABLE label_group ADD COLUMN CREATOR_ID bigint unsigned DEFAULT NULL COMMENT '创建人id';
            ALTER TABLE label_group ADD COLUMN UPDATOR_ID bigint unsigned DEFAULT NULL COMMENT '修改人id';
            ALTER TABLE label_group ADD COLUMN UPDATOR varchar(90) DEFAULT NULL COMMENT '修改人名称';
            ALTER TABLE label_group ADD COLUMN UPDATE_TIME datetime DEFAULT NULL COMMENT '修改时间';
            ALTER TABLE label_group ADD COLUMN VERSION int DEFAULT '1' COMMENT '版本号';
            ALTER TABLE label_group ADD COLUMN DELETED int DEFAULT '0' COMMENT '是否删除';
        </sql>
    </changeSet>

</databaseChangeLog>
