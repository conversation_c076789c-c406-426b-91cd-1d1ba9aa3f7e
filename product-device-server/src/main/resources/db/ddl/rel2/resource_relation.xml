<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="liuyiran (generated)" id="20220407165318">
        <sql>
            DROP TABLE IF EXISTS resource_relation;
            CREATE TABLE resource_relation(
            ID BIGINT unsigned NOT NULL COMMENT 'ID' ,
            device_id BIGINT unsigned NOT NULL COMMENT '设备ID' ,
            product_id BIGINT unsigned NOT NULL COMMENT '产品ID' ,
            thing_model_id BIGINT unsigned NOT NULL COMMENT '物模型ID' ,
            VERSION INT DEFAULT 1 COMMENT '版本号' ,
            DELETED TINYINT(1) DEFAULT 0 COMMENT '逻辑删除，1-删除' ,
            CREATOR_ID BIGINT unsigned COMMENT '创建人ID' ,
            CREATOR VARCHAR(90) COMMENT '创建人' ,
            CREATE_TIME DATETIME COMMENT '创建时间' ,
            ENGINEERING_ID BIGINT  COMMENT '工程ID' ,
            MODULE_ID BIGINT  COMMENT '模块ID' ,
            SPACE_ID BIGINT  COMMENT '空间ID',
            PRIMARY KEY (ID)
            )  COMMENT = '资源关系表';
        </sql>
    </changeSet>

    <changeSet author="liuyiran" id="20220429115007">
        <dropColumn tableName="resource_relation">
            <column name="thing_model_id"></column>
        </dropColumn>
    </changeSet>
    
</databaseChangeLog>