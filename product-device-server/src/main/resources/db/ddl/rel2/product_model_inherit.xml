<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="sushangqun" id="1651057988">
        <sql>
            CREATE TABLE product_model_inherit (
                id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',

                product_id BIGINT NOT NULL COMMENT '产品id',
                inherit_thing_model_id BIGINT NOT NULL COMMENT '继承的物模型id',
                
                sort_no INT NOT NULL COMMENT '继承顺序',
                
                create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品模型继承表';
        </sql>
    </changeSet>
    
    <changeSet author="sushangqun" id="1651058076">
        <sql>
            ALTER TABLE product_model_inherit ADD COLUMN ENGINEERING_ID bigint COMMENT '工程id';
            ALTER TABLE product_model_inherit ADD COLUMN SPACE_ID bigint COMMENT '空间id';
            ALTER TABLE product_model_inherit ADD COLUMN MODULE_ID bigint COMMENT '模块id';
            ALTER TABLE product_model_inherit ADD COLUMN CREATOR varchar(90) DEFAULT NULL COMMENT '创建人名称';
            ALTER TABLE product_model_inherit ADD COLUMN CREATOR_ID bigint unsigned DEFAULT NULL COMMENT '创建人id';
            ALTER TABLE product_model_inherit ADD COLUMN UPDATOR_ID bigint unsigned DEFAULT NULL COMMENT '修改人id';
            ALTER TABLE product_model_inherit ADD COLUMN UPDATOR varchar(90) DEFAULT NULL COMMENT '修改人名称';
            ALTER TABLE product_model_inherit ADD COLUMN UPDATE_TIME datetime DEFAULT NULL COMMENT '修改时间';
            ALTER TABLE product_model_inherit ADD COLUMN VERSION int DEFAULT '1' COMMENT '版本号';
            ALTER TABLE product_model_inherit ADD COLUMN DELETED int DEFAULT '0' COMMENT '是否删除';
        </sql>
    </changeSet>
</databaseChangeLog>