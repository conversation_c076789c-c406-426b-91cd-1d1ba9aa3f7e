<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="liuyiran (generated)" id="20220407165057">
        <sql>
            DROP TABLE IF EXISTS tag_bind_relation;
            CREATE TABLE tag_bind_relation(
            ID BIGINT unsigned NOT NULL,
            tag_id BIGINT unsigned NOT NULL COMMENT '关联标识ID表' ,
            resource_type INT NOT NULL COMMENT '资源类型:1-产品 2-设备 3-网关' ,
            target_id BIGINT unsigned NOT NULL COMMENT '目标对象ID' ,
            CREATOR_ID BIGINT unsigned COMMENT '创建人ID' ,
            CREATOR VARCHAR(90) COMMENT '创建人' ,
            CREATE_TIME DATETIME COMMENT '创建时间' ,
            ENGINEERING_ID BIGINT  COMMENT '工程ID' ,
            MODULE_ID BIGINT  COMMENT '模块ID' ,
            SPACE_ID BIGINT  COMMENT '空间ID',
            PRIMARY KEY (ID)
            )  COMMENT = '标志关系表';
        </sql>
    </changeSet>
    
</databaseChangeLog>