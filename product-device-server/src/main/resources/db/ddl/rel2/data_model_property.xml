<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="sushangqun (generated)" id="1649740541">
        <createTable remarks="数据模型属性表" tableName="data_model_property">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="属性名称，英文数字下划线，英文开头" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="descript" remarks="描述" type="TEXT"/>
            <column name="required" remarks="是否必填，0-否，1-是" type="TINYINT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="access" remarks="0-不可读写(n)，1-只读(r)，2-只写(w)，3-读写(rw)" type="TINYINT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="persist" remarks="是否持久化，0-否，1-是" type="TINYINT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="data_type" remarks="数据类型，1-bool,2-byte,3-short,4-int,5-float,6-string,7-datamodel" type="TINYINT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="is_array" remarks="是否是数组，0-否，1-是" type="TINYINT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="property_data_model_id" remarks="是否是数组，0-否，1-是" type="BIGINT" />
            <column name="default_value" remarks="默认值，基础类型才能设置" type="VARCHAR(128)" />
            <column name="min" remarks="最小值，short/int/float才能设置" type="VARCHAR(128)" />
            <column name="max" remarks="最大值，short/int/float才能设置" type="VARCHAR(128)" />

        </createTable>
    </changeSet>

    <changeSet id="1649742465-data_model_property" author="sushangqun">
        <sql>
            ALTER TABLE data_model_property ADD COLUMN ENGINEERING_ID bigint COMMENT '工程id';
            ALTER TABLE data_model_property ADD COLUMN SPACE_ID bigint COMMENT '空间id';
            ALTER TABLE data_model_property ADD COLUMN MODULE_ID bigint COMMENT '模块id';
            ALTER TABLE data_model_property ADD COLUMN CREATOR varchar(90) DEFAULT NULL COMMENT '创建人名称';
            ALTER TABLE data_model_property ADD COLUMN CREATOR_ID bigint unsigned DEFAULT NULL COMMENT '创建人id';
            ALTER TABLE data_model_property ADD COLUMN CREATE_TIME datetime DEFAULT NULL COMMENT '创建时间';
            ALTER TABLE data_model_property ADD COLUMN UPDATOR_ID bigint unsigned DEFAULT NULL COMMENT '修改人id';
            ALTER TABLE data_model_property ADD COLUMN UPDATOR varchar(90) DEFAULT NULL COMMENT '修改人名称';
            ALTER TABLE data_model_property ADD COLUMN UPDATE_TIME datetime DEFAULT NULL COMMENT '修改时间';
            ALTER TABLE data_model_property ADD COLUMN VERSION int DEFAULT '1' COMMENT '版本号';
            ALTER TABLE data_model_property ADD COLUMN DELETED int DEFAULT '0' COMMENT '是否删除';
        </sql>
    </changeSet>
    
    <changeSet author="sushangqun (generated)" id="1649830837">
        <addColumn tableName="data_model_property" >
            <column name="data_model_id" remarks="所属数据模型id" type="BIGINT" >
                <constraints nullable="false"/>
            </column>
        </addColumn> 
    </changeSet>


    <changeSet author="sushangqun (generated)" id="1649918056">
        <dropColumn tableName="data_model_property">
            <column name="min"></column>
            <column name="max"></column>
            <column name="required"></column>
            <column name="access"></column>
            <column name="persist"></column>
        </dropColumn>
    </changeSet>


    <changeSet author="sushangqun" id="1650000439">
        <sql>
            ALTER TABLE data_model_property MODIFY  property_data_model_id BIGINT(11) COMMENT '属性数据模型id，dataModel类型才能设置';
        </sql>
    </changeSet>

</databaseChangeLog>
