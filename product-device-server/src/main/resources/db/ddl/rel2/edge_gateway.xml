<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun (generated)" id="1648525882826-1">
        <addColumn tableName="edge_gateway" >
            <column name="model"  
                type="JSON" 
                afterColumn="descript" >  
                <constraints nullable="true" />  
            </column>  
        </addColumn> 
    </changeSet>
    
    <changeSet author="sushangqun (generated)" id="1648525882826-1-1">
        <dropColumn columnName="model"  
                tableName="edge_gateway">  
        </dropColumn>
        
    </changeSet>
    <changeSet author="sushangqun (generated)" id="1648525882826-1-2">
        <addColumn tableName="edge_gateway" >
            <column name="model"  
                type="JSON" 
                remarks="网关模型"
                afterColumn="descript" >  
                <constraints nullable="true" />  
            </column>  
        </addColumn> 
    </changeSet>

    <changeSet id="20220425102043" author="guosheng">
        <sql>
            ALTER TABLE edge_gateway ADD COLUMN ENGINEERING_ID bigint COMMENT '工程id';
            ALTER TABLE edge_gateway ADD COLUMN SPACE_ID bigint COMMENT '空间id';
            ALTER TABLE edge_gateway ADD COLUMN MODULE_ID bigint COMMENT '模块id';
            ALTER TABLE edge_gateway ADD COLUMN CREATOR varchar(90) DEFAULT NULL COMMENT '创建人名称';
            ALTER TABLE edge_gateway ADD COLUMN CREATOR_ID bigint unsigned DEFAULT NULL COMMENT '创建人id';
            ALTER TABLE edge_gateway ADD COLUMN UPDATOR_ID bigint unsigned DEFAULT NULL COMMENT '修改人id';
            ALTER TABLE edge_gateway ADD COLUMN UPDATOR varchar(90) DEFAULT NULL COMMENT '修改人名称';
            ALTER TABLE edge_gateway ADD COLUMN UPDATE_TIME datetime DEFAULT NULL COMMENT '修改时间';
            ALTER TABLE edge_gateway ADD COLUMN VERSION int DEFAULT '1' COMMENT '版本号';
            ALTER TABLE edge_gateway ADD COLUMN DELETED int DEFAULT '0' COMMENT '是否删除';
        </sql>
    </changeSet>

</databaseChangeLog>
