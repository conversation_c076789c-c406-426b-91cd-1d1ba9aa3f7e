<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
<!--    逻辑删除改为tinyint-->
    <changeSet author="guosheng" id="addDemotion">
        <sql>
            ALTER TABLE strategy ADD COLUMN demotion tinyint  COMMENT '是否降级';
        </sql>
    </changeSet>

    <changeSet id="20220424102043" author="guosheng">
        <sql>
            ALTER TABLE strategy ADD COLUMN ENGINEERING_ID bigint COMMENT '工程id';
            ALTER TABLE strategy ADD COLUMN SPACE_ID bigint COMMENT '空间id';
            ALTER TABLE strategy ADD COLUMN MODULE_ID bigint COMMENT '模块id';
            ALTER TABLE strategy ADD COLUMN CREATOR varchar(90) DEFAULT NULL COMMENT '创建人名称';
            ALTER TABLE strategy ADD COLUMN CREATOR_ID bigint unsigned DEFAULT NULL COMMENT '创建人id';
            ALTER TABLE strategy ADD COLUMN UPDATOR_ID bigint unsigned DEFAULT NULL COMMENT '修改人id';
            ALTER TABLE strategy ADD COLUMN UPDATOR varchar(90) DEFAULT NULL COMMENT '修改人名称';
            ALTER TABLE strategy ADD COLUMN UPDATE_TIME datetime DEFAULT NULL COMMENT '修改时间';
            ALTER TABLE strategy ADD COLUMN VERSION int DEFAULT '1' COMMENT '版本号';
            ALTER TABLE strategy ADD COLUMN DELETED int DEFAULT '0' COMMENT '是否删除';
        </sql>
    </changeSet>


</databaseChangeLog>
