<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="liuyiran" id="20220704102948739">
        <sql>
            CREATE TABLE device_model_inherit (
                id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',

                device_id BIGINT NOT NULL COMMENT '设备id',
                inherit_thing_model_id BIGINT NOT NULL COMMENT '继承的物模型id',
                
                sort_no INT NOT NULL COMMENT '继承顺序',

                VERSION INT   DEFAULT 1 COMMENT '版本号' ,
                DELETED INT   DEFAULT 0 COMMENT '删除' ,
                CREATOR_ID BIGINT unsigned COMMENT '创建人ID' ,
                CREATOR VARCHAR(90)    COMMENT '创建人' ,
                CREATE_TIME DATETIME    COMMENT '创建时间' ,
                UPDATOR_ID BIGINT unsigned COMMENT '更新人ID' ,
                UPDATOR VARCHAR(90)    COMMENT '更新人' ,
                UPDATE_TIME DATETIME    COMMENT '更新时间' ,
                TENANT_ID BIGINT  COMMENT '租户id' ,
                ENGINEERING_ID BIGINT COMMENT '工程ID' ,
                MODULE_ID BIGINT COMMENT '模块ID' ,
                SPACE_ID BIGINT COMMENT '空间ID',
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备模型继承表';
        </sql>
    </changeSet>
    
    <changeSet author="liuyiran" id="20220704112548739">
        <createIndex tableName="device_model_inherit" indexName="tenant_device_inherit">
            <column name="TENANT_ID"/>
            <column name="device_id"/>
        </createIndex>
        <createIndex tableName="device_model_inherit" indexName="tenant_model_inherit">
            <column name="inherit_thing_model_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>