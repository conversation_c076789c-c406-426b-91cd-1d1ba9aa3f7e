<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="liuyiran" id="20220704102548739">
        <sql>
            CREATE TABLE device_service(
            id BIGINT unsigned NOT NULL,
            device_id BIGINT NOT NULL   COMMENT '所属设备ID' ,
            service_name VARCHAR(255) NOT NULL   COMMENT '服务名称' ,
            descript TEXT    COMMENT '描述' ,
            override INT NOT NULL   COMMENT '是否允许覆盖' ,
            async TINYINT NOT NULL COMMENT '调用方式:0-sync(同步调用) 1-async(异步调用)',
            input_data JSON    COMMENT '输入参数 JSON对象' ,
            output_data JSON    COMMENT '结果 JSON对象' ,
            service_code TEXT COMMENT '代码文本 需要限制代码长度' ,

            VERSION INT   DEFAULT 1 COMMENT '版本号' ,
            DELETED INT   DEFAULT 0 COMMENT '删除' ,
            CREATOR_ID BIGINT unsigned COMMENT '创建人ID' ,
            CREATOR VARCHAR(90)    COMMENT '创建人' ,
            CREATE_TIME DATETIME    COMMENT '创建时间' ,
            UPDATOR_ID BIGINT unsigned COMMENT '更新人ID' ,
            UPDATOR VARCHAR(90)    COMMENT '更新人' ,
            UPDATE_TIME DATETIME    COMMENT '更新时间' ,
            TENANT_ID BIGINT  COMMENT '租户id' ,
            ENGINEERING_ID BIGINT COMMENT '工程ID' ,
            MODULE_ID BIGINT COMMENT '模块ID' ,
            SPACE_ID BIGINT COMMENT '空间ID',
            PRIMARY KEY (ID)
            )  COMMENT = '设备服务表';
        </sql>
    </changeSet>
    <changeSet id="20220704102548738" author="liuyiran">
        <createIndex tableName="device_service" indexName="tenant_device">
            <column name="TENANT_ID"/>
            <column name="device_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>