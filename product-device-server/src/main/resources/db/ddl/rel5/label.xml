<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="20220704102120" author="guosheng">
        <sql>
            ALTER TABLE label ADD alias varchar(32)  COMMENT '别名' ;
        </sql>
    </changeSet>


    <changeSet id="20220704132120" author="guosheng">
        <sql>
            ALTER TABLE label ADD read_only TINYINT(1)  COMMENT '是否只读' ;
        </sql>
    </changeSet>


    <changeSet id="20220704132121" author="guosheng">
        <sql>
            ALTER TABLE label MODIFY name varchar(128)  COMMENT '名称' ;
        </sql>
    </changeSet>

</databaseChangeLog>
