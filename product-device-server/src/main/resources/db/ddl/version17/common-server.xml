<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="gongrongcheng" id="1668742911">
        <sql>
            CREATE TABLE IF NOT EXISTS `app_secret` (
                                                        `ID` bigint NOT NULL,
                                                        `name` varchar(32) COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '应用密钥名称',
                `description` varchar(256) COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '应用密钥描述',
                `app_key` varchar(32) COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT 'appKey',
                `secret_key` varchar(32) COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT 'secretKey',
                `expiration_time` datetime DEFAULT NULL COMMENT '到期时间',
                `VERSION` int DEFAULT '1' COMMENT '版本号',
                `DELETED` tinyint(1) DEFAULT '0' COMMENT '逻辑删除，1-删除',
                `CREATOR_ID` bigint DEFAULT NULL COMMENT '创建人ID',
                `CREATOR` varchar(90) COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人',
                `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                `UPDATOR_ID` bigint DEFAULT NULL COMMENT '创建人ID',
                `UPDATOR` varchar(90) COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人',
                `UPDATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                `ENGINEERING_ID` bigint DEFAULT NULL COMMENT '工程ID',
                `MODULE_ID` bigint DEFAULT NULL COMMENT '模块ID',
                `SPACE_ID` bigint DEFAULT NULL COMMENT '空间ID',
                PRIMARY KEY (`ID`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_as_cs COMMENT='应用密钥';

            CREATE TABLE IF NOT EXISTS `engineering_doc` (
                                                             `ID` bigint NOT NULL,
                                                             `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '数据版本名称',
                `doc_size` bigint DEFAULT NULL COMMENT '文件大小',
                `generate_type` tinyint DEFAULT NULL COMMENT '生成类型 0自动 1导入',
                `download_link` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '下载链接',
                `VERSION` int DEFAULT '1' COMMENT '版本号',
                `DELETED` tinyint(1) DEFAULT '0' COMMENT '逻辑删除，1-删除',
                `CREATOR_ID` bigint DEFAULT NULL COMMENT '创建人ID',
                `CREATOR` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人',
                `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                `UPDATOR_ID` bigint DEFAULT NULL COMMENT '创建人ID',
                `UPDATOR` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人',
                `UPDATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                `ENGINEERING_ID` bigint DEFAULT NULL COMMENT '工程ID',
                `MODULE_ID` bigint DEFAULT NULL COMMENT '模块ID',
                `SPACE_ID` bigint DEFAULT NULL COMMENT '空间ID',
                `platform_version` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '平台版本',
                `remark` varchar(256) COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '版本描述',
                PRIMARY KEY (`ID`) USING BTREE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_as_cs ROW_FORMAT=DYNAMIC COMMENT='工程文件版本表';

            CREATE TABLE IF NOT EXISTS `redirect` (
                                                      `id` bigint NOT NULL,
                                                      `engineering_id` bigint DEFAULT NULL COMMENT '工程id',
                                                      `module_id` bigint DEFAULT NULL COMMENT '模块id',
                                                      `space_id` bigint DEFAULT NULL COMMENT '空间id',
                                                      `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                                      `redirect_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '回调名称',
                `redirect_type` tinyint(1) DEFAULT NULL COMMENT '回调类型 0-webhook',
                `redirect_request_timeout` int DEFAULT NULL COMMENT '回调请求超时时长',
                `redirect_request_connect_timeout` int DEFAULT NULL COMMENT '回调超时时间',
                `redirect_invoke_time` int DEFAULT NULL COMMENT '回调被调用次数',
                `redirect_fn` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '回调方法明细 json',
                `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述',
                `creator_id` bigint DEFAULT NULL COMMENT '创建的用户id',
                `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建的用户姓名',
                `updator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改的用户姓名',
                `updator_id` bigint DEFAULT NULL COMMENT '最后一次修改的用户id',
                `create_time` datetime DEFAULT NULL COMMENT '创建数据时间',
                `update_time` datetime DEFAULT NULL COMMENT '更新数据时间',
                `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被删除',
                `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
                PRIMARY KEY (`id`) USING BTREE,
                KEY `index_tenant_scope` (`engineering_id`,`module_id`,`space_id`,`tenant_id`) USING BTREE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='回调操作';


            CREATE TABLE IF NOT EXISTS `tag` (
                                                 `id` bigint NOT NULL,
                                                 `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                                 `engineering_id` bigint DEFAULT NULL COMMENT '工程id',
                                                 `module_id` bigint DEFAULT NULL COMMENT '模块id',
                                                 `tag_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '键',
                `tag_value` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '值',
                `creator_id` bigint DEFAULT NULL COMMENT '创建的用户id',
                `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建的用户姓名',
                `updator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改的用户姓名',
                `updator_id` bigint DEFAULT NULL COMMENT '最后一次修改的用户id',
                `create_time` datetime DEFAULT NULL COMMENT '创建数据时间',
                `update_time` datetime DEFAULT NULL COMMENT '更新数据时间',
                `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被删除',
                `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
                `space_id` bigint DEFAULT NULL COMMENT '空间id',
                PRIMARY KEY (`id`) USING BTREE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='租户id';

            CREATE TABLE IF NOT EXISTS `white_list` (
                                                        `ID` bigint NOT NULL,
                                                        `ip` varchar(15) COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT 'ip地址',
                `remark` varchar(256) COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '备注',
                `app_secret_id` bigint DEFAULT NULL COMMENT '应用密钥',
                `VERSION` int DEFAULT '1' COMMENT '版本号',
                `DELETED` tinyint(1) DEFAULT '0' COMMENT '逻辑删除，1-删除',
                `CREATOR_ID` bigint DEFAULT NULL COMMENT '创建人ID',
                `CREATOR` varchar(90) COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人',
                `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                `UPDATOR_ID` bigint DEFAULT NULL COMMENT '创建人ID',
                `UPDATOR` varchar(90) COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人',
                `UPDATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                `ENGINEERING_ID` bigint DEFAULT NULL COMMENT '工程ID',
                `MODULE_ID` bigint DEFAULT NULL COMMENT '模块ID',
                `SPACE_ID` bigint DEFAULT NULL COMMENT '空间ID',
                PRIMARY KEY (`ID`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_as_cs COMMENT='应用密钥白名单';
        </sql>
    </changeSet>


</databaseChangeLog>
