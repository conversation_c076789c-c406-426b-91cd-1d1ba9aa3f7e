<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="wxd" id="20230306057">
        <sql>
            CREATE TABLE device_template(
            ID BIGINT NOT NULL,
            name VARCHAR(256) COMMENT '设备模板名称' ,
            doc_size BIGINT COMMENT '文件大小' ,
            generate_type tinyint COMMENT '生成类型 0本地 1导入' ,
            download_link VARCHAR(512) COMMENT '下载链接' ,
            platform_version VARCHAR(16) COMMENT '平台版本',
            descript VARCHAR(512) COMMENT '模板描述',
            VERSION INT DEFAULT 1 COMMENT '版本号' ,
            DELETED TINYINT(1) DEFAULT 0 COMMENT '逻辑删除，1-删除' ,
            CREATOR_ID BIGINT COMMENT '创建人ID' ,
            CREATOR VARCHAR(90) COMMENT '创建人' ,
            CREATE_TIME DATETIME COMMENT '创建时间' ,
            UPDATOR_ID BIGINT COMMENT '创建人ID' ,
            UPDATOR VARCHAR(90) COMMENT '创建人' ,
            UPDATE_TIME DATETIME COMMENT '创建时间' ,
            tenant_id bigint COMMENT '租户id' ,
            ENGINEERING_ID BIGINT  COMMENT '工程ID' ,
            MODULE_ID BIGINT  COMMENT '模块ID' ,
            SPACE_ID BIGINT  COMMENT '空间ID',
            PRIMARY KEY (ID)
            )  COMMENT = '设备模板表';
        </sql>
    </changeSet>
    
</databaseChangeLog>