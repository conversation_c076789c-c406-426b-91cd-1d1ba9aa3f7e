<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
  
  <changeSet author="linql" id="20230314145176">
    <sql>
      CREATE TABLE `upgrade_package` (
      `id` bigint NOT NULL,
      `upgrade_package_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '升级包名称',
      `upgrade_version` varchar(32) NOT NULL COMMENT '升级包版本',
      `upgrade_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '升级包类型(0 普通,1 紧急)',
      `is_expect_version` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否期望版本(0 否,1 是)',
      `download_link` varchar(512) COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '下载链接',
      `descript` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '升级包描述',
      `upgrade_range` json DEFAULT NULL COMMENT '升级范围',
      `md5_proofread` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'md5校对',
      `engineering_id` bigint DEFAULT NULL COMMENT '工程id',
      `module_id` bigint DEFAULT NULL COMMENT '模块id',
      `space_id` bigint DEFAULT NULL COMMENT '空间id',
      `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
      `creator_id` bigint DEFAULT NULL COMMENT '创建的用户id',
      `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建的用户姓名',
      `updator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改的用户姓名',
      `updator_id` bigint DEFAULT NULL COMMENT '最后一次修改的用户id',
      `create_time` datetime DEFAULT NULL COMMENT '创建数据时间',
      `update_time` datetime DEFAULT NULL COMMENT '更新数据时间',
      `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被删除',
      `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
      PRIMARY KEY (`id`) USING BTREE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='升级包配置';
    </sql>
  </changeSet>
  
  <changeSet author="linql" id="20230314145136">
    <addColumn tableName="edge_gateway" >
      <column name="target_version"
              type="VARCHAR(32)"
              remarks="期望版本号"
              afterColumn="runtime_info" >
      </column>
    </addColumn>
  </changeSet>
  <changeSet author="linql" id="20230314145137">
    <addColumn tableName="edge_gateway" >
      <column name="upgrade_begin_time"
              type="datetime"
              remarks="升级开始时间"
              afterColumn="target_version" >
      </column>
    </addColumn>
  </changeSet>
  <changeSet author="linql" id="20230315145157">
    <addColumn tableName="edge_gateway" >
      <column name="upgrade_status"
              type="tinyint(1)" defaultValue="0"
              remarks="升级状态：0-不处理，1，下载安装包 ，2，下载中，3-立即升级，4-升级中"
              afterColumn="upgrade_begin_time" >
      </column>
    </addColumn>
  </changeSet>
    
</databaseChangeLog>
