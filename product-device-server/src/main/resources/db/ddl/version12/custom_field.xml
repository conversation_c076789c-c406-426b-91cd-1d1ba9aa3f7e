<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun" id="1677120523">
        <sql>
            ALTER TABLE custom_field MODIFY target_type tinyint(1) NOT NULL COMMENT '所属类型，1-driver协议字段, 2-message协议包字段' ;
        </sql>
    </changeSet>
    
    <changeSet author="sushangqun" id="1677120524">
        <sql>
            ALTER TABLE custom_field ADD COLUMN part_type tinyint(1) NOT NULL COMMENT '部分类型，1-header头,2-body体,3-tail尾' AFTER target_id;
        </sql>
    </changeSet>
    
    <changeSet author="sushangqun" id="1677120525">
        <sql>
            ALTER TABLE custom_field MODIFY part_type tinyint(1) NOT NULL DEFAULT 1 COMMENT '部分类型，1-header头,2-body体,3-tail尾' ;
        </sql>
    </changeSet>
    
    <changeSet author="sushangqun" id="1677120526">
        <sql>
            ALTER TABLE custom_field MODIFY part_type tinyint(1) COMMENT '部分类型，1-header头,2-body体,3-tail尾' ;
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1677120527">
        <sql>
            ALTER TABLE custom_field MODIFY part_type tinyint(1) null COMMENT '部分类型，1-header头,2-body体,3-tail尾' ;
        </sql>
    </changeSet>
</databaseChangeLog>
