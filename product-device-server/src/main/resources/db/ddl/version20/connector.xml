<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
  <changeSet author="linql" id="20230711165525">
    <sql>
      create table `connector` (
          `id` bigint NOT NULL,
          `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '连接器名称',
          `descript` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '描述',
          `mode_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '模式类型 0-接收;1-发送',
          `connector_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '连接器类型 0-MQTT;1-HTTP',
          `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '连接器状态 0-停用;1-启用',
          `connector_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '连接器信息明细 json',
          `engineering_id` bigint DEFAULT NULL COMMENT '工程id',
          `space_id` bigint DEFAULT NULL COMMENT '空间id',
          `module_id` bigint DEFAULT NULL COMMENT '模块id',
          `tenant_id` bigint DEFAULT NULL COMMENT '上报租户id',
          `creator_id` bigint DEFAULT NULL COMMENT '创建的用户id',
          `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建的用户姓名',
          `updator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改的用户姓名',
          `updator_id` bigint DEFAULT NULL COMMENT '最后一次修改的用户id',
          `create_time` datetime DEFAULT NULL COMMENT '创建数据时间',
          `update_time` datetime DEFAULT NULL COMMENT '更新数据时间',
          `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被删除',
          `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
          PRIMARY KEY (`id`) USING BTREE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='连接器';
    </sql>
  </changeSet>
  
  <changeSet author="linql" id="20230711185518">
    <sql>
      create table `connector_item` (
          `id` bigint NOT NULL,
          `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
          `topic` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'topic名称',
          `descript` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '描述',
          `process_code` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '处理代码',
          `connector_id` bigint NOT NULL COMMENT '连接器id',
          `engineering_id` bigint DEFAULT NULL COMMENT '工程id',
          `space_id` bigint DEFAULT NULL COMMENT '空间id',
          `module_id` bigint DEFAULT NULL COMMENT '模块id',
          `tenant_id` bigint DEFAULT NULL COMMENT '上报租户id',
          `creator_id` bigint DEFAULT NULL COMMENT '创建的用户id',
          `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建的用户姓名',
          `updator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改的用户姓名',
          `updator_id` bigint DEFAULT NULL COMMENT '最后一次修改的用户id',
          `create_time` datetime DEFAULT NULL COMMENT '创建数据时间',
          `update_time` datetime DEFAULT NULL COMMENT '更新数据时间',
          `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被删除',
          `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
          PRIMARY KEY (`id`) USING BTREE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='连接器详情项';
    </sql>
  </changeSet>
</databaseChangeLog>
