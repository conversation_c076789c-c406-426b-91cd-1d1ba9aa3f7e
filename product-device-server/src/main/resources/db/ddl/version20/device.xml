<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="wxd" id="20230506172117">
        <addColumn tableName="device">
            <column name="channel"
                    type="varchar(256)"
                    remarks="设备关联通道"
                    afterColumn="sync_status">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

<!--    <changeSet author="wxd" id="20230507172117">-->
<!--        <sql>-->
<!--            ALTER TABLE device change column source source_group varchar(1024)  COMMENT '设备关联分组' ;-->
<!--        </sql>-->
<!--    </changeSet>-->

    <changeSet id="20230711140023" author="lll">
        <sql>
            ALTER TABLE `device` CHANGE `name` `name` VARCHAR(512)  CHARACTER SET utf8mb4  COLLATE utf8mb4_0900_ai_ci  NULL  DEFAULT NULL  COMMENT '设备名称';
        </sql>
    </changeSet>

    <changeSet id="202309011640001" author="grc">
        <sql>
            ALTER TABLE `device`
                ADD COLUMN resource_id VARCHAR(255) DEFAULT NULL COMMENT '设备标识';
        </sql>
    </changeSet>
    <changeSet id="202309011640002" author="grc">
        <sql>
            UPDATE `device`
            SET resource_id = id;
        </sql>
    </changeSet>
    <changeSet id="202309011640003" author="grc">
        <sql>
            ALTER TABLE `device`
                MODIFY COLUMN resource_id VARCHAR(255) NOT NULL COMMENT '设备标识';
        </sql>
    </changeSet>
</databaseChangeLog>
