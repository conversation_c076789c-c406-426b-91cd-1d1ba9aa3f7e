<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="202305110123" author="wxd">
        <addColumn tableName="label_bind_relation">
            <column name="label_name"
                    afterColumn="property_name"
                    type="VARCHAR(512)"
                    remarks="属性绑定的数据来源完整路径，channelName.labelGroupName.labelName"
            >
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="202305120123" author="wxd">
        <addColumn tableName="label_bind_relation">
            <column name="edge_gateway_id"
                    afterColumn="property_name"
                    type="BIGINT"
                    remarks="标签所属的网关！">
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="202305260123" author="wxd">
        <addColumn tableName="label_bind_relation">
            <column name="label_group_name"
                    afterColumn="label_name"
                    type="VARCHAR(128)"
                    remarks="属性绑定的数据来源绑定的分组名称，labelGroupName"
            >
            </column>
            <column name="channel_name"
                    afterColumn="label_name"
                    type="VARCHAR(256)"
                    remarks="属性绑定的数据来源绑定的通道名称，channelName"
            >
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="202305261723" author="wxd">
        <dropNotNullConstraint tableName="label_bind_relation" columnName="label_id" columnDataType="BIGINT"/>
    </changeSet>

</databaseChangeLog>
