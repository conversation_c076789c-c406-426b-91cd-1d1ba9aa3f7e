<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="20230804165555" author="linql">
      <sql>
        ALTER TABLE subscription ADD COLUMN from_id VARCHAR(512) COMMENT '来源id集合' AFTER target_service;
      </sql>
    </changeSet>

    <changeSet id="1692858470" author="sushangqun">
      <sql>
        ALTER TABLE subscription ADD COLUMN no_change_seconds int COMMENT '未改变时长，单位秒' AFTER send_one_by_one;
      </sql>
    </changeSet>

</databaseChangeLog>
