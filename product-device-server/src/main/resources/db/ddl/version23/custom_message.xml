<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="sushangqun" id="1698137829">
        <sql>
            ALTER TABLE custom_message ADD COLUMN auto_send TINYINT COMMENT '自动发送，0-关闭，1-开启' AFTER auto_response_config;
            ALTER TABLE custom_message ADD COLUMN auto_send_interval INT COMMENT '自动发送间隔，毫秒' AFTER auto_response_config;
            ALTER TABLE custom_message ADD COLUMN auto_send_var_str TEXT COMMENT '自动发送内容，json' AFTER auto_response_config;
        </sql>
    </changeSet>

    <changeSet author="zhoulc" id="202312261137">
        <sql>
            ALTER TABLE device_template
            ADD COLUMN inherit_thing_model_name varchar(255)  COMMENT '继承物模型名称' AFTER descript,
            ADD COLUMN channel_type varchar(32)  COMMENT '通道类型' AFTER inherit_thing_model_name,
            ADD COLUMN label_count int  COMMENT '模板标签数量' AFTER channel_type;
        </sql>
    </changeSet>


    <changeSet author="zhoulc" id="202401031737">
        <sql>
            ALTER TABLE device_template
            ADD COLUMN inherit_thing_model_id varchar(255)  COMMENT '继承物模型id' AFTER inherit_thing_model_name,
            ADD COLUMN channel_driver int COMMENT '通道驱动' AFTER channel_type,
            ADD COLUMN label_file_name varchar(255)  COMMENT '标签文件名称' AFTER label_count,
            ADD COLUMN label_file_link varchar(512)  COMMENT '标签文件存储路径' AFTER label_file_name;
        </sql>
    </changeSet>


    <changeSet author="linql" id="202405151041">
        <createIndex tableName="device" indexName="index_tenant_id">
            <column name="TENANT_ID"/>
            <column name="id"/>
        </createIndex>
        <createIndex tableName="edge_gateway" indexName="index_tenant_id">
            <column name="TENANT_ID"/>
            <column name="id"/>
        </createIndex>
        <createIndex tableName="redirect" indexName="index_tenant_id">
            <column name="TENANT_ID"/>
        </createIndex>
    </changeSet>

    <changeSet author="linql" id="202405200901">
        <createIndex tableName="device_model_inherit" indexName="index_tenant_id_inherit_id">
            <column name="TENANT_ID"/>
            <column name="inherit_thing_model_id"/>
        </createIndex>
        <createIndex tableName="thing_model" indexName="index_tenant_id">
            <column name="TENANT_ID"/>
        </createIndex>
        <createIndex tableName="thing_model_inherit" indexName="index_tenant_id_inherit_id">
            <column name="TENANT_ID"/>
            <column name="inherit_thing_model_id"/>
        </createIndex>
        <createIndex tableName="thing_service" indexName="index_tenant_id_model_id">
            <column name="TENANT_ID"/>
            <column name="thing_model_id"/>
        </createIndex>
    </changeSet>


    <changeSet author="linql" id="202406211737">
        <sql>
            ALTER TABLE redirect CHANGE redirect_request_connect_timeout request_connect_timeout INT;
        </sql>
    </changeSet>

</databaseChangeLog>
