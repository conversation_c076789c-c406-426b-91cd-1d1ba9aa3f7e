<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="sushangqun" id="1668732930">
        <sql>
            CREATE TABLE custom_field(
                id BIGINT AUTO_INCREMENT NOT NULL COMMENT 'id',
                target_type TINYINT NOT NULL COMMENT '所属类型，1-fixHeaderField, 2-messageField',
                target_id BIGINT NOT NULL COMMENT '所属id，fixHeaderField->协议id，messageField->消息id',
                
                sort_no INT NOT NULL COMMENT '排序号',
                field_name VARCHAR(255) NOT NULL   COMMENT '字段名称' ,
                field_descript TEXT    COMMENT '描述' ,
                start_byte INT COMMENT '开始byte位置，单位byte，从0开始',
                start_bit INT COMMENT '开始位索引，单位bit，范围0-7,null为0',
                data_type TINYINT  COMMENT '数据类型，参考kep数据类型，ThingDataTypeEnum',
                bytes INT COMMENT '字节长度，当dataType为string或byte才有效',
                field_type TINYINT COMMENT '字段类型，const-常量，variable-变量，byteLength-字节长度，count-数量',
                const_value VARCHAR(255) COMMENT '当字段类型为常量时，指定的常量值',
                byte_length_fields VARCHAR(512) COMMENT '当字段类型为字节长度时，需要计算的入参字段，逗号分隔',
                count_fields VARCHAR(512) COMMENT '当字段类型为数量时，需要计算的入参字段，逗号分隔',

                VERSION INT   DEFAULT 1 COMMENT '版本号' ,
                DELETED INT   DEFAULT 0 COMMENT '删除' ,
                CREATOR_ID BIGINT unsigned COMMENT '创建人ID' ,
                CREATOR VARCHAR(90)    COMMENT '创建人' ,
                CREATE_TIME DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                UPDATOR_ID BIGINT unsigned COMMENT '更新人ID' ,
                UPDATOR VARCHAR(90)    COMMENT '更新人' ,
                UPDATE_TIME DATETIME    COMMENT '更新时间' ,
                TENANT_ID BIGINT  COMMENT '租户id' ,
                ENGINEERING_ID BIGINT COMMENT '工程ID' ,
                MODULE_ID BIGINT COMMENT '模块ID' ,
                SPACE_ID BIGINT COMMENT '空间ID',

                PRIMARY KEY (id)
            )  COMMENT = '自定义字段表';
        </sql>
    </changeSet>
    
    

    <changeSet author="sushangqun" id="1669252982">
        <sql>
            ALTER TABLE custom_field ADD COLUMN field_value VARCHAR(511) COMMENT '当字段类型为常量时，指定的常量值。当字段类型为字节长度时，需要计算的入参字段，逗号分隔。当字段类型为数量时，需要计算的入参字段，逗号分隔' AFTER field_type;
            ALTER TABLE custom_field DROP COLUMN const_value;
            ALTER TABLE custom_field DROP COLUMN byte_length_fields;
            ALTER TABLE custom_field DROP COLUMN count_fields;
        </sql>
    </changeSet>

    <changeSet id="16687329282" author="sushangqun">
        <sql>
            ALTER TABLE custom_field CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs;
        </sql>
    </changeSet>

</databaseChangeLog>
