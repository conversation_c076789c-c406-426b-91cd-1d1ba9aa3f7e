<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="20221104110123" author="guosheng">
        <sql>
            ALTER TABLE label_bind_relation ADD deleted int DEFAULT '0' COMMENT '是否删除' ;
        </sql>
    </changeSet>


    <changeSet id="20221110110123" author="guosheng">
        <sql>
            DROP INDEX udx_tenant_id_device_property ON label_bind_relation;
        </sql>
    </changeSet>

    <changeSet author="guosheng" id="202211221052">
        <sql>
            alter  table  label_bind_relation  add  index  idx_tenant_id  (tenant_id);
        </sql>
    </changeSet>

    <changeSet id="20221112210123" author="guosheng">
        <sql>
            DROP INDEX idx_tenant_id ON label_bind_relation;
        </sql>
    </changeSet>

</databaseChangeLog>
