<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun (generated)" id="1648525882825-6">
        <createTable remarks="采集任务表" tableName="gather_task">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="edge_gateway_id" remarks="所属网关id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="name" remarks="任务名称" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="content" remarks="任务内容" type="json"/>
            <column defaultValueBoolean="false" name="enable_status" remarks="启用状态，0-关闭，1-启用" type="TINYINT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="1" name="version" remarks="版本号" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="create_time" remarks="创建时间" type="datetime">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    
</databaseChangeLog>
