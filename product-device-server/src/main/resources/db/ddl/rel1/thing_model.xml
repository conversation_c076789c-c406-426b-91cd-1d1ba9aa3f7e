<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="sushangqun (generated)" id="1648525882825-12">
        <createTable remarks="物模型表" tableName="thing_model">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="物模型名称，如：灯、风扇" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="model" remarks="物模型定义" type="json"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="create_time" remarks="创建时间" type="datetime">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet author="sushangqun" id="1649985871">
        <sql>
            ALTER TABLE thing_model ADD COLUMN ENGINEERING_ID bigint COMMENT '工程id';
            ALTER TABLE thing_model ADD COLUMN SPACE_ID bigint COMMENT '空间id';
            ALTER TABLE thing_model ADD COLUMN MODULE_ID bigint COMMENT '模块id';
            ALTER TABLE thing_model ADD COLUMN CREATOR varchar(90) DEFAULT NULL COMMENT '创建人名称';
            ALTER TABLE thing_model ADD COLUMN CREATOR_ID bigint unsigned DEFAULT NULL COMMENT '创建人id';
            ALTER TABLE thing_model ADD COLUMN UPDATOR_ID bigint unsigned DEFAULT NULL COMMENT '修改人id';
            ALTER TABLE thing_model ADD COLUMN UPDATOR varchar(90) DEFAULT NULL COMMENT '修改人名称';
            ALTER TABLE thing_model ADD COLUMN UPDATE_TIME datetime DEFAULT NULL COMMENT '修改时间';
            ALTER TABLE thing_model ADD COLUMN VERSION int DEFAULT '1' COMMENT '版本号';
            ALTER TABLE thing_model ADD COLUMN DELETED int DEFAULT '0' COMMENT '是否删除';
        </sql>
    </changeSet>
    
</databaseChangeLog>
