<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="sushangqun (generated)" id="1648525882825-2">
        <createTable remarks="参数描述" tableName="channel_param">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="channel_id" remarks="所属通道id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="name" remarks="通道参数名称，如ip/port/rack/slot等" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="value" remarks="通道参数值" type="VARCHAR(128)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="create_time" remarks="创建时间" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="descript" remarks="参数描述" type="TEXT"/>
        </createTable>
    </changeSet>

</databaseChangeLog>
