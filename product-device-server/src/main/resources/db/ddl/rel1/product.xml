<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="sushangqun (generated)" id="1648525882825-10">
        <createTable remarks="产品表" tableName="product">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="产品名称" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="descript" remarks="产品描述" type="TEXT"/>
            <column name="node_type" remarks="节点类型，1-直连设备，2-网关子设备，3-网关设备" type="TINYINT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="create_time" remarks="创建时间" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="thing_model_id" remarks="物模型id" type="BIGINT"/>
        </createTable>
    </changeSet>

</databaseChangeLog>
