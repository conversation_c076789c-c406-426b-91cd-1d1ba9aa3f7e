<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="sushangqun (generated)" id="1648525882825-7">
        <createTable remarks="标签表" tableName="label">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="标签名称" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="descript" remarks="标签描述" type="TEXT"/>
            <column name="address" remarks="地址，如DB50.DBB1" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="length" remarks="长度" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="create_time" remarks="创建时间" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="data_type" remarks="数据类型，bool/byte/short/int/float/string" type="VARCHAR(16)">
                <constraints nullable="false"/>
            </column>
            <column name="is_array" remarks="是否数组，根据上面3个数据自动识别" type="TINYINT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="label_group_id" remarks="所属标签分组id" type="BIGINT"/>
            <column name="string_bytes" remarks="type是string类型时，表示string元素的byte长度，其他type类型放空" type="INT"/>
            <column name="tag" remarks="标签标识（用”,“隔开）" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <changeSet id="20220424102041" author="guosheng">
        <sql>
            ALTER TABLE label ADD COLUMN ENGINEERING_ID bigint COMMENT '工程id';
            ALTER TABLE label ADD COLUMN SPACE_ID bigint COMMENT '空间id';
            ALTER TABLE label ADD COLUMN MODULE_ID bigint COMMENT '模块id';
            ALTER TABLE label ADD COLUMN CREATOR varchar(90) DEFAULT NULL COMMENT '创建人名称';
            ALTER TABLE label ADD COLUMN CREATOR_ID bigint unsigned DEFAULT NULL COMMENT '创建人id';
            ALTER TABLE label ADD COLUMN UPDATOR_ID bigint unsigned DEFAULT NULL COMMENT '修改人id';
            ALTER TABLE label ADD COLUMN UPDATOR varchar(90) DEFAULT NULL COMMENT '修改人名称';
            ALTER TABLE label ADD COLUMN UPDATE_TIME datetime DEFAULT NULL COMMENT '修改时间';
            ALTER TABLE label ADD COLUMN VERSION int DEFAULT '1' COMMENT '版本号';
            ALTER TABLE label ADD COLUMN DELETED int DEFAULT '0' COMMENT '是否删除';
        </sql>
    </changeSet>
    
</databaseChangeLog>
