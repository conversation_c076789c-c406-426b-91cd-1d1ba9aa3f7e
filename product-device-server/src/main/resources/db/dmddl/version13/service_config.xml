<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="gongrongcheng" id="202204071650999">
        <sql>
            DROP TABLE IF EXISTS service_config;
            CREATE TABLE service_config
            (
                ID               BIGINT  NOT NULL,
                thing_service_id BIGINT  NOT NULL COMMENT '物服务ID',
                thing_model_id   BIGINT  COMMENT '关联的直属的物模型 方便物模型更改时查找影响',
                device_id        BIGINT  COMMENT '所属设备',
                config_map       CLOB   DEFAULT NULL COMMENT '服务配置',
                CREATOR_ID       BIGINT  COMMENT '创建人ID',
                CREATOR          VARCHAR(90) COMMENT '创建人',
                CREATE_TIME      DATETIME COMMENT '创建时间',
                UPDATOR          VARCHAR(90) COMMENT '修改人',
                UPDATE_TIME      DATETIME COMMENT '修改时间',
                ENGINEERING_ID   BIGINT COMMENT '工程ID',
                MODULE_ID        BIGINT COMMENT '模块ID',
                SPACE_ID         BIGINT COMMENT '空间ID',
                tenant_id        bigint DEFAULT NULL COMMENT '租户id',
                deleted          int    DEFAULT 0 COMMENT '是否删除',
                PRIMARY KEY (ID)
            );
            COMMENT ON TABLE service_config IS '通用物服务配置表';
        </sql>
    </changeSet>

    <!-- 通用模板 通用服务 -->
    <changeSet id="92302932932032324" author="grc">
        <sql>
            INSERT INTO thing_model (
                id,
                name,
                model,
                descript,
                model_type,
                create_time,
                ENGINEERING_ID,
                SPACE_ID,
                MODULE_ID,
                CREATOR,
                CREATOR_ID,
                UPDATOR_ID,
                UPDATOR,
                UPDATE_TIME,
                VERSION,
                deleted,
                tenant_id
            )
            VALUES
                (
                    1,
                    '通用开关模型',
                    NULL,
                    '设备通用开关模型',
                    4,
                    '2023-03-06 11:37:39',
                    0,
                    0,
                    0,
                    'admin',
                    0,
                    0,
                    'admin',
                    '2023-03-06 11:37:52',
                    1,
                    0,
                    0
                );
        </sql>
    </changeSet>

  <!--  /*INSERT INTO thing_service (
    ID,
    thing_model_id,
    service_name,
    service_type,
    descript,
    override,
    input_data,
    output_data,
    service_code,
    VERSION,
    DELETED,
    CREATOR_ID,
    CREATOR,
    CREATE_TIME,
    UPDATOR_ID,
    UPDATOR,
    UPDATE_TIME,
    ENGINEERING_ID,
    MODULE_ID,
    SPACE_ID,
    async,
    tenant_id
    )
    VALUES
    (
    1,
    1,
    'switch_control',
    4,
    '通用开关服务',
    0,
    '[{\"jSON\": true, \"name\": \"func\", \"isArray\": false, \"dataType\": 14, \"descript\": \"开关操作 open or close\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"closeVal\", \"isArray\": false, \"dataType\": 1, \"descript\": \"关属性值\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"openVal\", \"isArray\": false, \"dataType\": 1, \"descript\": \"开属性值\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"closeProp\", \"isArray\": false, \"dataType\": 14, \"descript\": \"关属性\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"openProp\", \"isArray\": false, \"dataType\": 14, \"descript\": \"开属性\", \"editable\": null, \"dataModelId\": null}]',
    '{\"jSON\": true, \"isArray\": null, \"dataType\": 1, \"descript\": \"执行结果\", \"dataModelId\": null, \"outputDataDescript\": \"\"}',
    'if(input.func){\n    if(input.func == \"open\"){\n        \n    	return me.writeProperty(input.openProp, input.openVal)\n      \n    }else if (input.func == \"close\"){\n        \n        return me.writeProperty(input.closeProp, input.closeVal)\n    }\n}',
    1,
    0,
    0,
    'admin',
    '2023-03-06 11:52:43',
    0,
    'admin',
    '2023-03-06 11:52:43',
    0,
    0,
    0,
    0,
    0
    );*/-->

</databaseChangeLog>