<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="liuyiran" id="20220922114300">
        <createTable tableName="subscription">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="订阅名称" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="descript" remarks="设备描述" type="TEXT"/>
            <column name="properties" remarks="订阅属性，用“,”隔开" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="event_type" remarks="事件类型，1-anyDataChange，0-dataChange" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="callback_id" remarks="回调ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="directly_model_id" remarks="直属模型ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="model_type" remarks="直属模型类型，1-设备模型，0-物模型" type="INT">
                <constraints nullable="false"/>
            </column>

            <column name="engineering_id" type="BIGINT" remarks="工程id"/>
            <column name="module_id" type="BIGINT" remarks="模块id"/>
            <column name="space_id" type="BIGINT" remarks="空间id"/>
            <column name="tenant_id" type="BIGINT" remarks="租户id"/>
            <column name="creator_id" type="BIGINT" remarks="创建的用户id"/>
            <column name="creator" type="VARCHAR(64)" remarks="创建的用户姓名"/>
            <column name="updator" type="VARCHAR(64)" remarks="修改的用户姓名"/>
            <column name="updator_id" type="BIGINT" remarks="最后一次修改的用户id"/>
            <column name="create_time" type="DATETIME" remarks="创建数据时间"/>
            <column name="update_time" type="DATETIME" remarks="更新数据时间"/>
            <column name="deleted" type="INT" defaultValueBoolean="false" remarks="是否被删除">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int" defaultValueNumeric="0" remarks="乐观锁版本号">
                <constraints nullable="false"/>
            </column>

        </createTable>

    </changeSet>

    <changeSet id="20220922144300" author="liuyiran">
        <createIndex tableName="subscription" indexName="subscription_tenant_index">
            <column name="tenant_id"/>
            <column name="model_type"/>
            <column name="directly_model_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="20220926145100" author="liuyiran">
        <addColumn tableName="subscription">
            <column name="data_conversion_code"
                    type="TEXT"
                    remarks="数据转换代码"
                    >
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

<!--    <changeSet id="20221010145100" author="liuyiran">
        <modifyDataType tableName="subscription" columnName="PROPERTIES" newDataType="TEXT"/>
    </changeSet>-->


</databaseChangeLog>
