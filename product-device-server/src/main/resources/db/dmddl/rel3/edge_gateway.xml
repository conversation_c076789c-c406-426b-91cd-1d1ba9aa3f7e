<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="guosheng" id="20220505145952">
        <sql>
            ALTER TABLE edge_gateway ADD "type" int;
            COMMENT ON COLUMN edge_gateway."type" IS '网关类型 虚拟网关/网关设备';

            ALTER TABLE edge_gateway ADD status int;
            COMMENT ON COLUMN edge_gateway.status IS '状态 1启用 0停用 2未激活';

            ALTER TABLE edge_gateway ADD sync_time datetime;
            COMMENT ON COLUMN edge_gateway.sync_time IS '同步时间';

        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1652067664">
        <dropColumn tableName="edge_gateway">
            <column name="model"></column>
        </dropColumn>
    </changeSet>


    <changeSet id="20220513172117" author="guosheng">
        <sql>
            ALTER TABLE edge_gateway ADD tenant_id bigint;
            COMMENT ON COLUMN edge_gateway.tenant_id IS '租户id';
        </sql>
    </changeSet>

    <changeSet id="20220513172118" author="guosheng">
        <sql>
            ALTER TABLE edge_gateway ADD sync_status INT NOT NULL DEFAULT 0;
            COMMENT ON COLUMN edge_gateway.sync_status IS '同步状态:0-未同步，1-已同步';
        </sql>
    </changeSet>

    <changeSet id="1653276145" author="sushangqun">
        <sql>
            ALTER TABLE edge_gateway MODIFY status INT NOT NULL DEFAULT 2;
            COMMENT ON COLUMN edge_gateway.status IS '状态 1启用 0停用 2未激活';
        </sql>
    </changeSet>

</databaseChangeLog>
