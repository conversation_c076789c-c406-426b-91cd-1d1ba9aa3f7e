<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd
        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">
    <preConditions>
        <dbms type="oracle"/>
    </preConditions>
    <changeSet id="create.table" author="zhenglifan">
        <createTable tableName="pkg_package_version" remarks="打包记录">
            <column name="id" type="BIGINT" remarks="主键">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT" remarks="租户id" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="space_id" type="BIGINT" remarks="空间id"/>
            <column name="engineering_id" type="bigint" remarks="工程id">
                <constraints nullable="false"/>
            </column>

            <column name="module_id" type="BIGINT" remarks="模块id"/>

            <column name="package_version" type="varchar(32)" remarks="包版本">
                <constraints nullable="false"/>
            </column>
            <column name="oss_path" type="varchar(256)" remarks="oss文件地址">
                <constraints nullable="false"/>
            </column>

            <column name="oss_file_id" type="bigint" remarks="oss文件id">
                <constraints nullable="false"/>
            </column>
            <column name="file_name" type="varchar(256)" remarks="文件名" defaultValue="">
                <constraints nullable="false"/>
            </column>

            <column name="creator_id" type="bigint" remarks="创建的用户id"/>
            <column name="updator_id" type="bigint" remarks="最后一次修改的用户id"/>
            <column name="create_time" type="datetime" remarks="创建数据时间"/>
            <column name="creator" type="varchar(64)" remarks="创建的用户姓名"/>
            <column name="updator" type="varchar(64)" remarks="修改的用户姓名"/>
            <column name="update_time" type="datetime" remarks="更新数据时间"/>
            <column name="deleted" type="int" defaultValue="0" remarks="是否被删除">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int" defaultValue="0" remarks="乐观锁版本号">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="1654492665" author="liuyiran">
        <addColumn tableName="pkg_package_version">
            <column name="base_sys_path" type="varchar(256)" remarks="基础版本包" >
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>


</databaseChangeLog>