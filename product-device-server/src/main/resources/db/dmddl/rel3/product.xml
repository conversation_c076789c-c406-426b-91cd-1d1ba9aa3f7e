<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="guosheng" id="20220505092152">
        <sql>
            ALTER TABLE product ADD image_id varchar(32);
            COMMENT ON COLUMN product.image_id IS '图片id';

        </sql>
    </changeSet>

    <changeSet author="guosheng" id="20220506130652">
        <sql>
            ALTER TABLE product ADD model_update_time datetime;
            COMMENT ON COLUMN product.model_update_time IS '模型最后变动时间';

        </sql>
    </changeSet>


    <changeSet id="20220510172117" author="guosheng">
        <sql>
            ALTER TABLE product ADD tenant_id bigint;
            COMMENT ON COLUMN product.tenant_id IS '租户id';

        </sql>
    </changeSet>

    <changeSet id="20220510172118" author="guosheng">
        <sql>
            ALTER TABLE product_model_inherit ADD tenant_id bigint;
            COMMENT ON COLUMN product_model_inherit.tenant_id IS '租户id';

        </sql>
    </changeSet>

    <changeSet id="20220510172119" author="guosheng">
        <sql>
            ALTER TABLE product_service ADD tenant_id bigint;
            COMMENT ON COLUMN product_service.tenant_id IS '租户id';

        </sql>
    </changeSet>

</databaseChangeLog>
