<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">


    <changeSet id="20220513172117" author="guosheng">
        <sql>
            ALTER TABLE channel ADD tenant_id bigint;
            COMMENT ON COLUMN channel.tenant_id IS '租户id';
        </sql>
    </changeSet>

    <changeSet id="20220513172118" author="guosheng">
        <sql>
            ALTER TABLE channel_param ADD tenant_id bigint;
            COMMENT ON COLUMN channel_param.tenant_id IS '租户id';
        </sql>
    </changeSet>

    <changeSet id="20220513172119" author="guosheng">
        <sql>
            ALTER TABLE channel ADD md5_proofread varchar(32);
            COMMENT ON COLUMN channel.md5_proofread IS 'md5校对';
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1652948876">
        <addColumn tableName="channel" >
            <column name="runtime_info"  
                type="CLOB"
                remarks="通道运行时信息"
                 >
                <constraints nullable="true" />  
            </column>
        </addColumn> 
    </changeSet>
    
</databaseChangeLog>
