<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="20220510172120" author="guosheng">
        <sql>
            ALTER TABLE label ADD tenant_id bigint;
            COMMENT ON COLUMN label.tenant_id IS '租户id';
        </sql>
    </changeSet>

    <changeSet id="20220510172121" author="guosheng">
        <sql>
            ALTER TABLE label_bind_relation ADD tenant_id bigint;
            COMMENT ON COLUMN label_bind_relation.tenant_id IS '租户id';
        </sql>
    </changeSet>

    <changeSet id="20220510172122" author="guosheng">
        <sql>
            ALTER TABLE label_group ADD tenant_id bigint;
            COMMENT ON COLUMN label_group.tenant_id IS '租户id';
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1650346227">
        <addColumn tableName="label" >
            <column name="gather_param"  
                type="CLOB"
                remarks="采集任务内容"
                 >
                <constraints nullable="true" />  
            </column>
        </addColumn> 
    </changeSet>
</databaseChangeLog>
