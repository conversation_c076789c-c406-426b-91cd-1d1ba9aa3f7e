<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="liuyiran (generated)" id="20220407164203">
        <addColumn tableName="product">
            <column defaultValueBoolean="false" name="status" remarks="产品状态，1-启用，0-关闭" type="TINYINT(1)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <sql>
            ALTER TABLE product ADD VERSION INT   DEFAULT 1;
            COMMENT ON COLUMN product.VERSION IS '版本号';

            ALTER TABLE product ADD DELETED INT DEFAULT 0;
            COMMENT ON COLUMN product.DELETED IS '逻辑删除，1-删除';

            ALTER TABLE product ADD ENGINEERING_ID BIGINT;
            COMMENT ON COLUMN product.ENGINEERING_ID IS '工程ID';

            ALTER TABLE product ADD MODULE_ID BIGINT;
            COMMENT ON COLUMN product.MODULE_ID IS '模块ID';

            ALTER TABLE product ADD SPACE_ID BIGINT;
            COMMENT ON COLUMN product.SPACE_ID IS '空间ID';

            ALTER TABLE product ADD CREATOR_ID BIGINT;
            COMMENT ON COLUMN product.CREATOR_ID IS '创建人ID';

            ALTER TABLE product ADD CREATOR VARCHAR(90);
            COMMENT ON COLUMN product.CREATOR IS '创建人';

            ALTER TABLE product ADD UPDATOR_ID BIGINT;
            COMMENT ON COLUMN product.UPDATOR_ID IS '更新人ID';

            ALTER TABLE product ADD UPDATOR VARCHAR(90);
            COMMENT ON COLUMN product.UPDATOR IS '更新人';

            ALTER TABLE product ADD UPDATE_TIME DATETIME;
            COMMENT ON COLUMN product.UPDATE_TIME IS '更新时间';

        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1651045037">
        <addColumn tableName="product">
            <column name="model" remarks="产品模型" type="CLOB" >
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="liuyiran" id="20220429105007">
        <dropColumn tableName="product">
            <column name="thing_model_id"></column>
        </dropColumn>
    </changeSet>

</databaseChangeLog>
