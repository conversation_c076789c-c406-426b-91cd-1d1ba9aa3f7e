<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun (generated)" id="1648525882826-1">
        <addColumn tableName="edge_gateway" >
            <column name="model"  
                type="CLOB"
                 >
                <constraints nullable="true" />
            </column>  
        </addColumn> 
    </changeSet>
    
    <changeSet author="sushangqun (generated)" id="1648525882826-1-1">
        <dropColumn columnName="model"  
                tableName="edge_gateway">  
        </dropColumn>
        
    </changeSet>
    <changeSet author="sushangqun (generated)" id="1648525882826-1-2">
        <addColumn tableName="edge_gateway" >
            <column name="model"  
                type="CLOB"
                remarks="网关模型"
                 >
                <constraints nullable="true" />
            </column>  
        </addColumn> 
    </changeSet>

    <changeSet id="20220425102043" author="guosheng">
        <sql>
            ALTER TABLE edge_gateway ADD COLUMN ENGINEERING_ID bigint;
            COMMENT ON COLUMN edge_gateway.ENGINEERING_ID IS '工程id';

            ALTER TABLE edge_gateway ADD COLUMN SPACE_ID bigint;
            COMMENT ON COLUMN edge_gateway.SPACE_ID IS '空间id';

            ALTER TABLE edge_gateway ADD COLUMN MODULE_ID bigint;
            COMMENT ON COLUMN edge_gateway.MODULE_ID IS '模块id';

            ALTER TABLE edge_gateway ADD COLUMN CREATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN edge_gateway.CREATOR IS '创建人名称';

            ALTER TABLE edge_gateway ADD COLUMN CREATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN edge_gateway.CREATOR_ID IS '创建人id';

            ALTER TABLE edge_gateway ADD COLUMN UPDATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN edge_gateway.UPDATOR_ID IS '修改人id';

            ALTER TABLE edge_gateway ADD COLUMN UPDATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN edge_gateway.UPDATOR IS '修改人名称';

            ALTER TABLE edge_gateway ADD COLUMN UPDATE_TIME datetime DEFAULT NULL;
            COMMENT ON COLUMN edge_gateway.UPDATE_TIME IS '修改时间';

            ALTER TABLE edge_gateway ADD COLUMN VERSION int DEFAULT 1;
            COMMENT ON COLUMN edge_gateway.VERSION IS '版本号';

            ALTER TABLE edge_gateway ADD COLUMN DELETED int DEFAULT 0;
            COMMENT ON COLUMN edge_gateway.DELETED IS '是否删除';
        </sql>
    </changeSet>

</databaseChangeLog>
