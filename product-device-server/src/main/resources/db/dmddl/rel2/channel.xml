<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
<!--    逻辑删除改为tinyint-->
    <changeSet author="guosheng" id="editChannel">
        <sql>
            ALTER TABLE channel ADD COLUMN ENGINEERING_ID bigint;
            COMMENT ON COLUMN channel.ENGINEERING_ID IS '工程id';

            ALTER TABLE channel ADD COLUMN SPACE_ID bigint;
            COMMENT ON COLUMN channel.SPACE_ID IS '空间id';

            ALTER TABLE channel ADD COLUMN MODULE_ID bigint;
            COMMENT ON COLUMN channel.MODULE_ID IS '模块id';

            ALTER TABLE channel ADD COLUMN CREATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN channel.CREATOR IS '创建人名称';

            ALTER TABLE channel ADD COLUMN CREATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN channel.CREATOR_ID IS '创建人id';

            ALTER TABLE channel ADD COLUMN UPDATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN channel.UPDATOR_ID IS '修改人id';

            ALTER TABLE channel ADD COLUMN UPDATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN channel.UPDATOR IS '修改人名称';

            ALTER TABLE channel ADD COLUMN UPDATE_TIME datetime DEFAULT NULL;
            COMMENT ON COLUMN channel.UPDATE_TIME IS '修改时间';

            ALTER TABLE channel ADD COLUMN VERSION int DEFAULT '1';
            COMMENT ON COLUMN channel.VERSION IS '版本号';

            ALTER TABLE channel ADD COLUMN DELETED int DEFAULT '0';
            COMMENT ON COLUMN channel.DELETED IS '是否删除';

            ALTER TABLE channel ADD COLUMN descript varchar(256);
            COMMENT ON COLUMN channel.descript IS '描述';

        </sql>
    </changeSet>

    <changeSet author="guosheng" id="editChannelParam">
        <sql>
            ALTER TABLE channel_param ADD COLUMN ENGINEERING_ID bigint;
            COMMENT ON COLUMN channel_param.ENGINEERING_ID IS '工程id';

            ALTER TABLE channel_param ADD COLUMN SPACE_ID bigint;
            COMMENT ON COLUMN channel_param.SPACE_ID IS '空间id';

            ALTER TABLE channel_param ADD COLUMN MODULE_ID bigint;
            COMMENT ON COLUMN channel_param.MODULE_ID IS '模块id';

            ALTER TABLE channel_param ADD COLUMN CREATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN channel_param.CREATOR IS '创建人名称';

            ALTER TABLE channel_param ADD COLUMN CREATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN channel_param.CREATOR_ID IS '创建人id';

            ALTER TABLE channel_param ADD COLUMN UPDATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN channel_param.UPDATOR_ID IS '修改人id';

            ALTER TABLE channel_param ADD COLUMN UPDATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN channel_param.UPDATOR IS '修改人名称';

            ALTER TABLE channel_param ADD COLUMN UPDATE_TIME datetime DEFAULT NULL;
            COMMENT ON COLUMN channel_param.UPDATE_TIME IS '修改时间';

            ALTER TABLE channel_param ADD COLUMN VERSION int DEFAULT '1';
            COMMENT ON COLUMN channel_param.VERSION IS '版本号';

            ALTER TABLE channel_param ADD COLUMN DELETED int DEFAULT '0';
            COMMENT ON COLUMN channel_param.DELETED IS '是否删除';

        </sql>
    </changeSet>


    <changeSet author="guosheng" id="addNecessary">
        <sql>
            ALTER TABLE channel_param ADD COLUMN necessary tinyint;
            COMMENT ON COLUMN channel_param.necessary IS '是否默认参数';
        </sql>
    </changeSet>
</databaseChangeLog>
