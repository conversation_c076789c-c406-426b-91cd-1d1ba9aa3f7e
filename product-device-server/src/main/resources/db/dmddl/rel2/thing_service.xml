<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="liuyiran (generated)" id="1649816128">
        <sql>
            DROP TABLE IF EXISTS thing_service;
            CREATE TABLE thing_service(
            ID BIGINT  NOT NULL,
            thing_model_id BIGINT  NOT NULL   COMMENT '所属物模型ID' ,
            service_name VARCHAR(255) NOT NULL   COMMENT '服务名称' ,
            descript TEXT    COMMENT '描述' ,
            override INT NOT NULL   COMMENT '是否允许覆盖' ,
            call_type INT NOT NULL   COMMENT '调用方式:0-sync（同步调用） 1-async（异步调用）' ,
            input_data CLOB    COMMENT '输入参数 JSON对象' ,
            output_data CLOB    COMMENT '结果 JSON对象' ,
            output_data_descript VARCHAR(900)    COMMENT '结果描述' ,
            service_code TEXT COMMENT '代码文本 需要限制代码长度' ,
            VERSION INT   DEFAULT 1 COMMENT '版本号' ,
            DELETED INT   DEFAULT 0 COMMENT '删除' ,
            CREATOR_ID BIGINT  COMMENT '创建人ID' ,
            CREATOR VARCHAR(90)    COMMENT '创建人' ,
            CREATE_TIME DATETIME    COMMENT '创建时间' ,
            UPDATOR_ID BIGINT  COMMENT '更新人ID' ,
            UPDATOR VARCHAR(90)    COMMENT '更新人' ,
            UPDATE_TIME DATETIME    COMMENT '更新时间' ,
            ENGINEERING_ID BIGINT  COMMENT '工程ID' ,
            MODULE_ID BIGINT  COMMENT '模块ID' ,
            SPACE_ID BIGINT  COMMENT '空间ID',
            PRIMARY KEY (ID)
            );
            COMMENT ON TABLE thing_service IS '物服务表';
        </sql>
    </changeSet>

    <changeSet author="liuyiran" id="1650016543">
        <dropColumn tableName="thing_service">
            <column name="call_type"></column>
        </dropColumn>
        <sql>
            ALTER TABLE thing_service ADD async INT NOT NULL;
            COMMENT ON COLUMN thing_service.async IS '调用方式:0-sync(同步调用) 1-async(异步调用)';

        </sql>
    </changeSet>
    <changeSet id="1652088333" author="liuyiran">
        <addUniqueConstraint tableName="thing_service" columnNames="thing_model_id,service_name,DELETED"/>
    </changeSet>
    
</databaseChangeLog>