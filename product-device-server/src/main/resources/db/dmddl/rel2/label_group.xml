<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun" id="1650346227">
        <addColumn tableName="label_group" >
            <column name="gather_task"  
                type="CLOB"
                remarks="采集任务内容"
                 >
                <constraints nullable="true" />  
            </column>
        </addColumn> 
    </changeSet>

    <changeSet id="20220424102042" author="guosheng">
        <sql>
            ALTER TABLE label_group ADD COLUMN ENGINEERING_ID bigint;
            COMMENT ON COLUMN label_group.ENGINEERING_ID IS '工程id';

            ALTER TABLE label_group ADD COLUMN SPACE_ID bigint;
            COMMENT ON COLUMN label_group.SPACE_ID IS '空间id';

            ALTER TABLE label_group ADD COLUMN MODULE_ID bigint;
            COMMENT ON COLUMN label_group.MODULE_ID IS '模块id';

            ALTER TABLE label_group ADD COLUMN CREATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN label_group.CREATOR IS '创建人名称';

            ALTER TABLE label_group ADD COLUMN CREATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN label_group.CREATOR_ID IS '创建人id';

            ALTER TABLE label_group ADD COLUMN UPDATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN label_group.UPDATOR_ID IS '修改人id';

            ALTER TABLE label_group ADD COLUMN UPDATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN label_group.UPDATOR IS '修改人名称';

            ALTER TABLE label_group ADD COLUMN UPDATE_TIME datetime DEFAULT NULL;
            COMMENT ON COLUMN label_group.UPDATE_TIME IS '修改时间';

            ALTER TABLE label_group ADD COLUMN VERSION int DEFAULT 1;
            COMMENT ON COLUMN label_group.VERSION IS '版本号';

            ALTER TABLE label_group ADD COLUMN DELETED int DEFAULT 0;
            COMMENT ON COLUMN label_group.DELETED IS '是否删除';

        </sql>
    </changeSet>

</databaseChangeLog>
