<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="sushangqun" id="1651057988">
        <sql>
            CREATE TABLE product_model_inherit (
                id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',

                product_id BIGINT NOT NULL COMMENT '产品id',
                inherit_thing_model_id BIGINT NOT NULL COMMENT '继承的物模型id',
                
                sort_no INT NOT NULL COMMENT '继承顺序',
                
                create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                PRIMARY KEY (id)
            );
            COMMENT ON TABLE product_model_inherit IS '产品模型继承表';
        </sql>
    </changeSet>
    
    <changeSet author="sushangqun" id="1651058076">
        <sql>
            ALTER TABLE product_model_inherit ADD COLUMN ENGINEERING_ID bigint;
            COMMENT ON COLUMN product_model_inherit.ENGINEERING_ID IS '工程id';

            ALTER TABLE product_model_inherit ADD COLUMN SPACE_ID bigint;
            COMMENT ON COLUMN product_model_inherit.SPACE_ID IS '空间id';

            ALTER TABLE product_model_inherit ADD COLUMN MODULE_ID bigint;
            COMMENT ON COLUMN product_model_inherit.MODULE_ID IS '模块id';

            ALTER TABLE product_model_inherit ADD COLUMN CREATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN product_model_inherit.CREATOR IS '创建人名称';

            ALTER TABLE product_model_inherit ADD COLUMN CREATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN product_model_inherit.CREATOR_ID IS '创建人id';

            ALTER TABLE product_model_inherit ADD COLUMN UPDATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN product_model_inherit.UPDATOR_ID IS '修改人id';

            ALTER TABLE product_model_inherit ADD COLUMN UPDATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN product_model_inherit.UPDATOR IS '修改人名称';

            ALTER TABLE product_model_inherit ADD COLUMN UPDATE_TIME datetime DEFAULT NULL;
            COMMENT ON COLUMN product_model_inherit.UPDATE_TIME IS '修改时间';

            ALTER TABLE product_model_inherit ADD COLUMN VERSION int DEFAULT 1;
            COMMENT ON COLUMN product_model_inherit.VERSION IS '版本号';

            ALTER TABLE product_model_inherit ADD COLUMN DELETED int DEFAULT 0;
            COMMENT ON COLUMN product_model_inherit.DELETED IS '是否删除';

        </sql>
    </changeSet>
</databaseChangeLog>