<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="liuyiran (generated)" id="1648525882826-4">
        <dropColumn tableName="device">
            <column name="metadata"></column>
        </dropColumn>
        <sql>
            ALTER TABLE device ADD VERSION INT DEFAULT 1;
            COMMENT ON COLUMN device.VERSION IS '版本号';

            ALTER TABLE device ADD DELETED INT DEFAULT 0;
            COMMENT ON COLUMN device.DELETED IS '逻辑删除，1-删除';

            ALTER TABLE device ADD ENGINEERING_ID BIGINT;
            COMMENT ON COLUMN device.ENGINEERING_ID IS '工程ID';

            ALTER TABLE device ADD MODULE_ID BIGINT;
            COMMENT ON COLUMN device.MODULE_ID IS '模块ID';

            ALTER TABLE device ADD SPACE_ID BIGINT;
            COMMENT ON COLUMN device.SPACE_ID IS '空间ID';

            ALTER TABLE device ADD CREATOR_ID BIGINT;
            COMMENT ON COLUMN device.CREATOR_ID IS '创建人ID';

            ALTER TABLE device ADD CREATOR VARCHAR(90);
            COMMENT ON COLUMN device.CREATOR IS '创建人';

            ALTER TABLE device ADD UPDATOR_ID BIGINT;
            COMMENT ON COLUMN device.UPDATOR_ID IS '更新人ID';

            ALTER TABLE device ADD UPDATOR VARCHAR(90);
            COMMENT ON COLUMN device.UPDATOR IS '更新人';

            ALTER TABLE device ADD UPDATE_TIME DATETIME;
            COMMENT ON COLUMN device.UPDATE_TIME IS '更新时间';

        </sql>
    </changeSet>

</databaseChangeLog>
