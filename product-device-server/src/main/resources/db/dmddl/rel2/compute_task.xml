<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun" id="1650356705">
        <dropColumn tableName="compute_task">
            <column name="gather_task_id"></column>
        </dropColumn>
        <addColumn tableName="compute_task">
            <column name="device_id" 
                remarks="所属设备id"
                type="BIGINT"
            >
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="compute_task">
            <column name="event_name" 
                remarks="所属事件名" 
                type="VARCHAR(128)"
            >
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="sushangqun" id="1650941420">
        <dropColumn tableName="compute_task">
            <column name="version"></column>
        </dropColumn>
    </changeSet>
    <changeSet author="sushangqun" id="1650941501">
        <sql>
            ALTER TABLE compute_task ADD COLUMN ENGINEERING_ID bigint;
            COMMENT ON COLUMN compute_task.ENGINEERING_ID IS '工程id';

            ALTER TABLE compute_task ADD COLUMN SPACE_ID bigint;
            COMMENT ON COLUMN compute_task.SPACE_ID IS '空间id';

            ALTER TABLE compute_task ADD COLUMN MODULE_ID bigint;
            COMMENT ON COLUMN compute_task.MODULE_ID IS '模块id';

            ALTER TABLE compute_task ADD COLUMN CREATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN compute_task.CREATOR IS '创建人名称';

            ALTER TABLE compute_task ADD COLUMN CREATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN compute_task.CREATOR_ID IS '创建人id';

            ALTER TABLE compute_task ADD COLUMN UPDATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN compute_task.UPDATOR_ID IS '修改人id';

            ALTER TABLE compute_task ADD COLUMN UPDATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN compute_task.UPDATOR IS '修改人名称';

            ALTER TABLE compute_task ADD COLUMN UPDATE_TIME datetime DEFAULT NULL;
            COMMENT ON COLUMN compute_task.UPDATE_TIME IS '修改时间';

            ALTER TABLE compute_task ADD COLUMN VERSION int DEFAULT '1';
            COMMENT ON COLUMN compute_task.VERSION IS '版本号';

            ALTER TABLE compute_task ADD COLUMN DELETED int DEFAULT '0';
            COMMENT ON COLUMN compute_task.DELETED IS '是否删除';

        </sql>
    </changeSet>
    
</databaseChangeLog>