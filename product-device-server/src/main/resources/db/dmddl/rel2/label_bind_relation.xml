<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="liuyiran (generated)" id="20220407165007">
        <sql>
            DROP TABLE IF EXISTS label_bind_relation;
            CREATE TABLE label_bind_relation(
            ID BIGINT  NOT NULL,
            thing_model_id BIGINT  NOT NULL COMMENT '关联的直属的物模型 方便物模型更改时查找影响' ,
            label_id BIGINT  NOT NULL COMMENT '绑定标签' ,
            device_id BIGINT  NOT NULL COMMENT '所属设备' ,
            param_name VARCHAR(255) NOT NULL COMMENT '属性名，应该是带层级的，可以直接匹配的' ,
            data_model_id BIGINT   COMMENT '关联的直属数据模型 方便数据模型更改是查找影响（如果属性是数据模型里的）' ,
            CREATOR_ID BIGINT  COMMENT '创建人ID' ,
            CREATOR VARCHAR(90) COMMENT '创建人' ,
            CREATE_TIME DATETIME COMMENT '创建时间' ,
            ENGINEERING_ID BIGINT  COMMENT '工程ID' ,
            MODULE_ID BIGINT  COMMENT '模块ID' ,
            SPACE_ID BIGINT  COMMENT '空间ID',
            PRIMARY KEY (ID)
            );
            COMMENT ON TABLE label_bind_relation IS '标签绑定关系表';
        </sql>
    </changeSet>

    <changeSet author="liuyiran" id="20220426165007">
        <sql>
            alter table label_bind_relation rename column param_name to property_name;
        </sql>
    </changeSet>
    <changeSet author="liuyiran" id="20220429095007">
        <sql>
            alter table label_bind_relation rename column thing_model_id to directly_model_id;
            alter table label_bind_relation add model_type INT;
            COMMENT ON COLUMN label_bind_relation.model_type IS '直属模型类型，1-物模型 2-产品模型';

        </sql>
    </changeSet>
</databaseChangeLog>