<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="sushangqun (generated)" id="1648525882826-2">
        <createTable remarks="数据模型表" tableName="data_model">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="数据模型名称" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="descript" remarks="描述" type="TEXT"/>

            <column defaultValueComputed="CURRENT_TIMESTAMP" name="create_time" remarks="创建时间" type="datetime">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="1649742465-data_model" author="sushangqun">
        <sql>
            ALTER TABLE data_model ADD COLUMN ENGINEERING_ID bigint;
            COMMENT ON COLUMN data_model.ENGINEERING_ID IS '工程id';

            ALTER TABLE data_model ADD COLUMN SPACE_ID bigint;
            COMMENT ON COLUMN data_model.SPACE_ID IS '空间id';

            ALTER TABLE data_model ADD COLUMN MODULE_ID bigint;
            COMMENT ON COLUMN data_model.MODULE_ID IS '模块id';

            ALTER TABLE data_model ADD COLUMN CREATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN data_model.CREATOR IS '创建人名称';

            ALTER TABLE data_model ADD COLUMN CREATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN data_model.CREATOR_ID IS '创建人id';

            ALTER TABLE data_model ADD COLUMN UPDATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN data_model.UPDATOR_ID IS '修改人id';

            ALTER TABLE data_model ADD COLUMN UPDATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN data_model.UPDATOR IS '修改人名称';

            ALTER TABLE data_model ADD COLUMN UPDATE_TIME datetime DEFAULT NULL;
            COMMENT ON COLUMN data_model.UPDATE_TIME IS '修改时间';

            ALTER TABLE data_model ADD COLUMN VERSION int DEFAULT '1';
            COMMENT ON COLUMN data_model.VERSION IS '版本号';

            ALTER TABLE data_model ADD COLUMN DELETED int DEFAULT '0';
            COMMENT ON COLUMN data_model.DELETED IS '是否删除';

        </sql>
    </changeSet>
    
</databaseChangeLog>
