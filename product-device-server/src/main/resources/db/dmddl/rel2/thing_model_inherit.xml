<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="sushangqun (generated)" id="1649834981">
        <createTable remarks="物模型继承表" tableName="thing_model_inherit">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="thing_model_id" remarks="物模型id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="inherit_thing_model_id" remarks="继承的物模型id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="sort_no" remarks="继承顺序，从小到大排列，后面模型的属性名、事件名不能跟前面的同名，服务允许覆盖才允许同名" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="1649835005" author="sushangqun">
        <sql>
            ALTER TABLE thing_model_inherit ADD COLUMN ENGINEERING_ID bigint;
            COMMENT ON COLUMN thing_model_inherit.ENGINEERING_ID IS '工程id';

            ALTER TABLE thing_model_inherit ADD COLUMN SPACE_ID bigint;
            COMMENT ON COLUMN thing_model_inherit.SPACE_ID IS '空间id';

            ALTER TABLE thing_model_inherit ADD COLUMN MODULE_ID bigint;
            COMMENT ON COLUMN thing_model_inherit.MODULE_ID IS '模块id';

            ALTER TABLE thing_model_inherit ADD COLUMN CREATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN thing_model_inherit.CREATOR IS '创建人名称';

            ALTER TABLE thing_model_inherit ADD COLUMN CREATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN thing_model_inherit.CREATOR_ID IS '创建人id';

            ALTER TABLE thing_model_inherit ADD COLUMN UPDATOR_ID bigint  DEFAULT NULL;
            COMMENT ON COLUMN thing_model_inherit.UPDATOR_ID IS '修改人id';

            ALTER TABLE thing_model_inherit ADD COLUMN UPDATOR varchar(90) DEFAULT NULL;
            COMMENT ON COLUMN thing_model_inherit.UPDATOR IS '修改人名称';

            ALTER TABLE thing_model_inherit ADD COLUMN UPDATE_TIME datetime DEFAULT NULL;
            COMMENT ON COLUMN thing_model_inherit.UPDATE_TIME IS '修改时间';

            ALTER TABLE thing_model_inherit ADD COLUMN VERSION int DEFAULT 1;
            COMMENT ON COLUMN thing_model_inherit.VERSION IS '版本号';

            ALTER TABLE thing_model_inherit ADD COLUMN DELETED int DEFAULT 0;
            COMMENT ON COLUMN thing_model_inherit.DELETED IS '是否删除';

        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1649985325">
        <sql>
            ALTER TABLE thing_model_inherit ADD COLUMN CREATE_TIME DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP;
            COMMENT ON COLUMN thing_model_inherit.CREATE_TIME IS '创建时间';

        </sql>
    </changeSet>
    
</databaseChangeLog>
