<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <preConditions>
        <dbms type="oracle"/>
    </preConditions>

    <changeSet id="create.table" author="ben">
        <createTable tableName="tag" remarks="标记表">
            <column name="id" type="BIGINT" remarks="主键">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="engineering_id" type="BIGINT" remarks="工程id"/>
            <column name="module_id" type="BIGINT" remarks="模块id"/>
            <column name="tag_key" type="VARCHAR(128)" remarks="键"/>
            <column name="tag_value" type="VARCHAR(128)" remarks="值"/>
            <column name="creator_id" type="BIGINT" remarks="创建的用户id"/>
            <column name="creator" type="VARCHAR(64)" remarks="创建的用户姓名"/>
            <column name="updator" type="VARCHAR(64)" remarks="修改的用户姓名"/>
            <column name="updator_id" type="BIGINT" remarks="最后一次修改的用户id"/>
            <column name="create_time" type="DATETIME" remarks="创建数据时间"/>
            <column name="update_time" type="DATETIME" remarks="更新数据时间"/>
            <column name="deleted" type="INT" defaultValue="0" remarks="是否被删除">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int" defaultValue="0" remarks="乐观锁版本号">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="modify.table" author="ben">
        <addColumn tableName="tag">
            <column name="space_id" type="BIGINT" remarks="空间id"/>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn.tenant_id" author="ben">
        <addColumn tableName="tag">
            <column name="tenant_id" type="BIGINT" remarks="租户id"/>
        </addColumn>
        <sql> update tag set tenant_id=engineering_id where tenant_id is null</sql>
    </changeSet>


</databaseChangeLog>
