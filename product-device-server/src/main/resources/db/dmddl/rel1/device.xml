<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun (generated)" id="1648525882825-4">
        <createTable remarks="设备描述" tableName="device">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="设备名称" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="product_id" remarks="所属产品id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="metadata" remarks="元数据，基于物模型定义，赋予设备相应的元数据" type="CLOB"/>
            <column defaultValueBoolean="false" name="status" remarks="设备状态，1-启用，0-关闭" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="create_time" remarks="创建时间" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="edge_gateway_id" remarks="所属边缘网关id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="descript" remarks="设备描述" type="TEXT"/>
        </createTable>
    </changeSet>

</databaseChangeLog>
