<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun (generated)" id="1648525882825-5">
        <createTable remarks="边缘网关表" tableName="edge_gateway">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="边缘网关名称" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="host" remarks="host" type="VARCHAR(32)"/>
            <column name="port" remarks="port" type="INT"/>
            <column name="descript" remarks="描述" type="VARCHAR(512)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="create_time" remarks="创建时间" type="datetime">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    
</databaseChangeLog>
