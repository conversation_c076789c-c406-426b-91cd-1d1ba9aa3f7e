<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="sushangqun (generated)" id="1648525882825-11">
        <createTable remarks="策略表" tableName="strategy">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="策略名称" type="VARCHAR(256)">
                <constraints nullable="false"/>
            </column>
            <column name="type" remarks="策略类型，1-按时间间隔，2-cron" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="interval_ms" remarks="时间间隔，单位毫秒，当策略类型为1时，不为空" type="INT"/>
            <column name="cron" remarks="定时策略表达式，当策略类型为2时，不为空" type="VARCHAR(64)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="create_time" remarks="创建时间" type="datetime">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    
</databaseChangeLog>
