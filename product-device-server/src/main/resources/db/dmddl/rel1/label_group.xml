<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun (generated)" id="1648525882825-8">
        <createTable remarks="标签分组表" tableName="label_group">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="channel_id" remarks="所属通道id" type="BIGINT"/>
            <column name="name" remarks="分组名称" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="tag" remarks="分组标识,逗号分隔" type="VARCHAR(128)"/>
            <column name="strategy_id" remarks="使用策略id" type="BIGINT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="create_time" remarks="创建时间" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="descript" remarks="分组描述" type="TEXT"/>
        </createTable>
    </changeSet>

</databaseChangeLog>
