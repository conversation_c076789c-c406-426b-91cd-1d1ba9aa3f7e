<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="sushangqun (generated)" id="1648525882825-1">
        <createTable remarks="通道表" tableName="channel">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="通道名称" type="VARCHAR(256)">
                <constraints nullable="false"/>
            </column>
            <column name="driver" remarks="所属驱动类型，1-Snap7，2-Modbus，3-OPC UA，4-ZigBee，5-BLE" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="create_time" remarks="创建时间" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="edge_gateway_id" remarks="所属边缘网关id" type="BIGINT"/>
        </createTable>
    </changeSet>
    
</databaseChangeLog>
