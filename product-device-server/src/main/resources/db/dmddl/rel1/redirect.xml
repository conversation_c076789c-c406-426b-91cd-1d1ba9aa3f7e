<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <preConditions>
        <dbms type="oracle"/>
    </preConditions>

    <changeSet id="create.table" author="ben">
        <createTable tableName="redirect" remarks="回调操作">
            <column name="id" type="BIGINT" remarks="主键">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="engineering_id" type="BIGINT" remarks="工程id"/>
            <column name="module_id" type="BIGINT" remarks="模块id"/>
            <column name="space_id" type="BIGINT" remarks="空间id"/>
            <column name="tenant_id" type="BIGINT" remarks="租户id"/>
            <column name="redirect_name" type="VARCHAR(64)" remarks="回调名称"/>
            <column name="redirect_type" type="INT" remarks="回调类型 0-webhook"/>
            <column name="redirect_request_timeout" type="INT" remarks="回调请求超时时长"/>
            <column name="request_connect_timeout" type="int" remarks="回调超时时间"></column>
            <column name="redirect_invoke_time" type="INT" remarks="回调被调用次数"/>
            <column name="redirect_fn" type="TEXT"  remarks="回调方法明细 json"/>
            <column name="description" type="VARCHAR(256)" remarks="描述"></column>
            <column name="creator_id" type="BIGINT" remarks="创建的用户id"/>
            <column name="creator" type="VARCHAR(64)" remarks="创建的用户姓名"/>
            <column name="updator" type="VARCHAR(64)" remarks="修改的用户姓名"/>
            <column name="updator_id" type="BIGINT" remarks="最后一次修改的用户id"/>
            <column name="create_time" type="DATETIME" remarks="创建数据时间"/>
            <column name="update_time" type="DATETIME" remarks="更新数据时间"/>
            <column name="deleted" type="INT" defaultValue="0" remarks="是否被删除">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int" defaultValue="0" remarks="乐观锁版本号">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="redirect" indexName="index_tenant_scope">
            <column name="engineering_id"></column>
            <column name="module_id"></column>
            <column name="space_id"></column>
            <column name="tenant_id"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>
