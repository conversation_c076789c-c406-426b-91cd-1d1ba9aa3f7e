<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="sushangqun" id="1698137829">
        <sql>
            ALTER TABLE custom_message ADD COLUMN auto_send INT;
            COMMENT ON COLUMN custom_message.auto_send IS '自动发送，0-关闭，1-开启';

            ALTER TABLE custom_message ADD COLUMN auto_send_interval INT;
            COMMENT ON COLUMN custom_message.auto_send_interval IS '自动发送间隔，毫秒';

            ALTER TABLE custom_message ADD COLUMN auto_send_var_str TEXT;
            COMMENT ON COLUMN custom_message.auto_send_var_str IS '自动发送内容，json';

        </sql>
    </changeSet>

    <changeSet author="zhoulc" id="202312261137">
        <sql>
            ALTER TABLE device_template ADD COLUMN inherit_thing_model_name varchar(255);
            ALTER TABLE device_template ADD COLUMN channel_type varchar(32);
            ALTER TABLE device_template ADD COLUMN label_count int;
            COMMENT ON COLUMN device_template.inherit_thing_model_name IS '继承物模型名称';
            COMMENT ON COLUMN device_template.channel_type IS '通道类型';
            COMMENT ON COLUMN device_template.label_count IS '模板标签数量';

        </sql>
    </changeSet>


    <changeSet author="zhoulc" id="202401031737">
        <sql>
            ALTER TABLE device_template ADD COLUMN inherit_thing_model_id varchar(255);
            ALTER TABLE device_template ADD COLUMN channel_driver int;

            ALTER TABLE device_template ADD COLUMN label_file_name varchar(255);

            ALTER TABLE device_template ADD COLUMN label_file_link varchar(512);

            COMMENT ON COLUMN device_template.inherit_thing_model_id IS '继承物模型id';
            COMMENT ON COLUMN device_template.channel_driver IS '通道驱动';
            COMMENT ON COLUMN device_template.label_file_name IS '标签文件名称';
            COMMENT ON COLUMN device_template.label_file_link IS '继承物模型id';

        </sql>
    </changeSet>


    <changeSet author="linql" id="202405151041">
        <createIndex tableName="device" indexName="device_index_tenant_id">
            <column name="TENANT_ID"/>
            <column name="id"/>
        </createIndex>
        <createIndex tableName="edge_gateway" indexName="edge_gateway_index_tenant_id">
            <column name="TENANT_ID"/>
            <column name="id"/>
        </createIndex>
        <createIndex tableName="redirect" indexName="redirect_index_tenant_id">
            <column name="TENANT_ID"/>
        </createIndex>
    </changeSet>

    <changeSet author="linql" id="202405200901">
        <createIndex tableName="device_model_inherit" indexName="device_model_inherit_index_tenant_id_inherit_id">
            <column name="TENANT_ID"/>
            <column name="inherit_thing_model_id"/>
        </createIndex>
        <createIndex tableName="thing_model" indexName="thing_model_index_tenant_id">
            <column name="TENANT_ID"/>
        </createIndex>
        <createIndex tableName="thing_model_inherit" indexName="thing_model_inherit_index_tenant_id_inherit_id">
            <column name="TENANT_ID"/>
            <column name="inherit_thing_model_id"/>
        </createIndex>
        <createIndex tableName="thing_service" indexName="thing_service_index_tenant_id_model_id">
            <column name="TENANT_ID"/>
            <column name="thing_model_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
