<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="guosheng" id="20220725165057">
        <sql>
            DROP TABLE IF EXISTS app_secret;
            CREATE TABLE app_secret(
            ID BIGINT NOT NULL,
            name VARCHAR(32) COMMENT '应用密钥名称' ,
            description VARCHAR(256) COMMENT '应用密钥描述' ,
            app_key VARCHAR(32) COMMENT 'appKey' ,
            secret_key VARCHAR(32) COMMENT 'secretKey' ,
            expiration_time DATETIME COMMENT '到期时间' ,
            VERSION INT DEFAULT 1 COMMENT '版本号' ,
            DELETED INT DEFAULT 0 COMMENT '逻辑删除，1-删除' ,
            CREATOR_ID BIGINT COMMENT '创建人ID' ,
            CREATOR VARCHAR(90) COMMENT '创建人' ,
            CREATE_TIME DATETIME COMMENT '创建时间' ,
            UPDATOR_ID BIGINT COMMENT '创建人ID' ,
            UPDATOR VARCHAR(90) COMMENT '创建人' ,
            UPDATE_TIME DATETIME COMMENT '创建时间' ,
            tenant_id bigint COMMENT '租户id' ,
            ENGINEERING_ID BIGINT  COMMENT '工程ID' ,
            MODULE_ID BIGINT  COMMENT '模块ID' ,
            SPACE_ID BIGINT  COMMENT '空间ID',
            PRIMARY KEY (ID)
            );
            COMMENT ON TABLE app_secret IS '应用密钥';
        </sql>
    </changeSet>
    
</databaseChangeLog>