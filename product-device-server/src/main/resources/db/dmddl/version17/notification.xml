<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="gongrongcheng" id="1668742922">
        <sql>
            CREATE TABLE notification(
                id BIGINT AUTO_INCREMENT NOT NULL COMMENT 'id',
                title VARCHAR(255) NOT NULL   COMMENT '通知标题' ,
                notification_content TEXT    COMMENT '描述' ,
                notification_type INT NOT NULL COMMENT '通知类型，1-全员，2-指定',
                version_info VARCHAR(64)    COMMENT '版本信息' ,
                notification_status INT COMMENT '通知状态，1-待审，2-通过，3-未通过',
                deleted INT DEFAULT 0 COMMENT '删除' ,
                creator_id BIGINT COMMENT '创建人ID' ,
                creator VARCHAR(90) COMMENT '创建人' ,
                create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                updator_id BIGINT COMMENT '更新人ID' ,
                updator VARCHAR(90)    COMMENT '更新人' ,
                update_time DATETIME    COMMENT '更新时间' ,
                tenant_id BIGINT  COMMENT '租户id' ,
                engineering_id BIGINT COMMENT '工程ID' ,
                module_id BIGINT COMMENT '模块ID' ,
                space_id BIGINT COMMENT '空间ID',
                PRIMARY KEY (id)
            );
            COMMENT ON TABLE notification IS '系统通知表';
        </sql>
    </changeSet>


</databaseChangeLog>
