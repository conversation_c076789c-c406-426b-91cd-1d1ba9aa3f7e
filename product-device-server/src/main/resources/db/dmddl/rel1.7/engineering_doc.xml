<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="guosheng" id="20221102105057">
        <sql>
            DROP TABLE IF EXISTS engineering_doc;
            CREATE TABLE engineering_doc(
            ID BIGINT NOT NULL,
            name VARCHAR(256) COMMENT '数据版本名称' ,
            doc_size BIGINT COMMENT '文件大小' ,
            generate_type INT COMMENT '生成类型 0自动 1导入' ,
            download_link VARCHAR(512) COMMENT '下载链接' ,
            VERSION INT DEFAULT 1 COMMENT '版本号' ,
            DELETED INT DEFAULT 0 COMMENT '逻辑删除，1-删除' ,
            CREATOR_ID BIGINT COMMENT '创建人ID' ,
            CREATOR VARCHAR(90) COMMENT '创建人' ,
            CREATE_TIME DATETIME COMMENT '创建时间' ,
            UPDATOR_ID BIGINT COMMENT '创建人ID' ,
            UPDATOR VARCHAR(90) COMMENT '创建人' ,
            UPDATE_TIME DATETIME COMMENT '创建时间' ,
            tenant_id bigint COMMENT '租户id' ,
            ENGINEERING_ID BIGINT  COMMENT '工程ID' ,
            MODULE_ID BIGINT  COMMENT '模块ID' ,
            SPACE_ID BIGINT  COMMENT '空间ID',
            PRIMARY KEY (ID)
            );
            COMMENT ON TABLE engineering_doc IS '工程文件版本表';
        </sql>
    </changeSet>


    <changeSet id="20221107110123" author="guosheng">
        <sql>
            ALTER TABLE engineering_doc ADD platform_version VARCHAR(16);
            COMMENT ON COLUMN engineering_doc.platform_version IS '平台版本';
        </sql>
    </changeSet>
    
</databaseChangeLog>