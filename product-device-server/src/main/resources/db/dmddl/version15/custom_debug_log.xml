<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="sushangqun" id="1682558521">
        <sql>
            CREATE TABLE custom_debug_log (
                id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',

                tenant_id BIGINT NOT NULL COMMENT '租户id',
                edge_gateway_id BIGINT NOT NULL COMMENT '网关id',

                type VARCHAR(255) COMMENT '类型',
                content TEXT COMMENT '内容',
                
                create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                PRIMARY KEY (id)
            );
            COMMENT ON TABLE custom_debug_log IS '自定义协议日志表';
        </sql>
    </changeSet>
    
    <!-- 
    <changeSet author="sushangqun" id="1682558685">
        <sql>
            ALTER TABLE custom_debug_log ADD COLUMN ENGINEERING_ID bigint COMMENT '工程id';
            ALTER TABLE custom_debug_log ADD COLUMN SPACE_ID bigint COMMENT '空间id';
            ALTER TABLE custom_debug_log ADD COLUMN MODULE_ID bigint COMMENT '模块id';
            ALTER TABLE custom_debug_log ADD COLUMN CREATOR varchar(90) DEFAULT NULL COMMENT '创建人名称';
            ALTER TABLE custom_debug_log ADD COLUMN CREATOR_ID bigint unsigned DEFAULT NULL COMMENT '创建人id';
            ALTER TABLE custom_debug_log ADD COLUMN UPDATOR_ID bigint unsigned DEFAULT NULL COMMENT '修改人id';
            ALTER TABLE custom_debug_log ADD COLUMN UPDATOR varchar(90) DEFAULT NULL COMMENT '修改人名称';
            ALTER TABLE custom_debug_log ADD COLUMN UPDATE_TIME datetime DEFAULT NULL COMMENT '修改时间';
            ALTER TABLE custom_debug_log ADD COLUMN VERSION int DEFAULT '1' COMMENT '版本号';
            ALTER TABLE custom_debug_log ADD COLUMN DELETED int DEFAULT '0' COMMENT '是否删除';
        </sql>
    </changeSet> 
    -->
    
</databaseChangeLog>
