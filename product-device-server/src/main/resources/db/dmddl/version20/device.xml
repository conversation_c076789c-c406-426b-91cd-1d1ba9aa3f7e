<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="wxd" id="20230506172117">
        <addColumn tableName="device">
            <column name="channel"
                    type="varchar(256)"
                    remarks="设备关联通道"
                    >
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

<!--    <changeSet author="wxd" id="20230507172117">-->
<!--        <sql>-->
<!--            ALTER TABLE device change column source source_group varchar(1024)  COMMENT '设备关联分组' ;-->
<!--        </sql>-->
<!--    </changeSet>-->

    <changeSet id="20230711140023" author="lll">
        <sql>
            ALTER TABLE device ALTER COLUMN name SET DEFAULT NULL;
            COMMENT ON COLUMN device.name IS '设备名称';
        </sql>
    </changeSet>

    <changeSet id="202309011640001" author="grc">
        <sql>
            ALTER TABLE device
                ADD COLUMN resource_id VARCHAR(255) DEFAULT NULL;
            COMMENT ON COLUMN device.resource_id IS '设备标识';

        </sql>
    </changeSet>
    <changeSet id="202309011640002" author="grc">
        <sql>
            UPDATE device
            SET resource_id = id;
        </sql>
    </changeSet>
    <changeSet id="202309011640003" author="grc">
        <sql>
            ALTER TABLE device ADD COLUMN new_resource_id VARCHAR(255) NOT NULL;
            UPDATE device SET new_resource_id = resource_id;
            ALTER TABLE device DROP COLUMN resource_id;
            ALTER TABLE device RENAME COLUMN new_resource_id TO resource_id;
            COMMENT ON COLUMN device.resource_id IS '设备标识';
        </sql>
    </changeSet>
</databaseChangeLog>
