<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="sushangqun" id="1668732928">
        <sql>
            CREATE TABLE custom_driver(
                id BIGINT AUTO_INCREMENT NOT NULL COMMENT 'id',
                driver_name VARCHAR(255) NOT NULL   COMMENT '协议名称' ,
                descript TEXT    COMMENT '描述' ,
                format_type INT NOT NULL   COMMENT '格式类型，1-固定头fixHeader' ,
                endian INT NOT NULL COMMENT '大小端，1-大端bigEndian，2-小端littleEndian',
                read_length_fields VARCHAR(256)    COMMENT '总报文长度读取字段，英文逗号分隔' ,
                VERSION INT   DEFAULT 1 COMMENT '版本号' ,
                DELETED INT   DEFAULT 0 COMMENT '删除' ,
                CREATOR_ID BIGINT  COMMENT '创建人ID' ,
                CREATOR VARCHAR(90)    COMMENT '创建人' ,
                CREATE_TIME DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                UPDATOR_ID BIGINT  COMMENT '更新人ID' ,
                UPDATOR VARCHAR(90)    COMMENT '更新人' ,
                UPDATE_TIME DATETIME    COMMENT '更新时间' ,
                TENANT_ID BIGINT  COMMENT '租户id' ,
                ENGINEERING_ID BIGINT COMMENT '工程ID' ,
                MODULE_ID BIGINT COMMENT '模块ID' ,
                SPACE_ID BIGINT COMMENT '空间ID',
                PRIMARY KEY (id)
            );
            COMMENT ON TABLE custom_driver IS '自定义协议表';
        </sql>
    </changeSet>
    
    <changeSet author="sushangqun" id="1669195762">
        <sql>
            ALTER TABLE custom_driver ADD COLUMN runtime_info CLOB;
            COMMENT ON COLUMN custom_driver.runtime_info IS '自定义协议名称';
        </sql>
    </changeSet>

  <!--  <changeSet id="16687329281" author="sushangqun">
        <sql>
            ALTER TABLE custom_driver CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs;
        </sql>
    </changeSet>-->

    <changeSet author="gongrongcheng" id="1669195772">
        <sql>
            ALTER TABLE custom_driver ADD COLUMN extra_length INT;
            COMMENT ON COLUMN custom_driver.extra_length IS '额外包长度';
        </sql>
    </changeSet>

</databaseChangeLog>
