<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="sushangqun" id="1668732929">
        <sql>
            CREATE TABLE custom_message(
                id BIGINT AUTO_INCREMENT NOT NULL COMMENT 'id',
                message_name VARCHAR(255) NOT NULL   COMMENT '消息名称' ,
                descript TEXT    COMMENT '描述' ,
                custom_driver_id BIGINT NOT NULL COMMENT '所属自定义协议id',
                VERSION INT   DEFAULT 1 COMMENT '版本号' ,
                DELETED INT   DEFAULT 0 COMMENT '删除' ,
                CREATOR_ID BIGINT  COMMENT '创建人ID' ,
                CREATOR VARCHAR(90)    COMMENT '创建人' ,
                CREATE_TIME DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                UPDATOR_ID BIGINT  COMMENT '更新人ID' ,
                UPDATOR VARCHAR(90)    COMMENT '更新人' ,
                UPDATE_TIME DATETIME    COMMENT '更新时间' ,
                TENANT_ID BIGINT  COMMENT '租户id' ,
                ENGINEERING_ID BIGINT COMMENT '工程ID' ,
                MODULE_ID BIGINT COMMENT '模块ID' ,
                SPACE_ID BIGINT COMMENT '空间ID',
                PRIMARY KEY (id)
            );
            COMMENT ON TABLE custom_message IS '自定义消息表';
        </sql>
    </changeSet>
    
    <!--<changeSet id="16687329283" author="sushangqun">
        <sql>
            ALTER TABLE custom_message CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs;
        </sql>
    </changeSet>-->
</databaseChangeLog>
