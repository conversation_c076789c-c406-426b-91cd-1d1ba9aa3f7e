<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="sushangqun" id="1674976461">
        <sql>
            ALTER TABLE custom_message ADD COLUMN direction INT NOT NULL DEFAULT 1;
            COMMENT ON COLUMN custom_message.direction IS '方向，1-发送和接收，2-发送，3-接收';
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1675041262">
        <sql>
            ALTER TABLE custom_message ADD COLUMN 
                auto_response INT NOT NULL DEFAULT 0;
            COMMENT ON COLUMN custom_message.auto_response IS '自动响应，0-关闭，1-开启';
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1675041330">
        <sql>
            ALTER TABLE custom_message ADD COLUMN 
                auto_response_message_name VARCHAR(255);
            COMMENT ON COLUMN custom_message.auto_response_message_name IS '自动响应消息名';
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1675041805">
        <sql>
            ALTER TABLE custom_message ADD COLUMN 
                auto_response_config CLOB;
            COMMENT ON COLUMN custom_message.auto_response_config IS '自动响应配置';
        </sql>
    </changeSet>
</databaseChangeLog>
