<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun" id="1673514430">
        <sql>
            ALTER TABLE custom_field ADD COLUMN check_type INT;
            COMMENT ON COLUMN custom_field.check_type IS '校验类型';

        </sql>
    </changeSet>
    
    <changeSet author="sushangqun" id="1675318753">
        <sql>
            ALTER TABLE custom_field ADD COLUMN dynamic_length_config CLOB;
            COMMENT ON COLUMN custom_field.dynamic_length_config IS '动态长度参数配置';

        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1675318767">
        <sql>
            ALTER TABLE custom_field ADD COLUMN field_transform CLOB;
            COMMENT ON COLUMN custom_field.field_transform IS '字段内容转换';

        </sql>
    </changeSet>
    
    <changeSet author="sushangqun" id="1675411679">
        <sql>
            ALTER TABLE custom_field ADD COLUMN end_byte INT;
            COMMENT ON COLUMN custom_field.end_byte IS '结束位置';

        </sql>
    </changeSet>
    
    
    <changeSet author="sushangqun" id="1675411680">
        <sql>
            ALTER TABLE custom_field ADD COLUMN end_bit INT;
            COMMENT ON COLUMN custom_field.end_bit IS '结束位置索引';

        </sql>
    </changeSet>

</databaseChangeLog>
