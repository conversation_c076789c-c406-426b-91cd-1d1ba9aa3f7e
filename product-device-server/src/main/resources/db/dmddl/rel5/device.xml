<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="liuyiran" id="2022070410525999">
        <addColumn tableName="device">
            <column name="model" remarks="设备模型" type="CLOB" >
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="sushangqun" id="1656911044">
        <sql>
            ALTER TABLE device ADD model_update_time datetime;
            COMMENT ON COLUMN device.model_update_time IS '模型最后变动时间';
        </sql>
    </changeSet>

    <changeSet author="liuyiran" id="2022070710525999">
        <dropColumn tableName="device">
            <column name="product_id"/>
        </dropColumn>
    </changeSet>
</databaseChangeLog>
