<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="linql" id="20230217145133">
        <sql>
            CREATE TABLE change_notice (
                id BIGINT NOT NULL,
                name VARCHAR ( 255 ) NOT NULL COMMENT '变动通知名称',
                change_subject INT NOT NULL DEFAULT 1 COMMENT '变动项目(1网关,2设备)',
                change_type INT NOT NULL DEFAULT 1 COMMENT '变动类型(1 新建,2,删除,3更新4,状态变化)',
                redirect_id BIGINT DEFAULT NULL COMMENT '回调id',
                engineering_id BIGINT DEFAULT NULL COMMENT '工程id',
                module_id BIGINT DEFAULT NULL COMMENT '模块id',
                space_id BIGINT DEFAULT NULL COMMENT '空间id',
                tenant_id BIGINT DEFAULT NULL COMMENT '租户id',
                creator_id BIGINT DEFAULT NULL COMMENT '创建的用户id',
                creator VARCHAR ( 64 ) DEFAULT NULL COMMENT '创建的用户姓名',
                updator VARCHAR ( 64 ) DEFAULT NULL COMMENT '修改的用户姓名',
                updator_id BIGINT DEFAULT NULL COMMENT '最后一次修改的用户id',
                create_time datetime DEFAULT NULL COMMENT '创建数据时间',
                update_time datetime DEFAULT NULL COMMENT '更新数据时间',
                deleted INT NOT NULL DEFAULT 0 COMMENT '是否被删除',
                version INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
                PRIMARY KEY ( id )
            );
            COMMENT ON TABLE change_notice IS '变动通知';
        </sql>
    </changeSet>

</databaseChangeLog>
