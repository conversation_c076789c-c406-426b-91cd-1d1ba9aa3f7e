<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="sushangqun" id="1676363835">
        <sql>
            ALTER TABLE edge_gateway 
            ADD COLUMN visit_public_mqtt TINYINT NOT NULL DEFAULT 0;
            COMMENT ON COLUMN edge_gateway.visit_public_mqtt IS '是否访问公共MQTT接口';

        </sql>
    </changeSet>

    <changeSet author="linql" id="20230221145136">
        <addColumn tableName="edge_gateway" >
            <column name="runtime_info"
                    type="CLOB"
                    remarks="网关运行时信息"
                     >
                <constraints nullable="true" />
            </column>
        </addColumn>
    </changeSet>


</databaseChangeLog>
