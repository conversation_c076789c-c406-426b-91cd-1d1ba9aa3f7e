<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
 
    <changeSet author="gongrongcheng" id="20230918141300">
        <sql>
            CREATE TABLE audit_log(
                id BIGINT AUTO_INCREMENT NOT NULL COMMENT 'id',
                action_info VARCHAR(64) NOT NULL   COMMENT '操作' ,
                target VARCHAR(64)  NOT NULL  COMMENT '操作对象' ,
                details text COMMENT '操作明细',
                user_id BIGINT  COMMENT '操作人ID' ,
                user_name VARCHAR(64) COMMENT '操作人',
                action_timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间' ,
                action_status tinyint COMMENT '操作状态',
                PRIMARY KEY (id)
            );
            COMMENT ON TABLE audit_log IS '操作审计日志表';
        </sql>
    </changeSet>

    <changeSet author="gongrongcheng" id="20230922140255">
        <sql>
            ALTER TABLE audit_log ADD COLUMN tenant_id bigint;
            COMMENT ON COLUMN audit_log.tenant_id IS '租户ID';

        </sql>
    </changeSet>

</databaseChangeLog>
