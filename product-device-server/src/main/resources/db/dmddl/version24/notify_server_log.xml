<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="createTable" author="guosheng">
        <createTable tableName="notify_server_log">
            <column name="id" type="bigint" >
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="source_id" type="bigint" remarks="源id"/>
            <column name="log_type" type="int" remarks="日志类型"/>
            <column name="log_description" type="varchar(64)" remarks="日志描述"/>
            <column name="request_params" type="varchar(1024)" remarks="请求参数"/>
            <column name="fail_response" type="varchar(2048)" remarks="失败的返回结果"/>
            <column name="error_description" type="varchar(2048)" remarks="错误描述"/>
            <column name="engineering_id" type="bigint" remarks="工程id"/>
            <column name="space_id" type="bigint" remarks="空间id"/>
            <column name="module_id" type="bigint" remarks="模板id"/>
            <column name="creator" type="varchar(90)" remarks="创建人名称"/>
            <column name="creator_id" type="bigint" remarks="创建人id"/>
            <column name="create_Time" type="datetime" remarks="创建时间"/>
            <column name="updator" type="varchar(90)" remarks="更新人名称"/>
            <column name="updator_id" type="bigint" remarks="更新人id"/>
            <column name="update_time" type="datetime" remarks="更新时间"/>
            <column name="version" type="int" remarks="版本号" defaultValueNumeric="1"/>
            <column name="deleted" type="int" remarks="是否删除" defaultValueNumeric="0"/>
        </createTable>
    </changeSet>
    <changeSet id="20220511110122" author="guosheng">
        <sql>
            ALTER TABLE notify_server_log ADD tenant_id bigint  ;
            COMMENT ON COLUMN notify_server_log.tenant_id IS '租户id' ;
        </sql>
    </changeSet>

    <changeSet id="20220726172117" author="guosheng">
        <sql>
            ALTER TABLE notify_server_log MODIFY request_params varchar(2048)  ;
            COMMENT ON COLUMN notify_server_log.request_params IS '请求参数' ;
        </sql>
    </changeSet>
    <changeSet id="20220726172118" author="guosheng">
        <sql>
            ALTER TABLE notify_server_log MODIFY request_params varchar(4096) ;
            COMMENT ON COLUMN notify_server_log.request_params IS '请求参数' ;
        </sql>
    </changeSet>

    <changeSet id="20230810095600" author="grc">
        <sql>
            ALTER TABLE notify_server_log add message_aggregation_id bigint  ;
            COMMENT ON COLUMN notify_server_log.message_aggregation_id IS '消息聚合id' ;
        </sql>
    </changeSet>

    <changeSet id="20230810095601" author="grc">
        <sql>
            create index msg_aggregation_index on notify_server_log(message_aggregation_id);
        </sql>
    </changeSet>

    <changeSet id="20230814095600" author="grc">
        <sql>
            ALTER TABLE notify_server_log add subscription_id bigint  ;
            COMMENT ON COLUMN notify_server_log.subscription_id IS '所属订阅ID' ;
        </sql>
    </changeSet>

    <changeSet id="20230814095601" author="grc">
        <sql>
            create index subscription_id_index on notify_server_log(subscription_id);
        </sql>
    </changeSet>
    
</databaseChangeLog>
