<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.ChannelParamMapper">

    <delete id="physicalDeleteByEdgeGatewayId">
        DELETE FROM channel_param p
        WHERE
            EXISTS (
                SELECT
                    t.id
                FROM
                    (
                        SELECT
                            cp.id
                        FROM
                            edge_gateway eg
                                LEFT JOIN channel c ON c.edge_gateway_id = eg.id
                                LEFT JOIN channel_param cp ON cp.channel_id = c.id
                        WHERE
                            eg.id = #{edgeGatewayId}
                          AND cp.id IS NOT NULL
                    ) t
                WHERE
                    t.id = p.id
            );
    </delete>


    <update id="saveOrUpdateBatch" >
        REPLACE INTO `channel_param`(`id`,`channel_id`,`name`,`value`,`create_time`,`descript`,`ENGINEERING_ID`
        ,`SPACE_ID`,`MODULE_ID`,`CREATOR`,`CREATOR_ID`,`UPDATOR_ID`,`UPDATOR`,`UPDATE_TIME`,`VERSION`,`DELETED`)
        values
        <foreach collection="list" item="entity" index="index" separator=",">
            ( #{entity.id},#{entity.channelId},#{entity.name},#{entity.value}
            ,#{entity.createTime},#{entity.descript},#{entity.engineeringId},#{entity.spaceId}
            ,#{entity.moduleId},#{entity.creator},#{entity.creatorId},#{entity.updatorId}
            ,#{entity.updator},#{entity.updateTime},#{entity.version},#{entity.deleted})
        </foreach>
    </update>
    <select id="listProofreadDataByChannelId"
            resultType="com.nti56.nlink.product.device.server.model.channel.dto.ProofreadChannelParamDTO">
         select * from channel_param
         where
         tenant_id = #{tenantIsolation.tenantId}
         and channel_id = #{channelId}
         and DELETED = 0
    </select>
  
    <select id="listChannelParamByEdgeGatewayId" resultType="com.nti56.nlink.product.device.server.model.channel.dto.ChannelParamDTO">
           select a.id,a.channel_id as channelId,a.`name` as name ,a.`value` as value from channel_param a inner join channel b
           where
           a.channel_id = b.id
           and a.tenant_id = #{tenantIsolation.tenantId}
           and b.edge_gateway_id = #{edgeGatewayId}
           and a.DELETED = 0
           order by b.create_time desc
      </select>
  
    <select id="listChannelParamByChannelIds" resultType="com.nti56.nlink.product.device.server.model.channel.dto.ChannelParamDTO">
           select a.id,a.channel_id as channelId,a.`name` as name ,a.`value` as value from channel_param a
           where
           a.tenant_id = #{tenantIsolation.tenantId}
           and a.DELETED = 0
          <if test="channelIds != null">
            and a.channel_id in (
            <foreach item="channelId" collection="channelIds" separator="," >
              #{channelId}
            </foreach>
            )
          </if>
    </select>

</mapper>
