<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.TemplateMapper">

    <select id="pageTemplate" resultType="com.nti56.nlink.product.device.server.model.template.vo.TemplateVO">
        SELECT t.*,c.name as channelName
        FROM template t  LEFT JOIN notify_channel c ON  t.notify_channel_id = c.id
        <where>
            t.tenant_id = #{tenantIsolation.tenantId}
            AND t.deleted = 0
            <if test="dto.name != null and dto.name != ''">
                AND t.name like CONCAT('%',#{dto.name},'%')
            </if>
            <if test="dto.notifyType != null" >
                AND t.notify_type = #{dto.notifyType}
            </if>

            <if test="dto.auditStatus != null" >
                AND t.audit_status = #{dto.auditStatus}
            </if>

        </where>
        order By t.create_time desc
    </select>


    <select id="listTemplate" resultType="com.nti56.nlink.product.device.server.model.template.vo.TemplateVO">
        SELECT t.*,c.name as channelName
        FROM template t  LEFT JOIN notify_channel c ON  t.notify_channel_id = c.id
        <where>
            t.tenant_id = #{tenantIsolation.tenantId}
            AND t.deleted = 0
            <if test="dto.name != null and dto.name != ''">
                AND t.name like CONCAT('%',#{dto.name},'%')
            </if>
            <if test="dto.notifyType != null" >
                AND t.notify_type = #{dto.notifyType}
            </if>

            <if test="dto.auditStatus != null" >
                AND t.audit_status = #{dto.auditStatus}
            </if>

        </where>
        order By t.create_time desc
    </select>

    <delete id="deleteAllByTenantId">
        delete from notify_channel where tenant_id = #{tenantId};
        delete from template where tenant_id = #{tenantId};
        delete from notify_server_log where tenant_id = #{tenantId};
    </delete>

</mapper>
