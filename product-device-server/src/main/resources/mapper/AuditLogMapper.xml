<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.AuditLogMapper">

    <resultMap id="BaseResultMap" type="com.nti56.nlink.product.device.server.entity.AuditLogEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="actionInfo" column="action_info" jdbcType="VARCHAR"/>
            <result property="target" column="target" jdbcType="VARCHAR"/>
            <result property="details" column="details" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="userName" column="user_name" jdbcType="VARCHAR"/>
            <result property="actionTimestamp" column="action_timestamp" jdbcType="TIMESTAMP"/>
            <result property="actionStatus" column="action_status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,action_info,target,
        details,user_id,user_name,
        action_timestamp,action_status
    </sql>
</mapper>
