<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.LabelMapper">

    <delete id="physicalDeleteByEdgeGatewayId">
        DELETE FROM label l
        WHERE
            EXISTS (
                SELECT
                    t.id
                FROM
                    (
                        SELECT
                            l1.id
                        FROM
                            edge_gateway eg
                                LEFT JOIN channel c ON c.edge_gateway_id = eg.id
                                LEFT JOIN label_group lg ON lg.channel_id = c.id
                                LEFT JOIN label l1 ON l1.label_group_id = lg.id
                        WHERE
                            eg.id = #{edgeGatewayId}
                          AND l1.id IS NOT NULL
                    ) t
                WHERE
                    t.id = l.id
            );
    </delete>

    <update id="deleteByEdgeGatewayId">
        UPDATE label l
        SET l.DELETED = 1
        WHERE
            EXISTS (
            SELECT
                t.id
            FROM
                (
                SELECT
                    l1.id
                FROM
                    edge_gateway eg
                    LEFT JOIN channel c ON c.edge_gateway_id = eg.id
                    LEFT JOIN label_group lg ON lg.channel_id = c.id
                    LEFT JOIN label l1 ON l1.label_group_id = lg.id
                WHERE
                    eg.id = #{edgeGatewayId}
                    AND l1.id IS NOT NULL
                ) t
            WHERE
                t.id = l.id
            );
    </update>

    <select id="listLabelByIds" resultType="com.nti56.nlink.product.device.server.model.LabelBo">
        SELECT l.*, 
            c.id AS channel_id,
            e.id AS edge_gateway_id,
            c.interval_ms
        FROM label l
            LEFT JOIN label_group g ON (l.label_group_id = g.id)
            LEFT JOIN channel c ON (g.channel_id = c.id)
            LEFT JOIN edge_gateway e ON (c.edge_gateway_id = e.id)
        WHERE l.deleted = 0 AND l.id IN (
            <foreach item="labelId" collection="labelIds" separator="," >
                #{labelId}
            </foreach>
        )
    </select>
    <select id="countByLabelGroupIds" resultType="com.nti56.nlink.product.device.server.model.CountByIdDTO">
        SELECT
            COUNT(*) count,
            lg.NAME
        FROM
            label l
            LEFT JOIN label_group lg ON l.label_group_id = lg.id
        <where>
            l.tenant_id = #{tenantId}
            AND l.deleted = 0
            <if test="labelGroupIds != null">
                and l.label_group_id in (
                <foreach item="labelGroupId" collection="labelGroupIds" separator="," >
                    #{labelGroupId}
                </foreach>
                )
            </if>
        </where>

        GROUP BY
            l.label_group_id
    </select>

    <select id="listGatherParam" resultType="com.nti56.nlink.product.device.client.model.dto.json.GatherParamField">
        SELECT l.gather_param
        FROM label l
            LEFT JOIN label_group g ON (l.label_group_id = g.id)
            LEFT JOIN channel c ON (g.channel_id = c.id)
        WHERE c.edge_gateway_id = #{edgeGatewayId}
            AND l.tenant_id = #{tenantId}
            AND l.gather_param IS NOT NULL
            AND l.deleted = 0
            AND c.status = 1 
    </select>
    
    <select id="listProofreadDataByLabelGroupId"
            resultType="com.nti56.nlink.product.device.server.model.label.dto.ProofreadLabelDTO">
        select * from label where label_group_id = #{labelGroupId} order by create_time desc
    </select>
    <select id="listVOByLabelGroupId"
            resultType="com.nti56.nlink.product.device.server.model.label.vo.LabelVO">
        select l.*,
            d.id as deviceId,
            d.name as deviceName,
            lbr.property_name as propertyName
        from label l
            left join label_bind_relation lbr on lbr.label_id = l.id
            left join device d on d.id = lbr.device_id and (d.DELETED = 0  or ISNULL(d.DELETED))
        where
            l.deleted = 0
            and (d.DELETED = 0  or ISNULL(d.DELETED))
            and l.label_group_id = #{labelGroupId}
        order by l.create_time desc
    </select>
  
    <select id="listLabelByLabelIds" resultType="com.nti56.nlink.product.device.server.model.LabelBo">
      SELECT l.id,l.name,l.address,l.length,l.data_type,l.is_array,l.string_bytes,
      g.channel_id
      FROM label l
      LEFT JOIN label_group g ON (l.label_group_id = g.id)
      WHERE
      l.tenant_id = #{tenantId}
      and l.id IN (
      <foreach item="labelId" collection="labelIds" separator="," >
        #{labelId}
      </foreach>
      )
    </select>

    <select id="listLabelByLabelGroupId" resultType="com.nti56.nlink.product.device.server.model.LabelBo">
        SELECT l.id,l.name,l.address,l.length,l.data_type,l.is_array,l.string_bytes,
        g.channel_id
        FROM label l
        LEFT JOIN label_group g ON (l.label_group_id = g.id)
        WHERE
        l.tenant_id = #{tenantId}
        and l.label_group_id =  #{labelGroupId}
        and l.DELETED = 0
    </select>

    <select id="pageLabel" resultType="com.nti56.nlink.product.device.server.model.label.vo.PageLabelVO">
        SELECT l.id,l.name,l.alias,l.address,l.data_type, l.descript,l.interval_ms,l.is_array,l.string_bytes,l.length,
               l.read_only, l.label_group_id,count(lbr.id) as isBind  FROM label l left join label_bind_relation lbr on lbr.label_id = l.id AND lbr.deleted = 0
        WHERE l.label_group_id = #{dto.labelGroupId}
        AND l.tenant_id = #{tenantIsolation.tenantId}
        AND l.deleted = 0
        <if test="dto.searchStr != null and dto.searchStr != ''">
            <if test="_databaseId=='mysql'"> AND l.name LIKE binary CONCAT('%', #{dto.searchStr},'%') </if>
            <if test="_databaseId=='dm'"> AND l.name LIKE  CONCAT('%', #{dto.searchStr},'%') escape '\' </if>
            <if test="_databaseId=='oracle'"> AND l.name LIKE  CONCAT('%', #{dto.searchStr},'%')  escape '\' </if>
            <if test="_databaseId=='sqlserver'"> AND l.name LIKE  CONCAT('%', #{dto.searchStr},'%')  escape '\' </if>
        </if>
        <if test="dto.dataType != null and dto.dataType != ''">
            AND l.data_type = #{dto.dataType}
        </if>
        <if test="_databaseId=='mysql'"> group by l.id </if>
        <if test="_databaseId=='dm'"> group by l.id, l.name, l.alias, l.address, l.data_type, l.descript, l.interval_ms, l.is_array, l.string_bytes, l.length, l.read_only, l.label_group_id </if>
        <if test="_databaseId=='oracle'"> group by l.id, l.name, l.alias, l.address, l.data_type, l.descript, l.interval_ms, l.is_array, l.string_bytes, l.length, l.read_only, l.label_group_id </if>
        <if test="_databaseId=='sqlserver'"> group by l.id, l.name, l.alias, l.address, l.data_type, l.descript, l.interval_ms, l.is_array, l.string_bytes, l.length, l.read_only, l.label_group_id </if>
        
        <if test="dto.sortType != null and dto.sortType != '' and dto.sortType == 0 and dto.sortColumn != null and dto.sortColumn != ''">
            order by l.${dto.sortColumn} ASC
        </if>
        <if test="dto.sortType != null and dto.sortType != '' and dto.sortType == 1 and dto.sortColumn != null and dto.sortColumn != ''">
            order by l.${dto.sortColumn} DESC
        </if>
    </select>
    <select id="pageLabelWithFullName" resultType="com.nti56.nlink.product.device.server.model.label.vo.PageLabelVO">
        select * from (
            select l.address, l.data_type, l.descript, l.id, l.label_group_id, concat(lg.name, '.', l.name) name,l.alias
            from label l, label_group lg
                where l.label_group_id = lg.id
                    AND l.tenant_id = #{tenantIsolation.tenantId}
                    AND l.deleted = 0
                    and l.label_group_id in (
                        <foreach item="labelGroupId" collection="dto.labelGroupIds" separator="," >
                            #{labelGroupId}
                        </foreach>
                    )
        ) lfn
        where 1 = 1
        <if test="dto.searchStr != null and dto.searchStr != ''">
            AND name LIKE binary CONCAT('%', #{dto.searchStr},'%')
        </if>
        <if test="dto.dataType != null and dto.dataType != ''">
            AND data_type = #{dto.dataType}
        </if>
        <if test="dto.sortType != null and dto.sortType != '' and dto.sortType == 0 and dto.sortColumn != null and dto.sortColumn != ''">
            order by ${dto.sortColumn} ASC
        </if>
        <if test="dto.sortType != null and dto.sortType != '' and dto.sortType == 1 and dto.sortColumn != null and dto.sortColumn != ''">
            order by ${dto.sortColumn} DESC
        </if>
    </select>

    <select id="labelWithFullName" resultType="com.nti56.nlink.product.device.server.model.label.dto.LabelDTO">
        select * from (
        select l.id sourceId,l.address, l.data_type, l.descript,l.length,l.is_array,l.string_bytes,l.read_only,l.interval_ms, concat(lg.name, '.', l.name) name,l.alias
        from label l, label_group lg
        where l.label_group_id = lg.id
        AND l.tenant_id = #{tenantIsolation.tenantId}
        AND l.deleted = 0
        and l.label_group_id in (
        <foreach item="labelGroupId" collection="labelGroupIds" separator="," >
            #{labelGroupId}
        </foreach>
        )
        ) lfn
        order by name
    </select>

    <select id="listByEditLabelAlias" resultType="com.nti56.nlink.product.device.server.entity.LabelEntity">
        <if test="dto.allFilter">
            SELECT l.* FROM (
            select * from channel c1
            WHERE
            c1.edge_gateway_id = #{dto.edgeGateWayId}
            ) c
            LEFT JOIN label_group lg ON lg.channel_id = c.id
            <if test="dto.labelGroupIds != null and dto.labelGroupIds.size() > 0 ">
                and lg.id in (
                <foreach item="labelGroupId" collection="dto.labelGroupIds" separator=",">
                    #{labelGroupId}
                </foreach>
                )
            </if>
            LEFT JOIN label l ON l.label_group_id = lg.id
            AND l.tenant_id = #{tenantIsolation.tenantId}
            AND l.deleted = 0
            <if test="dto.labelGroupIds != null and dto.labelGroupIds.size() > 0 ">
                and l.label_group_id in (
                <foreach item="labelGroupId" collection="dto.labelGroupIds" separator=",">
                    #{labelGroupId}
                </foreach>
                )
            </if>
            <if test="dto.searchStr != null and dto.searchStr != ''">
                AND l.name LIKE binary CONCAT('%', #{dto.searchStr},'%')
            </if>
            WHERE
            c.edge_gateway_id = #{dto.edgeGateWayId}
            AND l.tenant_id = #{tenantIsolation.tenantId}
            AND l.deleted = 0
            <if test="dto.labelGroupIds != null and dto.labelGroupIds.size() > 0 ">
                and l.label_group_id in (
                <foreach item="labelGroupId" collection="dto.labelGroupIds" separator=",">
                    #{labelGroupId}
                </foreach>
                )
            </if>
            <if test="dto.searchStr != null and dto.searchStr != ''">
                AND l.name LIKE binary CONCAT('%', #{dto.searchStr},'%')
            </if>
        </if>
        <if test="!dto.allFilter">
            SELECT l.* FROM
            label l where l.id
            in (
            <foreach item="labelId" collection="dto.labelIds" separator=",">
                #{labelId}
            </foreach>
            )
        </if>
    </select>
    <select id="listRepeatNameByLabelGroupId" resultType="java.lang.String">
        SELECT  `name`
        FROM    label
        where label_group_id = #{targetLabelGroupId}
        and tenant_id = #{tenantIsolation.tenantId}
        and DELETED = 0
        GROUP BY name
        HAVING  COUNT(name) > 1
    </select>
    <select id="getLabelDetail" resultType="com.nti56.nlink.product.device.server.model.LabelBindRelationBo">
        SELECT
            c.name as channel_name,
            eg.name as edge_gateway_name,
            g.name as label_group_name
            FROM label l
            LEFT JOIN label_group g ON (l.label_group_id = g.id)
            LEFT JOIN channel c ON (g.channel_id = c.id)
            LEFT JOIN edge_gateway eg ON (eg.id = c.edge_gateway_id)
        WHERE
            l.DELETED = 0 AND
            g.DELETED = 0 AND
            c.DELETED = 0 AND
            eg.DELETED = 0 AND
            l.id = #{labelId}
    </select>

    <update id="editLabelAlias">
        UPDATE label SET alias =
        CASE WHEN ISNULL(alias) or LENGTH(trim(alias)) &lt; 1 THEN

        <foreach collection="dto.filteringRules"  >
            REPLACE(
        </foreach>
        name
        <foreach item="filteringRule" collection="dto.filteringRules">
            ,#{filteringRule},'')
        </foreach>

        ELSE
        <foreach collection="dto.filteringRules"  >
            REPLACE(
        </foreach>
        alias
        <foreach item="filteringRule" collection="dto.filteringRules">
            ,#{filteringRule},'')
        </foreach>

        END
        where id
        <if test="dto.allFilter">
            in
            (
            SELECT tmp.id FROM(

            SELECT l.id FROM (
            select * from channel c1
            WHERE
            c1.edge_gateway_id = #{dto.edgeGateWayId}
            ) c
            LEFT JOIN label_group lg ON lg.channel_id = c.id
            <if test="dto.labelGroupIds != null and dto.labelGroupIds.size() > 0 ">
                and lg.id in (
                <foreach item="labelGroupId" collection="dto.labelGroupIds" separator=",">
                    #{labelGroupId}
                </foreach>
                )
            </if>
            LEFT JOIN label l ON l.label_group_id = lg.id
            AND l.tenant_id = #{tenantIsolation.tenantId}
            AND l.deleted = 0
            <if test="dto.labelGroupIds != null and dto.labelGroupIds.size() > 0 ">
                and l.label_group_id in (
                <foreach item="labelGroupId" collection="dto.labelGroupIds" separator=",">
                    #{labelGroupId}
                </foreach>
                )
            </if>
            <if test="dto.searchStr != null and dto.searchStr != ''">
                AND l.name LIKE binary CONCAT('%', #{dto.searchStr},'%')
            </if>
            WHERE
            c.edge_gateway_id = #{dto.edgeGateWayId}
            AND l.tenant_id = #{tenantIsolation.tenantId}
            AND l.deleted = 0
            <if test="dto.labelGroupIds != null and dto.labelGroupIds.size() > 0 ">
                and l.label_group_id in (
                <foreach item="labelGroupId" collection="dto.labelGroupIds" separator=",">
                    #{labelGroupId}
                </foreach>
                )
            </if>
            <if test="dto.searchStr != null and dto.searchStr != ''">
                AND l.name LIKE binary CONCAT('%', #{dto.searchStr},'%')
            </if>
            )tmp
            )
        </if>
        <if test="!dto.allFilter">
            in (
            <foreach item="labelId" collection="dto.labelIds" separator=",">
                #{labelId}
            </foreach>
            )
        </if>

    </update>

    <update id="batchUpdateGatherParam">
        <foreach collection="gatherParamList" item="item" index="index" separator=";">
            UPDATE label SET gather_param = #{item} WHERE id = #{item.labelId}
        </foreach>
    </update>

    <update id="saveOrUpdateBatch" >
        REPLACE INTO `label`(`id`,`name`,`descript`,`address`,`length`,`data_type`
        ,`is_array`,`label_group_id`,`string_bytes`,`tag`,`create_time`,`ENGINEERING_ID`,`SPACE_ID`,`MODULE_ID`,`CREATOR`
        ,`CREATOR_ID`,`UPDATOR_ID`,`UPDATOR`,`UPDATE_TIME`,`VERSION`,`DELETED`,`tenant_id`,`alias`,`read_only`)
        values
        <foreach collection="list" item="entity" index="index" separator=",">
            ( #{entity.id},#{entity.name},#{entity.descript},#{entity.address}
            ,#{entity.length},#{entity.data_type},#{entity.is_array}
            ,#{entity.label_group_id},#{entity.string_bytes},#{entity.tag},IFNULL(#{entity.create_time},now())
            ,#{entity.ENGINEERING_ID},#{entity.SPACE_ID},#{entity.MODULE_ID},#{entity.CREATOR}
            ,#{entity.CREATOR_ID},#{entity.UPDATOR_ID},#{entity.UPDATOR},now()
            ,#{entity.VERSION},#{entity.DELETED},#{entity.tenant_id},#{entity.alias}
            ,#{entity.read_only}
            )
        </foreach>
    </update>
    <update id="updateLabelGroupIdAndSetGatherParamNullByLabelIds">
        update label set label_group_id = #{targetLabelGroupId},gather_param = null
        where tenant_id = #{tenantIsolation.tenantId}
        and DELETED = 0
        <if test="labelIds != null">
            and id in (
            <foreach item="labelId" collection="labelIds" separator=",">
                #{labelId}
            </foreach>
            )
        </if>
    </update>
    <update id="batchUpdateInterval">
        update label set interval_ms = #{intervalMs}
        where tenant_id = #{tenantId}
        and DELETED = 0
        and id in (
        <foreach item="labelId" collection="ids" separator=",">
            #{labelId}
        </foreach>
        )
    </update>


    <insert id="saveBatchByNative">
        INSERT INTO `label`(`id`,`name`,`descript`,`address`,`length`,`data_type`, `gather_param`,  `interval_ms`
        ,`is_array`,`label_group_id`,`string_bytes`,`tag`,`create_time`,`ENGINEERING_ID`,`SPACE_ID`,`MODULE_ID`,`CREATOR`
        ,`CREATOR_ID`,`UPDATOR_ID`,`UPDATOR`,`UPDATE_TIME`,`VERSION`,`DELETED`,`tenant_id`,`alias`,`read_only`) VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.id},#{item.name},#{item.descript},#{item.address},#{item.length},#{item.dataType},#{item.gatherParam},#{item.intervalMs},
            #{item.isArray},#{item.labelGroupId},#{item.stringBytes},#{item.tag},#{item.createTime},#{item.engineeringId},#{item.spaceId},#{item.moduleId},#{item.creator},
            #{item.creatorId},#{item.updatorId},#{item.updator},#{item.updateTime},#{item.version},#{item.deleted},#{item.tenantId},#{item.alias},#{item.readOnly}
            )
        </foreach>
    </insert>

    <select id="listByLabelGroupIds" resultType="com.nti56.nlink.product.device.server.entity.LabelEntity">
        select l.id,l.name,l.data_type,l.is_array,l.read_only
        from label l
        where  l.tenant_id = #{tenantIsolation.tenantId}
        AND l.deleted = 0
        AND l.label_group_id in (
        <foreach item="labelGroupId" collection="modelLabelRequestBo.labelGroupIds" separator=",">
            #{labelGroupId}
        </foreach>
        )
        <if test="modelLabelRequestBo.name != null and modelLabelRequestBo.name != ''">
            AND l.name LIKE binary CONCAT('%', #{modelLabelRequestBo.name},'%')
        </if>
        group by l.id,l.name,l.data_type,l.is_array,l.read_only
        order by l.name
    </select>

</mapper>
