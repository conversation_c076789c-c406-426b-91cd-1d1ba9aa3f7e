<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.RedirectMapper">

    <resultMap id="BaseResultMap" type="com.nti56.nlink.product.device.server.entity.InstanceRedirectEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="engineeringId" column="engineering_id" jdbcType="BIGINT"/>
            <result property="moduleId" column="module_id" jdbcType="BIGINT"/>
            <result property="spaceId" column="space_id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="redirectName" column="redirect_name" jdbcType="VARCHAR"/>
            <result property="redirectType" column="redirect_type" jdbcType="TINYINT"/>
            <result property="redirectRequestTimeout" column="redirect_request_timeout" jdbcType="INTEGER"/>
            <result property="redirectInvokeTime" column="redirect_invoke_time" jdbcType="INTEGER"/>
            <result property="redirectFn" column="redirect_fn" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="updator" column="updator" jdbcType="VARCHAR"/>
            <result property="updatorId" column="updator_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="version" column="version" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,engineering_id,
        module_id,space_id,tenant_id,
        redirect_name,redirect_type,redirect_request_timeout,
        redirect_invoke_time,redirect_fn,description,creator_id,
        creator,updator,updator_id,
        create_time,update_time,deleted,
        version
    </sql>

    <delete id="deleteAllByTenantId">
        delete from redirect where tenant_id = #{tenantId};
        delete from tag where tenant_id = #{tenantId};
    </delete>
</mapper>
