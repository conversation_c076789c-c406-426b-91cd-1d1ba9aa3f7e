<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.ConnectorItemMapper">
  
    <select id="pageConnectorItem" resultType="com.nti56.nlink.product.device.server.entity.ConnectorItemEntity">
      SELECT c.*
      FROM connector_item c
      <where>
          c.deleted = 0
          and c.tenant_id = #{tenantId}
          and c.connector_id = #{queryConnectorItemDTO.connectorId}
        <if test="queryConnectorItemDTO.name != null and queryConnectorItemDTO.name != ''">
          AND c.name like CONCAT('%',#{queryConnectorItemDTO.name},'%')
        </if>
      </where>
      order By c.create_time desc
    </select>
  
</mapper>
