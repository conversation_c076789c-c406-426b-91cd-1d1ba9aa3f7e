<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.ThingServiceMapper">
    
    <select id="listByDataModelId" resultType="com.nti56.nlink.product.device.server.entity.ThingServiceEntity">
        SELECT *
        FROM thing_service ts
        WHERE 
            ts.tenant_id = #{tenantId}
            AND ts.deleted = 0
            AND (
                JSON_CONTAINS(ts.input_data, JSON_OBJECT('dataModelId', #{dataModelId}))
                OR
                JSON_CONTAINS(ts.output_data, JSON_OBJECT('dataModelId', #{dataModelId}))
            )
    </select>

    <select id="listAllCommonThingService"
            resultType="com.nti56.nlink.product.device.server.entity.ThingServiceEntity">

        SELECT *
        FROM thing_service ts
        WHERE ts.deleted = 0 and service_type = #{serviceType}
    </select>

</mapper>