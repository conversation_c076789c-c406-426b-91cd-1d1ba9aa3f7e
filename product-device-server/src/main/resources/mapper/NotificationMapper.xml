<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.NotificationMapper">

    <resultMap id="BaseResultMap" type="com.nti56.nlink.product.device.server.entity.NotificationEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="notificationContent" column="notification_content" jdbcType="VARCHAR"/>
            <result property="notificationType" column="notification_type" jdbcType="TINYINT"/>
            <result property="versionInfo" column="version_info" jdbcType="VARCHAR"/>
            <result property="notificationStatus" column="notification_status" jdbcType="TINYINT"/>
            <result property="deleted" column="deleted" jdbcType="INTEGER"/>
            <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updatorId" column="updator_id" jdbcType="BIGINT"/>
            <result property="updator" column="updator" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="engineeringId" column="engineering_id" jdbcType="BIGINT"/>
            <result property="moduleId" column="module_id" jdbcType="BIGINT"/>
            <result property="spaceId" column="space_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,notification_content,
        notification_type,version_info,notification_status,
        deleted,creator_id,creator,
        create_time,updator_id,updator,
        update_time,tenant_id,engineering_id,
        module_id,space_id
    </sql>
    <update id="updateSelf" parameterType="com.nti56.nlink.product.device.server.entity.NotificationEntity">
        update notification set title = #{byId.title},update_time = #{byId.updateTime}
            ,notification_content = #{byId.notificationContent},version_info = #{byId.versionInfo}
        where id = #{byId.id}
    </update>
</mapper>
