<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.UpgradePackageMapper">
  
    <select id="pageUpgradePackage" resultType="com.nti56.nlink.product.device.server.entity.UpgradePackageEntity">
      SELECT u.*
      FROM upgrade_package u
      <where>
          u.deleted = 0
        <if test="queryUpgradePackageDTO.upgradePackageName != null and queryUpgradePackageDTO.upgradePackageName != ''">
          AND u.upgrade_package_name like CONCAT('%',#{queryUpgradePackageDTO.upgradePackageName},'%')
        </if>
        <if test="queryUpgradePackageDTO.upgradeVersion != null and queryUpgradePackageDTO.upgradeVersion != ''">
          AND u.upgrade_version like CONCAT('%',#{queryUpgradePackageDTO.upgradeVersion},'%')
        </if>
      
      </where>
      order By u.create_time desc
    </select>
  
</mapper>
