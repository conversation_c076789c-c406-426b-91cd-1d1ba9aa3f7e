<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.DeviceModelInheritMapper">

    <select id="listInheritThingModelsByIds" resultType="com.nti56.nlink.product.device.server.entity.DeviceModelInheritEntity">
        SELECT
            *
        FROM
            device_model_inherit 
        WHERE
            tenant_id = #{tenantId}
            AND deleted = 0
            AND device_id IN (
            <foreach item="deviceId" collection="deviceIds" separator="," >
              #{deviceId}
            </foreach>
            )
    </select>
    
</mapper>