<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.NotifyServerLogMapper">

    <select id="listNewByLogType" resultType="com.nti56.nlink.product.device.server.entity.NotifyServerLogEntity">
        <if test="_databaseId=='dm'">
            SELECT * FROM notify_server_log WHERE
            id IN ( SELECT
                    SUBSTRING_INDEX( ids, ',', 1 )
                    FROM
                    ( SELECT WM_CONCAT( id  ) AS ids FROM notify_server_log GROUP BY source_id ) t1
                    <where>
                    <if test="retryTime != null">
                        AND LENGTH( ids ) - LENGTH(REPLACE ( ids, ',', '' )) &lt; #{retryTime}
                    </if>

                    </where>
                      )
            AND log_type = #{logType}
        </if>

        <if test="_databaseId=='mysql'">
            SELECT * FROM notify_server_log WHERE
            id IN ( SELECT
                    SUBSTRING_INDEX( ids, ',', 1 )
                    FROM
                    ( SELECT GROUP_CONCAT( id ORDER BY create_time DESC ) AS ids FROM notify_server_log GROUP BY source_id ) t1
                    <where>
                    <if test="retryTime != null">
                        AND LENGTH( ids ) - LENGTH(REPLACE ( ids, ',', '' )) &lt; #{retryTime}
                    </if>

                    </where>
                      )
            AND log_type = #{logType}
        </if>
    </select>
    <select id="pageNewByLogType" resultType="com.nti56.nlink.product.device.server.entity.NotifyServerLogEntity">
        <if test="_databaseId=='dm'">
        SELECT * FROM notify_server_log WHERE id IN ( SELECT SUBSTRING_INDEX( ids, ',', 1 ) FROM
        ( SELECT WM_CONCAT( id  ) AS ids FROM notify_server_log GROUP BY source_id ) t1
        <where>
            <if test="retryTime != null">
                AND LENGTH( ids ) - LENGTH(REPLACE ( ids, ',', '' )) &lt; #{retryTime}
            </if>

        </where>
        )
        AND log_type = #{logType}
    </if>
    <if test="_databaseId=='mysql'">
        SELECT * FROM notify_server_log WHERE id IN ( SELECT SUBSTRING_INDEX( ids, ',', 1 ) FROM
        ( SELECT GROUP_CONCAT( id ORDER BY create_time DESC ) AS ids FROM notify_server_log GROUP BY source_id ) t1
        <where>
            <if test="retryTime != null">
                AND LENGTH( ids ) - LENGTH(REPLACE ( ids, ',', '' )) &lt; #{retryTime}
            </if>

        </where>
        )
        AND log_type = #{logType}
    </if>
    </select>

</mapper>
