<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.EdgeGatewayMapper">
    <update id="editEdgeGateway">
        update edge_gateway
        <set>
            <if test="edgeGateway.name != null and edgeGateway.name != ''">
              name = #{edgeGateway.name},
            </if>
            <if test="edgeGateway.descript != null ">
                descript = #{edgeGateway.descript},
            </if>
            <if test="edgeGateway.type != null">
                type = #{edgeGateway.type},
            </if>
            host = #{edgeGateway.host},
            port = #{edgeGateway.port}
        </set>
        <where>
            id = #{edgeGateway.id}
            and DELETED = 0
        </where>


    </update>
    <update id="setEdgeGatewayNotSyncByLabelIds">
        update edge_gateway e
        set
            sync_status = 0
        where
            e.id in (
                select DISTINCT c.edge_gateway_id from label l
                left join label_group lg on l.label_group_id = lg.id
                left join channel c on lg.channel_id = c.id
                where l.id in (
                    <foreach item="labelId" collection="labelIds" separator="," >
                        #{labelId}
                    </foreach>
                    )
            )
        and e.DELETED = 0
    </update>

    <select id="pageEdgeGateway" resultType="com.nti56.nlink.product.device.server.model.edgegateway.vo.EdgeGatewayVO">
        SELECT eg.*
        FROM edge_gateway eg
        <if test="dto.tagIds != null">
            LEFT JOIN tag_bind_relation tbr ON eg.id = tbr.target_id
            and tbr.tag_id in (
                <foreach item="tagId" collection="dto.tagIds" separator="," >
                    #{tagId}
                </foreach>
            )
        </if>
        <where>
            eg.deleted = 0
            AND eg.tenant_id = #{tenantIsolation.tenantId}
            <if test="dto.IdList != null">
              AND eg.id in (
              <foreach item="id" collection="dto.IdList" separator="," >
                #{id}
              </foreach>
              )
            </if>
            <if test="dto.name != null">
                <if test="_databaseId=='mysql'">AND eg.name like CONCAT('%', #{dto.name},'%')</if>
                <if test="_databaseId=='dm'">AND eg.name like CONCAT('%', #{dto.name},'%')</if>
                <if test="_databaseId=='oracle'">AND eg.name LIKE '%' || #{dto.name} || '%'</if>
                <if test="_databaseId=='sqlserver'">AND eg.name LIKE '%' + #{dto.name} + '%'</if>
            </if>
            <if test="dto.type != null and dto.type != '-1'">
                <if test="_databaseId == 'dm'"> AND eg."type" = #{dto.type} </if>
                <if test="_databaseId == 'oracle'"> AND eg."type" = #{dto.type} </if>
                <if test="_databaseId == 'mysql'"> AND eg.type = #{dto.type} </if>
                <if test="_databaseId == 'sqlserver'"> AND eg.type = #{dto.type} </if>
            </if>
            <if test="dto.tagIds != null">
                and tbr.tag_id in (
                    <foreach item="tagId" collection="dto.tagIds" separator="," >
                        #{tagId}
                    </foreach>
                )
                and tbr.resource_type = 3
            </if>
            <if test="dto.createTimeStart != null and dto.createTimeEnd != null"><![CDATA[
                and eg.create_time >= #{dto.createTimeStart}
                and eg.create_time <= #{dto.createTimeEnd}
            ]]></if>
        </where>
        ORDER BY eg.create_time desc
    </select>
  
  <select id="pageEdgeGatewayEntity" resultType="com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity">
    SELECT e.*
    FROM edge_gateway e
    <where>
       e.deleted = 0
      <if test="versionQueryDTO.IdList != null">
        AND e.id in (
        <foreach item="id" collection="versionQueryDTO.IdList" separator="," >
          #{id}
        </foreach>
        )
      </if>
      <if test="versionQueryDTO.name != null and versionQueryDTO.name != ''">
        AND e.name like CONCAT('%',#{versionQueryDTO.name},'%')
      </if>
      <if test="versionQueryDTO.type != null and versionQueryDTO.type != ''">
          AND e.type = #{versionQueryDTO.type}
      </if>
    </where>
    order By e.create_time desc
  </select>
  
  <select id="selectEdgeGatewayIdList" resultType="java.lang.Long">
    select DISTINCT c.edge_gateway_id from label l
      left join label_group lg on l.label_group_id = lg.id
      left join channel c on lg.channel_id = c.id
      where l.id in (
      <foreach item="labelId" collection="labelIds" separator="," >
        #{labelId}
      </foreach>
      )
  </select>



  <select id="listByIds" resultType="com.nti56.nlink.product.device.server.model.edgegateway.vo.EdgeGatewayVO">
    select id, name, status,target_version as targetVersion,
        <if test="_databaseId=='mysql'"> `type` as type </if>
        <if test="_databaseId=='dm'"> "type" </if>
        <if test="_databaseId=='oracle'"> "type" </if>
        <if test="_databaseId=='sqlserver'"> type </if>
    from edge_gateway 
    where DELETED = 0
    and tenant_id = #{tenantId}
    and id in 
    <foreach collection='ids' item='id' open='(' separator=',' close=')'>
    #{id}
    </foreach>
    
  </select>

  <select id="getGwList" resultType="com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity">
    SELECT eg.*
        FROM edge_gateway eg
    where eg.deleted = 0
    and eg.tenant_id = #{tenantId}
    <if test="type != null">
        <if test="_databaseId=='mysql'"> and eg.`type` <![CDATA[ <> ]]> #{type} </if>
        <if test="_databaseId=='dm'"> and eg."type" <![CDATA[ <> ]]> #{type} </if>
        <if test="_databaseId=='oracle'"> and eg."type" <![CDATA[ <> ]]> #{type} </if>
        <if test="_databaseId=='sqlserver'"> and type <![CDATA[ <> ]]> #{type} </if>
    </if>
    <if test="name != null">
        <if test="_databaseId=='mysql'">AND eg.name like CONCAT('%', #{name},'%')</if>
        <if test="_databaseId=='dm'">AND eg.name like CONCAT('%', #{name},'%')</if>
        <if test="_databaseId=='oracle'">AND eg.name LIKE '%' || #{name} || '%'</if>
        <if test="_databaseId=='sqlserver'">AND eg.name LIKE '%' + #{name} + '%'</if>
    </if>
  </select>


  <select id="selectById" resultType="com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity">
    SELECT id,name,descript,heartbeat_uuid,host,port,imei,traffic_card,operators,visit_public_mqtt,public_mqtt_ip,public_mqtt_port,sync_time,sync_status,runtime_info,version,deleted,tenant_id,engineering_id,module_id,space_id,create_time,creator_id,creator,updator_id,updator,update_time,target_version,upgrade_begin_time,upgrade_status,memory_monitor,space_monitor
    <if test="_databaseId=='mysql'"> ,type </if>
    <if test="_databaseId=='dm'"> ,"type" </if>
    <if test="_databaseId=='oracle'"> ,"type" </if>
    <if test="_databaseId=='sqlserver'"> ,type </if>

        FROM edge_gateway 
    where id = #{id}
   
  </select>

  


    
</mapper>
