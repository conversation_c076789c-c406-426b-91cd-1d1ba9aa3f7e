<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.DataModelMapper">

    <select id="pageDataModel" resultType="com.nti56.nlink.product.device.server.entity.DataModelEntity">
        SELECT d.*
        FROM data_model d 
        <where>
            d.tenant_id = #{tenantId}
            AND d.deleted = 0
            <if test='searchStr != null and searchStr != ""'>
                AND d.name like '%${searchStr}%'
            </if>
        </where>

    </select>

    <select id="listDataModel" resultType="com.nti56.nlink.product.device.server.entity.DataModelEntity">
        SELECT d.*
        FROM data_model d 
        <where>
            d.tenant_id = #{tenantId}
            AND d.deleted = 0
        </where>
    </select>

    <select id="getById" resultType="com.nti56.nlink.product.device.server.entity.DataModelEntity">
        SELECT d.*
        FROM data_model d 
        <where>
            d.tenant_id = #{tenantId}
            AND d.id = #{id}
            AND d.deleted = 0
        </where>
    </select>

    
</mapper>