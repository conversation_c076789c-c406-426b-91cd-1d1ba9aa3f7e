<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.ComputeTaskMapper">
   
   <select id="listComputeTask" resultType="com.nti56.nlink.product.device.server.model.ComputeTaskBo">
        SELECT c.*,
            e.name AS edge_gateway_name
        FROM compute_task c
            LEFT JOIN edge_gateway e ON (c.edge_gateway_id = e.id)
        WHERE c.deleted = 0
            AND c.tenant_id = #{tenantId}
        <if test='edgeGatewayId != null'>
            AND c.edge_gateway_id = #{edgeGatewayId} 
        </if>
   </select>

</mapper>
