<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.ChannelMapper">
    <delete id="deleteAllByTenantId">

        delete from channel where tenant_id = #{tenantId};
        delete from channel_param where tenant_id = #{tenantId};
        delete from compute_task where tenant_id = #{tenantId};
        delete from data_model where tenant_id = #{tenantId};
        delete from data_model_property where tenant_id = #{tenantId};
        delete from device where tenant_id = #{tenantId};
        delete from device_model_inherit where tenant_id = #{tenantId};
        delete from device_service where tenant_id = #{tenantId};
        delete from edge_gateway where tenant_id = #{tenantId};
        delete from label where tenant_id = #{tenantId};
        delete from label_bind_relation where tenant_id = #{tenantId};
        delete from label_group where tenant_id = #{tenantId};
        delete from resource_relation where tenant_id = #{tenantId};
        delete from subscription where tenant_id = #{tenantId};
        delete from tag_bind_relation where tenant_id = #{tenantId};
        delete from thing_model where tenant_id = #{tenantId};
        delete from thing_model_inherit where tenant_id = #{tenantId};
        delete from thing_service where tenant_id = #{tenantId};

        delete from device_service_log where tenant_id = #{tenantId};
        
        delete from custom_driver where tenant_id = #{tenantId};
        delete from custom_message where tenant_id = #{tenantId};
        delete from custom_field where tenant_id = #{tenantId};
        delete from connector where tenant_id = #{tenantId};
        delete from connector_item where tenant_id = #{tenantId};

    </delete>

    <delete id="physicalDeleteByEdgeGatewayId">
        delete from channel where edge_gateway_id = #{edgeGatewayId};
    </delete>

    <select id="getChannelByPage" resultType="com.nti56.nlink.product.device.server.model.ChannelDto">
        SELECT c.*,e.name edge_gateway_name
        FROM channel c  LEFT JOIN edge_gateway e ON  c.edge_gateway_id = e.id
        <where>
            c.deleted = 0
            and c.tenant_id = #{tenantIsolation.tenantId}
            <if test="_databaseId=='mysql'">
                <if test="channel.name != null">
                    AND c.name like CONCAT('%', #{channel.name},'%')
                </if>
            </if>
            <if test="_databaseId=='dm'">
                <if test="channel.name != null and channel.name != ''">
                    AND c.name like CONCAT('%', #{channel.name},'%')
                </if>
            </if>
            <if test="_databaseId=='oracle'">
                <if test="channel.name != null">
                    AND c.name LIKE '%' || #{channel.name} || '%'
                </if>
            </if>
            <if test="_databaseId=='sqlserver'">
                <if test="channel.name != null">
                    AND c.name LIKE '%' + #{channel.name} + '%'
                </if>
            </if>
            <if test="channel.driver != null" >
                AND c.driver = #{channel.driver}
            </if>
        </where>
        ORDER BY c.create_time desc
    </select>

    <select id="listRuntimeInfo" resultType="com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField">
        SELECT c.runtime_info
        FROM channel c
        WHERE c.edge_gateway_id = #{edgeGatewayId}
            AND c.tenant_id = #{tenantId}
            AND c.runtime_info IS NOT NULL
            AND c.deleted = 0
            AND c.status = 1
    </select>
    <select id="getByLabelGroupId" resultType="com.nti56.nlink.product.device.server.entity.ChannelEntity">
        select c.* from label_group lg
        left join channel c on lg.channel_id = c.id
        where lg.id = #{labelGroupId}
        and c.DELETED = 0
    </select>
    <select id="getByLabelId" resultType="com.nti56.nlink.product.device.server.entity.ChannelEntity">
        select c.* from label l
        left join label_group lg on lg.id = l.label_group_id
        left join channel c on c.id = lg.channel_id
        where l.id = #{labelId}
        and c.DELETED = 0
    </select>

</mapper>
