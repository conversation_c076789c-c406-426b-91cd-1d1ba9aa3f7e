<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.LabelGroupMapper">

    <resultMap id="BaseResultMap" type="com.nti56.nlink.product.device.server.model.LabelGroupDto" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="channel_id" property="channelId" jdbcType="BIGINT" />
        <result column="strategy_id" property="strategyId" jdbcType="BIGINT" />
        <result column="descript" property="descript" jdbcType="VARCHAR" />
        <collection property="labelDtoList" javaType="java.util.ArrayList" ofType="com.nti56.nlink.product.device.server.model.LabelDto">
            <id column="lid" property="id" jdbcType="BIGINT" />
            <result column="lname" property="name" jdbcType="VARCHAR" />
            <result column="is_array" property="isArray" jdbcType="TINYINT" />
            <result column="data_type" property="dataType" jdbcType="VARCHAR" />
            <result column="address" property="address" jdbcType="VARCHAR" />
            <result column="descript" property="descript" jdbcType="VARCHAR" />
            <result column="length" property="length" jdbcType="INTEGER" />
            <result column="label_group_id" property="labelGroupId" jdbcType="BIGINT" />
            <result column="string_bytes" property="stringBytes" jdbcType="INTEGER" />
            <result column="read_only" property="readOnly" jdbcType="TINYINT" />
        </collection>
    </resultMap>

    <update id="physicalDeleteByEdgeGatewayId">
        DELETE FROM label_group lg
        WHERE
            EXISTS (
                SELECT
                    t.id
                FROM
                    (
                        SELECT
                            lg1.id
                        FROM
                            edge_gateway eg
                                LEFT JOIN channel c ON c.edge_gateway_id = eg.id
                                LEFT JOIN label_group lg1 ON lg1.channel_id = c.id
                        WHERE
                            eg.id = #{edgeGatewayId}
                          AND lg1.id IS NOT NULL
                    ) t
                WHERE
                    t.id = lg.id
            );
    </update>

    <update id="deleteByEdgeGatewayId">
        UPDATE label_group lg
        SET lg.DELETED = 1
        WHERE
            EXISTS (
            SELECT
                t.id
            FROM
                (
                SELECT
                    lg1.id
                FROM
                    edge_gateway eg
                    LEFT JOIN channel c ON c.edge_gateway_id = eg.id
                    LEFT JOIN label_group lg1 ON lg1.channel_id = c.id
                WHERE
                    eg.id = #{edgeGatewayId}
                    AND lg1.id IS NOT NULL
                ) t
            WHERE
                t.id = lg.id
            );
    </update>


    <!-- todo -->
    <select id="selectLabelGroupList" resultMap="BaseResultMap">
        SELECT g.*,
            l.id lid,
            l.name lname,
            l.*
        FROM label_group g 
            LEFT JOIN label l ON l.label_group_id = g.id
        <where>
            g.tenant_id = #{tenantId}
            <if test="getAll == null">
                AND l.id NOT IN (
                    SELECT r.label_id
                    FROM label_bind_relation r
                    WHERE r.tenant_id = #{tenantId}
                )
            </if>
            AND l.deleted = 0
            <if test="channelId != null">
                AND (g.channel_id = #{channelId} and g.deleted = 0)
            </if>
            <if test="name != null and name != ''">
                AND (l.name like '%${name}%' or g.name like '%${name}%')
            </if>
        </where>
    </select>

    <update id="moveLabelGroup">
        update label_group
        set channel_id = #{targetChannelId},name = CONCAT(#{newPrefix},SUBSTR(name,INSTR(name,#{oldPrefix})+LENGTH(#{oldPrefix}),LENGTH(name)))
        where name like CONCAT(#{likeStr},'%')
        and channel_id = #{sourceChannelId}
        and DELETED = 0
    </update>
    <select id="listCopyLabelGroup" resultType="com.nti56.nlink.product.device.server.entity.LabelGroupEntity">
        select * from label_group
        where name like CONCAT(#{likeStr},'%')
        and channel_id = #{sourceChannelId}
        and DELETED = 0
    </select>
    <select id="getByLabelId" resultType="com.nti56.nlink.product.device.server.entity.LabelGroupEntity">
        select lg.* from label l
        left join label_group lg on lg.id = l.label_group_id
        where l.id = #{labelId}
        and lg.DELETED = 0
    </select>
</mapper>
