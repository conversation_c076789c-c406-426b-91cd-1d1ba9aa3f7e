<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.ConnectorMapper">
  
    <select id="pageConnector" resultType="com.nti56.nlink.product.device.server.entity.ConnectorEntity">
      SELECT c.*
      FROM connector c
      <where>
          c.deleted = 0
          and c .tenant_id = #{tenantId}
        <if test="queryConnectorDTO.name != null and queryConnectorDTO.name != ''">
          AND c.name like CONCAT('%',#{queryConnectorDTO.name},'%')
        </if>
      </where>
      order By c.create_time desc
    </select>
  
</mapper>
