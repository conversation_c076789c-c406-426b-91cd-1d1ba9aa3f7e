<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.NotAssignGatewayMapper">
    <select id="pageNotAssignGateway" resultType="com.nti56.nlink.product.device.server.model.notAssign.vo.NotAssignGatewayVo">
        SELECT n.*,e.name as edgeGatewayName
        FROM not_assign_gateway n
        left join edge_gateway e on n.edge_gateway_id = e.id
        where n.update_time > DATE_SUB(NOW(), INTERVAL 30 SECOND )
        order By n.id desc
    </select>
  
  <select id="getAssignGateway" resultType="com.nti56.nlink.product.device.server.entity.NotAssignGatewayEntity">
    SELECT n.*
        FROM not_assign_gateway n
        where 1=1
        and admin_port = #{adminPort}
        <if test="imei != null and imei != ''">
          AND n.imei = #{imei}
        </if>
        <if test="host != null and host != ''">
          AND n.host = #{host}
        </if>
        and n.update_time > DATE_SUB(NOW(), INTERVAL 30 SECOND )
        limit 1
    </select>

    <delete id="deleteNotAssignGateway">
        DELETE FROM not_assign_gateway n
        WHERE admin_port = #{adminPort}
        <if test="imei != null and imei != ''">
            AND n.imei = #{imei}
        </if>
        <if test="host != null and host != ''">
            AND n.host = #{host}
        </if>
    </delete>
</mapper>
