<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.CustomMessageMapper">

    <select id="listByDriverIds" resultType="com.nti56.nlink.product.device.server.entity.CustomMessageEntity">
        SELECT * 
        FROM custom_message
        WHERE tenant_id = #{tenantId}
            AND deleted = 0
            AND custom_driver_id IN (
            <foreach item="customDriverId" collection="customDriverIds" separator="," >
              #{customDriverId}
            </foreach>
            )
    </select>

</mapper>