<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.ThingModelInheritMapper">

    <select id="listInheritBoByThingModelId" resultType="com.nti56.nlink.product.device.server.model.ThingModelInheritBo">
        SELECT i.*,
            m.name AS inherit_thing_model_name
        FROM thing_model_inherit i
            LEFT JOIN thing_model m ON (i.inherit_thing_model_id = m.id)
        WHERE m.deleted = 0
            AND i.deleted = 0
            AND i.tenant_id = #{tenantId}
            AND i.thing_model_id = #{thingModelId}
        ORDER BY i.sort_no
    </select>

    <select id="listBeInheritBoByThingModelId" resultType="com.nti56.nlink.product.device.server.model.ThingModelInheritBo">
        SELECT i.*,
            m.name AS be_inherit_thing_model_name
        FROM thing_model_inherit i
            LEFT JOIN thing_model m ON (i.thing_model_id = m.id)
        WHERE m.deleted = 0
            AND i.deleted = 0
            AND i.tenant_id = #{tenantId}
            AND i.inherit_thing_model_id = #{thingModelId}
    </select>

    <select id="listInheritIdByThingModelId" resultType="java.lang.Long">
        SELECT i.inherit_thing_model_id
        FROM thing_model_inherit i
            LEFT JOIN thing_model m ON (i.thing_model_id = m.id)
        WHERE m.deleted = 0
            AND i.deleted = 0
            AND m.tenant_id = #{tenantId}
            AND i.thing_model_id = #{thingModelId}
        ORDER BY i.sort_no
    </select>

</mapper>