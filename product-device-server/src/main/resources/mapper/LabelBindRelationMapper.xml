<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.LabelBindRelationMapper">

    <delete id="physicalDeleteByEdgeGatewayId">
        delete from label_bind_relation where edge_gateway_id = #{edgeGatewayId};
    </delete>

    
<!--    <select id="countByLabelIds"-->
<!--            resultType="com.nti56.nlink.product.device.server.model.CountByIdDTO">-->
<!--        SELECT COUNT(1) count,l.name-->
<!--        FROM label_bind_relation lbr-->
<!--        LEFT JOIN label l on l.id = lbr.label_id-->
<!--        <where>-->
<!--            l.tenant_id = #{tenantId} AND lbr.tenant_id = #{tenantId}-->
<!--            <if test="labelIds != null">-->
<!--                and lbr.label_id in (-->
<!--                <foreach item="labelId" collection="labelIds" separator="," >-->
<!--                    #{labelId}-->
<!--                </foreach>-->
<!--                )-->
<!--            </if>-->
<!--            and l.DELETED = 0-->
<!--            and lbr.DELETED = 0-->
<!--        </where>-->
<!--        GROUP BY lbr.label_id-->
<!--    </select>-->

<!--    <select id="listByLabelIds"-->
<!--            resultType="com.nti56.nlink.product.device.server.entity.LabelBindRelationEntity">-->
<!--        SELECT *-->
<!--        FROM label_bind_relation lbr-->
<!--        <where>-->
<!--            lbr.tenant_id = #{tenantId}-->
<!--            and lbr.DELETED = 0-->
<!--            and lbr.label_id in (-->
<!--            <foreach item="labelId" collection="labelIds" separator="," >-->
<!--                #{labelId}-->
<!--            </foreach>-->
<!--            )-->
<!--        </where>-->
<!--    </select>-->
<!--    <select id="countByChannelId" resultType="java.lang.Integer">-->
<!--        SELECT-->
<!--            COUNT(lbr.id)-->
<!--        FROM-->
<!--            label_group lg-->
<!--            LEFT JOIN label l ON lg.id = l.label_group_id-->
<!--            LEFT JOIN label_bind_relation lbr ON l.id = lbr.label_id-->
<!--        WHERE-->
<!--            lg.channel_id = #{channelId}-->
<!--            and lg.DELETED = 0-->
<!--            and lbr.DELETED = 0-->
<!--    </select>-->

    <select id="listDeviceChannel" resultType="com.nti56.nlink.product.device.server.model.DeviceChannelBo">
        SELECT r.device_id as deviceId,g.channel_id as channelId
        FROM label_bind_relation r
                 LEFT JOIN label l ON (r.label_id = l.id)
                 LEFT JOIN label_group g ON (l.label_group_id = g.id)
        WHERE
            r.DELETED = 0 AND
            l.DELETED = 0 AND
            g.DELETED = 0 AND
            r.device_id = #{deviceId} AND
            l.tenant_id = #{tenantId}
        group by g.channel_id
    </select>


</mapper>
