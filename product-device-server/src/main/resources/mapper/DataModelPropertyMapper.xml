<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.DataModelPropertyMapper">

    <select id="listBoByDataModelId" resultType="com.nti56.nlink.product.device.server.model.DataModelPropertyBo">
        SELECT p.*,
            m.name AS propertyDataModelName
        FROM data_model_property p
            LEFT JOIN data_model m ON (p.property_data_model_id = m.id)
        WHERE 
            p.tenant_id = #{tenantId}
            AND p.deleted = 0
            AND p.data_model_id = #{dataModelId}
    </select>

    <select id="getById" resultType="com.nti56.nlink.product.device.server.entity.DataModelPropertyEntity">
        SELECT p.*
        FROM data_model_property p
        WHERE 
            p.tenant_id = #{tenantId}
            AND p.deleted = 0
            AND p.id = #{id}
    </select>
    
</mapper>