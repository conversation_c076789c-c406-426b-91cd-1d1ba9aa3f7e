<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.ServiceConfigMapper">

   <!-- <resultMap id="BaseResultMap" type="com.nti56.nlink.product.device.server.entity.ServiceConfigEntity">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="thingServiceId" column="thing_service_id" jdbcType="BIGINT"/>
        <result property="thingModelId" column="thing_model_id" jdbcType="BIGINT"/>
        <result property="deviceId" column="device_id" jdbcType="BIGINT"/>
        <result property="configMap" column="config_map" jdbcType="OTHER"/>
        <result property="creatorId" column="CREATOR_ID" jdbcType="BIGINT"/>
        <result property="creator" column="CREATOR" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="engineeringId" column="ENGINEERING_ID" jdbcType="BIGINT"/>
        <result property="moduleId" column="MODULE_ID" jdbcType="BIGINT"/>
        <result property="spaceId" column="SPACE_ID" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>-->

    <sql id="Base_Column_List">
        ID
        ,thing_service_id,thing_model_id,
        device_id,config_map,CREATOR_ID,
        CREATOR,CREATE_TIME,ENGINEERING_ID,
        MODULE_ID,SPACE_ID,tenant_id,
        deleted
    </sql>

    <select id="selectByServiceIdAndDeviceId"
            resultType="com.nti56.nlink.product.device.server.entity.ServiceConfigEntity">
        select *
        from service_config
        where thing_service_id = #{serviceId}
          and device_id = #{deviceId}
          and tenant_id = #{tenantId}
          and deleted = 0
    </select>

    <select id="selectByServiceIdAndModel"
            resultType="com.nti56.nlink.product.device.server.entity.ServiceConfigEntity">
        select *
        from service_config
        where thing_service_id = #{serviceId}
          and thing_model_id = #{modelId}
          and tenant_id = #{tenantId}
          and deleted = 0

    </select>


    <select id="selectByServiceIdAndDeviceIds"
            resultType="com.nti56.nlink.product.device.server.entity.ServiceConfigEntity">
        select *
        from service_config
        where thing_service_id = #{serviceId}
          and device_id in
          <foreach collection="deviceIds" open="(" close=")" item="item" separator=",">
                #{item}
          </foreach>
          and tenant_id = #{tenantId}
          and deleted = 0
    </select>


</mapper>
