<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.MessageAggregationMapper">

    <resultMap id="BaseResultMap" type="com.nti56.nlink.product.device.server.entity.MessageAggregationEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="aggregationId" column="aggregation_id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_Time" jdbcType="TIMESTAMP"/>
            <result property="updator" column="updator" jdbcType="VARCHAR"/>
            <result property="updatorId" column="updator_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,content,title,
        aggregation_id,tenant_id,creator,
        creator_id,create_Time,updator,
        updator_id,update_time
    </sql>
</mapper>
