<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.CustomFieldMapper">

    <delete id="deleteMessageFieldByDriverId">
        DELETE FROM custom_field f 
        WHERE f.tenant_id = #{tenantId} 
            AND f.target_type = 2 
            AND f.target_id IN (
                SELECT m.id FROM custom_message m
                WHERE m.tenant_id = #{tenantId} 
                    AND m.custom_driver_id = #{customDriverId}
            )
    </delete>
    
    <delete id="deleteMessageFieldByMessageId">
        DELETE FROM custom_field f 
        WHERE f.tenant_id = #{tenantId} 
            AND f.target_type = 2 
            AND f.target_id = #{customMessageId}
    </delete>
    
</mapper>