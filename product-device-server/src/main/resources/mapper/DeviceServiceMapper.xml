<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.DeviceServiceMapper">
    
    <select id="listByDataModelId" resultType="com.nti56.nlink.product.device.server.entity.DeviceServiceEntity">
        SELECT *
        FROM device_service ps
        WHERE 
            ps.tenant_id = #{tenantId}
            AND ps.deleted = 0
            AND (
                JSON_CONTAINS(ps.input_data, JSON_OBJECT('dataModelId', #{dataModelId}))
                OR
                JSON_CONTAINS(ps.output_data, JSON_OBJECT('dataModelId', #{dataModelId}))
            )
    </select>

</mapper>