package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.YesNoEnum;
import com.nti56.nlink.product.device.server.entity.ConnectorEntity;
import com.nti56.nlink.product.device.server.entity.ConnectorItemEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.loader.ConnectorInstanceLoader;
import com.nti56.nlink.product.device.server.loader.ConnectorScriptLoader;
import com.nti56.nlink.product.device.server.mapper.ConnectorItemMapper;
import com.nti56.nlink.product.device.server.model.connectorItem.dto.CreateConnectorItemDTO;
import com.nti56.nlink.product.device.server.model.connectorItem.dto.EditConnectorItemDTO;
import com.nti56.nlink.product.device.server.model.connectorItem.dto.QueryConnectorItemDTO;
import com.nti56.nlink.product.device.server.model.connectorItem.vo.ConnectorItemVO;
import com.nti56.nlink.product.device.server.service.IConnectorItemService;
import com.nti56.nlink.product.device.server.service.IConnectorService;
import com.nti56.nlink.product.device.server.serviceEngine.DataConversionService;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 类说明:
 * <AUTHOR>
 * @version 1.0
 * @since JDK 1.8
 */
@Service
public class ConnectorItemServiceImpl extends BaseServiceImpl<ConnectorItemMapper, ConnectorItemEntity> implements IConnectorItemService {
    
    @Autowired
    private ConnectorItemMapper connectorItemMapper;
    
    @Autowired
    private ConnectorInstanceLoader connectorInstanceLoader;
    
    @Autowired
    private IConnectorService connectorService;

    @Autowired
    private ConnectorScriptLoader connectorScriptLoader;
    
    @Override
    public Result<Page<ConnectorItemVO>> pageConnectorItem(PageParam pageParam, QueryConnectorItemDTO queryConnectorItemDTO, TenantIsolation tenantIsolation) {
        Page<ConnectorItemEntity> connectorItemEntityPage = connectorItemMapper.pageConnectorItem(pageParam.toPage(ConnectorItemEntity.class), queryConnectorItemDTO, tenantIsolation.getTenantId());
        
        Page<ConnectorItemVO> packageVOPage = new Page<>();
        packageVOPage.setTotal(connectorItemEntityPage.getTotal());
        packageVOPage.setCurrent(connectorItemEntityPage.getCurrent());
        packageVOPage.setSize(connectorItemEntityPage.getSize());
        packageVOPage.setPages(connectorItemEntityPage.getPages());
        
        if (CollectionUtil.isNotEmpty(connectorItemEntityPage.getRecords())) {
            List<ConnectorItemVO> collect = connectorItemEntityPage.getRecords().stream().map(t -> {
                ConnectorItemVO connectorVO = new ConnectorItemVO();
                BeanUtil.copyProperties(t, connectorVO);
                return connectorVO;
            }).collect(Collectors.toList());
            packageVOPage.setRecords(collect);
        } else {
            packageVOPage.setRecords(new ArrayList<>());
        }
        return Result.ok(packageVOPage);
    }
    
    @Override
    public Result<ConnectorItemEntity> createConnectorItem(CreateConnectorItemDTO createConnectorItemDTO, TenantIsolation tenantIsolation) {
        Result<Void> processCodeResult = getCheckResult(tenantIsolation, null, createConnectorItemDTO.getName(), createConnectorItemDTO.getProcessCode(),createConnectorItemDTO.getConnectorId());
        if (!processCodeResult.getSignal()) {
            return Result.error(processCodeResult.getMessage());
        }
        ConnectorItemEntity connectorItemEntity = new ConnectorItemEntity();
        BeanUtil.copyProperties(createConnectorItemDTO, connectorItemEntity);
        connectorItemMapper.insert(connectorItemEntity);
        ConnectorEntity connectorEntity = connectorService.getById(createConnectorItemDTO.getConnectorId());
        if(YesNoEnum.YES.getValue().equals(connectorEntity.getStatus())){
            connectorScriptLoader.loadScript(
                tenantIsolation.getTenantId(), 
                connectorEntity.getId(), 
                connectorItemEntity.getId(), 
                connectorItemEntity.getProcessCode()
            );
            connectorInstanceLoader.startConnector(connectorEntity.getId());
        }
        return Result.ok(connectorItemEntity);
    }
    
    @Override
    public Result<Void> editConnectorItem(EditConnectorItemDTO editConnectorItemDTO, TenantIsolation tenantIsolation) {
        Result<Void> processCodeResult = getCheckResult(tenantIsolation, editConnectorItemDTO.getId(), editConnectorItemDTO.getName(), editConnectorItemDTO.getProcessCode(),editConnectorItemDTO.getConnectorId());
        if (!processCodeResult.getSignal()) {
            return Result.error(processCodeResult.getMessage());
        }
        ConnectorItemEntity connectorItemEntity = new ConnectorItemEntity();
        BeanUtil.copyProperties(editConnectorItemDTO, connectorItemEntity);
        connectorItemMapper.updateById(connectorItemEntity);
        ConnectorEntity connectorEntity = connectorService.getById(editConnectorItemDTO.getConnectorId());
        if(YesNoEnum.YES.getValue().equals(connectorEntity.getStatus())){
            connectorScriptLoader.loadScript(
                tenantIsolation.getTenantId(), 
                connectorEntity.getId(), 
                connectorItemEntity.getId(), 
                connectorItemEntity.getProcessCode()
            );
            connectorInstanceLoader.startConnector(connectorEntity.getId());
        }
        return Result.ok();
    }
    
    private Result<Void> getCheckResult(TenantIsolation tenantIsolation, Long id, String name, String processCode,Long connectorId) {
        Result<Void> result = uniqueName(id, name, tenantIsolation,connectorId);
        if (!result.getSignal()) {
            throw new BizException(result.getMessage());
        }
        Result<Void> processCodeResult = checkDataConversionCode(processCode);
        if (!processCodeResult.getSignal()) {
            return Result.error(ServiceCodeEnum.SUBSCRIPTION_CODE_ERROR, processCodeResult.getMessage());
        }
        return Result.ok();
    }
    
    @Override
    public Result<Void> deleteConnectorItem(Long id, TenantIsolation tenantIsolation) {
        Result<ConnectorItemEntity> old = this.getByIdAndTenantIsolation(id, tenantIsolation);
        if (old.getResult() == null) {
            throw new BizException("该租户下不存在此渠道");
        }
        connectorItemMapper.deleteById(id);
        ConnectorEntity connectorEntity = connectorService.getById(old.getResult().getConnectorId());
        if(YesNoEnum.YES.getValue().equals(connectorEntity.getStatus())){
            connectorScriptLoader.unloadScript(
                tenantIsolation.getTenantId(), 
                connectorEntity.getId(), 
                old.getResult().getId()
            );
            connectorInstanceLoader.startConnector(connectorEntity.getId());
        }
        return Result.ok();
    }
    
    /**
     * 判断详情项目是否存在
     * @param connectorId
     */
    public Result<Boolean> existConnectorItem(Long connectorId) {
        LambdaQueryWrapper<ConnectorItemEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConnectorItemEntity::getConnectorId, connectorId);
        List<ConnectorItemEntity> connectorItemEntityList = connectorItemMapper.selectList(lqw);
        if (CollectionUtil.isNotEmpty(connectorItemEntityList)) {
            return Result.ok(true);
        }
        return Result.ok(false);
    }
    
    @Override
    public Result<ConnectorItemVO> getConnectorItemInfo(Long id) {
        ConnectorItemEntity connectorItemEntity = connectorItemMapper.selectById(id);
        ConnectorItemVO connectorItemVO = new ConnectorItemVO();
        BeanUtil.copyProperties(connectorItemEntity,connectorItemVO);
        return Result.ok(connectorItemVO);
    }
    
    /**
     * 判断名称是否唯一
     * @param id
     * @param name
     * @param tenantIsolation
     */
    private Result<Void> uniqueName(Long id, String name, TenantIsolation tenantIsolation,Long connectorId) {
        LambdaQueryWrapper<ConnectorItemEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, ConnectorItemEntity::getId, id)
            .eq(ConnectorItemEntity::getName, name)
            .eq(ConnectorItemEntity::getConnectorId, connectorId)
            .eq(ConnectorItemEntity::getTenantId, tenantIsolation.getTenantId());
        if (connectorItemMapper.selectCount(lqw) > 0) {
            return Result.error("该连接器下连接器详情项已经存在该名称的,名称：" + name);
        }
        return Result.ok();
    }
    
    private Result<ConnectorItemEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<ConnectorItemEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConnectorItemEntity::getId, id).eq(ConnectorItemEntity::getTenantId, tenantIsolation.getTenantId());
        ConnectorItemEntity connectorEntity = connectorItemMapper.selectOne(lqw);
        return Result.ok(connectorEntity);
    }
    
    public static Result<Void> checkDataConversionCode(String code) {
        ScriptEngine nashorn = new ScriptEngineManager().getEngineByName("nashorn");
        try {
            DataConversionService.loadCode(DataConversionService.initServiceName(null, code), nashorn);
        } catch (BizException e) {
            return Result.error(e.getMessage());
        }
        return Result.ok();
    }
}
