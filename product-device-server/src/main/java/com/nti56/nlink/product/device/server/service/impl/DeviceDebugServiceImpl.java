package com.nti56.nlink.product.device.server.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.QueryApi;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.it.ITResult;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.channel.Channel;
import com.nti56.nlink.product.device.server.domain.thing.device.Device;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceDataResource;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceTwin;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewaySpiProxy;
import com.nti56.nlink.product.device.server.mapper.DeviceMapper;
import com.nti56.nlink.product.device.server.mapper.LabelBindRelationMapper;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.model.device.dto.TenantIdDeviceNameDto;
import com.nti56.nlink.product.device.server.model.deviceLog.DeviceStateVo;
import com.nti56.nlink.product.device.server.model.deviceLog.PropertyLogBo;
import com.nti56.nlink.product.device.server.model.product.vo.DeviceVO;
import com.nti56.nlink.product.device.server.service.IChannelService;
import com.nti56.nlink.product.device.server.service.IDeviceDebugService;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.util.redis.RedisUtil;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 类说明: 设备服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:10:11
 * @since JDK 1.8
 */
@Service
@Slf4j
public class DeviceDebugServiceImpl implements IDeviceDebugService, InitializingBean {

    @Autowired
    IDeviceService deviceService;

    @Lazy
    @Autowired
    IChannelService channelService;

    @Autowired
    IEdgeGatewaySpiProxy edgeGatewaySpiProxy;

    @Autowired
    private CommonFetcherFactory commonFetcherFactory;

    @Autowired
    LabelBindRelationMapper labelBindRelationMapper;

    @Autowired
    DeviceDataResource deviceDataResource;

    RedisUtil redisUtil;

    @Autowired @Lazy
    RedisTemplate redisTemplate;

    @Autowired
    DeviceMapper deviceMapper;

    @Autowired
    InfluxDBClient influxDBClient;

    @Value("${influx.bucket}")
    private String bucket;
    
    @Override
    public void afterPropertiesSet() throws Exception {
        redisUtil = new RedisUtil(this.redisTemplate, RedisConstant.DEVICE_TWIN_PREFIX);
    }

    @Override
    public Result<EventCheckResult> getRuntimeData(Long tenantId, Long deviceId, String eventName) {

        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        if (!Optional.ofNullable(deviceEntity).isPresent() || !tenantId.equals(deviceEntity.getTenantId())) {
            log.error("tenantId error! tenantId:{},deviceId:{}",tenantId,deviceId);
            return Result.ok(EventCheckResult.builder().eventCheck(false).eventCheckMsg("设备未定义！").build());
        }
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        Result<Device> deviceResult = Device.checkInfoToSingleEvent(deviceEntity, commonFetcher, eventName, true);
        if (!deviceResult.getSignal()) {
            log.error("device check error:{}",deviceResult.getMessage());
            return Result.ok(EventCheckResult.builder().eventCheck(false).eventCheckMsg(deviceResult.getMessage()).build());
        }
        Result<List<ChannelElm>> usefullyChannel = getUsefullyChannel(tenantId, deviceEntity.getEdgeGatewayId(),commonFetcher);
        if (!usefullyChannel.getSignal()) {
            log.warn("edgeGateway has no usefully channel! edgeGatewayId:{}",deviceEntity.getEdgeGatewayId());
            return Result.ok(EventCheckResult.builder().eventCheck(false).eventCheckMsg("网关下没有可用的通道!").build());
        }
        EventCheckResult eventCheckResult = EventCheckResult.builder().eventCheck(true).build();
        List<AccessElm> accessElms = deviceResult.getResult().getEventConditionPropertyAccess(eventName);
        if (!CollectionUtil.isEmpty(accessElms)) {
            Result<List<ConnectResult>> conditionValue = edgeGatewaySpiProxy.connectLabel(deviceEntity.getEdgeGatewayId(), tenantId, accessElms, usefullyChannel.getResult());
            if (!conditionValue.getSignal()) {
                log.warn("获取触发条件属性值失败：{}",conditionValue.getMessage());
                eventCheckResult.setConditionValueMsg(conditionValue.getMessage());
            }
            eventCheckResult.setConditionValue(conditionValue.getResult());
        }else {
            eventCheckResult.setConditionValueMsg("获取触发条件属性值失败!");
        }
        List<AccessElm> reportPropertyAccess = deviceResult.getResult().getTriggerEventReportPropertyAccess(eventName);
        if (!CollectionUtil.isEmpty(reportPropertyAccess)) {
            Result<List<ConnectResult>> reportValue = edgeGatewaySpiProxy.connectLabel(deviceEntity.getEdgeGatewayId(), tenantId, reportPropertyAccess, usefullyChannel.getResult());
            if (!reportValue.getSignal()) {
                log.warn("获取上报属性值失败:{}",reportValue.getMessage());
                eventCheckResult.setReportValueMsg(reportValue.getMessage());
            }
            eventCheckResult.setReportValue(reportValue.getResult());
        }else {
            eventCheckResult.setReportValueMsg("获取上报属性值失败!");
        }
        return Result.ok(eventCheckResult);
    }



    @Override
    public Result<EventDebugResult> eventDebug(Long tenantId, Long deviceId, String eventName, Map<String, Object> input) {
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        if (!Optional.ofNullable(deviceEntity).isPresent() || !tenantId.equals(deviceEntity.getTenantId())) {
            log.error("tenantId error! tenantId:{},deviceId:{}",tenantId,deviceId);
            Result<EventDebugResult> error = Result.error(1,ServiceCodeEnum.DEVICE_UNDEFINED.getMessage());
            error.setResult(EventDebugResult.builder().ok(false).message(ServiceCodeEnum.DEVICE_UNDEFINED.getMessage()).build());
            return error;
        }
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        Result<Device> deviceResult = Device.checkInfoToSingleEvent(deviceEntity, commonFetcher, eventName, false);
        if (!deviceResult.getSignal()) {
            log.error("device check error:{}",deviceResult.getMessage());
            Result<EventDebugResult> error = Result.error(1,deviceResult.getMessage());
            error.setResult(EventDebugResult.builder().ok(false).message(deviceResult.getMessage()).build());
            return error;
        }
        Device device = deviceResult.getResult();
        EventDebugResult result = device.triggerEventDebug(eventName, input);
        return Result.ok(result);
    }

    @Override
    public Result<List<ChannelElm>> getUsefullyChannel(Long tenantId,Long edgeGatewayId,CommonFetcher commonFetcher){
        Result<List<ChannelEntity>> channelsResult = channelService.listChannelByEdgeGatewayId(edgeGatewayId, tenantId,false);
        if (!channelsResult.getSignal()) {
            log.warn("edgeGateway has no channel! edgeGatewayId:{}",edgeGatewayId);
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        if (!Optional.ofNullable(commonFetcher).isPresent()) {
            commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        }
        return Result.ok(Channel.getUsefullyChannel(channelsResult.getResult(), commonFetcher));
    }

    @Override
    public Result getByTenantId(TenantIsolation tenantIsolation, DeviceTwinRequestBo request) {
        List<Long> deviceIds ;
        if (CollectionUtil.isNotEmpty(request.getIds())) {
            deviceIds = request.getIds();
        }else {
            deviceIds = deviceService.getDeviceIdsByTenantId(tenantIsolation.getTenantId(),request);
        }
        Map<Long,Map<String,Object>> result = deviceDataResource.getByIds(deviceIds);
        return Result.ok(result);
    }

    @Override
    public Result<Set<Long>> getSubscriptionState(Long tenantId, Long deviceId) {
        Set<Long> list = deviceDataResource.getSubscriptionEnable(tenantId,deviceId);
        return Result.ok(list);
    }

    @Override
    public Result<DeviceVO> getActual(Long tenantId, Long deviceId) {
        DeviceTwin deviceTwin = new DeviceTwin(deviceDataResource, deviceId);
        DeviceVO build = DeviceVO.builder()
                .deviceTwinActual(deviceTwin.getActual())
                .deviceChangeTime(deviceTwin.getChangeTime())
                .id(deviceId)
                .build();
        return Result.ok(build);
    }

    public Result<Map<String, Object>> getActuralPropertyOfDevices(Long tenantId, String property, Long[] deviceIds){
        Map<String,Object> result = MapUtil.createMap(HashMap.class);
        for(Long deviceId: deviceIds){
            Object value = redisUtil.hget(getDeviceActualKeyByDeviceId(deviceId), property);
            result.put(deviceId.toString(), value);
        }
        return Result.ok(result);
    }

    public Result<Map<String, Object>> getActuralPropertyOfDevices(Long tenantId, String property, List<String> deviceNames){
        List<DeviceEntity> devices = deviceMapper.listDeviceByNames(tenantId, deviceNames);
        
        Map<String,Object> result = MapUtil.createMap(HashMap.class);
        for(DeviceEntity device: devices){
            Object value = redisUtil.hget(getDeviceActualKeyByDeviceId(device.getId()), property);
            result.put(device.getName(), value);
        }
        return Result.ok(result);
    }
    public Result<Map<String, Object>> getActuralPropertyOfDevicesByTenantIds(Set<Long> tenantIds, String property, List<String> deviceNames){
        List<DeviceEntity> devices = deviceMapper.listDeviceByNameTanentIds(tenantIds, deviceNames);
        
        Map<String,Object> result = MapUtil.createMap(HashMap.class);
        for(DeviceEntity device: devices){
            Object value = redisUtil.hget(getDeviceActualKeyByDeviceId(device.getId()), property);
            result.put(device.getName(), value);
        }
        return Result.ok(result);
    }

    private Result<List<DeviceEntity>> listDevices(List<String> deviceNames, List<Long> tenantIds){
        List<TenantIdDeviceNameDto> tenantIdDeviceNames = new ArrayList<>();
        
        for(int i = 0; i<deviceNames.size(); i++){
            TenantIdDeviceNameDto tenantIdDeviceName = new TenantIdDeviceNameDto();
            tenantIdDeviceName.setTenantId(tenantIds.get(i));
            tenantIdDeviceName.setDeviceName(deviceNames.get(i));
            tenantIdDeviceNames.add(tenantIdDeviceName);
        }
        List<DeviceEntity> devices = deviceMapper.listDeviceByTenantIdDeviceNames(tenantIdDeviceNames);
        return Result.ok(devices);
    }

    public Result<Map<String, Object>> getActuralPropertyOfDevices(String property, List<String> deviceNames, List<Long> tenantIds){
        if(deviceNames == null || tenantIds == null){
            return Result.ok(new HashMap<>());
        }
        if(deviceNames.size() != tenantIds.size()){
            return Result.error("参数数量不匹配");
        }
        Result<List<DeviceEntity>> res = listDevices(deviceNames, tenantIds);
        if(!res.getSignal()){
            return Result.error(res.getMessage());
        }
        List<DeviceEntity> devices = res.getResult();

        Map<String,Object> result = MapUtil.createMap(HashMap.class);
        for(DeviceEntity device: devices){
            Object value = redisUtil.hget(getDeviceActualKeyByDeviceId(device.getId()), property);
            result.put(device.getTenantId() + "_" + device.getName(), value);
        }
        return Result.ok(result);
    }

    public Result<Map<String, PropertyValueWithTime>> getActuralPropertyAndTimeOfDevices(Long tenantId, String property, List<String> deviceNames){
        List<DeviceEntity> devices = deviceMapper.listDeviceByNames(tenantId, deviceNames);

        Map<String,PropertyValueWithTime> r = MapUtil.createMap(HashMap.class);
        for(DeviceEntity device: devices){
            Object value = redisUtil.hget(getDeviceActualKeyByDeviceId(device.getId()), property);
            Object time = redisUtil.hget(getDeviceChangeTimeKeyByDeviceId(device.getId()), property);
            r.put(device.getName(), new PropertyValueWithTime(value, time));
        }
        return Result.ok(r);
    }
    

    public Result<Map<String, PropertyValueWithTime>> getActuralPropertyAndTimeOfDevices(String property, List<String> deviceNames, List<Long> tenantIds){
        if(deviceNames == null || tenantIds == null){
            return Result.ok(new HashMap<>());
        }
        if(deviceNames.size() != tenantIds.size()){
            return Result.error("参数数量不匹配");
        }
        Result<List<DeviceEntity>> res = listDevices(deviceNames, tenantIds);
        if(!res.getSignal()){
            return Result.error(res.getMessage());
        }
        List<DeviceEntity> devices = res.getResult();

        Map<String,PropertyValueWithTime> r = MapUtil.createMap(HashMap.class);
        for(DeviceEntity device: devices){
            Object value = redisUtil.hget(getDeviceActualKeyByDeviceId(device.getId()), property);
            Object time = redisUtil.hget(getDeviceChangeTimeKeyByDeviceId(device.getId()), property);
            r.put(device.getTenantId() + "_" + device.getName(), new PropertyValueWithTime(value, time));
        }
        return Result.ok(r);
    }
    

    public Result<Map<String, Object>> getActuralPropertiesOfDevice(Long tenantId, List<String> properties, String deviceName){
        DeviceEntity device = deviceMapper.getDeviceByName(tenantId, deviceName);
        
        Map<String,Object> result = MapUtil.createMap(HashMap.class);
        for(String property: properties){
            Object value = redisUtil.hget(getDeviceActualKeyByDeviceId(device.getId()), property);
            result.put(property, value);
        }
        return Result.ok(result);
    }

    public Result<Map<String, PropertyValueWithTime>> getActuralPropertiesAndTimesOfDevice(Long tenantId, List<String> properties, String deviceName){
        DeviceEntity device = deviceMapper.getDeviceByName(tenantId, deviceName);
        if(device == null){
            return Result.error("ot找不到设备：" + deviceName);
        }
        
        Map<String,PropertyValueWithTime> result = MapUtil.createMap(HashMap.class);
        if(properties == null || properties.size() <= 0){
            return Result.ok(result);
        }
        for(String property: properties){
            Object value = redisUtil.hget(getDeviceActualKeyByDeviceId(device.getId()), property);
            Object time = redisUtil.hget(getDeviceChangeTimeKeyByDeviceId(device.getId()), property);
            result.put(property, new PropertyValueWithTime(value, time));
        }
        return Result.ok(result);
    }

    private String getDeviceActualKeyByDeviceId(Long deviceId){
        return new StringBuilder().append("actual").append(":").append(deviceId).toString();
    }
    private String getDeviceChangeTimeKeyByDeviceId(Long deviceId) {
        return new StringBuilder().append("changeTime").append(":").append(deviceId).toString();
    }

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    private Result<Pair<LocalDateTime, LocalDateTime>> checkBeginEnd(String begin, String end){
        LocalDateTime beginTime = LocalDateTime.parse(begin, formatter);
        LocalDateTime endTime = LocalDateTime.parse(end, formatter);
        if(beginTime.isAfter(endTime)){
            return Result.error("查询开始时间不能大于查询结束时间");
        }
        return Result.ok(Pair.of(beginTime, endTime));
    }
    
    @Override
    public Result<Page<DeviceStateVo>> pageStateOfDevice(
        Long tenantId, String deviceName, Integer size, Integer current, String begin, String end
    ) {
        if(begin == null || "".equals(begin)){
            begin = "1970-01-01T00:00:00";
        }
        if(end == null || "".equals(end)){
            end = formatter.format(LocalDateTime.now());
        }
        
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if(!checkResult.getSignal()){
            return Result.error(checkResult.getMessage());
        }

        DeviceEntity device = deviceMapper.getDeviceByName(tenantId, deviceName);
        Long deviceId = device.getId();

        Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
        Long beginTime = pair.getLeft().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        Long endTime = pair.getRight().toInstant(ZoneOffset.of("+8")).toEpochMilli();

        QueryApi queryApi = influxDBClient.getQueryApi();
        Flux flux = Flux.from(bucket)
            .range(beginTime/1000, endTime/1000)
            .filter(Restrictions.and(
                Restrictions.measurement().equal(tenantId.toString()),
                Restrictions.tag("deviceId").equal(deviceId.toString()),
                Restrictions.tag("property").equal("State")
            ))
            .map("({ r with prop: r[\"property\"] })")
            .keep(new String[]{"_time", "_value", "prop"})
            .map("({ r with property: r[\"prop\"] })")
            .keep(new String[]{"_time", "_value", "property"})
            .sort(new String[]{"_time"},true)
            .limit(size, size * (current - 1))
            ;
        String query = flux.toString();
        log.info("query: {}", query);
        List<DeviceStateVo> list = queryApi.query(query, DeviceStateVo.class);
        Page<DeviceStateVo> page = new Page<>();
        page.setSize(size);
        page.setCurrent(current);
        page.setRecords(list);
        return Result.ok(page);
    }
}
