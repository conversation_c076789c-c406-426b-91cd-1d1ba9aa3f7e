package com.nti56.nlink.product.device.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.domain.thing.custom.CustomDriver;
import com.nti56.nlink.product.device.server.domain.thing.custom.CustomField;
import com.nti56.nlink.product.device.server.domain.thing.custom.CustomMessage;
import com.nti56.nlink.product.device.server.entity.CustomFieldEntity;
import com.nti56.nlink.product.device.server.entity.CustomMessageEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.CustomFieldMapper;
import com.nti56.nlink.product.device.server.mapper.CustomMessageMapper;
import com.nti56.nlink.product.device.server.model.custom.CustomMessageDto;
import com.nti56.nlink.product.device.server.model.custom.CustomMessageFullBo;
import com.nti56.nlink.product.device.server.service.ICustomMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 自定义消息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-11-18 11:43:28
 * @since JDK 1.8
 */
@Service
public class CustomMessageServiceImpl extends BaseServiceImpl<CustomMessageMapper,CustomMessageEntity>  implements ICustomMessageService {

    @Autowired
    CustomMessageMapper mapper;

    @Autowired
    CommonFetcherFactory commonFetcherFactory;

    @Autowired
    CustomFieldMapper customFieldMapper;

    @Override
    public Result<CustomMessageFullBo> getById(TenantIsolation tenant, @NotNull Long entityId) {
        CustomMessageEntity entity = mapper.selectById(entityId);
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenant.getTenantId());
        
        Result<CustomMessage> result = CustomMessage.checkInfo(entity, commonFetcher);
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }
        CustomMessage customMessage = result.getResult();
        CustomMessageFullBo bo = customMessage.toFullBo();
        return Result.ok(bo);
    }

    @Override
    @Transactional
    public Result<Long> create(TenantIsolation tenant, @NotNull CustomMessageDto dto) {
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenant.getTenantId());
        
        Result<CustomMessage> customMessageResult = CustomMessage.checkCreate(
            dto.getCustomMessageEntity(),
            dto.getMessageFieldEntityList(),
            commonFetcher
        );
        
        if(!customMessageResult.getSignal()){
            return Result.error(customMessageResult.getMessage());
        }
        
        CustomMessage customMessage = customMessageResult.getResult();
        CustomMessageEntity customMessageEntity = customMessage.toEntity();
        Integer c1 = mapper.insert(customMessageEntity);
        if(c1 == null || c1 != 1){
            return Result.error("插入消息失败");
        }

        List<CustomField> fieldList = customMessage.getMessageFieldList();

        if(fieldList == null || fieldList.size() <= 0){
            return Result.ok(customMessageEntity.getId());
        }

        //创建fixHeader
        for(CustomField field:fieldList){
            CustomFieldEntity fieldEntity = field.toEntity(false);
            fieldEntity.setTargetId(customMessageEntity.getId());
            Integer c2 = customFieldMapper.insert(fieldEntity);
            if(c2 == null || c2 != 1){
                throw new BizException("插入消息字段失败"); //回滚
            }
        }

        return Result.ok(customMessageEntity.getId());
    }

    @Override
    @Transactional
    public Result<Void> update(TenantIsolation tenant, @NotNull CustomMessageDto dto) {
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenant.getTenantId());
        
        //检查CustomDriver
        Result<CustomMessage> customMessageResult = CustomMessage.checkUpdate(
            dto.getCustomMessageEntity(), 
            dto.getMessageFieldEntityList(),
            commonFetcher
        );
        if(!customMessageResult.getSignal()){
            return Result.error(customMessageResult.getMessage());
        }

        CustomMessage customMessage = customMessageResult.getResult();

        deleteSub(tenant.getTenantId(), customMessage.getId());

        //更新customMessage
        CustomMessageEntity customMessageEntity = customMessage.toEntity();
        Integer c1 = mapper.updateById(customMessageEntity);
        if(c1 == null || c1 != 1){
            return Result.error("插入协议包失败");
        }

        //插入新的messageField
        List<CustomField> messageFieldList = customMessage.getMessageFieldList();
        if(messageFieldList != null && messageFieldList.size() > 0){
            for(CustomField field:messageFieldList){
                CustomFieldEntity fieldEntity = field.toEntity(false);
                Integer c2 = customFieldMapper.insert(fieldEntity);
                if(c2 == null || c2 != 1){
                    throw new BizException("插入协议包字段失败"); //回滚
                }
            }
        }

        return Result.ok();
    }

    @Override
    public Result<Page<CustomMessageEntity>> getPage(TenantIsolation tenant, CustomMessageEntity entity, Page<CustomMessageEntity> page) {
        Page<CustomMessageEntity> list = mapper.selectPage(page, new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    @Override
    public Result<List<CustomMessageEntity>> list(TenantIsolation tenant, CustomMessageEntity entity) {
        List<CustomMessageEntity> list = mapper.selectList(new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    private void deleteSub(Long tenantId, Long customMessageId){
        //删除所有messageField
        customFieldMapper.deleteMessageFieldByMessageId(tenantId, customMessageId);
    }

    @Override
    @Transactional
    public Result<Void> deleteById(TenantIsolation tenant, @NotNull Long entityId) {
        deleteSub(tenant.getTenantId(), entityId);

        if (mapper.deleteById(entityId) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

}
