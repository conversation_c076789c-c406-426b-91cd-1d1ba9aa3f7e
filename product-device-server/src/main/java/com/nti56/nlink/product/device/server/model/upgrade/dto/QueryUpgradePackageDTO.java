package com.nti56.nlink.product.device.server.model.upgrade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/3/17 12:41<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "升级包查询")
public class QueryUpgradePackageDTO {
    
    /**
     * 升级包名称
     */
    @Schema(description = "升级包名称")
    private String upgradePackageName;
    
    /**
     * 升级包版本
     */
    @Schema(description = "升级包版本")
    private String upgradeVersion;

}
