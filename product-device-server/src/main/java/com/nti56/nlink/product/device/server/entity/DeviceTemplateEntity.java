package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 设备模板表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2023-03-07 17:33:19
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("device_template")
@Schema(description = "DeviceTemplate对象")
public class DeviceTemplateEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 设备模板名称
     */
    @Schema(description = "设备模板名称")
    @NotNull(message = "设备模板名称不能为空")
    @Size(max = 256, message = "设备模板名称长度不能超过256")
    private String name;

    /**
     * 文件大小
     */
    @Schema(description = "文件大小")
    private Long docSize;

    /**
     * 生成类型 0本地 1导入
     */
    @Schema(description = "生成类型 0本地 1导入")
    private Integer generateType;

    /**
     * 下载链接
     */
    @Schema(description = "下载链接")
    private String downloadLink;

    /**
     * 平台版本
     */
    @Schema(description = "平台版本")
    private String platformVersion;

    /**
     * 模板描述
     */
    @Schema(description = "模板描述")
    @Size(max = 512, message = "模板描述长度不能超过512")
    private String descript;

    /**
     * 继承物模型名称
     */
    @Schema(description = "继承物模型名称")
    private String inheritThingModelName;

    /**
     * 继承物模型id
     */
    @Schema(description = "继承物模型id")
    private String inheritThingModelId;

    /**
     * 通道类型
     */
    @Schema(description = "通道类型")
    private String channelType;

    /**
     * 通道驱动
     */
    @Schema(description = "通道驱动")
    private Integer channelDriver;

    /**
     * 模板标签数量
     */
    @Schema(description = "模板标签数量")
    private Integer labelCount;

    /**
     * 标签文件名称
     */
    @Schema(description = "标签文件名称")
    private String labelFileName;


    /**
     * 标签文件存储路径
     */
    @Schema(description = "标签文件存储路径")
    private String labelFileLink;


    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

    /**
     * 逻辑删除，1-删除
     */
    @Schema(description = "逻辑删除，1-删除")
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @Schema(description = "工程ID")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @Schema(description = "模块ID")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @Schema(description = "空间ID")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;


}
