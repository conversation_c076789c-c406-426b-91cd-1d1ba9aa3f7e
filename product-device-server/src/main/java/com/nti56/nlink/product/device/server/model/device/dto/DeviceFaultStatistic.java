package com.nti56.nlink.product.device.server.model.device.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 类说明：
 *
 * @ClassName DeviceFaultStatistic
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/11/3 16:26
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class DeviceFaultStatistic implements Serializable {

    private List<DeviceFaultInstance> faultInstanceList;

    private Integer deviceTotal;

    private Integer faultDevice;

    private Integer normalDevice;


}
