package com.nti56.nlink.product.device.server.util.aliyun;

import com.aliyun.dysmsapi20170525.models.*;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaException;
import com.aliyun.tea.TeaPair;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/19 9:45<br/>
 * @since JDK 1.8
 */
public class AliYunSmsSign {
    /**
     * 添加短信签名
     * @param client 调用AliyunSmsBase.createClient()创建
     * @param signName 签名名称。签名必须符合个人用户签名规范https://help.aliyun.com/document_detail/108076.html?spm=api-workbench.API%20Explorer.0.0.1f6b1e0fdiG0AW
     * @param signSource 签名来源。取值：0：企事业单位的全称或简称。1：工信部备案网站的全称或简称。2：App应用的全称或简称。3：公众号或小程序的全称或简称。4：电商平台店铺名的全称或简称。5：商标名的全称或简称。
     * @param remark 短信签名申请说明。请在申请说明中详细描述您的业务使用场景，申请工信部备案网站的全称或简称请在此处填写域名，长度不超过200个字符。
     * @param fileContents 签名的资质证明文件经base64编码后的字符串。图片不超过2 MB。
     * @param fileSuffix 签名的证明文件格式，支持上传多张图片。当前支持JPG、PNG、GIF或JPEG格式的图片。
     * 返回示例
    {
    "SignName": "阿里云",
    "Message": "OK",
    "RequestId": "F655A8D5-B967-440B-8683-DAD6FF8DE990",
    "Code": "OK"
    }
     * @throws Exception
     */
    public static void addSmsSign(com.aliyun.dysmsapi20170525.Client client, String signName, Integer signSource, String remark, String fileContents, String fileSuffix) throws Exception {
        AddSmsSignRequest.AddSmsSignRequestSignFileList signFileList = new AddSmsSignRequest.AddSmsSignRequestSignFileList();
        signFileList.fileContents = fileContents;
        signFileList.fileSuffix = fileSuffix;
        AddSmsSignRequest req = new AddSmsSignRequest()
                .setSignName(signName)
                .setSignSource(signSource)
                .setRemark(remark)
                .setSignFileList(java.util.Arrays.asList(
                        signFileList
                ));
        AddSmsSignResponse resp = client.addSmsSign(req);
        if (!com.aliyun.teautil.Common.equalString(resp.body.code, "OK")) {
            throw new TeaException(TeaConverter.buildMap(
                    new TeaPair("code", resp.body.code),
                    new TeaPair("message", resp.body.message)
            ));
        }
        System.err.println("添加短信签名：");
        System.err.println(com.aliyun.teautil.Common.toJSONString(com.aliyun.teautil.Common.toMap(resp)));
    }

    /**
     * 删除短信签名
     * @param client 调用AliyunSmsBase.createClient()创建
     * @param signName 短信签名。必须是本账号已申请的短信签名。
     * @throws Exception
     */
    public static void deleteSmsSign(com.aliyun.dysmsapi20170525.Client client, String signName) throws Exception {
        DeleteSmsSignRequest req = new DeleteSmsSignRequest()
                .setSignName(signName);
        DeleteSmsSignResponse resp = client.deleteSmsSign(req);
        if (!com.aliyun.teautil.Common.equalString(resp.body.code, "OK")) {
            throw new TeaException(TeaConverter.buildMap(
                    new TeaPair("code", resp.body.code),
                    new TeaPair("message", resp.body.message)
            ));
        }
        System.err.println("删除短信签名：");
        System.err.println(com.aliyun.teautil.Common.toJSONString(com.aliyun.teautil.Common.toMap(resp)));
    }

    /**
     * 修改短信签名
     * @param client 调用AliyunSmsBase.createClient()创建
     * @param signName 签名名称。签名必须符合个人用户签名规范https://help.aliyun.com/document_detail/108076.html?spm=api-workbench.API%20Explorer.0.0.1f6b1e0fdiG0AW
     * @param signSource 签名来源。取值：0：企事业单位的全称或简称。1：工信部备案网站的全称或简称。2：App应用的全称或简称。3：公众号或小程序的全称或简称。4：电商平台店铺名的全称或简称。5：商标名的全称或简称。
     * @param remark 短信签名申请说明。请在申请说明中详细描述您的业务使用场景，申请工信部备案网站的全称或简称请在此处填写域名，长度不超过200个字符。
     * @param fileContents 签名的资质证明文件经base64编码后的字符串。图片不超过2 MB。
     * @param fileSuffix 签名的证明文件格式，支持上传多张图片。当前支持JPG、PNG、GIF或JPEG格式的图片。
     * @throws Exception
     */
    public static void modifySmsSign(com.aliyun.dysmsapi20170525.Client client, String signName, Integer signSource, String remark, String fileContents, String fileSuffix) throws Exception {
        ModifySmsSignRequest.ModifySmsSignRequestSignFileList signFileList = new ModifySmsSignRequest.ModifySmsSignRequestSignFileList();
        signFileList.fileContents = fileContents;
        signFileList.fileSuffix = fileSuffix;
        ModifySmsSignRequest req = new ModifySmsSignRequest()
                .setSignName(signName)
                .setSignSource(signSource)
                .setRemark(remark)
                .setSignFileList(java.util.Arrays.asList(
                        signFileList
                ));
        ModifySmsSignResponse resp = client.modifySmsSign(req);
        if (!com.aliyun.teautil.Common.equalString(resp.body.code, "OK")) {
            throw new TeaException(TeaConverter.buildMap(
                    new TeaPair("code", resp.body.code),
                    new TeaPair("message", resp.body.message)
            ));
        }

        System.err.println("修改短信签名：");
        System.err.println(com.aliyun.teautil.Common.toJSONString(com.aliyun.teautil.Common.toMap(resp)));
    }

    /**
     * 查询短信签名申请状态
     * @param client 调用createClient()创建
     * @param signName 签名名称。
     * 返回示例
    {
    "Message": "OK",
    "RequestId": "CC89A90C-978F-46AC-B80D-54738371E7CA",
    "SignStatus": 1,
    "CreateDate": " 2019-01-08 16:44:13",
    "Code": "OK",
    "SignName": "阿里云",
    "Reason": "文件不能证明信息真实性，请重新上传"
    }
     * @throws Exception
     */
    public static QuerySmsSignResponse querySmsSign(com.aliyun.dysmsapi20170525.Client client, String signName) throws Exception {
        QuerySmsSignRequest req = new QuerySmsSignRequest()
                .setSignName(signName);
        return client.querySmsSign(req);
    }




//   public static void main(String[] args_) throws Exception {
//
//        Client client = AliyunSmsBase.createClient("", "");
//        //签名名称
//        String signName = "**";
//        //签名来源
//        Integer signSource = 0;
//        //短信签名申请说明
//        String remark = "我想申请签名";
//        //签名的资质证明文件(不超过2M的图片转成base64编码)
//        String fileContents = "R0lGODlhHAAmAKIHAKqqqsvLy0hISObm5vf394uLiwAA";
//        //签名的证明文件格式(图片后缀JPG、PNG、GIF或JPEG)
//        String fileSuffix = "jpg";
//        addSmsSign(client, signName, signSource, remark, fileContents, fileSuffix);
//
//        querySmsSign(client, signName);
//
//    }

}
