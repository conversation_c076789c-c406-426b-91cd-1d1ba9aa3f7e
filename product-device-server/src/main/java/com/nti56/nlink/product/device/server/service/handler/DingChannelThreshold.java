package com.nti56.nlink.product.device.server.service.handler;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 类说明：
 *
 * @ClassName DingChannelThreshold
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/6/19 17:54
 * @Version 1.0
 */

public class DingChannelThreshold {

    private static Map<Long, LeakyBucketMessageSender> thresholdMap = new ConcurrentHashMap<>();


    public static LeakyBucketMessageSender getSender(Long channelId){
        return thresholdMap.get(channelId);
    }

    public static void registerSender(Long id, LeakyBucketMessageSender leakyBucketMessageSender){
        thresholdMap.put(id, leakyBucketMessageSender);
    }

    public static void removeSender(Long id){
        thresholdMap.remove(id);
    }
}
