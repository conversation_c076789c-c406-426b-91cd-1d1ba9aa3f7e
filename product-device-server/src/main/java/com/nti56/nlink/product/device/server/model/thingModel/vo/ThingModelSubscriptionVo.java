package com.nti56.nlink.product.device.server.model.thingModel.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 类说明: 物模型服务领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 16:41:31
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ThingModelSubscriptionVo implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Long id;

    private String name;

    private String descript;

    private String properties;

    private String events;

    private Integer eventType;

    private Long callbackId;

    private Long directlyModelId;

    private Integer modelType;

    private Boolean sendOneByOne;

    private Integer outType;

    private Long targetDevice;

    private String targetService;

    private String receivers;

    private Boolean enable;

    private String dataConversionCode;

    private Long thingModelId;

    private String thingModelName;

    private Boolean repeat;

    private String belongModel;
}
