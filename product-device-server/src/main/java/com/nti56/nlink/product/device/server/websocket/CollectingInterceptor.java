package com.nti56.nlink.product.device.server.websocket;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.List;
import java.util.Map;

/**
 * 说明：
 *
 * <AUTHOR>
 * @date 2022/9/22 18:42
 * @Version 1.0
 */
@Slf4j
@Component
public class CollectingInterceptor implements HandshakeInterceptor {

    /**
     * 握手前
     *
     * @param request
     * @param response
     * @param wsHandler
     * @param attributes
     * @return
     * @throws Exception
     */
    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        log.debug("握手开始");
        // 获得请求参数
        Map<String, List<String>> paramMap =  HttpUtil.decodeParams(request.getURI().getQuery(), "utf-8");
        String edgeGatewayId = paramMap.get("edgeGatewayId").get(0);
        String tenantId = paramMap.get("tenantId").get(0);
        String sessionId = paramMap.get("sessionId").get(0);
        if (StrUtil.isNotBlank(edgeGatewayId)) {
            // 放入属性域
            attributes.put("edgeGatewayId", Long.valueOf(edgeGatewayId));
            attributes.put("tenantId", Long.valueOf(tenantId));
            attributes.put("sessionId", sessionId);
            log.info("websocket连接成功，tenantId:{},edgeGatewayId:{}",edgeGatewayId);
            return true;
        }
        log.warn("websocket连接失败");
        return false;
    }

    /**
     * 握手后
     *
     * @param request
     * @param response
     * @param wsHandler
     * @param exception
     */
    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Exception exception) {
        log.info("握手完成");
    }
}
