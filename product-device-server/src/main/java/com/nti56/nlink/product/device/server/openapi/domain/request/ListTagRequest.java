package com.nti56.nlink.product.device.server.openapi.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/12/19 10:48<br/>
 * @since JDK 1.8
 */
@Data
public class ListTagRequest {

    @Schema(description = "key")
    private String tagKey;

    @Schema(description = "value")
    private String tagValue;
}
