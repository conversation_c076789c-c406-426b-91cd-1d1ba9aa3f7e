package com.nti56.nlink.product.device.server.schedule;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ChangeSubjectEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ChangeTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.EdgeGatewayTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Property;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.loader.ConnectorInstanceLoader;
import com.nti56.nlink.product.device.server.model.channel.vo.ChannelVO;
import com.nti56.nlink.product.device.server.service.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/19 16:23<br/>
 * @since JDK 1.8
 */

@Slf4j
@Component
public class DeviceCloudSchedule {

    @Autowired
    private IEdgeGatewayService edgeGatewayService;

    @Autowired
    private IAsyncExecutorService asyncExecutorService;

    @Autowired
    private INotAssignGatewayService notAssignGatewayService;

    @Autowired @Lazy
    private StringRedisTemplate stringRedisTemplate;
    
    @Autowired
    private IDeviceService deviceService;
    
    @Autowired
    private IChangeNoticeService changeNoticeService;
    
    @Autowired
    private IChannelService channelService;

    @Autowired
    private IConnectorService connectorService;

    @Autowired @Lazy
    private ConnectorInstanceLoader connectorInstanceLoader;

    @Autowired
    private ILabelBindRelationService labelBindRelationService;
    
    @Scheduled(fixedDelay = 1000 * 15 , initialDelay = 1000*3)
    public void edgeGatewayStatusNotify(){
        log.debug("edgeGateway-status-trace begin");

        List<EdgeGatewayEntity> edgeGatewayEntityList =  edgeGatewayService.getAllEdgeGateway().getResult();
        if(CollectionUtil.isEmpty(edgeGatewayEntityList)){
            return;
        }
        List<ChangeNoticeEntity> changeNoticeEntityList = changeNoticeService.getChangeNoticeList().getResult();
        Map<Long, List<ChangeNoticeEntity>> changeNoticeEntityMap = changeNoticeEntityList.stream().collect(Collectors.groupingBy(ChangeNoticeEntity::getTenantId));
        Map<Long, List<ConnectorEntity>> connectorEntityMap = connectorService.getAllConnectorEntity().getResult().stream().collect(Collectors.groupingBy(ConnectorEntity::getEdgeGatewayId));
        for(EdgeGatewayEntity edgeGatewayEntity : edgeGatewayEntityList){
            String syncing = stringRedisTemplate.opsForValue().get(
                RedisConstant.SYNCING_PREFIX + edgeGatewayEntity.getTenantId() + "_" + edgeGatewayEntity.getId()
            );
            if(syncing != null && !"".equals(syncing)){
                //网关在同步中，跳过状态改变处理
                log.info("edgeGateway-status-skip {}", edgeGatewayEntity.getId());
                continue;
            }
            Long connectorId = 0L;
            if(edgeGatewayEntity.getType() == EdgeGatewayTypeEnum.CONNECTOR_GATEWAY.getValue()){
                List<ConnectorEntity> connectorEntityList = connectorEntityMap.get(edgeGatewayEntity.getId());
                if(CollectionUtil.isNotEmpty(connectorEntityList)){
                    connectorId = connectorEntityList.get(0).getId();
                }
            }
            asyncExecutorService.edgeGatewayStatusProcess(edgeGatewayEntity,changeNoticeEntityMap,connectorId);
        }
        log.debug("edgeGateway-status-trace end");
    }
    
    @Scheduled(fixedDelay = 1000 * 15 , initialDelay = 1000*3)
    public void deviceStatusNotify(){
        log.debug("device-status-trace begin");
        List<DeviceEntity> deviceEntityList =  deviceService.getAllDeviceRunList().getResult();
        if(CollectionUtil.isEmpty(deviceEntityList)){
            return;
        }
        List<LabelBindRelationEntity> labelBindRelationEntity = labelBindRelationService.allLabelBindRelation();
        // 按设备 ID 分组
        Map<Long, List<LabelBindRelationEntity>> labelBindRelationEntityMap = labelBindRelationEntity.stream().collect(Collectors.groupingBy(LabelBindRelationEntity::getDeviceId));

        Map<Long, List<ConnectorEntity>> connectorEntityMap = connectorService.getAllConnectorEntity().getResult().stream().collect(Collectors.groupingBy(ConnectorEntity::getEdgeGatewayId));
        List<ChangeNoticeEntity> changeNoticeEntityList = changeNoticeService.getChangeNoticeList().getResult();
        Map<Long, List<ChangeNoticeEntity>> changeNoticeEntityMap = changeNoticeEntityList.stream().filter(f-> ChangeSubjectEnum.DEVICE.getValue().equals(f.getChangeSubject()) && ChangeTypeEnum.STATUS.getValue().equals(f.getChangeType())).collect(Collectors.groupingBy(ChangeNoticeEntity::getTenantId));
        Map<Long, List<DeviceEntity>> tenantGroupBy = deviceEntityList.stream().collect(Collectors.groupingBy(DeviceEntity::getTenantId));

        List<EdgeGatewayEntity> edgeGatewayEntityList = edgeGatewayService.getAllEdgeGateway().getResult();
        Map<Long,EdgeGatewayEntity> edgeGatewayEntityMap  = BeanUtilsIntensifier.collection2Map(edgeGatewayEntityList, EdgeGatewayEntity::getId);

        for (Map.Entry<Long, List<DeviceEntity>> tenantEntry : tenantGroupBy.entrySet()) {
            String key = RedisConstant.DEVICE_STATUS + tenantEntry.getKey();
            String offlineKey = RedisConstant.DEVICE_OFFLINE_TIMES + tenantEntry.getKey();
            Map<Object,Object> deviceStatusMap = stringRedisTemplate.opsForHash().entries(key);
            Map<Object,Object> deviceOfflineTimesMap = stringRedisTemplate.opsForHash().entries(offlineKey);
            List<ChangeNoticeEntity> noticeEntityList = changeNoticeEntityMap.get(tenantEntry.getKey());
            if(CollectionUtil.isEmpty(noticeEntityList)){
                noticeEntityList = new ArrayList<>();
            }
            Map<Long, List<DeviceEntity>> edgeGatewayGroupBy = tenantEntry.getValue().stream().collect(Collectors.groupingBy(DeviceEntity::getEdgeGatewayId));
            for (Map.Entry<Long, List<DeviceEntity>> edgeGatewayEntry : edgeGatewayGroupBy.entrySet()) {
                Long connectorId = 0L;
                List<ConnectorEntity> connectorEntityList = connectorEntityMap.get(edgeGatewayEntry.getKey());
                if(CollectionUtil.isNotEmpty(connectorEntityList)){
                    connectorId = connectorEntityList.get(0).getId();
                }
                Boolean connect = false;
                if(connectorId!=0L){
                    Result<Boolean> connectStatusResult = connectorInstanceLoader.getConnectStatus(connectorId);
                    if(connectStatusResult.getSignal()){
                        connect = connectStatusResult.getResult();
                    }
                }else {
                    Result<Boolean> online = edgeGatewayService.edgeGatewayOnline(tenantEntry.getKey(),edgeGatewayEntry.getKey());
                    connect = online.getResult();
                }
                String channelStatusMapStr = stringRedisTemplate.opsForValue().get(RedisConstant.EDGE_GATEWAY_CHANNEL_STATUS_PREFIX + tenantEntry.getKey() + ":" + edgeGatewayEntry.getKey());
                Map<String,Boolean> channelStatusMap = new HashMap<>();
                if(!StringUtils.isEmpty(channelStatusMapStr)){
                    channelStatusMap = JSONObject.parseObject(channelStatusMapStr, Map.class);
                }
                EdgeGatewayEntity edgeGatewayEntity = edgeGatewayEntityMap.get(edgeGatewayEntry.getKey());
                for(DeviceEntity deviceEntity : edgeGatewayEntry.getValue()){
                    asyncExecutorService.deviceStatusProcess(deviceEntity,noticeEntityList,connect,channelStatusMap,deviceStatusMap,deviceOfflineTimesMap,tenantEntry.getKey().toString(),labelBindRelationEntityMap,edgeGatewayEntity.getType());
                }
            }
        }
        log.debug("device-status-trace end");
    }
    
    @Scheduled(fixedDelay = 1000 * 15 , initialDelay = 1000*3)
    public void channelStatusNotify(){
        log.debug("channel-status-trace begin");
        List<EdgeGatewayEntity> edgeGatewayEntityList =  edgeGatewayService.getAllEdgeGateway().getResult();
        if(CollectionUtil.isEmpty(edgeGatewayEntityList)){
            log.error("edgeGatewayEntityList is empty");
            return;
        }
        Result<List<ChannelVO>> channelListResult = channelService.listAllChannel();
        if(!channelListResult.getSignal()){
            log.error("channelListResult is empty");
            return;
        }
        Map<Long, List<ChannelVO>> groupBy = channelListResult.getResult().stream().filter(e->e.getEdgeGatewayId()!=null).collect(Collectors.groupingBy(ChannelVO::getEdgeGatewayId));
        for (Map.Entry<Long, List<ChannelVO>> entry : groupBy.entrySet()) {
            Long tenantId = entry.getValue().get(0).getTenantId();
            String key = RedisConstant.EDGE_CHANNEL_STATUS_CACHE_PREFIX + tenantId + ":" + entry.getKey();
            String reportChannelStatusMapStr = stringRedisTemplate.opsForValue().get(RedisConstant.EDGE_GATEWAY_CHANNEL_STATUS_PREFIX + tenantId + ":" + entry.getKey());
            Map<String,Boolean> reportChannelStatusMap = new HashMap<>();
            if(!StringUtils.isEmpty(reportChannelStatusMapStr)){
                reportChannelStatusMap = JSONObject.parseObject(reportChannelStatusMapStr, Map.class);
            }
            Map<Object,Object> channelStatusMap = stringRedisTemplate.opsForHash().entries(key);
            for(ChannelVO channelVO : entry.getValue()) {
                asyncExecutorService.channelStatusProcess(channelVO,key,reportChannelStatusMap,channelStatusMap);
            }
        }
        log.debug("channel-status end");
    }
    

    @Scheduled(cron="0 58 23 * * ?")
    public void clearNotAssignGateway(){
        notAssignGatewayService.clearNotAssignGateway();
    }
}
