package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.LabelGroupEntity;
import com.nti56.nlink.product.device.server.model.LabelGroupDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-18 17:43:46
 * @since JDK 1.8
 */
public interface LabelGroupMapper extends CommonMapper<LabelGroupEntity> {

    List<LabelGroupDto> selectLabelGroupList(@Param("tenantId") Long tenantId, @Param("name") String name ,@Param("channelId") Long channelId,@Param("getAll") Boolean getAll);

    @Select("SELECT g.*,c.name channel_name,e.name edge_gateway_name " +
            "FROM label_group g " +
                "LEFT JOIN channel c ON g.channel_id = c.id " +
                "LEFT JOIN edge_gateway e ON  c.edge_gateway_id = e.id  " +
            "WHERE  " +
                "e.deleted = 0 and c.deleted = 0 and g.deleted = 0 "+
                "and e.tenant_id = #{tenantIsolation.tenantId} "+
                "and e.id = #{edgeGatewayId}")
    List<LabelGroupDto> listLabelGroupByEdgeGateway(@Param("edgeGatewayId") Long edgeGatewayId,@Param("tenantIsolation") TenantIsolation tenantIsolation);

    @Select("SELECT * FROM label_group WHERE id = #{id} AND tenant_id = #{tenantId} AND deleted = 0")
    LabelGroupEntity getById(@Param("tenantId") Long tenantId, @Param("id") Long id);

    @Select("SELECT id FROM label_group WHERE channel_id = #{channelId} AND tenant_id = #{tenantId} AND deleted = 0")
    List<Long> listIdByChannlId(@Param("tenantId") Long tenantId, @Param("channelId") Long channelId);

    @Update({
            "<script>",
            "<foreach collection='labelGroups' item='labelGroup' open='' separator=';' close=';'>",
            "UPDATE label_group SET name = #{labelGroup.name} WHERE tenant_id = #{tenantId} AND id = #{labelGroup.id}",
            "</foreach>",
            "</script>"
    })
    Integer batchUpdate(@Param("labelGroups") List<LabelGroupEntity> labelGroups,@Param("tenantId") Long tenantId);

    void moveLabelGroup(@Param("newPrefix")String newPrefix,@Param("oldPrefix") String oldPrefix,@Param("targetChannelId") Long targetChannelId,@Param("likeStr") String likeStr,@Param("sourceChannelId") Long sourceChannelId);

    List<LabelGroupEntity> listCopyLabelGroup(@Param("likeStr") String likeStr,@Param("sourceChannelId") Long sourceChannelId);

    LabelGroupEntity getByLabelId(@Param("labelId") Long labelId);

    void deleteByEdgeGatewayId(Long edgeGatewayId);

    void physicalDeleteByEdgeGatewayId(@Param("edgeGatewayId")Long edgeGatewayId);
}
