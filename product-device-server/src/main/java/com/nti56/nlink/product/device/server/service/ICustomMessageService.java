package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.product.device.server.entity.CustomMessageEntity;
import com.nti56.nlink.product.device.server.model.custom.CustomMessageDto;
import com.nti56.nlink.product.device.server.model.custom.CustomMessageFullBo;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.util.Result;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.nlink.common.mybatis.IBaseService;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <p>
 * 自定义消息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-11-18 11:43:28
 * @since JDK 1.8
 */
public interface ICustomMessageService extends IBaseService<CustomMessageEntity> {

    Result<CustomMessageFullBo> getById(TenantIsolation tenant, Long id);

    Result<Long> create(TenantIsolation tenant, CustomMessageDto dto);

    Result<Void> update(TenantIsolation tenant, CustomMessageDto dto);

    Result<Page<CustomMessageEntity>> getPage(TenantIsolation tenant, @Nullable CustomMessageEntity entity, Page<CustomMessageEntity> page);

    Result<List<CustomMessageEntity>> list(TenantIsolation tenant, CustomMessageEntity entity);

    Result<Void> deleteById(TenantIsolation tenant, Long id);


}
