package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ChangeSubjectEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ChangeTypeEnum;
import com.nti56.nlink.product.device.server.entity.ChangeNoticeEntity;
import com.nti56.nlink.product.device.server.model.changeNotice.dto.CreateChangeNoticeDTO;
import com.nti56.nlink.product.device.server.model.changeNotice.dto.EditChangeNoticeDTO;
import com.nti56.nlink.product.device.server.model.changeNotice.dto.QueryChangeNoticeDTO;
import com.nti56.nlink.product.device.server.model.changeNotice.vo.ChangeNoticeVO;

import java.util.List;


/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-17 14:11:44
 * @since JDK 1.8
 */
public interface IChangeNoticeService extends IBaseService<ChangeNoticeEntity> {

    Result<List<ChangeNoticeVO>> listChangeNotice(QueryChangeNoticeDTO queryChangeNoticeDTO, TenantIsolation tenantIsolation);

    Result createChangeNotice(CreateChangeNoticeDTO createChangeNoticeDTO, TenantIsolation tenantIsolation);

    Result deleteChangeNotice(Long id, TenantIsolation tenantIsolation);

    Result editChangeNotice(EditChangeNoticeDTO editChangeNoticeDTO, TenantIsolation tenantIsolation);

    Result<List<ChangeNoticeEntity>> getChangeNoticeList();

    public <T> void changeNotice(Long tenantId, T noticeMsg, ChangeTypeEnum changeType, ChangeSubjectEnum changeSubject);
}
