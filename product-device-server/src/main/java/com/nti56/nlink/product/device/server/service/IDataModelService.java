package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.DataModelEntity;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <p>
 * 数据模型表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 13:31:16
 * @since JDK 1.8
 */
public interface IDataModelService extends IBaseService<DataModelEntity> {

    Result<DataModelEntity> save(TenantIsolation tenantIsolation, DataModelEntity entity);

    Result<Page<DataModelEntity>> getPage(@Nullable DataModelEntity entity, Page<DataModelEntity> page);

    Result<List<DataModelEntity>> list(TenantIsolation tenantIsolation);

    Result<Integer> update(TenantIsolation tenantIsolation, DataModelEntity entity);

    Result<Integer> deleteById(TenantIsolation tenantIsolation, Long id);

    Result<DataModelEntity> getById(TenantIsolation tenantIsolation, Long id);

    Result<Page<DataModelEntity>> page(TenantIsolation tenantIsolation, String searchStr, Page<DataModelEntity> page);

}
