package com.nti56.nlink.product.device.server.entity.json.element.connector;

import lombok.Data;

@Data
public class MqttConnectorInfo {
    
    private String ip;

    private Integer port;

    private String username;

    private String password;

    private Boolean needAuth;

    private Boolean needTLS;

    private String keyPath;

    private String certPath;

    private Integer keepAlive;

    private Long messageResendInterval;

    private Integer qos;

    private Long redirectId;

    private Boolean testConnect;

    private Long reconnectGapTime;

    private String clientId;

}
