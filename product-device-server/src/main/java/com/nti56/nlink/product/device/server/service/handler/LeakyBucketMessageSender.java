package com.nti56.nlink.product.device.server.service.handler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.product.device.server.constant.AliYunConstant;
import com.nti56.nlink.product.device.server.constant.NotifyConstant;
import com.nti56.nlink.product.device.server.entity.MessageAggregationEntity;
import com.nti56.nlink.product.device.server.enums.NotifyServerLogEnum;
import com.nti56.nlink.product.device.server.service.IMessageAggregationService;
import com.nti56.nlink.product.device.server.service.INotifyServerLogService;
import com.nti56.nlink.product.device.server.util.ListSplitterUtil;
import com.nti56.nlink.product.device.server.util.RSAUtils;
import com.nti56.nlink.product.device.server.util.SpringContextUtil;
import com.nti56.nlink.product.device.server.util.aliyun.DingDingRobot;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Date;
import java.util.Deque;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 类说明：
 *
 * @ClassName MessageSender
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/6/19 14:09
 * @Version 1.0
 */
@Slf4j
public class LeakyBucketMessageSender {

    private final Deque<MessageRequest> requestQueue;
    private final ScheduledExecutorService executorService;
    private long interval;
    private TimeUnit timeUnit;
    private final Long channelId;
    private INotifyServerLogService logService;
    private IMessageAggregationService messageAggregationService;
    private static final String AGGREGATION_TEMPLATE = "%s</br>共折叠%s条信息,登陆OT平台并在日志管理-消息记录中，通过搜索消息ID：%s查看折叠信息。";
    private final ReentrantLock lock;

    public LeakyBucketMessageSender(long interval, TimeUnit timeUnit, Long channelId, JSONObject channelParams) {
        requestQueue = new ArrayDeque<>();
        executorService = Executors.newScheduledThreadPool(Runtime.getRuntime().availableProcessors());
        this.interval = interval;
        this.timeUnit = timeUnit;
        this.channelId = channelId;
        this.lock = new ReentrantLock();
        try {
            logService = SpringContextUtil.getBean(INotifyServerLogService.class);
            messageAggregationService = SpringContextUtil.getBean(IMessageAggregationService.class);
        } catch (Exception e) {
            log.error("钉钉机器人创建客户端失败。channel:" + channelId, e);
        }

    }

    public void startProcessing() {

        executorService.scheduleAtFixedRate(() -> {
            try {
                processNextRequest();
            } catch (Exception e) {
                e.printStackTrace(); // 或者处理异常的其他逻辑
            }
        }, 0, interval, timeUnit);
    }

    public void stopProcessing() {
//        timer.cancel();
        executorService.shutdown();
    }

    public void addRequest(MessageRequest request) {
        try {
            if (lock.tryLock()) {
                requestQueue.offer(request);
            }
        } finally {
            lock.unlock();
        }

    }

    private void processNextRequest() {
        try {
            if (lock.tryLock()) {
                MessageRequest request = requestQueue.pollLast();
                if (request != null) {
                    // 处理请求
                    //                System.out.println("Processing request: " + request + "total msg: " + (requestQueue.size() + 1));
                    try {
                        DingTalkClient dingTalkClient = DingDingRobot.createClient(request.getChannelParams().getString(AliYunConstant.WEBHOOK),
                                RSAUtils.decryptByPrivateKey(request.getChannelParams().getString(AliYunConstant.SECRET), NotifyConstant.PRIVATE_KEY));
                        OapiRobotSendRequest dRequest = null;
                        Long aggregationId = null;
                        boolean isAggregation = false;
                        if (requestQueue.size() > 1) {
                            log.info("====当前合并消息数量：{},sender:{}", requestQueue.size() + 1, this);
                            //走合并聚合
                            isAggregation = true;
                            aggregationId = IdGenerator.generateId();
                            String sendContent = String.format(AGGREGATION_TEMPLATE, request.getContent(), requestQueue.size(), aggregationId);
                            dRequest = DingDingRobot.createMarkdownRequest(sendContent, StrUtil.isBlank(request.getTitle()) ? "新的通知" : request.getTitle(), request.getAtMobiles(), null, false);
                        } else {
                            if (StrUtil.isNotBlank(request.getTitle())) {
                                dRequest = DingDingRobot.createMarkdownRequest(request.getContent(), request.getTitle(), request.getAtMobiles(), null, false);
                            } else {
                                dRequest = DingDingRobot.createTextRequest(request.getContent(), request.getAtMobiles(), null, false);
                            }
                        }

                        OapiRobotSendResponse response = dingTalkClient.execute(dRequest);
                        if (!response.isSuccess()) {
                            log.error("钉钉机器人发送通知失败。templateId:{},content:{},errorMsg:{}", request.getTemplateId(), JSONUtil.toJsonStr(dRequest), response.getErrmsg());
                            if (!Objects.isNull(logService)) {
                                logService.createRequestFailLog(NotifyServerLogEnum.SEND_NOTIFY_FAIL, request.getRequestParam(), response.getErrmsg()
                                        , request.getTenantIsolation(), request.getSubscriptionId());
                            }
                        }
                        if (!Objects.isNull(logService)) {
                            if (isAggregation) {
                                List<MessageAggregationEntity> saveEntityList = new ArrayList<>(requestQueue.size());
                                //保存聚合消息
                                while (requestQueue.size() > 0) {
                                    MessageRequest poll = requestQueue.poll();
                                    MessageAggregationEntity messageAggregationEntity = new MessageAggregationEntity();
                                    messageAggregationEntity.setAggregationId(aggregationId);
                                    messageAggregationEntity.setContent(poll.getContent());
                                    messageAggregationEntity.setTenantId(poll.getTenantIsolation().getTenantId());
                                    messageAggregationEntity.setCreateTime(new Date());
                                    saveEntityList.add(messageAggregationEntity);
                                }
                                List<List<MessageAggregationEntity>> lists = ListSplitterUtil.splitList(saveEntityList, 100);
                                for (List<MessageAggregationEntity> list : lists) {
                                    messageAggregationService.saveBatch(list);
                                }
                                logService.createLog(null, NotifyServerLogEnum.SEND_NOTIFY_SUCCESS, request.getRequestParam(), "", "", request.getTenantIsolation(), aggregationId);
                            } else {
                                logService.createLog(NotifyServerLogEnum.SEND_NOTIFY_SUCCESS, request.getRequestParam(), request.getTenantIsolation());
                            }
                        }
                    } catch (ApiException e) {
                        log.error("钉钉机器人发送通知失败，API调用异常：{}", e.getErrMsg());
                    }
                    requestQueue.clear();
                } else {
                    System.out.println("No requests to process.");
                }
            }
        } catch (Exception e) {
            log.error("send dingding msg error.error reason:{}", e.getMessage());
            e.printStackTrace();
        } finally {
            lock.unlock();
        }

    }
}
