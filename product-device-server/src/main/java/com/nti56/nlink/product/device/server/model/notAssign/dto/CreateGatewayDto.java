package com.nti56.nlink.product.device.server.model.notAssign.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotBlank;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: lql
 * @Date: 2023/04/21/9:53
 * @Description:
 */
@Data
public class CreateGatewayDto{


    @Schema(description = "名称")
    @NotBlank(message = "网关名称不能为空")
    @Length(max = 32,message = "网关名称不能超过32个字符")
    private String name;

    /**
     * 上报imei号
     */
    @Schema(description = "上报imei号")
    private String imei;

    /**
     * 上报管理端口
     */
    @Schema(description = "上报管理端口")
    @NotNull(message = "上报管理端口不能为空")
    private Integer adminPort;

    /**
     * 上报ip
     */
    @Schema(description = "上报ip/host")
    private String host;

    /**
     * 上报端口
     */
    @Schema(description = "上报端口")
    private Integer port;

    /**
     * 分配租户id
     */
    @Schema(description = "分配租户id")
    @NotNull(message = "分配租户id不能为空")
    private Long tenantId;
}
