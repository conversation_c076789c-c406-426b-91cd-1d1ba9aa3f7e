package com.nti56.nlink.product.device.server.model.channel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 13:06<br/>
 * @since JDK 1.8
 */
@Data
@ToString
@Schema(description = "通道参数dto")
public class ProofreadChannelParamDTO implements Serializable {

    private Long id;

    @Schema(description = "参数名称")
    private String name;

    @Schema(description = "通道参数值")
    private String value;

}
