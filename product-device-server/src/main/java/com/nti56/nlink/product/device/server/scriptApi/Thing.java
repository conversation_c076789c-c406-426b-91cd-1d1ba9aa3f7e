package com.nti56.nlink.product.device.server.scriptApi;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.alibaba.fastjson.JSONObject;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.QueryApi;
import com.influxdb.query.FluxTable;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.PropertyMetadataItem;
import com.nti56.nlink.product.device.client.model.dto.json.scriptApi.Service;
import com.nti56.nlink.product.device.server.config.InfluxdbConfig;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceDataResource;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.entity.json.InputValueField;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewaySpiProxy;
import com.nti56.nlink.product.device.server.model.ConnectResult;
import com.nti56.nlink.product.device.server.model.edgegateway.WriteParam;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListTagRequest;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.service.impl.DeviceServiceImpl;
import com.nti56.nlink.product.device.server.service.impl.DeviceTwinFromRedisServiceImpl;
import com.nti56.nlink.product.device.server.serviceEngine.BaseService;
import com.nti56.nlink.product.device.server.util.ApplicationContextUtil;
import com.nti56.nlink.product.device.server.verticle.post.processor.label.Mapping2DeviceHandler;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import io.vertx.core.Context;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;


/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName Thing
 * @date 2022/4/15 9:05
 * @Version 1.0
 */
@Slf4j
public class Thing implements ThingSpi {

    @Getter
    private Device device;

    @Getter
    private String resourceId;

    @Getter
    private Long id;

    private IEdgeGatewaySpiProxy edgeGatewaySpiProxy;

    private DeviceDataResource deviceDataResource;

    private EngineeringFactory engineeringFactory;

//  todo 删掉
    private Mapping2DeviceHandler mapping2DeviceHandler;

    private String serviceName;

    private InfluxDBClient influxDBClient;

    @Getter
    private String influxBucket;

    @Getter
    private Long tenantId;

    private IDeviceService deviceService;

    Thing(Device device,String serviceName){
        this.device = device;
        this.tenantId = device.getTenantId();
        this.id = device.getId();
        this.resourceId = device.getResourceId();
        this.edgeGatewaySpiProxy = ApplicationContextUtil.getBean("edgeGatewaySpiProxy", IEdgeGatewaySpiProxy.class);
        this.deviceDataResource = ApplicationContextUtil.getBean("deviceTwinFromRedisServiceImpl", DeviceTwinFromRedisServiceImpl.class);
        this.engineeringFactory = ApplicationContextUtil.getBean("engineeringFactory", EngineeringFactory.class);
        this.mapping2DeviceHandler = ApplicationContextUtil.getBean("mapping2DeviceHandler", Mapping2DeviceHandler.class);
        this.influxDBClient = ApplicationContextUtil.getBean("influxDBClient", InfluxDBClient.class);
        this.influxBucket = ApplicationContextUtil.getBean("influxdbConfig", InfluxdbConfig.class).getBucket();
        this.serviceName = serviceName;
        this.deviceService = ApplicationContextUtil.getBean("deviceServiceImpl", DeviceServiceImpl.class);
        long start = System.currentTimeMillis();
        device.initDevice(this.deviceDataResource);
        long spend = System.currentTimeMillis() - start;
        if(spend>150){
            log.info(">>>>>1 spend:{}ms, deviceID:{} ",spend,device.getId());
        }

    }

    /**
     * 获取属性列表
     * @return
     */
    @Override
    public Map<String, Object> readProperties() {
        return SpiUtil.getTrueProperties(device);
    }

    @Override
    public Map<String, BaseService> getService() {
        return SpiUtil.services2BaseServices(device);
    }

    @Override
    public BaseService getServiceByName(String serviceName) {
        Service service = device.getServices().get(serviceName);
        Result<BaseService> result = SpiUtil.service2BaseService(service,device.getId());
        if (result.getSignal()) {
            return result.getResult();
        }
        return null;
    }

    @Override
    public List<String> getServiceName() {
        List<String> names = BeanUtilsIntensifier.getIds(device.getServices(), Service::getServiceName);
        return names;
    }

    @Override
    public Object readProperty(String propertyName) {

        Map<String, Object> dataMap = deviceDataResource.getById(device.getId());
        if (dataMap.containsKey(propertyName)) {
            Object data = dataMap.get(propertyName);
            List<String> list = Arrays.asList("id", "name", "status", "description");
            if (list.contains(propertyName)) {
                return data;
            }
            Result<Object> result = SpiUtil.checkAndGetPropertyValue(device.getPropertyMetadata().get(propertyName), data);
            if (result.getSignal()) {
                log.debug("将设备：{} 的属性：{}，读取: {}",device.getId() ,propertyName, result.getResult());
                return result.getResult();
            }
        }
        return null;
    }

    @Override
    public Object spiReadProperty(String propertyName) {
        Map<String, PropertyMetadataItem> propertyMetadata = device.getPropertyMetadata();
        if (!propertyMetadata.containsKey(propertyName)) {
            log.warn("属性不存在：{}",propertyName);
            return null;
        }
        PropertyMetadataItem propertyMetadataItem = propertyMetadata.get(propertyName);

        if (Optional.ofNullable(propertyMetadataItem.getLabelId()).isPresent()) {
            Result<Object> result = SpiUtil.readProperty(
                edgeGatewaySpiProxy,
                propertyMetadataItem,
                this.device.getChannelRuntimeInfoFieldMap().get(propertyMetadataItem.getChannelId()),
                device.getEdgeGatewayId(), 
                device.getTenantId()
            );
            if(!result.getSignal()){
                log.error("spiReadProperty失败" + result.getMessage());
                return null;
            }
            Object value = result.getResult();
            return value;
        }
        return null;
    }

    
    @Override
    public Boolean writeTwin(String propertyName, Object value) {
        Map<String, PropertyMetadataItem> propertyMetadata = device.getPropertyMetadata();
        if (!propertyMetadata.containsKey(propertyName)) {
            log.warn("属性不存在：{}",propertyName);
            return false;
        }
        PropertyMetadataItem propertyMetadataItem = propertyMetadata.get(propertyName);
        // if (propertyMetadataItem.getReadOnly()) {
        //     log.warn("设值失败，属性只读：{}",propertyName);
        //     return false;
        // }

        // 校验
        Result<Object> result = SpiUtil.checkAndGetPropertyValue(propertyMetadataItem, value);
        if (result.getSignal()) {
            value = result.getResult();
        }else {
            log.warn(result.getMessage());
            return false;
        }
        
        UpData upData = buildUpData(device.getId(), propertyMetadataItem, value);
        mapping2DeviceHandler.processWriteTwin(device.getEdgeGatewayId(), device.getId(), device.getTenantId(), upData);
        return true;
    }
    

    @Override
    public Boolean writeProperty(String propertyName, Object value) {
        log.debug("writeProperty public propertyName:{},value:{}",propertyName,value);
        return writeProperty(propertyName, value, false);
    }
    
    @Override
    public Boolean writePropertyAndTwin(String propertyName, Object value) {
        log.debug("writePropertyAndTwin propertyName:{},value:{}",propertyName,value);
        return writeProperty(propertyName, value, true);
    }

    /**
     * 判断权限，下放设备和修改内存要区分
     */
    private Boolean writeProperty(String propertyName, Object value, Boolean writeTwin) {
        log.debug("将设备：{} 的属性：{}，设值为：{}",device.getId() ,propertyName,value);
        Map<String, PropertyMetadataItem> propertyMetadata = device.getPropertyMetadata();
        if (!propertyMetadata.containsKey(propertyName)) {
            log.warn("属性不存在：{}",propertyName);
            return false;
        }
        PropertyMetadataItem propertyMetadataItem = propertyMetadata.get(propertyName);
        if (propertyMetadataItem.getReadOnly()) {
            log.warn("设值失败，属性只读：{}",propertyName);
            return false;
        }
        Result<Object> result = SpiUtil.checkAndGetPropertyValue(propertyMetadataItem, value);
        if (result.getSignal()) {
            value = result.getResult();
        }else {
            log.warn(result.getMessage());
            return false;
        }
        // 绑定标签，下放
        if (Optional.ofNullable(propertyMetadataItem.getLabelId()).isPresent()) {
            Boolean success = SpiUtil.writeProperty(edgeGatewaySpiProxy,propertyMetadataItem,
                    this.device.getChannelRuntimeInfoFieldMap().get(propertyMetadataItem.getChannelId()),
                    value ,device.getEdgeGatewayId(), device.getTenantId());
            if(writeTwin && success != null && success){
                UpData upData = buildUpData(device.getId(), propertyMetadataItem, value);
                mapping2DeviceHandler.processWriteTwin(device.getEdgeGatewayId(), device.getId(), device.getTenantId(), upData);
            }
            return success;
        }
        // 未绑定标签，修改孪生
        UpData upData = buildUpData(device.getId(), propertyMetadataItem, value);
        mapping2DeviceHandler.processWriteTwin(device.getEdgeGatewayId(), device.getId(), device.getTenantId(), upData,device.getDeviceTwin());
        return true;
    }
    
    public static UpData buildUpData(Long deviceId, PropertyMetadataItem propertyMetadataItem, Object value){
        UpData upData = new UpData();
        upData.setTimestamp(System.currentTimeMillis());
        List<UpProp> propList = new ArrayList<>();
        UpProp upProp = new UpProp();
        upProp.setProperty(propertyMetadataItem.getProperty());
        upProp.setDeviceId(deviceId);
        upProp.setDataType(propertyMetadataItem.getDataType().getType());
        upProp.setIsArray(propertyMetadataItem.getDataType().getIsArray());
        upProp.setLength(propertyMetadataItem.getLength()==null?0:propertyMetadataItem.getLength());
        upProp.setValue(value);
        propList.add(upProp);
        upData.setProp(propList);
        return upData;
    }

    @Override
    public Map<String,Boolean> writeProperties(Map<String,Object> properties) {
        log.info("设备：{} 批量设置属性：{}",device.getId() , properties);
        Map<String,Boolean> resultMap = new HashMap<>();
        Map<String, PropertyMetadataItem> propertyMetadata = device.getPropertyMetadata();
        Iterator<Map.Entry<String, Object>> iterator = properties.entrySet().iterator();
        List<WriteParam> writeParams = new ArrayList<>();
        Map<String,Object> deviceTwin = MapUtil.createMap(HashMap.class);
        while (iterator.hasNext()) {
            Map.Entry<String, Object> next = iterator.next();
            if (!propertyMetadata.containsKey(next.getKey())) {
                log.warn("设备：{}，设置值失败，属性不存在：{}", device.getId() , next.getKey());
                resultMap.put(next.getKey(),false);
                continue;
            }
            PropertyMetadataItem propertyMetadataItem = propertyMetadata.get(next.getKey());
            if (propertyMetadataItem.getReadOnly()) {
                log.warn("设值失败，属性只读：{}", next.getKey());
                resultMap.put(next.getKey(),false);
                continue;
            }
            Object value = next.getValue();
            Result<Object> result = SpiUtil.checkAndGetPropertyValue(propertyMetadataItem, value);
            if (result.getSignal()) {
                value = result.getResult();
            }else {
                log.warn("设备：{}，设置失败：{}",device.getId(),result.getMessage());
                resultMap.put(next.getKey(),false);
                continue;
            }
            //下放设备
            if (Optional.ofNullable(propertyMetadataItem.getLabelId()).isPresent()) {
                WriteParam writeParam = new WriteParam(propertyMetadataItem.toAccess(this.device.getChannelRuntimeInfoFieldMap().get(propertyMetadataItem.getChannelId())), value);
                writeParams.add(writeParam);
            }else {
                deviceTwin.put(propertyMetadataItem.getProperty(), value);
            }
        }

        Result<List<ConnectResult>> listResult = edgeGatewaySpiProxy.multiWrite(device.getEdgeGatewayId(), device.getTenantId(), writeParams);
        if (!listResult.getSignal()) {
            writeParams.forEach(writeParam -> {
                AccessElm access = writeParam.getAccess();
                resultMap.put(access.getProperty(),false);
            });
        }else {
            listResult.getResult().forEach(connectResult -> {
                if (!connectResult.getOk()) {
                    resultMap.put(connectResult.getProperty(),false);
                }
            });
        }
        return resultMap;
    }

    @Override
    public R callService(String serviceName, Map<String,Object> inputData) {
        DeviceServiceLogEntity build = DeviceServiceLogEntity.builder()
                .deviceId(this.device.getId())
                .deviceName(this.device.getName())
                .serviceName(serviceName)
                .inputData(InputValueField.valueOf(inputData))
                .callType(4)
                .creator(this.serviceName)
                .build();
        R r = callService(serviceName, inputData, build);
        return r;
    }

    @Override
    public R callService(BaseService baseService, Map<String, Object> inputData) {
        if (device.getServices().containsKey(baseService.getServiceName())) {
            return callService(baseService.getServiceName(),inputData);
        }
        String deviceName = baseService.getThing() == null?"":baseService.getThing().getDeviceName();
        return R.error("物服务未定义，服务名:" + serviceName);
    }

    @Override
    public R callService(String serviceName, Map<String, Object> inputData, DeviceServiceLogEntity logEntity) {
        logEntity.setTenantId(this.device.getTenantId());
        log.info("服务调用，服务名：{}，入参：{}",serviceName, JSONObject.toJSONString(inputData));
        if (!Optional.ofNullable(serviceName).isPresent()) {
            SpiUtil.dealLog(this.deviceDataResource,logEntity,ServiceCodeEnum.CODE_PARAM_ERROR.getMessage(), false);
            this.serviceName = null;
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        // if (Optional.ofNullable(this.serviceName).isPresent() && serviceName.equals(this.serviceName)) {
        //     log.warn("禁止服务递归调用：{}",serviceName);
        //     SpiUtil.dealLog(this.deviceDataResource,logEntity,"服务调用异常", false);
        //     this.serviceName = null;
        //     return R.error("服务调用异常,禁止服务递归调用：" + serviceName);
        // }
        Service service = device.getServices().get(serviceName);
        if (Optional.ofNullable(service).isPresent()) {
            Result<BaseService> baseServiceResult = SpiUtil.service2BaseService(service,this.device.getId(), logEntity);
            if (!baseServiceResult.getSignal()) {
                SpiUtil.dealLog(this.deviceDataResource,logEntity,baseServiceResult.getMessage(), false);
                log.warn("服务调用，报错：{},设备：{},服务：{}",baseServiceResult.getMessage(),this.device.getName(),serviceName);
                this.serviceName = null;
                return R.error(baseServiceResult.getMessage());
            }
            this.serviceName = serviceName;
            Result<Void> result = baseServiceResult.getResult().initBaseService(inputData,this,this.engineeringFactory.getEngineering(device.getTenantId()), this.deviceDataResource, device.getTenantId());
            if (!result.getSignal()) {
                SpiUtil.dealLog(this.deviceDataResource,logEntity,result.getMessage(), false);
                log.warn("服务调用,报错：{},设备：{},服务：{}",result.getMessage(),this.device.getName(),serviceName);
                this.serviceName = null;
                return R.result(result);
            }
            R r = baseServiceResult.getResult().callService();
            this.serviceName = null;
            return r;
        }
        SpiUtil.dealLog(this.deviceDataResource,logEntity,ServiceCodeEnum.THING_SERVICE_UNDEFINED.getMessage(), false);
        log.warn("服务不存在：{}",serviceName);
        this.serviceName = null;
        return R.error("物服务未定义，服务名:" + serviceName);
    }


    @Override
    public R callService(String serviceName, Map<String, Object> inputData, DeviceServiceLogEntity logEntity,Boolean isRecordLog) {
        logEntity.setTenantId(this.device.getTenantId());
        logEntity.setIsRecordLog(isRecordLog);
        log.debug("来源订阅服务调用，服务名：{}，入参：{}",serviceName, JSONObject.toJSONString(inputData));
        if (!Optional.ofNullable(serviceName).isPresent()) {
            if(!isRecordLog){
                log.debug("subscribe device service add log and logEntity the one place is", JSONUtil.toJsonStr(logEntity));
            }else {
                SpiUtil.dealLog(this.deviceDataResource,logEntity,ServiceCodeEnum.CODE_PARAM_ERROR.getMessage(), false);
            }
            this.serviceName = null;
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Service service = device.getServices().get(serviceName);
        if (Optional.ofNullable(service).isPresent()) {
            Result<BaseService> baseServiceResult = SpiUtil.service2BaseService(service,this.device.getId(), logEntity);
            if (!baseServiceResult.getSignal()) {
                if(!isRecordLog) {
                    log.debug("subscribe device service add log and logEntity the two place is", JSONUtil.toJsonStr(logEntity));
                }else {
                    SpiUtil.dealLog(this.deviceDataResource,logEntity,baseServiceResult.getMessage(), false);
                }
                log.warn("服务调用，报错：{},设备：{},服务：{}",baseServiceResult.getMessage(),this.device.getName(),serviceName);
                this.serviceName = null;
                return R.error(baseServiceResult.getMessage());
            }
            this.serviceName = serviceName;
            Result<Void> result = baseServiceResult.getResult().initBaseService(inputData,this,this.engineeringFactory.getEngineering(device.getTenantId()), this.deviceDataResource, device.getTenantId());
            if (!result.getSignal()) {
                if(!isRecordLog) {
                    log.debug("subscribe device service add log and logEntity the three place is", JSONUtil.toJsonStr(logEntity));
                }else {
                    SpiUtil.dealLog(this.deviceDataResource,logEntity,result.getMessage(), false);
                }
                log.warn("服务调用,报错：{},设备：{},服务：{}",result.getMessage(),this.device.getName(),serviceName);
                this.serviceName = null;
                return R.result(result);
            }
            R r = baseServiceResult.getResult().callService();
            this.serviceName = null;
            return r;
        }
        if(!isRecordLog) {
            log.debug("subscribe device service add log and logEntity the four place is", JSONUtil.toJsonStr(logEntity));
        }else {
            SpiUtil.dealLog(this.deviceDataResource,logEntity,ServiceCodeEnum.THING_SERVICE_UNDEFINED.getMessage(), false);
        }
        log.warn("服务不存在：{}",serviceName);
        this.serviceName = null;
        return R.error("物服务未定义，服务名:" + serviceName);
    }

    @Override
    public List queryData(String query) {
        QueryApi queryApi = influxDBClient.getQueryApi();
        log.debug("queryData: {}", query);
        long begin = System.currentTimeMillis();
        List<FluxTable> table = queryApi.query(query);
        long end = System.currentTimeMillis();
        log.debug("queryDataCost: {}ms", end-begin);
        return table;
    }

    @Override
    public void sleep(int seconds) {
        sleep(seconds, "s");
    }

    @Override
    public void sleep(long times, String unit) {
        if (Objects.equals(unit, "s")) {
            times = times * 1000;
        }else if (Objects.equals(unit, "m")) {
            times = times * 60 * 1000;
        }else if (Objects.equals(unit, "h")){
            times = times * 60 * 60 * 1000;
        }
        try {
            Thread.sleep(times);
        } catch (InterruptedException e) {
        }
    }

    @Override
    public void initData() {
        device.getDeviceTwin().initData();
    }

    @Override
    public String getDeviceName() {
        return device.getName();
    }

    @Override
    public List<DeviceEntity> listDeviceByTag(String tagList) {
        try {
            log.info("=========listDeviceByTag param is {}",tagList);
            List<ListTagRequest> requestList = JSONUtil.toList(tagList,ListTagRequest.class);
            Result<List<DeviceEntity>> result = deviceService.listDeviceByTag(tenantId,requestList);
            if(!result.getSignal()){
                throw new BizException(result.getMessage());
            }
            return result.getResult();
        }catch (Exception e){
            throw new BizException(e.getMessage());
        }
    }

}
