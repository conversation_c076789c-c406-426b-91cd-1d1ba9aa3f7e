package com.nti56.nlink.product.device.server.model.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/22 10:05<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "修改产品dto")
public class EditProductInheritDTO {

    private Long id;

    @Schema(description = "新增物模型id")
    private List<Long> addThingModelIds;

    @Schema(description = "删除物模型id")
    private List<Long> deleteThingModelIds;

}
