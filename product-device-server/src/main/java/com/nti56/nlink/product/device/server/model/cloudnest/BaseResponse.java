package com.nti56.nlink.product.device.server.model.cloudnest;

import java.io.Serializable;

public class BaseResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    public BaseResponse() {
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof BaseResponse)) {
            return false;
        } else {
            BaseResponse other = (BaseResponse) o;
            return other.canEqual(this);
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof BaseResponse;
    }


    public String toString() {
        return "BaseResponse()";
    }
}