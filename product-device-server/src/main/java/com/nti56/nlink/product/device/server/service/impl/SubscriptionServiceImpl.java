package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.send.NotifyDTO;
import com.nti56.nlink.product.device.client.model.dto.send.SendNotifyDTO;
import com.nti56.nlink.product.device.server.annotation.AuditLog;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.channel.Channel;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceDataResource;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceTwin;
import com.nti56.nlink.product.device.server.domain.thing.devicemodel.DeviceModel;
import com.nti56.nlink.product.device.server.domain.thing.edgegateway.EdgeGateway;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.StatusEventEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SubscriptionEventTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SubscriptionFromEnum;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.EventData;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.FaultData;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModel;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import com.nti56.nlink.product.device.server.entity.SubscriptionEntity;
import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
import com.nti56.nlink.product.device.server.enums.ActionEnum;
import com.nti56.nlink.product.device.server.enums.AuditTargetEnum;
import com.nti56.nlink.product.device.server.enums.RedirectRefEnum;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.DeviceModelInheritMapper;
import com.nti56.nlink.product.device.server.mapper.SubscriptionMapper;
import com.nti56.nlink.product.device.server.mapper.ThingModelInheritMapper;
import com.nti56.nlink.product.device.server.mapper.ThingModelMapper;
import com.nti56.nlink.product.device.server.model.channel.vo.ChannelVO;
import com.nti56.nlink.product.device.server.model.device.dto.FaultEventInstance;
import com.nti56.nlink.product.device.server.model.device.dto.NoChangeEventInstance;
import com.nti56.nlink.product.device.server.model.device.dto.TriggerEventInstance;
import com.nti56.nlink.product.device.server.model.redirect.RedirectReferenceDto;
import com.nti56.nlink.product.device.server.service.IChannelService;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;
import com.nti56.nlink.product.device.server.service.IInstanceRedirectService;
import com.nti56.nlink.product.device.server.service.ISubscriptionService;
import com.nti56.nlink.product.device.server.service.handler.SendNotifyService;
import com.nti56.nlink.product.device.server.serviceEngine.DataConversionService;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;

/**
 * <p>
 * 订阅模块 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-09-22 15:17:38
 * @since JDK 1.8
 */
@Service
@Slf4j
public class SubscriptionServiceImpl extends BaseServiceImpl<SubscriptionMapper, SubscriptionEntity> implements ISubscriptionService {

    @Autowired
    SubscriptionMapper mapper;

    @Autowired @Lazy
    IDeviceService deviceService;

    @Autowired
    ThingModelMapper thingModelMapper;

    @Autowired
    ThingModelInheritMapper thingModelInheritMapper;

    @Autowired
    DeviceModelInheritMapper deviceModelInheritMapper;

    @Autowired
    CommonFetcherFactory commonFetcherFactory;

    @Autowired
    DeviceDataResource deviceDataResource;

    /*@GrpcClient("common-server")
    private MqttCallbackGrpc.MqttCallbackStub mqttCallbackStub;*/

    @Autowired
    private IInstanceRedirectService instanceRedirectService;
    
    @Autowired 
    @Lazy
    private IEdgeGatewayService edgeGatewayService;
    
    @Autowired 
    @Lazy
    private IChannelService channelService;
    
    @Autowired
    private DataConversionService dataConversionService;
    
    @Autowired
    @Lazy
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.CREATE,target = AuditTargetEnum.SUBSCRIBE,details = "订阅创建")
    public Result<SubscriptionEntity> save(TenantIsolation tenantIsolation, SubscriptionEntity entity) {
        entity.setTenantId(tenantIsolation.getTenantId());
        mapper.insert(entity);
        Result<Subscription> result = Subscription.checkInfo(entity.getDirectlyModelId(), null, entity);
        if (!result.getSignal()) {
            throw new BizException(result.getMessage());
        }
        checkAndNotify(Objects.requireNonNull(ModelTypeEnum.typeOfValue(entity.getModelType())), entity.getDirectlyModelId(), tenantIsolation.getTenantId());
        addRedirectRef(tenantIsolation, entity);
        buildSubscriptionPreload(entity.getModelType(),entity.getFromId());
        return Result.ok(entity);
    }

    private void addRedirectRef(TenantIsolation tenantIsolation, SubscriptionEntity entity) {
        ModelTypeEnum modelTypeEnum = ModelTypeEnum.typeOfValue(entity.getModelType());
        RedirectReferenceDto newReference = null;
        switch (modelTypeEnum) {
            case THING_MODEL:
                ThingModelEntity thingModel = thingModelMapper.getById(tenantIsolation.getTenantId(), entity.getDirectlyModelId());
                if (!Objects.isNull(thingModel)) {
                    newReference = RedirectReferenceDto.builder()
                            .refId(thingModel.getId())
                            .redirectId(entity.getCallbackId())
                            .refName(thingModel.getName())
                            .updateType(1)
                            .typeName(RedirectRefEnum.MODEL.getTypeName())
                            .type(RedirectRefEnum.MODEL.getType())
                            .build();
                }
                break;

            case DEVICE_MODEL:
                DeviceEntity deviceEntity = deviceService.getById(entity.getDirectlyModelId());
                if (!Objects.isNull(deviceEntity)) {
                    newReference = RedirectReferenceDto.builder()
                            .refId(deviceEntity.getId())
                            .redirectId(entity.getCallbackId())
                            .refName(deviceEntity.getName())
                            .updateType(1)
                            .typeName(RedirectRefEnum.DEVICE.getTypeName())
                            .type(RedirectRefEnum.DEVICE.getType())
                            .build();
                }
                break;
        }
        if (!Objects.isNull(newReference)) {
            instanceRedirectService.updateReference(tenantIsolation, newReference);
        }
    }

    @Override
    public Result<Page<SubscriptionEntity>> getPage(SubscriptionEntity entity, Page<SubscriptionEntity> page) {
        List<Integer> modelTypeList = Arrays.asList(ModelTypeEnum.GATEWAY_MODEL.getValue(),ModelTypeEnum.CHANNEL_MODEL.getValue());
        QueryWrapper<SubscriptionEntity> queryWrapper = new QueryWrapper<SubscriptionEntity>()
            .eq("tenant_id",entity.getTenantId())
            .notIn("model_type",modelTypeList);
            if(!StringUtils.isEmpty(entity.getName())){
                queryWrapper.like("name",entity.getName());
            }
        Page<SubscriptionEntity> list = mapper.selectPage(page, queryWrapper);
        return Result.ok(list);
    }
    
    @Override
    public Result<Page<SubscriptionEntity>> subscriptionGatewayPage(Long edgeGatewayId,SubscriptionEntity entity, Page<SubscriptionEntity> page,TenantIsolation tenantIsolation) {
        Result<List<ChannelVO>> result = channelService.listChannel(ChannelEntity.builder().edgeGatewayId(edgeGatewayId).build(),tenantIsolation);
        List<Long> ids = new ArrayList<>();
        if(result.getSignal() && ObjectUtil.isNotNull(result.getResult())){
            ids = result.getResult().stream().map(c-> c.getId()).collect(Collectors.toList());
        }
        ids.add(edgeGatewayId);
        QueryWrapper<SubscriptionEntity> queryWrapper = new QueryWrapper<SubscriptionEntity>()
            .in("directly_model_id",ids);
        if(!StringUtils.isEmpty(entity.getName())){
            queryWrapper.like("name",entity.getName());
        }
        queryWrapper.orderByDesc("create_time");
        Page<SubscriptionEntity> list = mapper.selectPage(page, queryWrapper);
        return Result.ok(list);
    }
    
    @Override
    public Result<List<SubscriptionEntity>> list(SubscriptionEntity entity) {
        List<SubscriptionEntity> list = mapper.selectList(new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.SUBSCRIBE,details = "更新订阅")
    public Result update(TenantIsolation tenantIsolation, SubscriptionEntity entity) {
        SubscriptionEntity subscriptionEntity = this.getByIdAndTenantIsolation(entity.getId(), tenantIsolation).getResult();
        Long oldRefId = subscriptionEntity.getDirectlyModelId();
        Long oldRedirectId = subscriptionEntity.getCallbackId();
        if (subscriptionEntity == null) {
            throw new BizException("该租户下找不到该订阅");
        }
        Result<Subscription> result = Subscription.checkInfo(subscriptionEntity.getDirectlyModelId(), null, entity);
        if (!result.getSignal()) {
            throw new BizException(result.getMessage());
        }
        if (Objects.isNull(entity.getTargetDevice())) {
            entity.setTargetDevice(0l);
        }
        if (mapper.updateById(entity) == 1) {
            RedirectReferenceDto delRef = RedirectReferenceDto.builder()
                .refId(oldRefId)
                .redirectId(oldRedirectId)
                .updateType(0)
                .build();
            instanceRedirectService.updateReference(tenantIsolation, delRef);
            log.info("===更新订阅========");
            clearOldCache(subscriptionEntity);
            buildSubscriptionPreload(entity.getModelType(),entity.getFromId());
            addRedirectRef(tenantIsolation, entity);
            checkAndNotify(Objects.requireNonNull(ModelTypeEnum.typeOfValue(subscriptionEntity.getModelType())), subscriptionEntity.getDirectlyModelId(), tenantIsolation.getTenantId());
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }
    
    private void clearOldCache(SubscriptionEntity subscriptionEntity){
        if(!String.valueOf(ModelTypeEnum.CHANNEL_MODEL.getValue()).equals(String.valueOf(subscriptionEntity.getModelType()))  && !String.valueOf(ModelTypeEnum.GATEWAY_MODEL.getValue()).equals(String.valueOf(subscriptionEntity.getModelType()))){
            return;
        }
        if(StringUtils.isBlank(subscriptionEntity.getFromId())){
            return;
        }
        String start = RedisConstant.SUBSCRIPTION_CHANNEL_REGISTRY;
        List<Long> idList = new ArrayList<>();
        if(ModelTypeEnum.CHANNEL_MODEL.getValue() == subscriptionEntity.getModelType() ){
            idList = Arrays.stream(subscriptionEntity.getFromId().split(",")).map(Long::parseLong).collect(Collectors.toList());
        }else {
            start = RedisConstant.SUBSCRIPTION_EDGE_GATEWAY_REGISTRY;
            idList.add(Long.parseLong(subscriptionEntity.getFromId()));
        }
        for (Long id :idList){
            // 将逗号分隔的字符串拆分成字符串数组
            if(StringUtils.isNotBlank(subscriptionEntity.getEvents())){
                String[] eventArray = subscriptionEntity.getEvents().split(",");
                List<String> eventList = Arrays.asList(eventArray);
                for (int i = 0; i < eventList.size(); i++) {
                    if(stringRedisTemplate.opsForHash().hasKey(String.format(start,id,eventList.get(i)),String.valueOf(subscriptionEntity.getId()))){
                        stringRedisTemplate.opsForHash().delete(String.format(start,id,eventList.get(i)),String.valueOf(subscriptionEntity.getId()));
                    }
                }
            }
        }
    }

    @Override
    @AuditLog(action = ActionEnum.DELETE,target = AuditTargetEnum.SUBSCRIBE,details = "删除订阅")
    public Result deleteById(TenantIsolation tenantIsolation, Long entityId) {
        SubscriptionEntity subscriptionEntity = this.getByIdAndTenantIsolation(entityId, tenantIsolation).getResult();
        if (subscriptionEntity == null) {
            throw new BizException("该租户下找不到该订阅");
        }
        if (mapper.deleteById(entityId) == 1) {
            if (ModelTypeEnum.THING_MODEL.getValue().equals(subscriptionEntity.getModelType())) {
                deviceService.notifyDeviceSyncByThingModelId(subscriptionEntity.getDirectlyModelId(), tenantIsolation.getTenantId(), null);
            } else if (ModelTypeEnum.DEVICE_MODEL.getValue().equals(subscriptionEntity.getModelType())) {
                deviceService.setNotSyncById(subscriptionEntity.getDirectlyModelId());
            }
            RedirectReferenceDto delRef = RedirectReferenceDto.builder()
                    .refId(subscriptionEntity.getDirectlyModelId())
                    .redirectId(subscriptionEntity.getCallbackId())
                    .updateType(0)
                    .build();
            instanceRedirectService.updateReference(tenantIsolation, delRef);
            clearOldCache(subscriptionEntity);
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    private void checkAndNotify(ModelTypeEnum type, Long directlyModelId, Long tenantId) {
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        switch (type) {
            case THING_MODEL:
                Result<ThingModel> modelResult = ThingModel.checkSubscription(
                        thingModelMapper.getById(tenantId, directlyModelId),
                        commonFetcher
                );
                if (!modelResult.getSignal()) {
                    throw new BizException(modelResult);
                }
                ThingModel model = modelResult.getResult();
                Result<List<Long>> beInheritResult = model.checkBeInherit(commonFetcher);
                if (!beInheritResult.getSignal()) {
                    throw new BizException(beInheritResult);
                }
                deviceService.notifyDeviceSyncByThingModelId(directlyModelId, tenantId, commonFetcher);
                break;
            case DEVICE_MODEL:
                Result<DeviceModel> checkInfo = DeviceModel.checkInfo(commonFetcher.get(directlyModelId, DeviceEntity.class), commonFetcher);
                if (!checkInfo.getSignal()) {
                    throw new BizException(checkInfo);
                }
                deviceService.setNotSyncById(directlyModelId);
                break;
            case CHANNEL_MODEL:
            case GATEWAY_MODEL:
                if(checkIsRepeat(directlyModelId)){
                    throw new BizException("该网关下订阅名称重复");
                }
                break;
            default:
    
        }
    }


    @Override
    public Result<SubscriptionEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<SubscriptionEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SubscriptionEntity::getId, id)
                .eq(SubscriptionEntity::getTenantId, tenantIsolation.getTenantId());
        return Result.ok(mapper.selectOne(lqw));
    }

    @Override
    public void sendFaultEventSubscription(Long deviceId, Long tenantId, FaultEventInstance instance) {
        log.debug("fault事件订阅触发：tenantId:{},deviceId:{},fault:{}", tenantId, deviceId, instance);
        DeviceTwin deviceTwin = new DeviceTwin(deviceDataResource, deviceId);
        Set<String> names = new HashSet<>();
        names.add(RedisConstant.FAULT_EVENT_PREFIX + instance.getEventName());
        Map<String, Set<Subscription>> subscriptionMap = deviceDataResource.getSubscriptionRegistry2Map(deviceId, names);
        if (MapUtil.isNotEmpty(subscriptionMap)) {
            names.forEach(name -> {
                subscriptionMap.get(name).forEach(subscription -> {
                    FaultData faultData = FaultData.builder()
                            .faultEndTime(instance.getEventEndTime())
                            .faultStartTime(instance.getEventStartTime())
                            .faultLevel(instance.getFaultLevel())
                            .faultStatus(instance.getUpData().getFaultStatus() ? 1 : 0)
                            .build();
                    EventData event = EventData.builder()
                            .eventData(instance.getUpData().getProp())
                            .eventDesc(instance.getEventDesc())
                            .eventTime(instance.getUpData().getTimestamp())
                            .eventName(instance.getEventName())
                            .faultData(faultData)
                            .build();
                    if (ObjectUtil.isNotEmpty(faultData.getFaultEndTime())) {
                        faultData.setFaultOverTime(faultData.faultEndTime - faultData.faultStartTime);
                    } else {
                        faultData.setFaultOverTime(event.eventTime - faultData.faultStartTime);
                    }
                    subscription.setEvent(event);
                    subscription.subscriptionPostProcess(SubscriptionFromEnum.FAULT, deviceId, deviceTwin.getActual(), event.eventTime, SubscriptionEventTypeEnum.FAULT);
                });
            });
        }
    }

    @Override
    public void sendTriggerEventSubscription(Long deviceId, Long tenantId, TriggerEventInstance instance) {
        log.debug("trigger事件订阅触发：tenantId:{},deviceId:{},fault:{}", tenantId, deviceId, instance);
        DeviceTwin deviceTwin = new DeviceTwin(deviceDataResource, deviceId);
        Set<String> names = new HashSet<>();
        names.add(RedisConstant.TRIGGER_EVENT_PREFIX + instance.getEventName());
        Map<String, Set<Subscription>> subscriptionMap = deviceDataResource.getSubscriptionRegistry2Map(deviceId, names);
        if (MapUtil.isNotEmpty(subscriptionMap)) {
            names.forEach(name -> {
                subscriptionMap.get(name).forEach(subscription -> {
                    EventData event = EventData.builder()
                            .eventData(instance.getUpData().getProp())
                            .eventDesc(instance.getEventDesc())
                            .eventTime(instance.getUpData().getTimestamp())
                            .eventName(instance.getEventName())
                            .build();
                    subscription.setEvent(event);
                    subscription.subscriptionPostProcess(SubscriptionFromEnum.TRIGGER, deviceId, deviceTwin.getActual(), event.eventTime, SubscriptionEventTypeEnum.TRIGGER);
                });
            });
        }
    }

    @Override
    public void sendNoChangeEventSubscription(Long deviceId, Long tenantId, NoChangeEventInstance instance) {
        log.debug("noChange事件订阅触发：tenantId:{},deviceId:{},noChange:{}", tenantId, deviceId, instance);
        DeviceTwin deviceTwin = new DeviceTwin(deviceDataResource, deviceId);
        Set<String> names = new HashSet<>();
        names.add(instance.getProperty());
        Map<String, Set<Subscription>> subscriptionMap = deviceDataResource.getNoChangeSubscriptionRegistry2Map(deviceId, names);
        if (MapUtil.isNotEmpty(subscriptionMap)) {
            names.forEach(name -> {
                subscriptionMap.get(name).forEach(subscription -> {
                    EventData event = EventData.builder()
                            .eventData(instance.getUpData().getProp())
                            .eventTime(instance.getUpData().getTimestamp())
                            .eventName("NoChange")
                            .build();
                    subscription.setEvent(event);
                    subscription.subscriptionPostProcess(SubscriptionFromEnum.NO_CHANGE, deviceId, deviceTwin.getActual(), event.eventTime, SubscriptionEventTypeEnum.NO_CHANGE);
                });
            });
        }
    }

    @Override
    @Async("subscriptionAsyncExecutor")
    public void execRedirectWithPayload(Long tenant, Long callbackId, String payload) {
        log.debug("订阅回调信息：{}", payload);
        TenantIsolation tenantIsolation = new TenantIsolation();
        tenantIsolation.setTenantId(tenant);
        instanceRedirectService.execRedirectWithPayload(callbackId, tenantIsolation, payload);
        /*RedirectRequest request = RedirectRequest.newBuilder().setRedirectId(callbackId).setTenantId(tenant).
                setPayload(payload).build();
        mqttCallbackStub.invoke(request, new StreamObserver<InvokeResult>() {
            @Override
            public void onNext(InvokeResult invokeResult) {
                log.debug("订阅调用成功，返回:{}", invokeResult.getSuccess());

                // if (!StringUtils.isEmpty(payload) && payload.indexOf("traceId") > 0) {
                //     log.info("trace: onNext {}",payload);
                // }
            }

            @Override
            public void onError(Throwable throwable) {
                log.error("订阅调用失败，结果：{}", throwable.getMessage());
                throwable.printStackTrace();
                // if (!StringUtils.isEmpty(payload) && payload.indexOf("traceId") > 0) {
                //     log.info("trace: onError {}",payload);
                // }
            }

            @Override
            public void onCompleted() {
                log.debug("订阅调用成功！回调：{}", callbackId);
                // if (!StringUtils.isEmpty(payload) && payload.indexOf("traceId") > 0) {
                //     log.info("trace: onCompleted {}",payload);
                // }
            }
        });*/
    }

    @Override
    public void batchDeleteByDirectlyModelIds(Long tenantId, List<Long> ids, Integer modelType) {
        if (CollectionUtil.isNotEmpty(ids)) {
            LambdaUpdateWrapper<SubscriptionEntity> eq = new LambdaUpdateWrapper<SubscriptionEntity>()
                    .in(SubscriptionEntity::getDirectlyModelId, ids)
                    .eq(SubscriptionEntity::getModelType, modelType)
                    .eq(SubscriptionEntity::getTenantId, tenantId);
            mapper.delete(eq);
        }
    }

    // @Async("subscriptionAsyncExecutor")
    @Override
    public void invokeDeviceService(Long tenant, Long targetDevice, String serviceName, Map<String, Object> result, DeviceServiceLogEntity logEntity,Boolean isRecordLog) {
        deviceService.doServiceTask(tenant, targetDevice, serviceName, result, logEntity,isRecordLog);

    }

    @Autowired
    SendNotifyService notifyServiceClient;

    @Override
    @Async("subscriptionAsyncExecutor")
    public void sendNotifies(Long tenant, Long callbackId, String receivers, String params) {
        this.sendNotifies(tenant,null,callbackId,receivers,params);
    }

    @Override
    @Async("subscriptionAsyncExecutor")
    public void sendNotifies(Long tenant, Long subscriptionId, Long callbackId, String receivers, String params) {
        SendNotifyDTO sendNotifyDTO = new SendNotifyDTO();
        TenantIsolation tenantIsolation = new TenantIsolation();
        tenantIsolation.setTenantId(tenant);
        sendNotifyDTO.setTenantIsolation(tenantIsolation);
        NotifyDTO notifyDTO = new NotifyDTO();
        notifyDTO.setTemplateId(callbackId);
        notifyDTO.setReceivers(receivers);
        notifyDTO.setParams(params);
        notifyDTO.setSubscriptionId(subscriptionId);
        sendNotifyDTO.setNotify(notifyDTO);
        log.info("sendNotifyDTO is {}", JSONObject.toJSONString(sendNotifyDTO));
        R r = notifyServiceClient.send(sendNotifyDTO);
        if (!MapUtil.getBool(r, "ok")) {
            log.info("执行通知失败，原因详情{}", JSONObject.toJSONString(r));
        }
    }

    @Override
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.SUBSCRIBE, details = "订阅解绑")
    public Result<Void> deleteBatchByIds(TenantIsolation tenantIsolation, List<Long> ids) {
        if (CollectionUtil.isEmpty(ids) || Objects.isNull(tenantIsolation) || Objects.isNull(tenantIsolation.getTenantId())) {
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        LambdaQueryWrapper<SubscriptionEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SubscriptionEntity::getTenantId,tenantIsolation.getTenantId());
        lqw.in(SubscriptionEntity::getId, ids);
        Set<Long> deviceIds = new HashSet<>();
        Set<Long> thingModelIds = new HashSet<>();
        List<SubscriptionEntity> subscriptionEntityList = mapper.selectList(lqw);
        subscriptionEntityList.forEach(subscriptionEntity ->{
            if (ModelTypeEnum.THING_MODEL.getValue().equals(subscriptionEntity.getModelType())) {
                thingModelIds.add(subscriptionEntity.getDirectlyModelId());
            }else {
                deviceIds.add(subscriptionEntity.getDirectlyModelId());
            }
            clearOldCache(subscriptionEntity);
        });
        log.info("batch delete subscription,tenant:{}, ids:{}",tenantIsolation,ids);
        LambdaUpdateWrapper<SubscriptionEntity> w = new LambdaUpdateWrapper<SubscriptionEntity>()
                .eq(SubscriptionEntity::getTenantId, tenantIsolation.getTenantId())
                .in(SubscriptionEntity::getId, ids);
        mapper.delete(w);
        if (!deviceIds.isEmpty()) {
            deviceService.setNotSyncByIds(deviceIds);
        }
        if (!thingModelIds.isEmpty()){
            deviceService.notifyDeviceSyncByThingModelIds(thingModelIds, tenantIsolation.getTenantId(), null);
        }


        return Result.ok();
    }
    
    public Result<Void> buildSubscriptionPreload(Integer modelType,String fromId){
        if(String.valueOf(ModelTypeEnum.GATEWAY_MODEL.getValue()).equals(String.valueOf(modelType))){
            Long id = Long.parseLong(fromId);
            EdgeGatewayEntity edgeGatewayEntity = edgeGatewayService.getById(id);
            if(edgeGatewayEntity.getDeleted()){
                return Result.error("该网关已删除");
            }
            CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(edgeGatewayEntity.getTenantId());
            Result<EdgeGateway> edgeGatewayResult = EdgeGateway.checkInfo(edgeGatewayEntity,commonFetcher);
            if(!edgeGatewayResult.getSignal()){
                return Result.error(edgeGatewayResult.getMessage());
            }
            subscriptionRegistry(getSubscriptionRegistry(edgeGatewayResult.getResult().getSubscriptions()),edgeGatewayResult.getResult().getId(),RedisConstant.SUBSCRIPTION_EDGE_GATEWAY_REGISTRY);
        }else if(String.valueOf(ModelTypeEnum.CHANNEL_MODEL.getValue()).equals(String.valueOf(modelType))){
            List<Long> idList = Arrays.stream(fromId.split(",")).map(Long::parseLong).collect(Collectors.toList());
            for (int i = 0; i < idList.size(); i++) {
                ChannelEntity channelEntity = channelService.getById(idList.get(i));
                if(channelEntity.getDeleted()){
                    return Result.error("该通道已删除");
                }
                CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(channelEntity.getTenantId());
                Result<Channel> channelResult = Channel.checkInfo(channelEntity,commonFetcher);
                if(!channelResult.getSignal()){
                    return Result.error(channelResult.getMessage());
                }
                subscriptionRegistry(getSubscriptionRegistry(channelResult.getResult().getSubscriptions()),channelResult.getResult().getId(),RedisConstant.SUBSCRIPTION_CHANNEL_REGISTRY);
            }
        }
        return Result.ok();
    }
    
    private Map<String, List<Subscription>> getSubscriptionRegistry(List<Subscription> subscriptions){
        Map<String, List<Subscription>> registryMap = new HashMap<>();
        if (!subscriptions.isEmpty()){
            for (Subscription subscription : subscriptions) {
                SubscriptionEventTypeEnum eventTypeEnum = SubscriptionEventTypeEnum.typeOfValue(subscription.getEventType());
                if (ObjectUtil.isEmpty(eventTypeEnum)) {
                    continue;
                }
                switch (eventTypeEnum) {
                    case GATEWAY_EVENT:
                    case CHANNEL_EVENT: {
                        String events = subscription.getEvents();
                        if (StringUtils.isNotBlank(events)){
                            String[] eventArr = events.split(",");
                            for (String eventName : eventArr) {
                                String key = eventName;
                                List<Subscription> registrySubscriptions = registryMap.get(key);
                                if (registrySubscriptions == null){
                                    registrySubscriptions = new ArrayList<>();
                                }
                                registrySubscriptions.add(subscription);
                                registryMap.put(key,registrySubscriptions);
                            }
                        }
                        break;
                    }
                    default:
                        continue;
                }
            }
        }
        return registryMap;
    }
    
    private void subscriptionRegistry(Map<String, List<Subscription>> subscriptionRegistry, Long id,String key) {
        subscriptionRegistry.forEach((k,v) -> {
            v.forEach(subscription -> {
                if (subscription.getEnable()) {
                    stringRedisTemplate.opsForHash().put(String.format(key, id,k),String.valueOf(subscription.getId()),JSON.toJSONString(subscription));
                    dataConversionService.codePreload(subscription.getId(),subscription.getDataConversionCode());
                }
            });
        });
    }
    
    private Boolean checkIsRepeat(Long id){
        List<SubscriptionEntity> subscriptionEntityList = getSubscriptionEntityList(id);
        if(CollectionUtil.isEmpty(subscriptionEntityList)){
            return false;
        }
        Set<String> nameSet = subscriptionEntityList.stream()
            .map(SubscriptionEntity::getName)
            .collect(Collectors.toSet());
        if(subscriptionEntityList.size() != nameSet.size()){
            return true;
        }
        return false;
    }
    
    private List<SubscriptionEntity> getSubscriptionEntityList(Long id){
        LambdaQueryWrapper<SubscriptionEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SubscriptionEntity::getDirectlyModelId, id);
        return mapper.selectList(lqw);
    }
    
    public Result<Void> delByDirectlyModelId(Long directlyModelId,Long tenantId, ModelTypeEnum type){
        List<Long> idList = new ArrayList<>();
        switch (type) {
            case CHANNEL_MODEL:
                Result<ChannelEntity> channelEntityResult = channelService.getChannelById(directlyModelId);
                if(!channelEntityResult.getSignal()){
                    throw new BizException(channelEntityResult.getMessage());
                }
                ChannelEntity channelEntity = channelEntityResult.getResult();
                if(ObjectUtil.isEmpty(channelEntity) || channelEntity.getDeleted()){
                    return Result.ok();
                }
                delChannelCache(channelEntity.getId());
                idList.add(channelEntity.getId());
                break;
            case GATEWAY_MODEL:
                Result<List<ChannelEntity>> channelEntityListResult = channelService.listByEdgeGatewayId(directlyModelId,tenantId);
                if(!channelEntityListResult.getSignal()){
                    throw new BizException(channelEntityListResult.getMessage());
                }
                List<Long> channelIds = channelEntityListResult.getResult().stream().map(ChannelEntity::getId).collect(Collectors.toList());
                channelIds.forEach(id->{
                    delChannelCache(id);
                });
                idList.addAll(channelIds);
                delEdgeGateway(directlyModelId);
                idList.add(directlyModelId);
                break;
            default:
        }
        if(CollectionUtil.isNotEmpty(idList)){
            LambdaQueryWrapper<SubscriptionEntity> lqw = new LambdaQueryWrapper<>();
            lqw.in(SubscriptionEntity::getDirectlyModelId, idList);
            if(CollectionUtil.isNotEmpty(mapper.selectList(lqw))){
                mapper.delete(lqw);
            }
        }
        return Result.ok();
    }

    @Override
    @Async("subscriptionAsyncExecutor")
    public void deleteNotifyIfNeed(SubscriptionEntity entity) {
        if(!entity.getEnable() && entity.getOutType() == 2){
            notifyServiceClient.deleteTask(entity.getId());
        }
    }

    @Override
    @Async("subscriptionAsyncExecutor")
    public void deleteNotify(Long entityId) {
        notifyServiceClient.deleteTask(entityId);
    }


    private void delChannelCache(Long id){
        List<String> events = new ArrayList<>();
        events.add(StatusEventEnum.CONNECT.getName());
        events.add(StatusEventEnum.DISCONNECT.getName());
        events.forEach(event ->{
            String redisKey = String.format(RedisConstant.SUBSCRIPTION_CHANNEL_REGISTRY,id,event);
            if(stringRedisTemplate.hasKey(redisKey)){
                stringRedisTemplate.delete(redisKey);
            }
        });
    }
    
    private void delEdgeGateway(Long id){
        List<String> events = new ArrayList<>();
        events.add(StatusEventEnum.OFFLINE.getName());
        events.add(StatusEventEnum.ONLINE.getName());
        events.add(StatusEventEnum.MEMORY.getName());
        events.add(StatusEventEnum.SPACE.getName());

        events.forEach(event ->{
            String redisKey = String.format(RedisConstant.SUBSCRIPTION_EDGE_GATEWAY_REGISTRY,id,event);
            if(stringRedisTemplate.hasKey(redisKey)){
                stringRedisTemplate.delete(redisKey);
            }
        });
    }

    @Override
    public Result<List<SubscriptionEntity>> getChannelSubscriptionByEdgeGatewayId(Long edgeGatewayId) {
        LambdaQueryWrapper<SubscriptionEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SubscriptionEntity::getDirectlyModelId,edgeGatewayId);
        lqw.eq(SubscriptionEntity::getModelType,ModelTypeEnum.CHANNEL_MODEL.getValue());
        List<SubscriptionEntity> subscriptionEntityList = mapper.selectList(lqw);
        return Result.ok(subscriptionEntityList);
    }

    public boolean checkSubscription(SubscriptionEntity entity) {
        switch (entity.getOutType()){
            case 1:
                return ObjectUtil.isNotEmpty(entity.getTargetService());
            case 0:
                return ObjectUtil.isNotEmpty(entity.getCallbackId());
            case 2:
                return ObjectUtil.isNotEmpty(entity.getCallbackId()) && ObjectUtil.isNotEmpty(entity.getReceivers());
            default:
                return false;
        }

    }

    public void deleteByThingModelId(Long tenantId , Long thingModelId) {
       mapper.deleteByThingModelId(tenantId,thingModelId);
    }

}
