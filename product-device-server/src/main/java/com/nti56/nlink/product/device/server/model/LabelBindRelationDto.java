package com.nti56.nlink.product.device.server.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName LabelBindRelationDto
 * @date 2022/4/26 13:10
 * @Version 1.0
 */
@Data
public class LabelBindRelationDto {

    private Long id;

    @Schema(description = "设备Id")
    protected Long deviceId;

    @Schema(description = "设备名称")
    private String deviceName;
    /**
     * 属性名称
     */
    @Schema(description = "属性名称")
    private String propertyName;

    /**
     * 标签名称
     */
    @Schema(description = "标签名称")
    private String name;

    /**
     * 数据类型，bool/byte/short/int/float/string
     */
    @Schema(description = "数据类型，bool/byte/short/int/float/string")
    private String dataType;
    /**
     * 是否数组
     */
    @Schema(description = "是否数组")
    private Boolean isArray;


}
