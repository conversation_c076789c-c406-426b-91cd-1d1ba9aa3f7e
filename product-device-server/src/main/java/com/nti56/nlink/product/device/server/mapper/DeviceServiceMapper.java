package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.DeviceServiceEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 类说明: 设备服务mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:23
 * @since JDK 1.8
 */
public interface DeviceServiceMapper extends CommonMapper<DeviceServiceEntity> {

    List<DeviceServiceEntity> listByDataModelId(@Param("tenantId") Long tenantId,
                                                @Param("dataModelId") Long dataModelId);
}
