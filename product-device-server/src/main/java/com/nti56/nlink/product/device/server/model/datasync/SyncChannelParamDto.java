package com.nti56.nlink.product.device.server.model.datasync;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/********************************************************************************
 *
 * Title: 
 * Description: 
 * <AUTHOR>
 * create date on 2023/5/11
 * @version 1.0.0
 *
 *******************************************************************************/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "通道参数对象")
public class SyncChannelParamDto {
    
    /**
     * id
     */
    private Long id;
    
    /**
     * 所属通道id
     */
    @Schema(description = "通道ID")
    private Long channelId;
    
    /**
     * 通道参数名称，如ip/port/rack/slot等
     */
    @Schema(description = "参数名称")
    private String name;
    
    /**
     * 描述
     */
    @Schema(description = "描述")
    private String descript;
    
    /**
     * 通道参数值
     */
    @Schema(description = "通道参数值")
    private String value;
    
    /**
     * 是否必须
     */
    @Schema(description = "是否必须")
    private Boolean necessary;
    
}
