package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import com.nti56.nlink.product.device.client.model.dto.json.compute.ComputeEventItem;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.FaultRedisCache;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.FaultStatus;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpEventTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import com.nti56.nlink.product.device.server.entity.ComputeTaskEntity;
import com.nti56.nlink.product.device.server.model.ComputeTaskBo;

import java.util.List;
import java.util.Map;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 11:10:19
 * @since JDK 1.8
 */
public interface ITaskService extends IBaseService<ComputeTaskEntity> {

    /**
     * 网关调用部分
     */
    Result<List<GatherParamField>> listGatherParam(TenantIsolation tenant, Long edgeGatewayId);
    Result<List<ChannelRuntimeInfoField>> listChannelRuntimeInfo(TenantIsolation tenant, Long edgeGatewayId);
    Result<List<ComputeTaskBo>> listComputeTask(TenantIsolation tenant, Long edgeGatewayId);

    /**
     * 下发同步命令
     */
    Result<Void> syncTask(TenantIsolation tenant, Long edgeGatewayId);
    
    Result<Void> syncCustomDriver(TenantIsolation tenant, Long edgeGatewayId);

    Result<Void> syncAllTask(TenantIsolation tenant);

    /**
     * 通道信息部分
     */
    Result<Integer> updateChannelRuntimeInfoByChannelId(Long tenantId, Long channelId);
    Result<Integer> updateChannelRuntimeInfoByEdgeGatewayId(Long tenantId, Long edgeGatewayId);

    /**
     * 计算任务部分
     */
    Result<Void> disableDeviceComputeTask(TenantIsolation tenant, Long deviceId);
    Result<Void> enableDeviceComputeTask(TenantIsolation tenant, Long deviceId);

    ComputeTaskEntity updateComputeTask(Long tenantId, Long deviceId, ComputeTaskEntity task);


    boolean batchUpdateComputeTask(Long tenantId, Map<Long,ComputeTaskEntity> deviceTaskMap);
    /**
     * 采集任务部分
     */
    
    Result<GatherParamField> updateGatherParamByLabelId(Long tenantId, Long labelId);
    Result<List<GatherParamField>> updateGatherParamByLabelGroupId(Long tenantId, Long labelGroupId);
    Result<List<GatherParamField>> updateGatherParamByChannelId(Long tenantId, Long channelId);
    Result<List<GatherParamField>> updateGatherParamByEdgeGatewayId(Long tenantId, Long edgeGatewayId);
    Result<List<GatherParamField>> updateGatherParamByLabelIds(Long tenantId, List<Long> labelIds, CommonFetcher commonFetcher);

    /**
     * 自定义协议部分
     */
    Result<Integer> updateCustomDriverRuntimeInfo(Long tenantId);
    
    Result<Void> updateCustomDriverRuntimeInfoById(Long tenantId, Long customDriverId);

    Result<Void> batchDisableDeviceComputeTask(TenantIsolation tenant, List<Long> deviceIds);

    void batchenableDeviceComputeTask(TenantIsolation tenantIsolation, List<Long> ids);

    ComputeTaskEntity getComputeTaskByDeviceId(Long deviceId);

    void executeTask(ComputeTaskEntity task, Map<String, Object> map, Map<String, Object> actual, Long timestamp);

    void endFault(GwUpEventTopic.TopicInfo topicInfo, String deviceName,Integer level);

    void processFault(FaultRedisCache faultRedisCache,Integer level);
}

