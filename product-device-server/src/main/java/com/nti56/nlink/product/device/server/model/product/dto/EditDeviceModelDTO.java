package com.nti56.nlink.product.device.server.model.product.dto;

import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/28 9:34<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "设备模型dto")
public class EditDeviceModelDTO {

    @Schema(description = "物模型主键ID")
    @NotNull(message = "设备id不能为空")
    private Long id;

    @Schema(description = "物模型定义")
    private ModelField model;
}
