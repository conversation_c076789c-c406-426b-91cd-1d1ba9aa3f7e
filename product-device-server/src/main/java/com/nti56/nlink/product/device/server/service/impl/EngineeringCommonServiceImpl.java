package com.nti56.nlink.product.device.server.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.InstanceRedirectEntity;
import com.nti56.nlink.product.device.server.entity.Tag;
import com.nti56.nlink.product.device.server.mapper.RedirectMapper;
import com.nti56.nlink.product.device.server.model.engineering.dto.CommonServiceDataDTO;
import com.nti56.nlink.product.device.server.model.redirect.RedirectDTO;
import com.nti56.nlink.product.device.server.model.tag.TagDTO;
import com.nti56.nlink.product.device.server.service.IEngineeringCommonService;
import com.nti56.nlink.product.device.server.service.IInstanceRedirectService;
import com.nti56.nlink.product.device.server.service.ITagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/7 9:08<br/>
 * @since JDK 1.8
 */
@Service
public class EngineeringCommonServiceImpl implements IEngineeringCommonService {

    @Autowired
    private ITagService tagService;

    @Autowired
    private IInstanceRedirectService redirectService;

    @Autowired
    private RedirectMapper redirectMapper;

    @Override
    public Result<Void> deleteCommonDataByTenantId(Long tenantId) {
        redirectMapper.deleteAllByTenantId(tenantId);
        return Result.ok();
    }

    @Override
    public Result<CommonServiceDataDTO> getCommonDataByTenantId(Long tenantId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("tenant_id",tenantId);
        map.put("deleted", 0);
        return Result.ok(CommonServiceDataDTO.builder()
                .tagList(BeanUtilsIntensifier.copyBeanList(tagService.listByMap(map), TagDTO.class))
                .redirectList(BeanUtilsIntensifier.copyBeanList(redirectService.listByMap(map), RedirectDTO.class))
                .build());
    }

    @Override
    public Result<Void> createCommonData(CommonServiceDataDTO dto) {
        if (CollectionUtils.isNotEmpty(dto.getTagList())) {
            tagService.saveBatch(BeanUtilsIntensifier.copyBeanList(dto.getTagList(), Tag.class));
        }
        if (CollectionUtils.isNotEmpty(dto.getRedirectList())) {
            redirectService.saveBatch(BeanUtilsIntensifier.copyBeanList(dto.getRedirectList(), InstanceRedirectEntity.class));
        }
        return Result.ok();
    }
}
