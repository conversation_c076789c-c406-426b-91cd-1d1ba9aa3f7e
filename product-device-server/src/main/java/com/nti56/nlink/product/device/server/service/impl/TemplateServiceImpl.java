package com.nti56.nlink.product.device.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.exception.BizException;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.TemplateEntity;
import com.nti56.nlink.product.device.server.enums.AuditEnum;
import com.nti56.nlink.product.device.server.enums.error.NotifyErrorEnum;
import com.nti56.nlink.product.device.server.mapper.TemplateMapper;
import com.nti56.nlink.product.device.server.model.template.dto.CreateTemplateDTO;
import com.nti56.nlink.product.device.server.model.template.dto.EditTemplateDTO;
import com.nti56.nlink.product.device.server.model.template.dto.TemplateDTO;
import com.nti56.nlink.product.device.server.model.template.vo.TemplateVO;
import com.nti56.nlink.product.device.server.service.ITemplateService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/10 10:39<br/>
 * @since JDK 1.8
 */
@Service
public class TemplateServiceImpl extends ServiceImpl<TemplateMapper,TemplateEntity> implements ITemplateService {



    @Autowired
    private com.nti56.nlink.product.device.server.service.impl.NotifyStrategyContext notifyStrategyContext;

    @Autowired
    private TemplateMapper templateMapper;


    @Override
    public Result createTemplate(CreateTemplateDTO dto, TenantIsolation tenantIsolation) {
        Result result = this.uniqueName( dto.getName(), tenantIsolation);

        if (!result.getSignal()){
            return result;
        }
        return notifyStrategyContext.addTemplate(dto, tenantIsolation);
    }

    @Override
    public Result deleteTemplate(Long id, TenantIsolation tenantIsolation) {
        Result<TemplateEntity> templateEntityResult = this.getByIdAndTenantIsolation(id, tenantIsolation);
        TemplateEntity template = templateEntityResult.getResult();

        if (template == null){
            throw new BizException("该租户下不存在该模板");
        }

        if (AuditEnum.AUDITING.getValue().compareTo(template.getAuditStatus()) == 0){
            return Result.error(NotifyErrorEnum.AUDITING_TEMPLATE_CANNOT_OPERATION.getCode(),NotifyErrorEnum.AUDITING_TEMPLATE_CANNOT_OPERATION.getMessage());
        }

        return notifyStrategyContext.deleteTemplate(template,tenantIsolation);
    }

    @Override
    public Result<TemplateEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<TemplateEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TemplateEntity::getId,id)
                .eq(TemplateEntity::getTenantId,tenantIsolation.getTenantId());
        return Result.ok(this.getOne(lqw));
    }

    @Override
    public Result<List<TemplateVO>> listTemplate(TemplateDTO dto, TenantIsolation tenantIsolation) {
        return Result.ok(templateMapper.listTemplate(dto,tenantIsolation));
    }

    @Override
    public Result editTemplate(EditTemplateDTO dto, TenantIsolation tenantIsolation) {
        Result<TemplateEntity> templateEntityResult = this.getByIdAndTenantIsolation(dto.getId(), tenantIsolation);
        TemplateEntity template = templateEntityResult.getResult();
        if (template == null){
            throw new BizException("该租户下不存在该模板");
        }

        if (AuditEnum.AUDITING.getValue().compareTo(template.getAuditStatus()) == 0){
            return Result.error(NotifyErrorEnum.AUDITING_TEMPLATE_CANNOT_OPERATION.getCode(),NotifyErrorEnum.AUDITING_TEMPLATE_CANNOT_OPERATION.getMessage());
        }

        if(StringUtils.isNotBlank(dto.getName())){

            Result result = this.uniqueName(dto.getId(), dto.getName(), tenantIsolation);

            if (!result.getSignal()){
                return result;
            }
        }

        if (dto.getNotifyChannelId() == null){
            dto.setNotifyChannelId(template.getNotifyChannelId());
        }

        return notifyStrategyContext.editTemplate(dto,template,tenantIsolation);
    }

    @Override
    public Result<Integer> countByChannelId(Long id, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<TemplateEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TemplateEntity::getNotifyChannelId,id)
            .eq(TemplateEntity::getTenantId,tenantIsolation.getTenantId());
        return Result.ok(this.count(lqw));
    }

    @Override
    public Result<Page<TemplateVO>> pageTemplate(PageParam pageParam, TemplateDTO dto, TenantIsolation tenantIsolation) {
        return Result.ok(templateMapper.pageTemplate(pageParam.toPage(TemplateVO.class), dto,tenantIsolation));
    }

    private Result uniqueName(String name , TenantIsolation tenantIsolation){
        return this.uniqueName(null,name,tenantIsolation);
    }

    /**
     * 判断名称是否唯一
     * @param id
     * @param name
     * @param tenantIsolation
     * @return
     */
    private Result uniqueName(Long id ,String name , TenantIsolation tenantIsolation){
        LambdaQueryWrapper<TemplateEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null ,TemplateEntity::getId, id)
                .eq(TemplateEntity::getName,name)
                .eq(TemplateEntity::getTenantId,tenantIsolation.getTenantId());

        if (this.count(lqw) > 0){
            return Result.error(NotifyErrorEnum.TEMPLATE_UNIQUE_NAME.getCode(),NotifyErrorEnum.TEMPLATE_UNIQUE_NAME.getMessage());
        }

        return Result.ok();
    }

}
