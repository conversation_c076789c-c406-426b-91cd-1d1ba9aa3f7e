package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 订阅表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-09-22 15:17:38
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("subscription")
@Schema(description = "SubscriptionEntity对象")
public class SubscriptionEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    /**
     * 订阅名称
     */
    @Schema(description = "订阅名称")
    private String name;

    /**
     * 订阅描述
     */
    @Schema(description = "订阅描述")
    private String descript;

    /**
     * 订阅属性，用“,”隔开
     */
    @Schema(description = "订阅属性，用“,”隔开")
    private String properties;

    /**
     * 订阅事件，用“,”隔开
     */
    @Schema(description = "订阅事件，用“,”隔开")
    private String events;

    /**
     * 事件类型，
     * 0-fault 故障, 
     * 1-trigger 事件, 
     * 2-dataChange 数据改变, 
     * 3-gatewayStatusChange 网关状态改变, 
     * 4-channelStatusChange 通道状态改变, 
     */
    @Schema(description = "事件类型，0-fault, 1-trigger, 2-dataChange, 3-gatewayStatusChange, 4-channelStatusChange, 5-noChange")
    private Integer eventType;

    /**
     * 回调ID
     */
    @Schema(description = "回调ID")
    private Long callbackId;

    /**
     * 直属模型ID
     */
    @Schema(description = "直属模型ID")
    private Long directlyModelId;

    /**
     * 直属模型类型，1-物模型，2-设备模型，6-网关，7-通道
     */
    @Schema(description = "直属模型类型，1-物模型，2-设备模型，6-网关，7-通道")
    private Integer modelType;

    /**
     * 是否逐个发送
     */
    @Schema(description = "是否逐个发送，0-否，1-是")
    private Boolean sendOneByOne;

    @Schema(description = "数据传出类型，0-资源回调,1-设备服务,2-消息通知")
    private Integer outType;

    @Schema(description = "传出类型是设备服务时的目标设备ID，如果是自身调用则为0")
    private Long targetDevice;

    @Schema(description = "目标服务")
    private String targetService;

    @Schema(description = "接收账号，用逗号隔开")
    private String receivers;

    /**
     * 是否开启订阅
     */
    @Schema(description = "是否开启订阅，0-否，1-是")
    private Boolean enable;

    @Schema(description = "未改变持续时长，单位秒")
    private Integer noChangeSeconds;

    /**
     * 数据转换代码
     */
    @Schema(description = "数据转换代码")
    private String dataConversionCode;

    /**
     * 工程id
     */
    @Schema(description = "工程id")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块id
     */
    @Schema(description = "模块id")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间id
     */
    @Schema(description = "空间id")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 创建的用户id
     */
    @Schema(description = "创建的用户id")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建的用户姓名
     */
    @Schema(description = "创建的用户姓名")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 修改的用户姓名
     */
    @Schema(description = "修改的用户姓名")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 最后一次修改的用户id
     */
    @Schema(description = "最后一次修改的用户id")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 创建数据时间
     */
    @Schema(description = "创建数据时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新数据时间
     */
    @Schema(description = "更新数据时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否被删除
     */
    @Schema(description = "是否被删除")
    @TableLogic
    private Boolean deleted;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    @Version
    private Integer version;
    
    /**
     * 来源id集合
     */
    @Schema(description = "来源id集合")
    private String fromId;

}
