package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.model.device.DeviceSelectBo;
import com.nti56.nlink.product.device.server.model.deviceLog.EventTriggerLogBo;
import com.nti56.nlink.product.device.server.model.deviceLog.PropertyLogBo;
import com.nti56.nlink.product.device.server.model.deviceLog.PropertyLogConditionBo;
import com.nti56.nlink.product.device.server.model.deviceLog.ServiceCallLogRequestBo;

import java.util.List;
import java.util.Set;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName IDeviceLogService
 * @date 2022/8/8 16:59
 * @Version 1.0
 */
public interface IDeviceLogService extends IBaseService<DeviceServiceLogEntity> {

    Result<Page<PropertyLogBo>> pagePropertiesLog(Page<PropertyLogBo> page, PropertyLogConditionBo condition, TenantIsolation tenantIsolation);

    Result<Set<Object>> getPropertiesByDeviceIds(TenantIsolation tenantIsolation, PropertyLogConditionBo condition);

    Result<Page<EventTriggerLogBo>> pageEventTriggerLog(Page<EventTriggerLogBo> page, EventTriggerLogBo eventTriggerLogBo, TenantIsolation tenantIsolation);

    Result<Page<DeviceServiceLogEntity>> pageServiceCallLogBo(Page<DeviceServiceLogEntity> page, ServiceCallLogRequestBo serviceCallLogRequestBo, TenantIsolation tenantIsolation);

    Result<List<PropertyLogBo>> propertiesLog(PropertyLogConditionBo condition, TenantIsolation tenantIsolation);

    Result<List<DeviceSelectBo>> selectDevice(TenantIsolation tenantIsolation);

    void addThingServiceDailyColled(Long tenantId);

    void insertLog(DeviceServiceLogEntity logEntity);

}
