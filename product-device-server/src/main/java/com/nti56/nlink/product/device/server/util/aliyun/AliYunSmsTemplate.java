package com.nti56.nlink.product.device.server.util.aliyun;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.*;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/13 11:37<br/>
 * @since JDK 1.8
 */
public class AliYunSmsTemplate {
    /**
     * 添加短信模板
     * @param client 调用createClient()创建
     * @param templateType 0：验证码。1：短信通知。2：推广短信。3：国际/港澳台消息。
     * @param templateName 模板名称，长度为1~30个字符。
     * @param templateContent 模板内容，长度为1~500个字符。
     * @param remark 短信模板申请说明。长度为1~100个字符。
     * @return 记得把返回的值存起来 特别是TemplateCode(resp.getBody().templateCode)
     * 返回示例
    {
    "TemplateCode": "SMS_16703****",
    "Message": "OK",
    "RequestId": "0A974B78-02BF-4C79-ADF3-90CFBA1B55B1",
    "TemplateContent": "亲爱的会员！阿里云短信服务祝您新年快乐！",
    "TemplateName": "阿里云短信测试模板",
    "TemplateType": "1",
    "CreateDate": "2019-06-04 11:42:17",
    "Code": "OK",
    "Reason": "无审批备注",
    "TemplateStatus": "1"
    }
     * @throws Exception
     */
    public static AddSmsTemplateResponse addSmsTemplate(com.aliyun.dysmsapi20170525.Client client, Integer templateType, String templateName, String templateContent, String remark) throws Exception {
        AddSmsTemplateRequest req = new AddSmsTemplateRequest()
                .setTemplateType(templateType)
                .setTemplateName(templateName)
                .setTemplateContent(templateContent)
                .setRemark(remark);
        return client.addSmsTemplate(req);
    }

    /**
     * 删除短信模板
     * @param client 调用createClient()创建
     * @param templateCode 短信模板code 您可以在控制台模板管理页面或API接口addSmsTemplate()的返回参数中获取短信模板CODE。如果需要删除模板记得把添加短信模板中返回的值存起来
     * 返回示例
    {
    "TemplateCode": "SMS_20375****",
    "Message": "OK",
    "RequestId": "CCA2BCFF-2BA7-427C-90EE-AC6994748607",
    "Code": "OK"
    }
     * @throws Exception
     */
    public static DeleteSmsTemplateResponse deleteSmsTemplate(com.aliyun.dysmsapi20170525.Client client, String templateCode) throws Exception {
        DeleteSmsTemplateRequest req = new DeleteSmsTemplateRequest()
                .setTemplateCode(templateCode);
        return client.deleteSmsTemplate(req);
    }

    /**
     * 修改短信模板
     * @param client 调用createClient()创建
     * @param templateType 0：验证码。1：短信通知。2：推广短信。3：国际/港澳台消息。
     * @param templateName 模板名称，长度为1~30个字符。
     * @param templateCode 您可以登录短信服务控制台，选择国内消息或国际/港澳台消息，在模板管理页签下查看模板CODE。也可以通过AddSmsTemplate接口获取短信模板CODE。
     * @param templateContent 模板内容，长度为1~500个字符。
     * @param remark 短信模板申请说明。长度为1~100个字符。
     * 返回示例
    {
    "TemplateCode": "SMS_15255****",
    "Message": "OK",
    "RequestId": "F655A8D5-B967-440B-8683-DAD6FF8DE990",
    "Code": "OK"
    }
     * @throws Exception
     */
    public static ModifySmsTemplateResponse modifySmsTemplate(com.aliyun.dysmsapi20170525.Client client, Integer templateType, String templateName, String templateCode, String templateContent, String remark) throws Exception {
        ModifySmsTemplateRequest req = new ModifySmsTemplateRequest()
                .setTemplateType(templateType)
                .setTemplateName(templateName)
                .setTemplateCode(templateCode)
                .setTemplateContent(templateContent)
                .setRemark(remark);
        return client.modifySmsTemplate(req);
    }

    /**
     * 查询模板详情
     * @param client 调用createClient()创建
     * @param templateCode 您可以登录短信服务控制台，选择国内消息或国际/港澳台消息，在模板管理页签中查看模板CODE。也可以通过AddSmsTemplate接口获取模板CODE。
     * 返回示例
    {
    "TemplateCode": "SMS_16703****",
    "Message": "OK",
    "RequestId": "0A974B78-02BF-4C79-ADF3-90CFBA1B55B1",
    "TemplateContent": "亲爱的会员！阿里云短信服务祝您新年快乐！",
    "TemplateName": "阿里云短信测试模板",
    "TemplateType": "1",
    "CreateDate": "2019-06-04 11:42:17",
    "Code": "OK",
    "Reason": "无审批备注",
    "TemplateStatus": "1"
    }
     * @throws Exception
     */
    public static QuerySmsTemplateResponse querySmsTemplate(Client client, String templateCode) throws Exception {
        QuerySmsTemplateRequest req = new QuerySmsTemplateRequest()
                .setTemplateCode(templateCode);
        return client.querySmsTemplate(req);
    }



    public static void main(String[] args_) throws Exception {

        Client client = AliYunSmsBase.createClient("LTAI4G528qGjxsg7e8tm37BU", "******************************");
//         //短信类型
//        Integer templateType = 0;
//        // 模板名称
//        String templateName = "验证手机号的验证码";
//        // 模板内容
//        String templateContent = "验证码${code}，您正在验证手机号，若非本人操作，请勿泄露。";
//        // 短信模板申请说明
//        String remark = "短信通知客户绑定手机号的验证码,code变量为4位数的随机数";
//        AddSmsTemplateResponse addSmsTemplateResp = addSmsTemplate(client, templateType, templateName, templateContent, remark);
        QuerySmsTemplateListRequest querySmsTemplateListRequest = new QuerySmsTemplateListRequest()
                .setPageIndex(1)
                .setPageSize(40);
        QuerySmsTemplateListResponse querySmsTemplateListResponse = client.querySmsTemplateList(querySmsTemplateListRequest);
        QuerySmsTemplateListResponseBody body = querySmsTemplateListResponse.getBody();

//        JSONObject jsonObject = new JSONObject();
//
//        JSONObject jsonObject1 = new JSONObject();
//
//        jsonObject1.put("name", "xxx");
//
//        jsonObject.put("object",jsonObject1.toJSONString());
//        System.out.println(jsonObject.toJSONString());

    }

}
