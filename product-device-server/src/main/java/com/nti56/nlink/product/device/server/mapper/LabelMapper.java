package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import com.nti56.nlink.product.device.server.entity.AbsolutePathLabelEntity;
import com.nti56.nlink.product.device.server.entity.LabelEntity;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.model.label.dto.EditLabelAliasDTO;
import com.nti56.nlink.product.device.server.model.label.dto.LabelDTO;
import com.nti56.nlink.product.device.server.model.label.dto.MatchPropertyLabelDto;
import com.nti56.nlink.product.device.server.model.label.dto.PageLabelDTO;
import com.nti56.nlink.product.device.server.model.label.dto.ProofreadLabelDTO;
import com.nti56.nlink.product.device.server.model.label.vo.LabelDuplicateRemovalVO;
import com.nti56.nlink.product.device.server.model.label.vo.LabelVO;
import com.nti56.nlink.product.device.server.model.label.vo.PageLabelVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Set;

/**
 * 类说明: 标签mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:14
 * @since JDK 1.8
 */
public interface LabelMapper extends CommonMapper<LabelEntity> {

    List<LabelBo> listLabelByIds(@Param("labelIds") List<Long> labelIds, @Param("tenantId") Long tenantId);

    @Select({
        "<script>",
            "SELECT",
                "l.id,l.name ,l.data_type,l.is_array,r.device_id,r.property_name",
            "FROM label l",
                "LEFT JOIN label_bind_relation r ON l.id = r.label_id",
            "where ",
                "l.deleted = 0 ",
                "And l.tenant_id = #{tenantIsolation.tenantId} ",
                "<if test='relations != null and relations.size() >0'> ",
                    "And l.id in ",
                    "<foreach collection='relations' item='relation' open='(' separator=',' close=')'> ",
                        "(select r.label_id from label_bind_relation r where r.tenant_id = #{tenantIsolation.tenantId} AND r.device_id = #{relation.deviceId} AND r.property_name = #{relation.propertyName}) ",
                    "</foreach> ",
                "</if> ",
        "</script> "
    })
    @Deprecated
    List<LabelBindRelationDto> listByRelations(@Param("relations") List<LabelBindRelationDto> relations,
                                                @Param("tenantIsolation") TenantIsolation tenantIsolation);

    List<CountByIdDTO> countByLabelGroupIds(@Param("tenantId") Long tenantId, @Param("labelGroupIds") List<Long> labelGroupIds);

    List<GatherParamField> listGatherParam(@Param("tenantId") Long tenantId, @Param("edgeGatewayId") Long edgeGatewayId);
    
    List<ProofreadLabelDTO> listProofreadDataByLabelGroupId(Long labelGroupId);

    @Select("SELECT * FROM label WHERE id = #{id} AND tenant_id = #{tenantId} AND deleted = 0")
    LabelEntity getById(@Param("tenantId") Long tenantId, @Param("id") Long id);

    @Update("UPDATE label SET gather_param = #{gatherParam} WHERE id = #{id} AND tenant_id = #{tenantId} AND deleted = 0")
    Integer updateGatherParam(@Param("tenantId") Long tenantId, 
                                @Param("id") Long id, 
                                @Param("gatherParam") GatherParamField gatherParam);

    @Select("SELECT id FROM label WHERE label_group_id = #{labelGroupId} AND tenant_id = #{tenantId} AND deleted = 0")
    List<Long> listIdByLabelGroupId(@Param("tenantId") Long tenantId, @Param("labelGroupId") Long labelGroupId);

    List<LabelVO> listVOByLabelGroupId(Long labelGroupId);
    
    List<LabelBo> listLabelByLabelIds(@Param("tenantId") Long tenantId,@Param("labelIds") List<Long> labelIds);

    Page<PageLabelVO> pageLabel(IPage<PageLabelVO> labelPage, @Param("dto") PageLabelDTO dto, @Param("tenantIsolation") TenantIsolation tenantIsolation);

    Page<PageLabelVO> pageLabelWithFullName(IPage<PageLabelVO> labelPage, @Param("dto") PageLabelDTO dto, @Param("tenantIsolation") TenantIsolation tenantIsolation);

    List<LabelDTO> labelWithFullName(@Param("labelGroupIds") List<Long> labelGroupIds, @Param("tenantIsolation") TenantIsolation tenantIsolation);

    List<LabelEntity> listByEditLabelAlias(@Param("dto") EditLabelAliasDTO dto,@Param("tenantIsolation") TenantIsolation tenantIsolation);

    void saveOrUpdateBatch(@Param("list") List<LabelEntity> labelEntities);

    List<LabelBo> listLabelByLabelGroupId(@Param("tenantId") Long tenantId,@Param("labelGroupId") Long labelGroupId);

    Integer batchUpdateGatherParam(@Param("tenantId") Long tenantId, @Param("gatherParamList") List<GatherParamField> gatherParamList);


    void updateLabelGroupIdAndSetGatherParamNullByLabelIds(@Param("labelIds")List<Long> labelIds, @Param("targetLabelGroupId")Long targetLabelGroupId, @Param("tenantIsolation")TenantIsolation tenantIsolation);

    List<String> listRepeatNameByLabelGroupId(@Param("targetLabelGroupId")Long targetLabelGroupId,@Param("tenantIsolation") TenantIsolation tenantIsolation);

    void deleteByEdgeGatewayId(@Param("edgeGatewayId")Long edgeGatewayId);


    void saveBatchByNative(@Param("list")List<LabelEntity> list);

    LabelBindRelationBo getLabelDetail(@Param("labelId") Long labelId);

    void batchUpdateInterval(@Param("ids") List<Long> ids, @Param("intervalMs") Long intervalMs, @Param("tenantId") Long tenantId);

    List<LabelEntity> listByLabelGroupIds(@Param("modelLabelRequestBo") ModelLabelRequestBo modelLabelRequestBo, @Param("tenantIsolation") TenantIsolation tenantIsolation);


    void physicalDeleteByEdgeGatewayId(@Param("edgeGatewayId")Long edgeGatewayId);

    @Select({
            "<script> ",
            "SELECT l.* ,g.name labelGroupName,c.name channelName,c.id channelId,e.name edgeGatewayName,e.id edgeGatewayId " ,
                    "FROM label l " ,
                    "LEFT JOIN label_group g ON l.label_group_id = g.id " ,
                    "LEFT JOIN channel c ON g.channel_id = c.id " ,
                    "LEFT JOIN edge_gateway e ON e.id = c.edge_gateway_id " ,
                    "WHERE c.DELETED = 0 AND g.DELETED = 0 AND l.DELETED = 0 AND e.DELETED = 0 " ,
                    "AND l.tenant_id = #{tenantId} AND g.tenant_id = #{tenantId} AND c.tenant_id = #{tenantId}  AND e.tenant_id = #{tenantId} " ,
                    "AND c.name = #{channelName} " ,
                    "AND g.name = #{groupName} ",
                    "AND e.id = #{edgeGatewayId} ",
            "</script> "
    })
    List<AbsolutePathLabelEntity> listByAbsolutionName(@Param("tenantId") Long tenantId,@Param("edgeGatewayId") Long edgeGatewayId,@Param("channelName") String channelName,@Param("groupName") String groupName);

    @Select({
            "<script> ",
            "SELECT l.id labelId,l.NAME labelName ,g.name labelGroupName,c.name channelName,c.edge_gateway_id edgeGatewayId  " ,
                    "FROM label l " ,
                    "LEFT JOIN label_group g ON l.label_group_id = g.id " ,
                    "LEFT JOIN channel c ON g.channel_id = c.id " ,
                    "WHERE c.DELETED = 0 AND g.DELETED = 0 AND l.DELETED = 0  " ,
                    "AND l.tenant_id = #{tenantId} AND g.tenant_id = #{tenantId} AND c.tenant_id = #{tenantId}  " ,
                    "AND g.channel_id = #{channelId} ",
                    "AND c.edge_gateway_id = #{edgeGatewayId} ",
            "</script> "
    })
    List<MatchPropertyLabelDto> listMatchPropertyLabelDto(@Param("tenantId") Long tenantId, @Param("edgeGatewayId") Long edgeGatewayId, @Param("channelId") Long channelId);

    @Select({
            "<script> ",
            "SELECT l.* ,g.name labelGroupName,c.name channelName,c.id channelId,e.name edgeGatewayName,e.id edgeGatewayId " ,
            "FROM label l " ,
            "LEFT JOIN label_group g ON l.label_group_id = g.id " ,
            "LEFT JOIN channel c ON g.channel_id = c.id " ,
            "LEFT JOIN edge_gateway e ON e.id = c.edge_gateway_id " ,
            "WHERE c.DELETED = 0 AND g.DELETED = 0 AND l.DELETED = 0 AND e.DELETED = 0 " ,
            "AND l.tenant_id = #{tenantId} AND g.tenant_id = #{tenantId} AND c.tenant_id = #{tenantId}  AND e.tenant_id = #{tenantId} " ,
            "<if test='channelNames != null and channelNames.size() >0'> ",
            "<foreach collection='channelNames' item='channelName' open='And ( ' separator=' OR ' close=' )'> ",
            " c.name = #{channelName} ",
            "</foreach> ",
            "</if> ",
            "AND e.id = #{edgeGatewayId} ",
            "</script> "
    })
    List<AbsolutePathLabelEntity> listByEdgeGatewayAndChannelNames(@Param("tenantId") Long tenantId,@Param("edgeGatewayId") Long edgeGatewayId,@Param("channelNames") Set<String> channelName);
}
