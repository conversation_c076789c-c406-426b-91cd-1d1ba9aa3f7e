package com.nti56.nlink.product.device.server.model;

import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import lombok.Data;

import java.util.List;

@Data
public class GatherInfoBo {
    private List<GatherParamField> gatherParamList;
    private List<ChannelRuntimeInfoField> channelRuntimeInfoList;
}
