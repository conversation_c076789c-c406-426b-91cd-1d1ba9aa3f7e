//package com.nti56.nlink.product.device.server.model.neo4j;
//
//import lombok.Data;
//import org.neo4j.ogm.annotation.*;
//
//
///**
// * 类说明：
// *
// * @ClassName ActedIn
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/11/17 10:32
// * @Version 1.0
// */
//
//
////@RelationshipEntity("ACTED_IN")
//@Data
//public class ActedIn {
//
//    @Id @GeneratedValue
//    private Long relationshipId;
//    @Property
//    private String title;
//
//    @StartNode
//    private PersonDTO personDTO;
//
//    @EndNode
//    private MovieDTO movieDTO;
//}
//
