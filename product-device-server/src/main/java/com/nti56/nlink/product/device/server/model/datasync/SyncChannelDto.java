package com.nti56.nlink.product.device.server.model.datasync;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/********************************************************************************
 *
 * Title: 
 * Description: 
 * <AUTHOR>
 * create date on 2023/5/11
 * @version 1.0.0
 *
 *******************************************************************************/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "通道对象")
public class SyncChannelDto {
    
    /**
     * id
     */
    private Long id;
    
    /**
     * 通道名称
     */
    @Schema(description = "通道名字")
    private String name;
    
    /**
     * 所属驱动类型
     */
    @Schema(description = "所属驱动类型")
    private Integer driver;
    
    /**
     * 自定义协议名称
     */
    @Schema(description = "自定义协议名称")
    private String customDriverName;
    
    /**
     * 是否是服务端通道
     */
    @Schema(description = "是否是服务端通道")
    private Boolean isServer;
    
    /**
     * 描述
     */
    @Schema(description = "描述")
    private String descript;
    
    /**
     * 时间间隔，单位毫秒
     */
    @Schema(description = "时间间隔，单位毫秒")
    private Integer intervalMs;
    
    /**
     * 通道状态：0-停用，1-启用
     */
    @Schema(description = "通道状态：0-停用，1-启用")
    private Integer status;
}
