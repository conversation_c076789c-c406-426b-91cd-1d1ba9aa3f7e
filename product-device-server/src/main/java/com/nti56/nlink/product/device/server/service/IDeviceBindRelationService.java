package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.model.*;

import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:11:44
 * @since JDK 1.8
 */
public interface IDeviceBindRelationService extends IBaseService<DeviceEntity> {


    Result<List<DeviceRespondBo>> deviceBindRelationBatchDelete(TenantIsolation tenantIsolation, List<Long> ids);


    Result<Void> createOrEditBindRelation(TenantIsolation tenantIsolation, DeviceRequestBo requestBo);

    Result<List<DeviceRespondBo>> deviceBatchChannelBind(TenantIsolation tenantIsolation, DeviceRequestBo requestBo);

    Result<List<DeviceRespondBo>> deviceBatchLabelGroupBind(TenantIsolation tenantIsolation, DeviceRequestBo requestBo, Integer operate, Integer level);

    Result<List<DeviceEntity>> getRelationByGroup(TenantIsolation tenantIsolation, Long edgeGatewayId, Long channelName, String labelGroupName);
}
