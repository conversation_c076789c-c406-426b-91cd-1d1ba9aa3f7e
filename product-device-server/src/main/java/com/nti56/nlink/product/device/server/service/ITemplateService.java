package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.TemplateEntity;
import com.nti56.nlink.product.device.server.model.template.dto.CreateTemplateDTO;
import com.nti56.nlink.product.device.server.model.template.dto.EditTemplateDTO;
import com.nti56.nlink.product.device.server.model.template.dto.TemplateDTO;
import com.nti56.nlink.product.device.server.model.template.vo.TemplateVO;

import java.util.List;


public interface ITemplateService extends IService<TemplateEntity> {
    Result createTemplate(CreateTemplateDTO dto, TenantIsolation tenantIsolation);

    Result deleteTemplate(Long id, TenantIsolation tenantIsolation);

    Result editTemplate(EditTemplateDTO dto, TenantIsolation tenantIsolation);

    Result<Integer> countByChannelId(Long id, TenantIsolation tenantIsolation);

    Result<Page<TemplateVO>> pageTemplate(PageParam pageParam, TemplateDTO dto, TenantIsolation tenantIsolation);

    Result<TemplateEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation);

    Result<List<TemplateVO>> listTemplate(TemplateDTO dto, TenantIsolation tenantIsolation);
}
  