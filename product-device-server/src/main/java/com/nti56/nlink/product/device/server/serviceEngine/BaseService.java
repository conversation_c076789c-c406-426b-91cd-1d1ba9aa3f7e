package com.nti56.nlink.product.device.server.serviceEngine;

import cn.hutool.core.util.ObjectUtil;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.InputDataField;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.datamodel.DataModel;
import com.nti56.nlink.product.device.server.domain.thing.datamodel.DataModelProperty;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceDataResource;
import com.nti56.nlink.product.device.server.entity.DataModelEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.DataModelMapper;
import com.nti56.nlink.product.device.server.mapper.DataModelPropertyMapper;
import com.nti56.nlink.product.device.server.scriptApi.Engineering;
import com.nti56.nlink.product.device.server.scriptApi.EngineeringSpi;
import com.nti56.nlink.product.device.server.scriptApi.SpiUtil;
import com.nti56.nlink.product.device.server.scriptApi.Thing;
import com.nti56.nlink.product.device.server.scriptApi.ThingSpi;
import com.nti56.nlink.product.device.server.util.ApplicationContextUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName Service
 * @date 2022/4/14 16:32
 * @Version 1.0
 */
@Slf4j
public abstract class BaseService {

    private Long tenantId;

    @Getter @Setter
    private String serviceName;

    protected Map<String,Object> input;

    private Object output;

    protected com.nti56.nlink.product.device.client.model.dto.json.scriptApi.Service serviceDefined;

    private DataModelMapper dataModelMapper;

    private DataModelPropertyMapper dataModelPropertyMapper;

    private CommonFetcher commonFetcher;

    protected ServiceEngine engine;

    @Getter 
    protected Thing thing;

    protected Engineering engineering;

    @Getter @Setter
    private Long deviceId;

    @Getter @Setter
    private DeviceServiceLogEntity logEntity;

    @Getter
    private DeviceDataResource deviceDataResource;

    public Result<Void> initBaseService(Map<String, Object> inputData, Thing thing, Engineering engineering, DeviceDataResource deviceDataResource ,Long tenantId) {
        this.thing = thing;
        this.tenantId = tenantId;
        this.engineering = engineering;
        this.input = inputData;
        this.deviceDataResource = deviceDataResource;
        Result<Void> r = initService(input, serviceDefined);
        if (!r.getSignal()) {
            return r;
        }
        this.dataModelMapper = ApplicationContextUtil.getBean("dataModelMapper",DataModelMapper.class);
        CommonFetcherFactory commonFetcherFactory = ApplicationContextUtil.getBean("commonFetcherFactory",CommonFetcherFactory.class);
        this.commonFetcher = commonFetcherFactory.buildCommonFetcher(this.tenantId);
        this.dataModelPropertyMapper = ApplicationContextUtil.getBean("dataModelPropertyMapper",DataModelPropertyMapper.class);
        if (!this.checkInput()) {
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        ServiceEngine engine = ApplicationContextUtil.getBean("serviceEngineFactory", ServiceEngineFactory.class).getServiceEngine();
        if (!Optional.ofNullable(engine).isPresent()) {
            return Result.error(ServiceCodeEnum.SERVICE_ENGINE_CREATE_FAIL);
        }
        this.engine = engine;
        return Result.ok();
    }

    void definedBaseService(com.nti56.nlink.product.device.client.model.dto.json.scriptApi.Service service){
        this.serviceDefined = service;
    }

    /**
     * 钩子方法，供子类实现
     * @param input
     * @param service
     * @return
     */
    public Result<Void> initService(Map<String, Object> input, com.nti56.nlink.product.device.client.model.dto.json.scriptApi.Service service){
        return Result.ok();
    }


    /**
     * 最终要执行的任务，要放多线程里支持
     * @return
     */
    abstract Object doTask();

    public R callService(){
        R r = engine.doService(this);

        return r;
    }

    /**
     * 入参校验
     * @return
     */
    private Boolean checkInput() {
        InputDataField[] inputDefined = serviceDefined.getInputDataDefined();
        if ((Optional.ofNullable(input).isPresent() && !Optional.ofNullable(inputDefined).isPresent())) {
            log.warn("入参或入参定义不存在！");
            return false;
        }
        return checkParamValue(inputDefined,input);
    }

    /**
     * 入参校验
     * @param inputDefined
     * @param input
     * @return
     */
    private boolean checkParamValue(InputDataField[] inputDefined, Map<String,Object> input){
        for (InputDataField defined : inputDefined) {
            if (!input.containsKey(defined.getName())) {
                continue;
            }
            ThingDataTypeEnum dataType = ThingDataTypeEnum.typeOfValue(defined.getDataType());
            if (dataType == null) {
                return false;
            }
            Object o;
            try {
                o = getArrayElements(defined.getIsArray(),input.get(defined.getName()),dataType);
            }catch (BizException e){
                log.warn(e.getMessage());
                return false;
            }
            if (!Optional.ofNullable(o).isPresent()) {
                continue;
            }

            if (ThingDataTypeEnum.DATA_MODEL.equals(dataType)) {
                //定义了数据模型传参也需要用Map
                Map<String, Object> i = (Map<String, Object>) o;
                Long dataModelId = defined.getDataModelId();
                DataModelEntity modelEntity = dataModelMapper.selectById(dataModelId);
                Result<DataModel> modelResult = DataModel.checkInfo(modelEntity, commonFetcher);
                if (!modelResult.getSignal()) {
                    log.warn(modelResult.getMessage());
                    return false;
                }
                if (!checkDataModel(modelResult.getResult(),i)) {
                    log.warn("数据模型入参校验失败，入参：{}",i);
                    return false;
                }
            }else {
                if (!checkBaseType(dataType,o)) {
                    log.warn("入参校验失败，入参定义：{}，值：{}",defined,o);
                    return false;
                }
            }
        }
        return true;
    }

    private Object getArrayElements(Boolean isArray, Object o,ThingDataTypeEnum dataType) {
        if (ObjectUtil.isEmpty(o) || StringUtils.isEmpty(o.toString().trim())) {
            return null;
        }
        if (!((o.getClass().isArray() == isArray) || (o instanceof List == isArray))) {
            throw new BizException(ServiceCodeEnum.THING_SERVICE_PARAM_TYPE_ERROR);
        }
        if (isArray) {
            if (o.getClass().isArray()) {
                o = Arrays.asList(o);
            }
            List list = (List) o;
            if (list.size() <= 0) {
                return null;
            }
            if (!SpiUtil.checkArrayParam(dataType,list)) {
                throw new BizException(ServiceCodeEnum.THING_SERVICE_ARRAY_PARAM_ERROR);
            }
            o = list.get(0);
        }
        return o;
    }

    /**
     * 数据模型校验
     * @param dataModel
     * @param input
     * @return
     */
    private boolean checkDataModel(DataModel dataModel,Map<String,Object> input){
        Boolean signal = false;
        for (DataModelProperty property : dataModel.getProperties()) {
            if (!input.containsKey(property.getName())) {
                continue;
            }
            Object o;
            try {
                //TODO:需要将字符串转成数组并赋值给input
                o = getArrayElements(property.getIsArray(),input.get(property.getName()),property.getDataType());
            }catch (BizException e){
                log.warn(e.getMessage());
                return false;
            }
            if (!Optional.ofNullable(o).isPresent()) {
                continue;
            }
            if (ThingDataTypeEnum.DATA_MODEL.equals(property.getDataType())) {
                if (!checkDataModel(property.getDataModel(), (Map<String, Object>) o)) {
                    return false;
                }
            }else {
                if (!checkBaseType(property.getDataType(),o)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 基础类型校验
     * @param dataType
     * @param o
     * @return
     */
    public static boolean checkBaseType(ThingDataTypeEnum dataType, Object o){
        BigDecimal bigDecimal = SpiUtil.toBigDecimal(o);
        if(bigDecimal == null && (dataType!=ThingDataTypeEnum.BOOLEAN)
        && dataType != ThingDataTypeEnum.BYTE && dataType != ThingDataTypeEnum.STRING){
            try{
                bigDecimal = new BigDecimal(String.valueOf(o));
            }catch (Exception e){
                log.error("属性类型转换异常");
            }
        }
        switch (dataType){
            case BOOLEAN:
                if (checkType(o,Boolean.class)) {
                    return true;
                }
                break;
            case BYTE:
            case WORD:
            case DWORD:{
                if (ObjectUtil.isEmpty(bigDecimal) || bigDecimal.compareTo(new BigDecimal(0)) < 0) {
                    return false;
                }
            }
            case CHAR:
            case SHORT:
            case LONG:
            case FLOAT:
            case DOUBLE:
                if (ObjectUtil.isEmpty(bigDecimal)) {
                    return false;
                }
                return true;
            case STRING:
                if (checkType(o,String.class)) {
                    return true;
                }
                break;
            default:
        }
        return false;
    }

    /**
     * 类型校验
     * @param o
     * @param c
     * @return o 如果属于c类则返回true
     */
    private static boolean checkType(Object o,Class c){
        if (c.isInstance(o)) {
            return true;
        }
        return false;
    }

    public abstract String initServiceCode(String functionName);
}
