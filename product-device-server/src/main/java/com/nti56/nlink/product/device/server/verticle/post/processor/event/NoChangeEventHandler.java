package com.nti56.nlink.product.device.server.verticle.post.processor.event;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.condition.TriggerConditionElm;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.EventDpo;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.*;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpEventTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import com.nti56.nlink.product.device.server.model.device.dto.NoChangeEventInstance;
import com.nti56.nlink.product.device.server.model.product.vo.DeviceVO;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.verticle.post.processor.PostProcessorHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-24 17:22:41
 * @since JDK 1.8
 */
@Component
@Slf4j
public class NoChangeEventHandler extends PostProcessorHandler<GwUpEventTopic.TopicInfo> {

    @Autowired
    private IDeviceModelService deviceModelService;

    @Autowired
    private ISubscriptionService subscriptionService;

    @Override
    public void process(GwUpEventTopic.TopicInfo topicInfo, UpData upData){
        super.process(topicInfo,upData);
    }

    @Override
    public void doProcess(GwUpEventTopic.TopicInfo topicInfo, UpData upData) {
        handleNoChangeEvent(topicInfo,upData);
    }

    private void handleNoChangeEvent(GwUpEventTopic.TopicInfo topicInfo, UpData upData) {
        log.info("handleNoChangeEvent: {}, {}", topicInfo, upData);
        Long deviceId = Long.valueOf(topicInfo.getDeviceId());
        Long tenantId = Long.valueOf(topicInfo.getTenantId());
        TenantIsolation tenantIsolation = new TenantIsolation();
        tenantIsolation.setTenantId(tenantId);
        Result<DeviceVO> deviceModelResult = deviceModelService.getDeviceModel(deviceId, tenantIsolation);
        if(!deviceModelResult.getSignal()){
            log.warn("cant find any device model.check whether device{} is exist first!",deviceId);
            return;
        }

        NoChangeEventInstance noChangeEventInstance = new NoChangeEventInstance();
        noChangeEventInstance.setProperty(topicInfo.getEventName());
        noChangeEventInstance.setUpData(upData);
        subscriptionService.sendNoChangeEventSubscription(deviceId, tenantId,  noChangeEventInstance);

    }

}
