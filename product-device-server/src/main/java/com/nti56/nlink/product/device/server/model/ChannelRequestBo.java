package com.nti56.nlink.product.device.server.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明:
 *
 * @version 1.0
 * @since JDK 1.8
 */
@Data
@Schema(description = "通道请求对象")
public class ChannelRequestBo {

    @Schema(description = "时间间隔，单位毫秒")
    private Integer intervalMs;

    /**
     * 通道状态：0-停用，1-启用
     */
    @Schema(description = "通道状态：0-停用，1-启用")
    private Integer status;

    @Schema(description = "选中的通道Id")
    private List<Long> ids;

}
