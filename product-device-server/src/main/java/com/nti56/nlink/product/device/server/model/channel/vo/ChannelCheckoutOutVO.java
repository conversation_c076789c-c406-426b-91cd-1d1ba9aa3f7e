package com.nti56.nlink.product.device.server.model.channel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/6/23 16:39<br/>
 * @since JDK 1.8
 */
@Data
public class ChannelCheckoutOutVO {
    
    @Schema(description = "通道ID")
    private Long id;

    @Schema(description = "通道名字")
    private String name;

    @Schema(description = "所属驱动类型，1-Snap7，2-Modbus，3-OPC UA，4-ZigBee，5-BLE")
    private Integer driver;
    
    @Schema(description = "驱动类型名称")
    private String driverName;

    @Schema(description = "ip地址")
    private String ip;
    
    @Schema(description = "端口")
    private String port;
    
    private String rack;
    
    private String slot;
    
    @Schema(description = "用户名")
    private String userName;
    
    @Schema(description = "密码")
    private String password;
    
    @Schema(description = "连接状态")
    private String connectionStatus;

    @Schema(description = "边缘网关Id")
    private Long edgeGatewayId;

}
