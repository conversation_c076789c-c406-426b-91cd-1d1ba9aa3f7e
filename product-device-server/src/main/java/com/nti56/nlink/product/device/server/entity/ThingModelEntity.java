package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 类说明: 物模型表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:08:44
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("thing_model")
@Schema
public class ThingModelEntity {

    /**
     * id
     */
    @Schema(description = "物模型主键ID")
    private Long id;
    /**
     * 物模型名称，如：灯、风扇
     */
    @Schema(description = "物模型名称")
    private String name;
    /**
     * 物模型定义
     */
    @Schema(description = "物模型定义")
    private ModelField model;

    /**
     * 描述
     */
    private String descript;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;
    
    /**
     * 工程id
     */
    @Schema(description = "工程id")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 空间id
     */
    @Schema(description = "空间id")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 模块id
     */
    @Schema(description = "模块id")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat()
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @Schema(description = "修改人id")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 修改人名称
     */
    @Schema(description = "修改人名称")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableLogic
    private Integer deleted;

    /**
     * 模型类型 0-普通 1-故障
     */
    @Schema(description = "模型类型")
    private Integer modelType;

}
