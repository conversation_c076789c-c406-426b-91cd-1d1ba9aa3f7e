package com.nti56.nlink.product.device.server.model.engineering.dto;

import com.nti56.nlink.engine.client.model.dto.RuleEngineServerDataDTO;
import com.nti56.nlink.product.device.client.model.dto.engineering.NotifyServiceDataDTO;
import com.nti56.nlink.product.device.client.model.dto.engineering.ProductDeviceServerDataDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/3 13:31<br/>
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "工程文件dto")
public class EngineeringDocInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long tenantId;

    private String platformVersion;

    private String password;

    private ProductDeviceServerDataDTO productDeviceServerData;

    private CommonServiceDataDTO commonServiceData;

    private RuleEngineServerDataDTO ruleEngineServerData;

    private NotifyServiceDataDTO notifyServiceData;



}
