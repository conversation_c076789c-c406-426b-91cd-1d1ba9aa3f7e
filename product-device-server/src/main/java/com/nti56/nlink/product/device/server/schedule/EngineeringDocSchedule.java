package com.nti56.nlink.product.device.server.schedule;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/19 16:23<br/>
 * @since JDK 1.8
 */

@Slf4j
@Component
public class EngineeringDocSchedule {


//    @Autowired
//    private IEngineeringDocService engineeringDocService;
//
//    @Autowired
//    private ITenantService tenantService;
//
//
//    @Scheduled(fixedDelay = 1000*3 , initialDelay = 1000*3)
//    public void backupEngineeringDoc(){
//
//        log.info("开始备份工程文件");
//        List<TenantEntity> tenantList = tenantService.list();
//        Date now = new Date();
//        Instant instant = now.toInstant();
//        ZoneId zoneId = ZoneId.systemDefault();
//        LocalDateTime localDateTime = instant.atZone(zoneId).withMinute(0).withSecond(0).withNano(0).toLocalDateTime();
//        for (TenantEntity tenantEntity : tenantList) {
//            Result<EngineeringDocEntity> theLatestDataResult = engineeringDocService.getTheLatestAutoData(tenantEntity.getId());
//            Integer versionNum = 1;
//            EngineeringDocEntity theLatestData = theLatestDataResult.getResult();
//            if (theLatestData != null){
//                versionNum  = Integer.parseInt(theLatestData.getName().replace("version", "")) + 1;
//
//            }
//            EngineeringDocEntity engineeringDocEntity = new EngineeringDocEntity();
//            engineeringDocEntity.setName("version" + versionNum);
//            engineeringDocEntity.setCreateTime(localDateTime);
//            engineeringDocEntity.setGenerateType(0);
//            engineeringDocEntity.setTenantId(tenantEntity.getId());
//            engineeringDocService.save(engineeringDocEntity);
//            //查询工程文件数据
//            //序列化数据保存成文件
//            ApplicationHome h = new ApplicationHome(this.getClass());
//            File tenantFile = new File(h.getSource().getParentFile().getParent()+"\\engineeringDoc",tenantEntity.getId()+"");
//            if(!tenantFile.exists()){
//                tenantFile.mkdirs();
//            }
//            File engineerDocFile = new File(tenantFile, engineeringDocEntity.getId() + ".nti");
//            try {
//                engineerDocFile.createNewFile();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//
//
//
//
//
//
//
//
//    }
//
//    public static void main(String[] args) throws IOException {
//        ApplicationHome h = new ApplicationHome(com.nti56.nlink.user.server.schedule.EngineeringDocSchedule.class);
//        File jarF = h.getSource();
//        System.out.println(new File(jarF.getParentFile().getParentFile(),"123").toString());
//    }






}
