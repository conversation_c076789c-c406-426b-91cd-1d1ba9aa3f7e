package com.nti56.nlink.product.device.server.model.deviceModel;

import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.ServiceElm;
import lombok.Data;

import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName DeviceModelBo
 * @date 2022/4/27 10:00
 * @Version 1.0
 */
@Data
public class DeviceModelBo {

    private List<PropertyBo> properties;

    private List<EventElm> events;

    private List<ServiceElm> services;

}
