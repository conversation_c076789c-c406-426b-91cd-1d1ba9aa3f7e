package com.nti56.nlink.product.device.server.job;


import com.nti56.nlink.product.device.server.job.callble.JobCallable;
import com.nti56.nlink.product.device.server.service.IJobService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName LogCronJob
 * @date 2022/8/15 10:05
 * @Version 1.0
 */
@Component
@Slf4j
public class LogCronJob {

    @Autowired
    IJobService jobService;

    @Autowired
    ExecutorService logCronJobExecutor;

    @XxlJob("serviceLogExpiredHandler")
    public void processExpiredServiceLog(){
        log.info("exec job:{}",jobService.getClass().getName());
        JobCallable jobCallable=new JobCallable(jobService);
        logCronJobExecutor.submit(jobCallable);
    }
}
