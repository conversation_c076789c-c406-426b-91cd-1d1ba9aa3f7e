package com.nti56.nlink.product.device.server.enums;

import com.nti56.nlink.product.device.server.constant.RedisConstant;
import lombok.Getter;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/13 16:56<br/>
 * @since JDK 1.8
 */
public enum VerificationTypeEnum {

    EDIT_PASSWORD(1, "SMS_192375271", RedisConstant.EDIT_PASSWORD_VERIFICATION_CODE,"修改密码"),
    BIND_PHONE(2,"SMS_192375270",RedisConstant.BIND_PHONE_VERIFICATION_CODE,"绑定手机"),
    LOGIN(3, "SMS_192375274",RedisConstant.LOGIN_VERIFICATION_CODE,"登录"),
    //todo：gs模板需要修改
    VERIFY_PHONE(4, "SMS_192375275",RedisConstant.VERIFY_PHONE_VERIFICATION_CODE,"验证手机号"),
    REGISTER(5, "SMS_192375272",RedisConstant.REGISTER_VERIFICATION_CODE,"注册")

    ;

    @Getter
    private Integer value;

    @Getter
    private String templateCode;

    @Getter
    private String redisKeyPre;

    @Getter
    private String desc;

    VerificationTypeEnum(Integer value, String templateCode,String redisKeyPre, String desc) {
        this.value = value;
        this.redisKeyPre = redisKeyPre;
        this.templateCode = templateCode;
        this.desc = desc;
    }


    public static VerificationTypeEnum typeOfValue(Integer value){
        VerificationTypeEnum[] values = VerificationTypeEnum.values();
        for (VerificationTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

}
