package com.nti56.nlink.product.device.server.model.upgrade;

import java.io.File;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName CreateUpgradePackageRep
 * @date 2023/3/15 19:30
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateUpgradePackageRep {
    private Long id;
    private File file;
}
