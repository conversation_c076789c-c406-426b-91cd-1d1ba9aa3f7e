package com.nti56.nlink.product.device.server.model.deviceTemplate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.File;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName CreateDeviceTemplateFileRep
 * @date 2023/3/7 19:30
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateDeviceTemplateFileRep {
    private Long id;
    private File file;
}
