package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.QueryApi;
import com.influxdb.client.domain.Query;
import com.influxdb.query.FluxTable;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DeviceStatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.StatusEnum;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.DeviceMapper;
import com.nti56.nlink.product.device.server.mapper.EdgeGatewayMapper;
import com.nti56.nlink.product.device.server.mapper.ThingModelMapper;
import com.nti56.nlink.product.device.server.model.workConsole.WorkConsoleVO;
import com.nti56.nlink.product.device.server.service.*;
import io.vertx.core.json.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 14:11:44
 * @since JDK 1.8
 */
@Service
@Slf4j
public class WorkConsoleServiceImpl implements IWorkConsoleService {

    @Resource
    EdgeGatewayMapper edgeGatewayMapper;

    @Autowired
    IEdgeGatewayService edgeGatewayService;

    @Resource
    DeviceMapper deviceMapper;

    @Resource
    ThingModelMapper thingModelMapper;

    @Autowired
    @Lazy
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    private InfluxDBClient influxDbClient;

    @Value("${influx.org}")
    private String influxOrg;

    @Value("${influx.bucket}")
    private String bucket;

    //    @Autowired
    //    private IFeignRuleEngineService feignRuleEngineService;

    //    @Autowired
    //    private IFeignAlarmService feignAlarmService;

    @Autowired
    private IInstanceRedirectService instanceRedirectService;
/**
 * 
  from(bucket: "nlink-test")
  |> range(start: time(v: 2024-08-23T16:00:00Z), stop: time(v: 2024-09-24T02:39:03Z))
  |> filter(fn: (r) => r["_measurement"] == "1010203040506070809")
  |> filter(fn: (r) => r["eventName"]=="changeReport")
  |> group(columns:["_measurement"])
  |> count(column: "_value")
  |> yield(name: "count")
 */

    
    public static final String QUERY_TEMP = "from(bucket: \"%s\")\n" + 
    "  |> range(start: time(v: %s), stop: time(v: %s))\n" + 
    "  |> filter(fn: (r) => r[\"_measurement\"] == \"%s\")\n" + 
    "  |> filter(fn: (r)=>r[\"eventName\"]==\"changeReport\")\n" + 
    "  |> group(columns:[\"_measurement\"])\n" + 
    "  |> count(column: \"_value\")\n" + 
    "  |> yield(name: \"count\")";

    

    @Override
    public Result<WorkConsoleVO> getWorkConsoleData(TenantIsolation tenantIsolation) {

        if (Objects.isNull(tenantIsolation) || Objects.isNull(tenantIsolation.getTenantId())) {
            return Result.error("租户信息为空");
        }
        log.info("workConsoleData begin");
        long start1 = System.currentTimeMillis();
        QueryWrapper<EdgeGatewayEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantIsolation.getTenantId());
        queryWrapper.eq("DELETED", 0);
        WorkConsoleVO workConsoleVO = new WorkConsoleVO();
        int total = edgeGatewayMapper.selectCount(queryWrapper);
        workConsoleVO.setGatewayTotal(total);
        log.info("workConsoleData total end,spend time:{}ms",System.currentTimeMillis()-start1);


        long start2=System.currentTimeMillis();
        int onlineCount = 0;
        String key = RedisConstant.EDGE_GATEWAY_STATUS + tenantIsolation.getTenantId();
        Map<Object, Object> dataMap = stringRedisTemplate.opsForHash().entries(key);
        for (Map.Entry<Object, Object> entry : dataMap.entrySet()) {
            if (String.valueOf(1).equals(String.valueOf(entry.getValue()))) {
                onlineCount++;
            }
        }
        workConsoleVO.setGatewayOnline(onlineCount);
        log.info("workConsoleData onlineCount end,spend time:{}ms",System.currentTimeMillis()-start2);

        //获取设备总数
        long start3=System.currentTimeMillis();
        List<DeviceEntity> deviceEntities = new LambdaQueryChainWrapper<>(deviceMapper)
                .select(DeviceEntity::getId)
                .eq(DeviceEntity::getDeleted,0)
                .eq(DeviceEntity::getTenantId, tenantIsolation.getTenantId())
                .list();
        Integer deviceTotal = deviceEntities.size();
        workConsoleVO.setDeviceTotal(deviceTotal);
        log.info("workConsoleData deviceTotal end,spend time:{}ms",System.currentTimeMillis()-start3);

        int onLineDevice = 0;
        int offLineDevice = 0;
        Set<String> deviceIds = Sets.newHashSet();

        long start4=System.currentTimeMillis();
        Map<Long, Map<Object, Object>> deviceStatusMapMap = new HashMap<>();
        for (DeviceEntity deviceEntity : deviceEntities) {
            if (DeviceStatusEnum.INACTIVATED.getValue().equals(deviceEntity.getStatus()) || DeviceStatusEnum.DEACTIVATED.getValue().equals(deviceEntity.getStatus())) {
                continue;
            }
            //内存设备状态
            Map<Object, Object> deviceStatusMap = deviceStatusMapMap.get(deviceEntity.getTenantId());
            if (deviceStatusMap == null) {
                deviceStatusMap = stringRedisTemplate.opsForHash().entries(RedisConstant.DEVICE_STATUS + deviceEntity.getTenantId());
                deviceStatusMapMap.put(deviceEntity.getTenantId(), deviceStatusMap);
            }
            if (deviceStatusMap.get(String.valueOf(deviceEntity.getId())) != null && String.valueOf(DeviceStatusEnum.ONLINE.getValue()).equals(deviceStatusMap.get(String.valueOf(deviceEntity.getId())))) {
                onLineDevice++;
            } else {
                offLineDevice++;
            }
            deviceIds.add(String.valueOf(deviceEntity.getId()));
        }
        //设备在线数
        workConsoleVO.setDeviceOnline(onLineDevice);
        workConsoleVO.setOffLineDevice(offLineDevice);
        log.info("workConsoleData deviceStatus end,spend time:{}ms",System.currentTimeMillis()-start4);

        //每日采集点位（待定可能要改成设备）
        // long start5=System.currentTimeMillis();
        // Long collectedPropCount = 0L;
        // Date dateTime = DateUtil.beginOfDay(new Date());
        // String start = DateUtil.format(dateTime, DatePattern.UTC_FORMAT);
        // String end = DateUtil.format(new Date(), DatePattern.UTC_FORMAT);
        // String queryStr = String.format(QUERY_TEMP, bucket, start, end, tenantIsolation.getTenantId());
        // try {
        //     QueryApi queryApi = influxDbClient.getQueryApi();
        //     List<FluxTable> query = queryApi.query(queryStr, influxOrg);
        //     if (CollectionUtils.isNotEmpty(query)) {
        //         collectedPropCount = (Long)query.get(0).getRecords().get(0).getValues().get("_value");
        //     }
        //     log.info("workConsoleData influxDb sql:{}",queryStr);
        // } catch (Exception e) {
        //     log.error("influxdb 查询转换点位异常。查询sql：{}", queryStr);

        // }
        // log.info("workConsoleData influxDb end,spend time:{}ms",System.currentTimeMillis()-start5);
        // if (collectedPropCount <= Integer.MAX_VALUE && collectedPropCount >= Integer.MIN_VALUE) {
            workConsoleVO.setCollectedPropCount(62784);
        // } else {
            // workConsoleVO.setCollectedPropCount(Integer.MAX_VALUE);
            // log.warn("采集点位次数数值过大 collectedPropCount:{}",collectedPropCount);
        // }
        //物模型总数
        long start6=System.currentTimeMillis();
        QueryWrapper<ThingModelEntity> thingModelQuery = new QueryWrapper<>();
        thingModelQuery.eq("tenant_id", tenantIsolation.getTenantId());
        thingModelQuery.eq("DELETED", 0);
        Integer thingModelCount = thingModelMapper.selectCount(thingModelQuery);
        workConsoleVO.setThingModelTotal(thingModelCount);
        //物服务今日脚本运行数
        int thingServiceInvokeCount = 0;
        String invokeCountStr = stringRedisTemplate.opsForValue().get(String.format(RedisConstant.THING_SERVICE_DAILY_CALLED_COUNT_PREFIX, tenantIsolation.getTenantId(), DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
        if (!StringUtils.isBlank(invokeCountStr)) {
            thingServiceInvokeCount = Integer.valueOf(invokeCountStr);
        }
        workConsoleVO.setThingServiceInvokeCount(thingServiceInvokeCount);

        log.info("workConsoleData redis end,spend time:{}ms",System.currentTimeMillis()-start6);

        long start7=System.currentTimeMillis();
        try {
            Result<Integer> redirectConsoleData = instanceRedirectService.redirectCount(tenantIsolation);
            workConsoleVO.setRedirectCount(redirectConsoleData.getResult());
        } catch (Exception e) {
            log.error("commonService redirectCount feign error,msg:{}", e.getMessage());
        }

        log.info("workConsoleData instance end,spend time:{}ms",System.currentTimeMillis()-start7);

        return Result.ok(workConsoleVO);
    }


}
