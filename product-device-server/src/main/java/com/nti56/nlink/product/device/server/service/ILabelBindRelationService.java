package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.domain.thing.channel.Channel;
import com.nti56.nlink.product.device.server.domain.thing.label.Label;
import com.nti56.nlink.product.device.server.domain.thing.label.LabelGroup;
import com.nti56.nlink.product.device.server.entity.AbsolutePathLabelEntity;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.LabelBindRelationEntity;
import com.nti56.nlink.product.device.server.entity.LabelEntity;
import com.nti56.nlink.product.device.server.model.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 标签绑定关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-11 17:27:49
 * @since JDK 1.8
 */
public interface ILabelBindRelationService extends IBaseService<LabelBindRelationEntity> {

    Result<AbsolutePathLabelEntity> createLabelBindRelation(LabelBindRelationEntity relation);

    Result<List<LabelBindRelationBo>> list(Long deviceId,Long tenantId);

    Result<Void> deleteById(LabelBindRelationEntity relation);

    Result<LabelBindRelationEntity> getById(Long id);

    Result<Map<String, ServiceCodeEnum>> saveList(Long tenantId,Long deviceId, List<LabelBindRelationEntity> relations);

    Result<Void> unbindByGroupNames(Set<String> labelNames, Long edgeGatewayId, Long tenantId, String channelName);

    Result<List<LabelBindRelationEntity>> listByLabelId(Long labelId);

    Result<Integer> batchDelete(List<Long> ids,Long tenantId);

    void jdbcTemplateBatchSave(List<LabelBindRelationEntity> list, int batchSize);

    Result<List<DeviceChannelBo>> listDeviceChannel(Long deviceId,Long tenantId);

    void batchDeleteByDeviceIds(Long tenantId, List<Long> ids,List<DeviceEntity> deviceEntities);

    Result<List<LabelBindRelationDto>> getByAbsoluteLabelName(String labelGroupName,String labelName, Long edgeGatewayId, Long channelId, TenantIsolation tenantIsolation);

    Result<Void> unbind(List<AbsolutePathLabelEntity> list, Long tenantId);

    void updateByLabel(Label result, TenantIsolation tenantIsolation);
    
    void updateByLabelGroup(LabelGroup labelGroup, TenantIsolation tenantIsolation);
    
    void updateByChannel(Channel channel, TenantIsolation tenantIsolation);

    void clearLabelGroupBindRelation(LabelGroup labelGroup, TenantIsolation tenantIsolation);

    void clearLabelBindRelation(TenantIsolation tenantIsolation, List<LabelEntity> labelEntityList);

    Result<List<AdditionBindPreviewDto>> additionAddPreview(TenantIsolation tenantIsolation, AdditionLabelBindRelationDto bindRelationDto);

    Result<Void> additionAdd(TenantIsolation tenantIsolation, List<AdditionBindPreviewDto> bindRelationDto);

    List<LabelBindRelationEntity> allLabelBindRelation();

}
