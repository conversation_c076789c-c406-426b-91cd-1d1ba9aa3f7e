package com.nti56.nlink.product.device.server.model.device.vo;

import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/2/20 9:47<br/>
 * @since JDK 1.8
 */
@Data
public class DcmpDeviceVO {

    @Schema(description = "设备id")
    private Long id;

    @Schema(description = "设备名称")
    private String name;

    @Schema(description = "设备继承模型id")
    private Long modelId;

    @Schema(description = "设备继承模型名称")
    private String modelName;

    @Schema(description = "设备状态，2-已上线， 1-已离线，0-未激活")
    private Integer status;

    @Schema(description = "描述")
    private String descript;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "所属网关id")
    private Long edgeGatewayId;

    @Schema(description = "所属网关名称")
    private String edgeGatewayName;

    @Schema(description = "设备属性")
    private List<DcmpDevicePropertyVO> properties;


    @Schema(description = "绑定的标记集合")
    private List<TagRsp> tags;



}
