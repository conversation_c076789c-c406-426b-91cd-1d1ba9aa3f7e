package com.nti56.nlink.product.device.server.model.upgrade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @since JDK 1.8
 */
@Data
@Schema(description = "升级包批量操作")
public class BatchUpgradePackageDTO {
    
    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /**
     * 网关ID
     */
    @Schema(description = "网关ID")
    private Long edgeGatewayId;

}
