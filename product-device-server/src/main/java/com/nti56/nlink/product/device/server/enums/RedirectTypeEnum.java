package com.nti56.nlink.product.device.server.enums;

import lombok.Getter;

public enum RedirectTypeEnum {

    WEBHOOK((byte) 0,"webhook"),
    MQTT_SINK((byte) 1,"mqtt sink");

    @Getter
    private byte code;

    @Getter
    private String typeName;

    RedirectTypeEnum(byte code, String typeName) {
        this.code = code;
        this.typeName = typeName;
    }

    public static RedirectTypeEnum typeOfCode(byte code){
        RedirectTypeEnum[] values = RedirectTypeEnum.values();
        for (RedirectTypeEnum v : values) {
            if (v.code == code) {
                return v;
            }
        }
        return null;
    }

}
