package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.NotAssignGatewayEntity;
import com.nti56.nlink.product.device.server.model.notAssign.vo.NotAssignGatewayVo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-21 17:55:46
 * @since JDK 1.8
 */
@Mapper
public interface NotAssignGatewayMapper extends CommonMapper<NotAssignGatewayEntity> {

    Page<NotAssignGatewayVo> pageNotAssignGateway(IPage<NotAssignGatewayEntity> notAssignGatewayPage);
    
    @Delete("DELETE FROM not_assign_gateway WHERE 1=1")
    void clearNotAssignGateway();
    
    NotAssignGatewayEntity getAssignGateway(@Param("imei")String imei,@Param("host")String host,@Param("adminPort")Integer adminPort);

    void deleteNotAssignGateway(@Param("imei")String imei,@Param("host")String host,@Param("adminPort")Integer adminPort);
}
