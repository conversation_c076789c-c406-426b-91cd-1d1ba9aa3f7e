package com.nti56.nlink.product.device.server.model.send.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/13 16:56<br/>
 * @since JDK 1.8
 */
@Data
@Builder
public class NotifyTypeAndChannelParamsDTO {

    @Schema(description = "策略类型")
    private Integer type ;

    @Schema(description = "渠道参数")
    private JSONObject channelParams;
}
