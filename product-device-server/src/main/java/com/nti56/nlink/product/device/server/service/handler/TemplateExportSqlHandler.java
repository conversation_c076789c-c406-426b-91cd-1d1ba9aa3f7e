package com.nti56.nlink.product.device.server.service.handler;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nti56.nlink.common.export.handler.AbstractExportSqlHandler;
import com.nti56.nlink.common.util.GeneratorSqlUtil;
import com.nti56.nlink.product.device.server.entity.TemplateEntity;
import com.nti56.nlink.product.device.server.service.ITemplateService;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 类说明: 模版导出<br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/19 14:31<br/>
 * @version 1.0
 * @since JDK 1.8
 */
public class TemplateExportSqlHandler extends AbstractExportSqlHandler {
  @Override
  public void exportSqlDml(Long tenantId, List<String> sqlList) {
    exportTemplate(tenantId, sqlList);
    if (getNext() != null) {
      getNext().exportSqlDml(tenantId, sqlList);
    }
  }

  private void exportTemplate(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<TemplateEntity> queryWrapper = new LambdaQueryWrapper<TemplateEntity>().eq(TemplateEntity::getTenantId, tenantId);
    List<TemplateEntity> dtoList = SpringUtil.getBean(ITemplateService.class).list(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(TemplateEntity.class, dtoList));
    }
  }
}
