package com.nti56.nlink.product.device.server.model.label.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 11:27<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "标签dto")
public class EditLabelDTO {
    @NotNull(message = "唯一标识不能为空")
    private Long id;

    @Schema(description = "标签分组id")
    @NotNull(message = "标签分组不能为空")
    private Long labelGroupId;

    @Schema(description = "名称")
    @NotBlank(message = "标签名称不能为空")
    @Length(max = 128,message = "标签名称不能超过128个字符")
    private String name;

    @Schema(description = "别名")
    @Length(max = 256,message = "别名不能超过32个字符")
    private String alias;

    @Schema(description = "标签描述")
    @Length(max = 256,message = "标签描述不能超过256个字符")
    private String descript;

    @Schema(description = "地址，如DB50.DBB1")
    private String address;

    @Schema(description = "长度")
    private Integer length;

    @Schema(description = "数据类型，bool/byte/short/int/float/string")
    private String dataType;

    @Schema(description = "是否数组")
    private Boolean isArray;

    @Schema(description = "type是string类型时，表示string元素的byte长度，其他type类型放空")
    private Integer stringBytes;

    @Schema(description = "是否只读")
    private Boolean readOnly;

    @Schema(description = "时间间隔，单位毫秒")
    private Integer intervalMs;

}
