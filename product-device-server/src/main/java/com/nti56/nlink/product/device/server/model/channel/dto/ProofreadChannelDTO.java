package com.nti56.nlink.product.device.server.model.channel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 9:45<br/>
 * @since JDK 1.8
 */

@Data
@ToString
@Schema(description = "校对通道dto")
public class ProofreadChannelDTO implements Serializable {

    @Schema(description = "通道参数列表")
    private List<ProofreadChannelParamDTO> channelParamList;


}
