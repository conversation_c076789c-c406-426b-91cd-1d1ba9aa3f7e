package com.nti56.nlink.product.device.server.model.label.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/10/12 15:12<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "标签dto")
public class MoveOrCopyLabelGroupDTO {
    @Schema(description = "1为移动，2为复制")
    @Min(value = 1,message = "参数异常")
    @Max(value = 2,message = "参数异常")
    @NotNull(message = "参数异常")
    private Integer type;

    private Long targetLabelGroupId;

    @NotNull(message = "参数异常")
    private Long targetChannelId;

    @NotNull(message = "参数异常")
    private Long sourceLabelGroupId;

}
