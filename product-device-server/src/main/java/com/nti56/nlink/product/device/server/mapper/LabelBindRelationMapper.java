package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.LabelBindRelationEntity;
import com.nti56.nlink.product.device.server.model.CountByIdDTO;
import com.nti56.nlink.product.device.server.model.DeviceChannelBo;
import com.nti56.nlink.product.device.server.model.LabelBindRelationBo;
import com.nti56.nlink.product.device.server.model.LabelBindRelationDto;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * <p>
 * 标签绑定关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11 17:27:49
 */
public interface LabelBindRelationMapper extends CommonMapper<LabelBindRelationEntity> {

    @Select({
            "SELECT l.*,d.name deviceName",
            "FROM label_bind_relation l LEFT JOIN device d ON l.device_id = d.id",
            "WHERE l.tenant_id = #{tenantId} AND l.label_name = #{labelName} AND l.channel_name = #{channelName} AND l.label_group_name = #{labelGroupName} AND l.edge_gateway_id = #{edgeGatewayId} AND l.DELETED = 0"
    })
    List<LabelBindRelationDto> getByAbsoluteLabelName(@Param("channelName") String channelName, @Param("labelGroupName") String labelGroupName, @Param("labelName") String labelName, @Param("edgeGatewayId") Long edgeGatewayId,@Param("tenantId") Long tenantId);

    List<DeviceChannelBo> listDeviceChannel(@Param("deviceId")Long deviceId,@Param("tenantId")Long tenantId);

    void physicalDeleteByEdgeGatewayId(@Param("edgeGatewayId")Long edgeGatewayId);


    @Select({
            "SELECT * ",
            "FROM label_bind_relation",
            "WHERE  DELETED = 0"
    })
    List<LabelBindRelationEntity> allLabelBindRelation();
}
