package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.product.device.server.entity.NotifyServerLogEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/18 9:24<br/>
 * @since JDK 1.8
 */
public interface NotifyServerLogMapper extends BaseMapper<NotifyServerLogEntity> {

    List<NotifyServerLogEntity> listNewByLogType(@Param("logType")Integer logType,@Param("retryTime") Integer retryTime);


    Page<NotifyServerLogEntity> pageNewByLogType(IPage<NotifyServerLogEntity> notifyServerLogPage, @Param("logType")Integer logType, @Param("retryTime") Integer retryTime);
}
