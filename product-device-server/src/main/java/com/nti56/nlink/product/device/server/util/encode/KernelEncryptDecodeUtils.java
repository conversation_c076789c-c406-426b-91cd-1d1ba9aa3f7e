package com.nti56.nlink.product.device.server.util.encode;

import lombok.extern.slf4j.Slf4j;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @date 2019年8月28日 下午3:19:38 <br/>
 * @version 1.0.0
 * @since JDK 1.8
 * @history
 */
@Slf4j
public class KernelEncryptDecodeUtils {


    /**
     * 默认AES加密
     *
     * @param context 内容
     * @return 返回值
     */
    public static String encryptAes(String context) {

        try {

            return AesUtils.encrypt(context);

        } catch (Exception e) {
            log.error("encrypt error", e);
        }
        return context;
    }

    /**
     * AES根据自定义密钥加密
     *
     * @param context 内容
     * @param key     key
     * @return 返回值
     */
    public static String encryptAes(String context, String key) {

        try {

            return AesUtils.encrypt(context, key);

        } catch (Exception e) {
            log.error("encrypt error", e);
        }
        return context;
    }

    /**
     * AES解密
     * 使用默认密钥解密
     *
     * @param context 内容
     * @return 返回值
     */
    public static String decryptAes(String context) {

        try {

            return AesUtils.decrypt(context);
        } catch (Exception e) {
            log.error("decrypt error", e);
        }
        return context;
    }

    /**
     * AES解密
     * 使用自定义密钥解密
     *
     * @param context 解密数据
     * @param key     key
     * @return 返回值
     */
    public static String decryptAes(String context, String key) {

        try {

            return AesUtils.decrypt(context, key);
        } catch (Exception e) {
            log.error("decrypt error", e);
        }
        return context;
    }


    /**
     * 方法说明:des加密. <br/>
     *
     * @param data 内容
     * @param key  密钥
     * @return 返回值
     */
    public static String encodeDes(String data, String key) {

        try {
            return DesUtil.encode(data, key);

        } catch (Exception e) {
            log.error("encode error", e);
        }
        return data;
    }

    /**
     * 方法说明:des解密. <br/>
     *
     * @param data 内容
     * @param key  密钥
     * @return 返回值
     */
    public static String decodeDes(String data, String key) {
        try {
            return DesUtil.decode(data, key);
        } catch (Exception e) {
            log.error("decode error", e);
        }
        return data;

    }

    /**
     * 方法说明:默认des加密. <br/>
     *
     * @param data 加密数据
     * @return 返回值
     */
    public static String encodeDes(String data) {

        try {
            return DesUtil.encode(data);
        } catch (Exception e) {
            log.error("encode error", e);
        }
        return data;
    }

    /**
     * 方法说明:默认des解密. <br/>
     *
     * @param data 解密数据
     * @return 返回值
     */
    public static String decodeDes(String data) {

        try {
            return DesUtil.decode(data);
        } catch (Exception e) {
            log.error("decode error", e);
        }
        return data;
    }

}