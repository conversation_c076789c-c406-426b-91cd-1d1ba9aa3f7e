package com.nti56.nlink.product.device.server.model.inherit;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SubscriptionOfInherit {

    @TableId(value = "id")
    private Long id;

    /**
     * 订阅名称
     */
    @Schema(description = "订阅名称")
    private String name;

    /**
     * 订阅描述
     */
    @Schema(description = "订阅描述")
    private String descript;

    /**
     * 订阅属性，用“,”隔开
     */
    @Schema(description = "订阅属性，用“,”隔开")
    private String properties;

    /**
     * 事件类型
     */
    @Schema(description = "事件类型")
    private Integer eventType;

    /**
     * 回调ID
     */
    @Schema(description = "回调ID")
    private Long callbackId;

    /**
     * 直属模型ID
     */
    @Schema(description = "直属模型ID")
    private Long directlyModelId;

    /**
     * 直属模型类型，1-设备模型，0-物模型
     */
    @Schema(description = "直属模型类型，1-设备模型，0-物模型")
    private Integer modelType;

    @Schema(description = "直属物模型id")
    private Long baseThingModelId;

    @Schema(description = "直属物模型名称")
    private String baseThingModelName;


}
