package com.nti56.nlink.product.device.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.entity.DeviceModelInheritEntity;
import com.nti56.nlink.product.device.server.entity.ThingModelInheritEntity;
import com.nti56.nlink.product.device.server.mapper.ThingModelInheritMapper;
import com.nti56.nlink.product.device.server.model.ThingModelInheritBo;
import com.nti56.nlink.product.device.server.service.IThingModelInheritService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 物模型继承表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 15:37:55
 * @since JDK 1.8
 */
@Service
public class ThingModelInheritServiceImpl extends BaseServiceImpl<ThingModelInheritMapper, ThingModelInheritEntity> implements IThingModelInheritService {

    @Autowired
    ThingModelInheritMapper mapper;

    @Override
    public Result<ThingModelInheritEntity> save(TenantIsolation tenantIsolation, ThingModelInheritEntity entity) {
        entity.setTenantId(tenantIsolation.getTenantId());
        if (mapper.insert(entity) == 1) {
            return Result.ok(entity);
        }
        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }


    @Override
    public Result<List<ThingModelInheritEntity>> list(ThingModelInheritEntity entity) {
        QueryWrapper<ThingModelInheritEntity> wrapper = new QueryWrapper<>(entity);
        List<ThingModelInheritEntity> list = mapper.selectList(wrapper);
        return Result.ok(list);
    }

    @Override
    public Result<Integer> deleteById(TenantIsolation tenantIsolation, Long entityId) {
        if (mapper.deleteById(tenantIsolation.getTenantId(), entityId) == 1) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    @Override
    public Result<ThingModelInheritEntity> getById(TenantIsolation tenantIsolation, Long entityId) {
        ThingModelInheritEntity entity = mapper.getById(tenantIsolation.getTenantId(), entityId);
        return Result.ok(entity);
    }

    @Override
    public Result<List<ThingModelInheritBo>> listBoByThingModelId(TenantIsolation tenantIsolation, Long thingModelId) {
        List<ThingModelInheritBo> list = mapper.listInheritBoByThingModelId(tenantIsolation.getTenantId(), thingModelId);
        return Result.ok(list);
    }


}
