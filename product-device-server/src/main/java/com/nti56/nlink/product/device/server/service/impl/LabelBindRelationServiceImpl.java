package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.google.common.base.Stopwatch;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.DataTypeElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import com.nti56.nlink.product.device.server.annotation.AuditLog;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.channel.Channel;
import com.nti56.nlink.product.device.server.domain.thing.devicemodel.DeviceModel;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SyncStatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.label.Label;
import com.nti56.nlink.product.device.server.domain.thing.label.LabelGroup;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Property;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.enums.ActionEnum;
import com.nti56.nlink.product.device.server.enums.AuditTargetEnum;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.*;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.service.cache.redis2Mem.MemoryCache;
import com.nti56.nlink.product.device.server.util.DeviceNameMatcher;
import com.nti56.nlink.product.device.server.util.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.NotNull;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 标签绑定关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-11 17:27:49
 * @since JDK 1.8
 */
@Service
@Slf4j
public class LabelBindRelationServiceImpl extends BaseServiceImpl<LabelBindRelationMapper, LabelBindRelationEntity> implements ILabelBindRelationService {

    @Autowired
    private LabelBindRelationMapper mapper;

    @Autowired
    private DeviceModelMapper deviceModelMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private ILabelService labelService;
    
    @Autowired
    private ILabelGroupService labelGroupService;

    @Autowired
    private RedisTemplate redisTemplate;


    @Transactional
    @Override
    @AuditLog(action = ActionEnum.DELETE, target = AuditTargetEnum.LABEL, details = "单个或批量解绑标签")
    public Result<Integer> batchDelete(List<Long> ids, Long tenantId) {
        List<LabelBindRelationEntity> list = new LambdaQueryChainWrapper<>(mapper)
                .eq(LabelBindRelationEntity::getTenantId, tenantId)
                .in(LabelBindRelationEntity::getId, ids)
                .select(LabelBindRelationEntity::getDeviceId).list();
        LambdaUpdateWrapper<LabelBindRelationEntity> lqw = new LambdaUpdateWrapper<>();
        lqw.eq(LabelBindRelationEntity::getTenantId, tenantId)
                .in(LabelBindRelationEntity::getId, ids);
        if (mapper.delete(lqw) > 0) {
            Set<Long> deviceIds = list.stream().map(LabelBindRelationEntity::getDeviceId).collect(Collectors.toSet());
            deviceService.setNotSyncByIds(deviceIds);
        }
        return Result.ok();
    }

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void jdbcTemplateBatchSave(List<LabelBindRelationEntity> list, int batchSize) {
        Stopwatch sw = Stopwatch.createStarted();
        jdbcTemplate.batchUpdate("INSERT INTO `label_bind_relation` (`ID`, `directly_model_id`, `label_id`, " +
                        "`device_id`, `property_name`, `data_model_id`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, " +
                        "`ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `model_type`, `tenant_id`, `deleted`, `edge_gateway_id`, `label_name`, `channel_name`, `label_group_name`) VALUES " +
                        "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                list,
                batchSize,
                (PreparedStatement ps, LabelBindRelationEntity labelBindRelation) -> {

                    ps.setLong(1, labelBindRelation.getId());
                    if(labelBindRelation.getDirectlyModelId() != null){
                        ps.setLong(2, labelBindRelation.getDirectlyModelId());
                    }else {
                        ps.setObject(2, null);
                    }
                    if(labelBindRelation.getLabelId() != null){
                        ps.setLong(3, labelBindRelation.getLabelId());
                    }else {
                        ps.setObject(3, null);
                    }
                    if(labelBindRelation.getDeviceId() != null){
                        ps.setLong(4, labelBindRelation.getDeviceId());
                    }else {
                        ps.setObject(4, null);
                    }
                    if(labelBindRelation.getPropertyName() != null){
                        ps.setString(5, labelBindRelation.getPropertyName());
                    }else {
                        ps.setObject(5, null);
                    }
                    if(labelBindRelation.getDataModelId() != null){
                        ps.setLong(6, labelBindRelation.getDataModelId());
                    }else {
                        ps.setObject(6, null);
                    }
                    if(labelBindRelation.getCreatorId() != null){
                        ps.setLong(7, labelBindRelation.getCreatorId());
                    }else {
                        ps.setObject(7, null);
                    }
                    if(labelBindRelation.getCreator() != null){
                        ps.setString(8, labelBindRelation.getCreator());
                    }else {
                        ps.setObject(8, null);
                    }
                    if(labelBindRelation.getCreateTime() != null){
                        ps.setTimestamp(9, Timestamp.valueOf(labelBindRelation.getCreateTime()));
                    }else {
                        ps.setObject(9, null);
                    }
                    if(labelBindRelation.getEngineeringId() != null){
                        ps.setLong(10, labelBindRelation.getEngineeringId());
                    }else {
                        ps.setObject(10, null);
                    }
                    if(labelBindRelation.getModuleId() != null){
                        ps.setLong(11, labelBindRelation.getModuleId());
                    }else {
                        ps.setObject(11, null);
                    }
                    if(labelBindRelation.getSpaceId() != null){
                        ps.setLong(12, labelBindRelation.getSpaceId());
                    }else {
                        ps.setObject(12, null);
                    }
                    if(labelBindRelation.getModelType() != null){
                        ps.setInt(13, labelBindRelation.getModelType());
                    }else {
                        ps.setObject(13, null);
                    }
                    if(labelBindRelation.getTenantId() != null){
                        ps.setLong(14, labelBindRelation.getTenantId());
                    }else {
                        ps.setObject(14, null);
                    }

                    if (labelBindRelation.getDeleted() != null) {
                        ps.setInt(15, labelBindRelation.getDeleted() ? 1 : 0);
                    } else {
                        ps.setInt(15, 0);
                    }
                    if (labelBindRelation.getEdgeGatewayId() != null) {
                        ps.setLong(16, labelBindRelation.getEdgeGatewayId());
                    } else {
                        ps.setLong(16, 0);
                    }
                    if (labelBindRelation.getLabelName() != null) {
                        ps.setString(17, labelBindRelation.getLabelName());
                    } else {
                        ps.setString(17, null);
                    }
                    if (labelBindRelation.getChannelName() != null) {
                        ps.setString(18, labelBindRelation.getChannelName());
                    } else {
                        ps.setString(18, null);
                    }
                    if (labelBindRelation.getLabelGroupName() != null) {
                        ps.setString(19, labelBindRelation.getLabelGroupName());
                    } else {
                        ps.setString(19, null);
                    }
                });
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>> labelbind :" + list.size() + ":" + sw.elapsed(TimeUnit.SECONDS) + "s<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
        sw.stop();

    }

    @Autowired
    AbsolutePathLabelMapper absolutePathLabelMapper;

    @Transactional
    @Override
    @AuditLog(action = ActionEnum.CREATE, target = AuditTargetEnum.LABEL, details = "新增标签绑定")
    public Result<AbsolutePathLabelEntity> createLabelBindRelation(LabelBindRelationEntity entity) {
        LambdaQueryChainWrapper<AbsolutePathLabelEntity> wrapper = new LambdaQueryChainWrapper<>(absolutePathLabelMapper).eq(AbsolutePathLabelEntity::getId, entity.getLabelId());
        AbsolutePathLabelEntity absolutePathLabelEntity = absolutePathLabelMapper.one(wrapper.getWrapper());
        if (!Optional.ofNullable(absolutePathLabelEntity).isPresent()) {
            return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
        }
        entity.setLabelName(absolutePathLabelEntity.getName());
        entity.setChannelName(absolutePathLabelEntity.getChannelName());
        entity.setLabelGroupName(absolutePathLabelEntity.getLabelGroupName());
        entity.setEdgeGatewayId(absolutePathLabelEntity.getEdgeGatewayId());
        LabelEntity label = BeanUtilsIntensifier.copyBean(absolutePathLabelEntity, LabelEntity.class);
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(entity.getTenantId());
        Result<DeviceModel> modelResult = DeviceModel.checkInfo(
                deviceModelMapper.getByDeviceId(entity.getTenantId(), entity.getDeviceId()),
                commonFetcher
        );
        if (!modelResult.getSignal()) {
            return Result.error(modelResult.getServiceCode(), modelResult.getMessage());
        }
        Map<String, Property> propertyMap = BeanUtilsIntensifier.collection2Map(modelResult.getResult().getProperties(), Property::getName);
        if (!propertyMap.containsKey(entity.getPropertyName())) {
            return Result.error(ServiceCodeEnum.DEVICE_LABEL_PARAM_NULL);
        }
        Result<Void> result = checkLabelParam(entity.getDeviceId(), entity, propertyMap.get(entity.getPropertyName()), label);
        if (!result.getSignal()) {
            return Result.error(ServiceCodeEnum.typeOfValue(result.getServiceCode()));
        }
        this.uniqueDeviceProperty(entity.getDeviceId(), entity.getPropertyName(), entity.getTenantId());
        if (mapper.insert(entity) == 1) {
            deviceService.setNotSyncById(entity.getDeviceId());
            LabelBindRelationBo labelBindRelationBo = BeanUtilsIntensifier.copyBean(entity, LabelBindRelationBo.class);
            labelBindRelationBo.setLabelName(label.getName());
            return Result.ok(absolutePathLabelEntity);
        }
        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }

    @Override
    public Result<List<LabelBindRelationBo>> list(Long deviceId, Long tenantId) {
        List<LabelBindRelationEntity> relations = new LambdaQueryChainWrapper<>(mapper)
                .eq(LabelBindRelationEntity::getDeviceId, deviceId).eq(LabelBindRelationEntity::getTenantId, tenantId).list();
        List<LabelBindRelationBo> labelBindRelationBos = BeanUtilsIntensifier.copyBeanList(relations, LabelBindRelationBo.class);
        Set<Long> edgeGatewayId = new HashSet<>();
        Set<String> channelNames = new HashSet<>();
        labelBindRelationBos.forEach(l -> {
            if (!ObjectUtils.isEmpty(l.getEdgeGatewayId())) {
                edgeGatewayId.add(l.getEdgeGatewayId());
            }
            if (!ObjectUtils.isEmpty(l.getChannelName())) {
                channelNames.add(l.getChannelName());
            }
            if (ObjectUtils.isEmpty(l.getLabelName()) || ObjectUtils.isEmpty(l.getLabelGroupName()) || ObjectUtils.isEmpty(l.getChannelName()) || ObjectUtils.isEmpty(l.getEdgeGatewayId())){
                l.setValidBinding(false);
            }else {
                l.setValidBinding(true);
            }
        });
        if (!edgeGatewayId.isEmpty() && !channelNames.isEmpty()) {
            List<AbsolutePathLabelEntity> absolutePathLabelEntities = labelMapper.listByEdgeGatewayAndChannelNames(tenantId, edgeGatewayId.iterator().next(), channelNames);
            Map<String,Map<String,Map<String,AbsolutePathLabelEntity>>> map = new HashMap<>();
            absolutePathLabelEntities.forEach(a -> {
                if (!map.containsKey(a.getChannelName())) {
                    map.put(a.getChannelName(),new HashMap<>());
                }
                if (!map.get(a.getChannelName()).containsKey(a.getLabelGroupName())) {
                    map.get(a.getChannelName()).put(a.getLabelGroupName(),new HashMap<>());
                }
                map.get(a.getChannelName()).get(a.getLabelGroupName()).put(a.getName(),a);
            });
            labelBindRelationBos.stream().filter(LabelBindRelationBo::getValidBinding).forEach(l -> {
                Map<String, Map<String, AbsolutePathLabelEntity>> map1 = map.get(l.getChannelName());
                if (ObjectUtils.isEmpty(map1)) {
                    l.setValidBinding(false);
                    return;
                }
                Map<String, AbsolutePathLabelEntity> map2 = map1.get(l.getLabelGroupName());
                if (ObjectUtils.isEmpty(map2)) {
                    l.setValidBinding(false);
                    return;
                }
                AbsolutePathLabelEntity label = map2.get(l.getLabelName());
                if (ObjectUtils.isEmpty(label)) {
                    l.setValidBinding(false);
                    return;
                }
                l.setDataType(label.getDataType());
                l.setIsArray(label.getIsArray());
                l.setEdgeGatewayName(label.getEdgeGatewayName());
                l.setLength(label.getLength());
                l.setReadOnly(label.getReadOnly());
                l.setStringBytes(label.getStringBytes());
            });
        }
        return Result.ok(labelBindRelationBos);
    }

    @Transactional
    @Override
    public Result<Void> deleteById(LabelBindRelationEntity relation) {
        LabelBindRelationEntity relationEntity = mapper.selectOne(new QueryWrapper<>(relation));
        if (!Optional.ofNullable(relationEntity).isPresent()) {
            return Result.ok();
        }
        if (mapper.deleteById(relationEntity.getId()) == 1) {
            deviceMapper.updateById(DeviceEntity.builder().id(relationEntity.getDeviceId()).syncStatus(SyncStatusEnum.NOT_SYNC.getValue()).build());
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    @Override
    public Result<LabelBindRelationEntity> getById(@NotNull Long entityId) {
        LabelBindRelationEntity entity = mapper.selectById(entityId);
        return Result.ok(entity);
    }

    @Autowired
    CommonFetcherFactory commonFetcherFactory;

    @Transactional
    @Override
    public Result<Map<String, ServiceCodeEnum>> saveList(Long tenantId, Long deviceId, List<LabelBindRelationEntity> relations) {
        if (!Optional.ofNullable(deviceId).isPresent()) {
            throw new BizException(ServiceCodeEnum.CODE_CREATE_FAIL);
        }
        LabelBindRelationEntity relationEntity = new LabelBindRelationEntity();
        relationEntity.setDeviceId(deviceId);
        mapper.delete(new QueryWrapper<>(relationEntity));
        if (CollectionUtil.isEmpty(relations)) {
            return Result.ok();
        }
        Map<String, ServiceCodeEnum> errorMap = new HashMap<>();
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantId);
        Result<DeviceModel> modelResult = DeviceModel.checkInfo(
                deviceModelMapper.getByDeviceId(tenantId, deviceId),
                commonFetcher
        );
        if (!modelResult.getSignal()) {
            throw new BizException(modelResult.getServiceCode(), modelResult.getMessage());
        }
        Map<String, Property> propertyMap = BeanUtilsIntensifier.collection2Map(modelResult.getResult().getProperties(), Property::getName);

        Iterator<LabelBindRelationEntity> iterator = relations.iterator();
        while (iterator.hasNext()) {
            LabelBindRelationEntity entity = iterator.next();
            if (!Optional.ofNullable(entity.getPropertyName()).isPresent()) {
                errorMap.put("未知属性", ServiceCodeEnum.DEVICE_LABEL_PARAM_NULL);
                iterator.remove();
                continue;
            }
            if (!Optional.ofNullable(entity.getDirectlyModelId()).isPresent()) {
                errorMap.put(entity.getPropertyName(), ServiceCodeEnum.DEVICE_LABEL_THING_MODEL_NULL);
                iterator.remove();
                continue;
            }
            if (!Optional.ofNullable(entity.getLabelId()).isPresent()) {
                errorMap.put(entity.getPropertyName(), ServiceCodeEnum.DEVICE_LABEL_NULL);
                iterator.remove();
                continue;
            }
            LabelEntity label = labelMapper.selectById(entity.getLabelId());
            if (!Optional.ofNullable(label).isPresent()) {
                errorMap.put(entity.getPropertyName(), ServiceCodeEnum.DEVICE_LABEL_NULL);
                iterator.remove();
                continue;
            }

            if (!propertyMap.containsKey(entity.getPropertyName())) {
                errorMap.put(entity.getPropertyName(), ServiceCodeEnum.DEVICE_LABEL_PARAM_NULL);
                iterator.remove();
                continue;
            }
            Result<Void> result = checkLabelParam(deviceId, entity, propertyMap.get(entity.getPropertyName()), label);
            if (!result.getSignal()) {
                errorMap.put(entity.getPropertyName(), ServiceCodeEnum.typeOfValue(result.getServiceCode()));
                iterator.remove();
                continue;
            }
            entity.setDeviceId(deviceId);
            mapper.insert(entity);
        }
        return Result.ok(errorMap);
    }

    @Override
    @Transactional
    public Result<Void> unbindByGroupNames(Set<String> labelGroupNames, Long edgeGatewayId, Long tenantId, String channelName) {
        if (CollectionUtil.isEmpty(labelGroupNames)) {
            return Result.ok();
        }
        List<LabelBindRelationEntity> list = new LambdaQueryChainWrapper<>(mapper)
                .eq(LabelBindRelationEntity::getTenantId, tenantId)
                .eq(LabelBindRelationEntity::getEdgeGatewayId,edgeGatewayId)
                .eq(LabelBindRelationEntity::getChannelName,channelName)
                .in(LabelBindRelationEntity::getLabelGroupName, labelGroupNames)
                .select(LabelBindRelationEntity::getDeviceId,LabelBindRelationEntity::getId).list();
        return doUnbind(list);
    }

    @Override
    public Result<List<LabelBindRelationEntity>> listByLabelId(Long labelId) {
        LambdaQueryWrapper<LabelBindRelationEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LabelBindRelationEntity::getLabelId, labelId);
        return Result.ok(mapper.selectList(lqw));
    }

    @Autowired
    IDeviceService deviceService;

    @Autowired
    ThingModelMapper thingModelMapper;

    @Autowired
    IThingModelInheritService thingModelInheritService;

    @Autowired
    IThingServiceService thingServiceService;

    @Autowired
    LabelMapper labelMapper;

    private Result<Void> checkLabelParam(Long deviceId, LabelBindRelationEntity relation, Property property, LabelEntity label) {
        Result<Property> propertyResult = label2Property(label, relation.getPropertyName(), relation.getDirectlyModelId(), property.getReportType().getValue());
        if (!propertyResult.getSignal()) {
            return Result.error(propertyResult.getServiceCode(), propertyResult.getMessage());
        }
        if (property.equals(propertyResult.getResult())) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.DEVICE_LABEL_PROPERTY_MISMATCH);
    }

    private static Result<Property> label2Property(LabelEntity labelEntity, String name, Long thingModelId, Integer reportType) {
        PropertyElm propertyElm = new PropertyElm();
        propertyElm.setBindLabel(true);
        propertyElm.setName(name);
        DataTypeElm dataTypeElm = new DataTypeElm();
        dataTypeElm.setIsArray(labelEntity.getIsArray());
        dataTypeElm.setType(labelEntity.getDataType());
        propertyElm.setDataType(dataTypeElm);
        propertyElm.setReportType(reportType);
        propertyElm.setReadOnly(labelEntity.getReadOnly());
        return Property.checkInfo(thingModelId, null, null, null, propertyElm);
    }

    private Result<Void> uniqueDeviceProperty(Long deviceId, String property, Long tenantId) {
        LambdaQueryWrapper<LabelBindRelationEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LabelBindRelationEntity::getDeviceId, deviceId)
                .eq(LabelBindRelationEntity::getPropertyName, property)
                .eq(LabelBindRelationEntity::getTenantId, tenantId);
        if (this.count(lqw) > 0) {
            throw new BizException("该设备属性已经绑定其他标签");
        }

        return Result.ok();
    }

    @Override
    public Result<List<DeviceChannelBo>> listDeviceChannel(Long deviceId,Long tenantId) {
        List<DeviceChannelBo> list = mapper.listDeviceChannel(deviceId,tenantId);
        return Result.ok(list);
    }

    @Override
    public void batchDeleteByDeviceIds(Long tenantId, List<Long> ids,List<DeviceEntity> deviceEntities) {
        if (CollectionUtil.isNotEmpty(ids)) {
            LambdaQueryWrapper<LabelBindRelationEntity> w = new LambdaQueryWrapper<LabelBindRelationEntity>()
                    .in(LabelBindRelationEntity::getDeviceId, ids)
                    .eq(LabelBindRelationEntity::getTenantId, tenantId);
            mapper.delete(w);
        }
        clearRedisGroupBindRelation(deviceEntities);
    }

    /**
     * 清除分组绑定映射，标签上报不会查到多余的绑定关系，不需要再通过deviceEnable做一次判断
     * @param deviceEntities
     */
    private void clearRedisGroupBindRelation(List<DeviceEntity> deviceEntities) {
        Map<Long,String> groupDeviceMapping = deviceEntities.stream().collect(Collectors.toMap(
                DeviceEntity::getId,
                v->String.format(RedisConstant.GROUP_DEVICE_MAPPING, v.getEdgeGatewayId(), v.getChannel(),v.getSource())
        ));
        // RedisUtil redisUtil = new RedisUtil(redisTemplate, null);
        groupDeviceMapping.forEach((k,v) -> {
            // redisUtil.hdel(v,k.toString());
            MemoryCache.deleteGroupDeviceMap(v,k.toString());
        });

    }

    @Autowired
    IChannelService channelService;

    @Override
    public Result<List<LabelBindRelationDto>> getByAbsoluteLabelName(String labelGroupName,String labelName, Long edgeGatewayId, Long channelId, TenantIsolation tenantIsolation) {
        Result<ChannelEntity> channelEntityResult = channelService.getByIdAndTenantIsolation(channelId, tenantIsolation);
        if (!channelEntityResult.getSignal()) {
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        List<LabelBindRelationDto> list = mapper.getByAbsoluteLabelName(channelEntityResult.getResult().getName(), labelGroupName, labelName, edgeGatewayId, tenantIsolation.getTenantId());
        return Result.ok(list);
    }

    @Override
    public Result<Void> unbind(List<AbsolutePathLabelEntity> labels, Long tenantId) {
        List<LabelBindRelationEntity> list = new LambdaQueryChainWrapper<>(mapper)
                .eq(LabelBindRelationEntity::getTenantId, tenantId)
                .eq(LabelBindRelationEntity::getEdgeGatewayId,labels.get(0).getEdgeGatewayId())
                .eq(LabelBindRelationEntity::getChannelName,labels.get(0).getChannelName())
                .eq(LabelBindRelationEntity::getLabelGroupName,labels.get(0).getLabelGroupName())
                .in(LabelBindRelationEntity::getLabelName, BeanUtilsIntensifier.getSomething(labels,AbsolutePathLabelEntity::getName))
                .select(LabelBindRelationEntity::getDeviceId,LabelBindRelationEntity::getId).list();
        return doUnbind(list);
    }

    @Override
    public void updateByLabel(Label label, TenantIsolation tenantIsolation) {
        new LambdaUpdateChainWrapper<>(mapper)
                .set(LabelBindRelationEntity::getLabelId,null)
                .eq(LabelBindRelationEntity::getTenantId,tenantIsolation.getTenantId())
                .eq(LabelBindRelationEntity::getLabelId,label.getId())
                .update();
        new LambdaUpdateChainWrapper<>(mapper)
                .eq(LabelBindRelationEntity::getTenantId,tenantIsolation.getTenantId())
                .eq(LabelBindRelationEntity::getLabelName,label.getName())
                .eq(LabelBindRelationEntity::getLabelGroupName,label.getLabelGroupName())
                .eq(LabelBindRelationEntity::getChannelName,label.getChannelName())
                .eq(LabelBindRelationEntity::getEdgeGatewayId,label.getEdgeGatewayId())
                .set(LabelBindRelationEntity::getLabelId,label.getId())
                .update();
    }
    
    @Override
    public void updateByLabelGroup(LabelGroup labelGroup, TenantIsolation tenantIsolation) {
        List<LabelEntity> labelEntityList = labelService.listLabelUnderLabelGroupId(
            labelGroup.getId(),
            labelGroup.getChannelId(), 
            labelGroup.getName(), 
            tenantIsolation
        );
        if(CollectionUtil.isEmpty(labelEntityList)){
            return;
        }
        clearLabelBindRelation(tenantIsolation, labelEntityList);
        LambdaQueryWrapper<LabelBindRelationEntity> queryWrapper = new LambdaQueryWrapper<LabelBindRelationEntity>()
            .eq(LabelBindRelationEntity::getTenantId,tenantIsolation.getTenantId())
            .and(i -> {
                i.likeRight(LabelBindRelationEntity::getLabelGroupName,labelGroup.getName())
                .or()
                .eq(LabelBindRelationEntity::getLabelGroupName,labelGroup.getName());
            })
            .eq(LabelBindRelationEntity::getChannelName,labelGroup.getChannelName())
            .eq(LabelBindRelationEntity::getEdgeGatewayId,labelGroup.getEdgeGatewayId());
        List<LabelBindRelationEntity> labelBindRelations = mapper.selectList(queryWrapper);
        if(CollectionUtil.isEmpty(labelBindRelations)){
            log.info("updateByLabelGroup labelBindRelationEntity isEmpty ");
            return;
        }
        for (LabelBindRelationEntity labelBindRelationEntity : labelBindRelations) {
            labelBindRelationEntity.setLabelId(labelBindRelationEntity.getPreLabelId());
        }
    
        if(CollectionUtil.isNotEmpty(labelBindRelations)){
            this.saveOrUpdateBatch(labelBindRelations);
        }
    }


    public void clearLabelGroupBindRelation(LabelGroup labelGroup, TenantIsolation tenantIsolation) {
        List<LabelEntity> labelEntityList = labelService.listByLabelGroupId(labelGroup.getId(),tenantIsolation);
        if(CollectionUtil.isEmpty(labelEntityList)){
            return;
        }
        clearLabelBindRelation(tenantIsolation, labelEntityList);
    }
    
    @Override
    public void updateByChannel(Channel channel, TenantIsolation tenantIsolation) {
        Result<List<LabelGroupDto>> listResult = labelGroupService.listLabelGroupByChannelId(channel.getId(),tenantIsolation);
        if(!listResult.getSignal()){
            log.error("updateByChannel err and message is {}",listResult.getMessage());
            return;
        }
        List<LabelGroupDto> groupDtoList = listResult.getResult();
        if(CollectionUtil.isEmpty(groupDtoList)){
            log.info("groupDtoList isEmpty !");
            return;
        }
        Set<Long> groupIds = groupDtoList.stream().map(LabelGroupDto::getId).collect(Collectors.toSet());
    
        List<LabelEntity> labelEntityList = labelService.listByLabelGroupIds(groupIds);
    
        clearLabelBindRelation(tenantIsolation, labelEntityList);
    
        LambdaQueryWrapper<LabelBindRelationEntity> queryWrapper = new LambdaQueryWrapper<LabelBindRelationEntity>()
            .eq(LabelBindRelationEntity::getTenantId,tenantIsolation.getTenantId())
            .eq(LabelBindRelationEntity::getChannelName,channel.getName())
            .eq(LabelBindRelationEntity::getEdgeGatewayId,channel.getEdgeGatewayId());
        List<LabelBindRelationEntity> labelBindRelations = mapper.selectList(queryWrapper);
        if(CollectionUtil.isEmpty(labelBindRelations)){
            log.info("updateByChannel labelBindRelationEntity isEmpty ");
            return;
        }
        for (LabelBindRelationEntity labelBindRelationEntity : labelBindRelations) {
            labelBindRelationEntity.setLabelId(labelBindRelationEntity.getPreLabelId());
        }
        if(CollectionUtil.isNotEmpty(labelBindRelations)){
            this.saveOrUpdateBatch(labelBindRelations);
        }
    }
    
    public void clearLabelBindRelation(TenantIsolation tenantIsolation, List<LabelEntity> labelEntityList) {
        Set<Long> labelIds = labelEntityList.stream().map(LabelEntity::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<LabelBindRelationEntity> wrapper = new LambdaQueryWrapper<LabelBindRelationEntity>()
            .in(LabelBindRelationEntity::getLabelId, labelIds)
            .eq(LabelBindRelationEntity::getTenantId, tenantIsolation.getTenantId());
        List<LabelBindRelationEntity> labelBindRelationEntityList = mapper.selectList(wrapper);
        for (LabelBindRelationEntity labelBindRelationEntity : labelBindRelationEntityList) {
            labelBindRelationEntity.setPreLabelId(labelBindRelationEntity.getLabelId());
            labelBindRelationEntity.setLabelId(0L);
        }
        if(CollectionUtil.isNotEmpty(labelBindRelationEntityList)){
            this.saveOrUpdateBatch(labelBindRelationEntityList);
        }
    }



    @Override
    public Result<List<AdditionBindPreviewDto>> additionAddPreview(TenantIsolation tenantIsolation, AdditionLabelBindRelationDto bindRelationDto) {

        if(CollectionUtil.isEmpty(bindRelationDto.getLabelGroupIds())){
            return Result.ok(Lists.newArrayList());
        }
        if(StrUtil.isBlank(bindRelationDto.getDeviceNameRegex())){
            return Result.ok(Lists.newArrayList());
        }
        List<Long> labelGroupIds = bindRelationDto.getLabelGroupIds();
        String deviceNameRegex = bindRelationDto.getDeviceNameRegex();
        List<AdditionBindPreviewDto> previewResult = Lists.newArrayList();
        for (Long labelGroupId : labelGroupIds) {
            LabelGroupEntity labelGroup = labelGroupService.getById(labelGroupId);
            String groupName = labelGroup.getName();
            Long channelId = labelGroup.getChannelId();
            ChannelEntity channel = channelService.getById(channelId);
            String channelName = channel.getName();
            String labelGroupName = channelName + "." + groupName;
            String matchDeviceName = DeviceNameMatcher.matchSingle(groupName,deviceNameRegex);
            //根据设备名称，租户查询设备是否存在
            QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("name",matchDeviceName);
            wrapper.eq("tenant_id",tenantIsolation.getTenantId());
            try {
                List<DeviceEntity> devices = deviceService.list(wrapper);
                DeviceEntity matchDevice = null;
                if(!CollectionUtil.isEmpty(devices)){
                    matchDevice = devices.get(0);
                }
                previewResult.add(AdditionBindPreviewDto.builder()
                        .labelGroupName(labelGroupName)
                        .mappingDeviceName(matchDeviceName)
                        .labelGroupId(labelGroupId)
                        .deviceExist(matchDevice != null ? true:false)
                        .deviceId(matchDevice != null ? matchDevice.getId():null)
                        .channelId(channelId)
                        .channelName(channelName)
                        .originalGroupName(groupName)
                        .edgeGatewayId(channel.getEdgeGatewayId())
                        .build());

            }catch (Exception e){
                e.printStackTrace();
            }


        }
        return Result.ok(previewResult);
    }

    @Override
    @Transactional
    public Result<Void> additionAdd(TenantIsolation tenantIsolation, List<AdditionBindPreviewDto> bindRelationDto) {
        //追加标签绑定
        if(CollectionUtil.isEmpty(bindRelationDto)){
            return Result.error("绑定内容为空，本次未新增绑定");
        }
        bindRelationDto = bindRelationDto.stream().filter(AdditionBindPreviewDto::isDeviceExist).collect(Collectors.toList());
        for (AdditionBindPreviewDto additionBindPreviewDto : bindRelationDto) {

            //找出分组所有标签
            LambdaQueryWrapper<LabelEntity> queryWrapper = new LambdaQueryWrapper<LabelEntity>()
                    .eq(LabelEntity::getLabelGroupId,additionBindPreviewDto.getLabelGroupId())
                    .eq(LabelEntity::getDeleted,0);
            List<LabelEntity> labelEntities = labelMapper.selectList(queryWrapper);
            List<String> baseNames = DeviceModel.getBaseModel().getProperties().stream().map(Property::getName).collect(Collectors.toList());
            for(LabelEntity labelEntity :labelEntities){
                if(baseNames.contains(labelEntity.getName())){
                    throw new BizException("标签名与属性保留字冲突");
                }
            }
            //获取设备属性列表
            CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
            Result<DeviceModel> modelResult = DeviceModel.checkInfo(
                    deviceModelMapper.getByDeviceId(tenantIsolation.getTenantId(), additionBindPreviewDto.getDeviceId()),
                    commonFetcher
            );
            if(modelResult.getSignal()){
                List<Property> properties = modelResult.getResult().getProperties();
                //匹配同名属性
                for (Property property : properties) {
                    String name = property.getName();
                    for (LabelEntity labelEntity : labelEntities) {
                        if(labelEntity.getName().equals(name)){
                            //匹配，增加绑定，结束当前属性
                            /*Long labelId = property.getLabelId();
                            if(labelId != null){*/
                                //该属a性存在绑定关系，覆盖绑定
                                LambdaQueryWrapper<LabelBindRelationEntity> deleteWrapper = new LambdaQueryWrapper<LabelBindRelationEntity>()
                                        .eq(LabelBindRelationEntity::getDeviceId,additionBindPreviewDto.getDeviceId())
                                        .eq(LabelBindRelationEntity::getTenantId,tenantIsolation.getTenantId())
                                        .eq(LabelBindRelationEntity::getPropertyName,name);
                                mapper.delete(deleteWrapper);
//                            }
                            LabelBindRelationEntity labelBindRelationEntity = LabelBindRelationEntity.builder()
                                    .directlyModelId(property.getDirectlyModelId())
                                    .labelId(labelEntity.getId())
                                    .deviceId(additionBindPreviewDto.getDeviceId())
                                    .propertyName(name)
                                    .edgeGatewayId(additionBindPreviewDto.getEdgeGatewayId())
                                    .labelName(labelEntity.getName())
                                    .channelName(additionBindPreviewDto.getChannelName())
                                    .labelGroupName(additionBindPreviewDto.getOriginalGroupName())
                                    .tenantId(tenantIsolation.getTenantId())
                                    .build();
                            mapper.insert(labelBindRelationEntity);
                            break;
                        }
                    }
                }
            }
            //刷新设备状态
            deviceService.setNotSyncById(additionBindPreviewDto.getDeviceId());
        }
        return Result.ok();
    }

    @Override
    public List<LabelBindRelationEntity> allLabelBindRelation() {
        List<LabelBindRelationEntity> labelBindRelationBos = mapper.allLabelBindRelation();
        return labelBindRelationBos;
    }

    private Result<Void> doUnbind(List<LabelBindRelationEntity> list) {
        if (CollectionUtil.isNotEmpty(list)) {
            LambdaUpdateWrapper<LabelBindRelationEntity> wrapper = new LambdaUpdateWrapper<LabelBindRelationEntity>()
                    .in(LabelBindRelationEntity::getId, BeanUtilsIntensifier.getIds(list,LabelBindRelationEntity::getId));
            if (mapper.delete(wrapper) > 0) {
                Set<Long> deviceIds = list.stream().map(LabelBindRelationEntity::getDeviceId).collect(Collectors.toSet());
                deviceService.setNotSyncByIds(deviceIds);
            }
        }
        return Result.ok();
    }



}