package com.nti56.nlink.product.device.server.service;

import java.util.List;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.model.oss.PackageBo;
import com.nti56.nlink.product.device.server.model.oss.PackageVersionDTO;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/20 13:50<br/>
 * @version 1.0
 * @since JDK 1.8
 */
public interface IPackageVersionService {

  Result<PackageVersionDTO> save(PackageVersionDTO dto);

  Result<List<PackageVersionDTO>> download(TenantIsolation tenantIsolation, PackageBo packageBo);

  /**
   * 删除操作
   * @param tenantIsolation
   * @param id
   * @param  check
   * @return
   */
  Result<String> delete(TenantIsolation tenantIsolation, Long id, boolean check);
}
