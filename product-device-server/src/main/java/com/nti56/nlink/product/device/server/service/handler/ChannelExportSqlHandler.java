package com.nti56.nlink.product.device.server.service.handler;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nti56.nlink.common.export.handler.AbstractExportSqlHandler;
import com.nti56.nlink.common.util.GeneratorSqlUtil;
import com.nti56.nlink.product.device.server.entity.NotifyChannelEntity;
import com.nti56.nlink.product.device.server.service.INotifyChannelService;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 类说明: 渠道导出<br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/19 14:31<br/>
 * @version 1.0
 * @since JDK 1.8
 */
public class ChannelExportSqlHandler extends AbstractExportSqlHandler {
  @Override
  public void exportSqlDml(Long tenantId, List<String> sqlList) {
    exportChannel(tenantId, sqlList);
    if (getNext() != null) {
      getNext().exportSqlDml(tenantId, sqlList);
    }
  }

  private void exportChannel(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<NotifyChannelEntity> queryWrapper = new LambdaQueryWrapper<NotifyChannelEntity>().eq(NotifyChannelEntity::getTenantId, tenantId);
    List<NotifyChannelEntity> dtoList = SpringUtil.getBean(INotifyChannelService.class).list(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(NotifyChannelEntity.class, dtoList));
    }
  }
}
