package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.config.XxlJobConfig;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.entity.ServiceConfigEntity;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import com.nti56.nlink.product.device.server.mapper.ThingServiceMapper;
import com.nti56.nlink.product.device.server.model.JobInfoDTO;
import com.nti56.nlink.product.device.server.openapi.domain.enums.CommonServiceTypeEnum;
import com.nti56.nlink.product.device.server.openapi.domain.request.DoCommonServiceRequest;
import com.nti56.nlink.product.device.server.openapi.domain.request.DoServiceTaskRequest;
import com.nti56.nlink.product.device.server.service.ICommonServiceService;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.service.IServiceConfigService;
import com.nti56.nlink.product.device.server.util.XxlJobUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明：
 *
 * @ClassName CommonServiceServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/3/7 10:49
 * @Version 1.0
 */
@Service
@Slf4j
public class CommonServiceServiceImpl implements ICommonServiceService {

    @Autowired
    private IServiceConfigService serviceConfigService;
    //@Autowired
    //private IModelGraphService modelGraphService;

    @Autowired
    IDeviceService deviceService;

    @Autowired(required = false)
    private XxlJobConfig xxlJobConfig;

    @Autowired
    @Lazy
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ThingServiceMapper thingServiceMapper;

    @Override
    public Result execute(TenantIsolation tenantIsolation, DoCommonServiceRequest request) {

        String serviceType = request.getServiceType();
        CommonServiceTypeEnum commonServiceTypeEnum = CommonServiceTypeEnum.typeOfName(serviceType);
        if (Objects.isNull(commonServiceTypeEnum)) {
            return Result.error("empty service type.can't execute your request");
        }
        final List<DoServiceTaskRequest> serviceRequests = new ArrayList<>();
        switch (commonServiceTypeEnum) {
            case SWITCH_CONTROL_SERVICE:
                List<Long> deviceIds = request.getDeviceId();
                if (CollectionUtil.isEmpty(deviceIds)) {
                    return Result.error("empty devices.can't execute your request");
                }
                QueryWrapper<ThingServiceEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("service_name", serviceType);
                queryWrapper.eq("tenant_id", tenantIsolation.getTenantId());
                ThingServiceEntity tenantCommonService = thingServiceMapper.selectOne(queryWrapper);
                if (Objects.isNull(tenantCommonService)) {
                    return Result.error("ot端没有继承" + serviceType + "通用服务，请配置后再试！");
                }
                List<ServiceConfigEntity> serviceConfigs = serviceConfigService.queryConfigByServiceIdAndDeviceIds(tenantCommonService.getId(), deviceIds, tenantIsolation.getTenantId());
                final Map<Long, ServiceConfigEntity> configMap = Maps.newHashMap();
                if (CollectionUtil.isNotEmpty(serviceConfigs)) {
                    configMap.putAll(serviceConfigs.stream().collect(Collectors.toMap(ServiceConfigEntity::getDeviceId, config -> config, (k1, k2) -> k2)));
                }
                String switchType = request.getSwitchType();
                deviceIds.forEach(id -> {
                    ServiceConfigEntity serviceConfigEntity = configMap.get(id);
                    if (Objects.isNull(serviceConfigEntity)) {
                        //从模型继承中取 取最接近设备的那个模型匹配
                        // Result<Object> result = modelGraphService.queryInheritByDeviceId(id);
                        Result<Set<Long>> result = deviceService.deviceModelInherits(id,tenantIsolation.getTenantId());
                        if (result.getSignal()) {
                            //JSONObject deviceModelInherits = JSONUtil.parseObj(result.getResult());
                            Set<Long> deviceModelInherits = result.getResult();
                            ServiceConfigEntity configFromModel = getServiceConfigFromInheritModels(deviceModelInherits, tenantCommonService.getId(), tenantIsolation.getTenantId());
                            if (!Objects.isNull(configFromModel)) {
                                //走参数组装逻辑
                                buildRequestData(tenantIsolation, serviceType, serviceRequests, switchType, id, configFromModel);
                            } else {
                                log.warn("device[] can't find any service config,unable to execute service for it", id);
                            }
                        }
                    } else {
                        // 设备上定义了
                        buildRequestData(tenantIsolation, serviceType, serviceRequests, switchType, id, serviceConfigEntity);
                    }
                });
                break;
            case CUSTOMER_SERVICE:
                //走用户自定义服务
                log.info("invoke custom service:{}", request.getServiceType());
                if (CollectionUtil.isEmpty(request.getTasks())) {
                    return Result.error("empty service task!can't execute your request");
                }
                request.getTasks().forEach(task -> {
                    DoServiceTaskRequest doServiceTaskRequest = new DoServiceTaskRequest();
                    doServiceTaskRequest.setServiceName(task.getServiceName());
                    doServiceTaskRequest.setInput(task.getInput());
                    doServiceTaskRequest.setTenantId(tenantIsolation.getTenantId());
                    doServiceTaskRequest.setDeviceId(task.getDeviceId());
                    serviceRequests.add(doServiceTaskRequest);
                });
                break;
            default:
                log.warn("unsupported service type:{}", serviceType);
                return Result.error("不支持的服务类型：" + serviceType);
        }
        if (CollectionUtil.isNotEmpty(serviceRequests)) {
            if (StringUtils.isNotBlank(request.getCron())) {
                //启动定时器
                if (xxlJobConfig == null) {
                    return Result.error("定时任务功能未启用，无法启动定时任务");
                }

                //创建定时任务
                try {
                    JobInfoDTO jobInfoDTO = JobInfoDTO.builder().jobDesc(request.getBusinessId() + "定时服务类型:" + request.getServiceType())
                            .cron(request.getCron())
                            .jobGroup(Long.valueOf(xxlJobConfig.getJobGroup()))
                            .adminUrl(xxlJobConfig.getAdminAddresses())
                            .userName(xxlJobConfig.getUserName())
                            .password(xxlJobConfig.getPassword())
//                            .jobParam(JSONUtil.toJsonStr(serviceRequests))
                            .taskHandler("serviceTaskHandler")
                            .build();

                    String existJob = stringRedisTemplate.opsForValue().get(String.format(RedisConstant.SERVICE_CRON_JOB, tenantIsolation.getTenantId(), request.getBusinessId()));
                    log.info("existJob:{}", existJob);
                    if (StrUtil.isBlank(existJob)) {
                        Long jobId = XxlJobUtil.add(xxlJobConfig.getAdminAddresses() + "/jobinfo/add", jobInfoDTO);
                        log.info("new jobId:{}", jobId);
                        jobInfoDTO.setJobId(jobId);
                        stringRedisTemplate.opsForValue().set(String.format(RedisConstant.JOB_PARAM_CACHE, jobId), JSONUtil.toJsonStr(serviceRequests));
                    } else {
                        JSONObject object = JSONUtil.parseObj(existJob);
                        Long jobId = object.getLong("jobId");
                        log.info("existJob jobId:{}", jobId);
                        jobInfoDTO.setJobId(jobId);
                        XxlJobUtil.add(xxlJobConfig.getAdminAddresses() + "/jobinfo/update", jobInfoDTO);
                        stringRedisTemplate.opsForValue().set(String.format(RedisConstant.JOB_PARAM_CACHE, jobId), JSONUtil.toJsonStr(serviceRequests));
                    }
                    XxlJobUtil.start(xxlJobConfig.getAdminAddresses() + "/jobinfo/start", jobInfoDTO);
                    stringRedisTemplate.opsForValue().set(String.format(RedisConstant.SERVICE_CRON_JOB, tenantIsolation.getTenantId(), request.getBusinessId()), JSONUtil.toJsonStr(jobInfoDTO));
                    log.info("service cron job rediskey:{}", String.format(RedisConstant.SERVICE_CRON_JOB, tenantIsolation.getTenantId(), request.getBusinessId()));
                } catch (Exception e) {
                    log.error("创建服务定时执行异常，异常原因：{}", e.getMessage());
                    return Result.error("创建定时任务失败，请重新创建！msg:"+e.getMessage());
                }

            } else {
                //直接执行
                serviceRequests.forEach(item -> {
                    DeviceServiceLogEntity logEntity = DeviceServiceLogEntity.builder()
                            .createTime(LocalDateTime.now()).callType(3).deviceId(item.getDeviceId())
                            .serviceName(item.getServiceName()).build();
                    R r = deviceService.doServiceTask(tenantIsolation.getTenantId(), item.getDeviceId(),
                            item.getServiceName(), item.getInput(), logEntity,true);
                });
            }

        }
        return Result.ok("success");
    }

    @Override
    public Result deleteServiceTask(TenantIsolation tenantIsolation, Long bId) {
        String existJob = stringRedisTemplate.opsForValue().get(String.format(RedisConstant.SERVICE_CRON_JOB, tenantIsolation.getTenantId(), bId));
        if(StrUtil.isNotBlank(existJob)){
            JobInfoDTO object = JSONUtil.toBean(existJob,JobInfoDTO.class);
            Long jobId = object.getJobId();
            if (xxlJobConfig == null) {
                return Result.error("定时任务功能未启用，无法删除定时任务");
            }
            boolean success=XxlJobUtil.remove(xxlJobConfig.getAdminAddresses() + "/jobinfo/remove", object);
            if (success) {
                stringRedisTemplate.delete(String.format(RedisConstant.JOB_PARAM_CACHE,jobId));
                stringRedisTemplate.delete(String.format(RedisConstant.SERVICE_CRON_JOB, tenantIsolation.getTenantId(), bId));
                return Result.ok();
            }
            return Result.error("删除定时任务失败，bId:{}");
        }
        return Result.error("删除定时任务不存在，bId:{}");
    }

    private void buildRequestData(TenantIsolation tenantIsolation, String serviceType, List<DoServiceTaskRequest> serviceRequests, String switchType, Long id, ServiceConfigEntity configFromModel) {
        JSONObject config = JSONUtil.parseObj(configFromModel.getConfigMap());
        Map<String, Object> input = new HashMap<>();
        input.put("openProp", config.getStr("openProp"));
        input.put("closeProp", config.getStr("closeProp"));
        input.put("func", switchType);
        input.put("openVal", true);
        input.put("closeVal", false);
        DoServiceTaskRequest doServiceTaskRequest = new DoServiceTaskRequest();
        doServiceTaskRequest.setServiceName(serviceType);
        doServiceTaskRequest.setInput(input);
        doServiceTaskRequest.setTenantId(tenantIsolation.getTenantId());
        doServiceTaskRequest.setDeviceId(id);
        serviceRequests.add(doServiceTaskRequest);
    }

    private ServiceConfigEntity getServiceConfigFromInheritModels(JSONObject deviceInherits, Long serviceId, Long tenantId) {
        JSONArray inherit = deviceInherits.getJSONArray("inherit");
        if (inherit != null) {
            for (int i = 0; i < inherit.size(); i++) {
                JSONObject model = inherit.getJSONObject(i);
                Long modelId = model.getLong("modelId");
                ServiceConfigEntity serviceConfigEntity = serviceConfigService.getConfigByServiceIdAndModelId(serviceId, modelId, tenantId);
                if (!Objects.isNull(serviceConfigEntity)) {
                    return serviceConfigEntity;
                } else {
                    ServiceConfigEntity tempEntity = getServiceConfigFromInheritModels(model, serviceId, tenantId);
                    if (!Objects.isNull(tempEntity)) {
                        return tempEntity;
                    }
                }
            }
        }
        return null;
    }

    private ServiceConfigEntity getServiceConfigFromInheritModels(Set<Long> deviceInherits, Long serviceId, Long tenantId) {
        if (CollectionUtil.isNotEmpty(deviceInherits)) {
            for(Long modelId :deviceInherits){
                ServiceConfigEntity serviceConfigEntity = serviceConfigService.getConfigByServiceIdAndModelId(serviceId, modelId, tenantId);
                if (!Objects.isNull(serviceConfigEntity)) {
                    return serviceConfigEntity;
                } else {
                    Set<Long> ids = new HashSet<>();
                    ids.add(modelId);
                    ServiceConfigEntity tempEntity = getServiceConfigFromInheritModels(ids, serviceId, tenantId);
                    if (!Objects.isNull(tempEntity)) {
                        return tempEntity;
                    }
                }
            }
        }
        return null;
    }
}
