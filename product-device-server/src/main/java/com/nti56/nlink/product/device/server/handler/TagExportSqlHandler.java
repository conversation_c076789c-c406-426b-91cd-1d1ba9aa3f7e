package com.nti56.nlink.product.device.server.handler;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nti56.nlink.common.export.handler.AbstractExportSqlHandler;
import com.nti56.nlink.common.util.GeneratorSqlUtil;
import com.nti56.nlink.product.device.server.entity.InstanceRedirectEntity;
import com.nti56.nlink.product.device.server.entity.Tag;
import com.nti56.nlink.product.device.server.manager.ITagManager;
import com.nti56.nlink.product.device.server.mapper.RedirectMapper;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/19 14:31<br/>
 * @version 1.0
 * @since JDK 1.8
 */
public class TagExportSqlHandler extends AbstractExportSqlHandler {
  @Override
  public void exportSqlDml(Long tenantId, List<String> sqlList) {
    exportTags(tenantId, sqlList);
    exportRedirect(tenantId, sqlList);
    if (getNext() != null) {
      getNext().exportSqlDml(tenantId, sqlList);
    }
  }

  private void exportTags(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<Tag>().eq(Tag::getTenantId, tenantId);
    List<Tag> dtoList = SpringUtil.getBean(ITagManager.class).list(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(Tag.class, dtoList));
    }
  }

  private void exportRedirect(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<InstanceRedirectEntity> queryWrapper = new LambdaQueryWrapper<InstanceRedirectEntity>().eq(InstanceRedirectEntity::getTenantId, tenantId);
    List<InstanceRedirectEntity> dtoList = SpringUtil.getBean(RedirectMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(InstanceRedirectEntity.class, dtoList));
    }
  }
}
