package com.nti56.nlink.product.device.server.model.oss;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/20 10:21<br/>
 * @version 1.0
 * @since JDK 1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "打包请求")
public class PackageBo {
  /**
   *
   */
  @Schema(description = "父工程")
  private Long engineeringPid;
  /**
   * 工程id
   */
  @Schema(description = "工程")
  private Long engineeringId;
  /**
   *
   */
  @Schema(description = "bucket")
  private Long bucketId;
  /**
   * 版本
   */
  @Schema(description = "版本")
  private String version;
  /**
   * 工程类型
   */
  @Schema(description = "工程类型")
  private Integer engineeringType;
}
