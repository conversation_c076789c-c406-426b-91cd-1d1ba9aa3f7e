package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.model.engineering.dto.CommonServiceDataDTO;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/7 9:06<br/>
 * @since JDK 1.8
 */
public interface IEngineeringCommonService {

    Result<Void> deleteCommonDataByTenantId(Long tenantId);

    Result<CommonServiceDataDTO> getCommonDataByTenantId(Long tenantId);

    Result<Void> createCommonData(CommonServiceDataDTO dto);
}
