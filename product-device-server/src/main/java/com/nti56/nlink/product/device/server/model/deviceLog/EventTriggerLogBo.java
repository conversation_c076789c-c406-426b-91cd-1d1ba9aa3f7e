package com.nti56.nlink.product.device.server.model.deviceLog;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.InstantDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.InstantSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName EventTriggerLogBo
 * @date 2022/8/8 17:11
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
@Schema(name = "事件触发记录")
public class EventTriggerLogBo {

    Long start;

    Long stop;

    String deviceId;

    String deviceName;

    @Schema(description = "事件触发时间")
    @JsonDeserialize(using = InstantDeserializer.class)
    @JsonSerialize(using = InstantSerializer.class)
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss.SSS")
    Instant time;

    String eventName;

    @Schema(description = "事件上报属性")
    List<PropertyVo> reportProperties;

}
