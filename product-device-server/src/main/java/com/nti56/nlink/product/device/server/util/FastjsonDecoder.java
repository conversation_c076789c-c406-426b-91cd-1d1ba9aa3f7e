package com.nti56.nlink.product.device.server.util;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.Charset;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.IOUtils;
import feign.FeignException;
import feign.Response;
import feign.codec.DecodeException;
import feign.codec.Decoder;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:23:08
 * @since JDK 1.8
 */
public class FastjsonDecoder implements Decoder{

    @Override
    public Object decode(Response response, Type type) throws IOException, DecodeException, FeignException {
        if (response.body() == null) {
            return null;
        } else {
            InputStream inputStream = response.body().asInputStream();
            String s = IOUtils.toString(inputStream, Charset.forName("UTF-8"));
            try {
                return JSONObject.parseObject(s, type);
            }catch (JSONException e){
                throw e;
            }
        }
    }
    
}
