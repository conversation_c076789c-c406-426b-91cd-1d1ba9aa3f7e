package com.nti56.nlink.product.device.server.verticle;

import com.nti56.nlink.product.device.server.domain.thing.topic.GwDebugTopic;
import com.nti56.nlink.product.device.server.entity.CustomDebugLogEntity;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayControlProxy;
import com.nti56.nlink.product.device.server.mapper.CustomDebugLogMapper;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;

import io.vertx.core.Promise;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

import org.springframework.beans.factory.annotation.Value;


/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-04-25 16:29:00
 * @since JDK 1.8
 */
@Component
@Scope(SCOPE_PROTOTYPE)
@Slf4j
public class MqttDebugConsumerVerticle extends MqttBaseVerticle {
    
    @Getter
    @Value("${mqtt.host}")
    private String host;

    @Getter
    @Value("${mqtt.port}")
    private Integer port;

    @Getter
    @Value("${mqtt.username}")
    private String username;

    @Getter
    @Value("${mqtt.password}")
    private String password;

    @Getter
    @Value("${mqtt.ssl:true}")
    private Boolean ssl;
    
    @Getter
    private Integer reconnectGapTime;
    
    @Autowired
    IEdgeGatewayService edgeGatewayService;

    @Autowired
    IEdgeGatewayControlProxy edgeGatewayControlProxy;

    @Autowired
    CustomDebugLogMapper customDebugLogMapper; 
    
    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        log.info("start-verticle mqttHeartbeat");
        super.start(startPromise);
    }

    @Override
    public void subscribe(){
        String topic = GwDebugTopic.createShareSubscribeTopic(MqttBaseVerticle.shareSubscribeGroup);
        this.client.publishHandler(s1 -> {

            String topicName = s1.topicName();
            String payload = s1.payload().toString();
            log.debug("topic: {}, msg: {}", topicName, payload);
            GwDebugTopic.TopicInfo topicInfo = GwDebugTopic.parseTopic(topicName);
            
            String type = topicInfo.getType();

            switch(type){
                case "deserialize-error":
                case "deserialize-exception":
                case "serialize-error":
                case "serialize-exception":{
                    Long tenantId = topicInfo.getTenantId();
                    Long edgeGatewayId = topicInfo.getEdgeGatewayId();
        
                    CustomDebugLogEntity entity = new CustomDebugLogEntity();
                    entity.setTenantId(tenantId);
                    entity.setEdgeGatewayId(edgeGatewayId);
                    entity.setType(type);
                    entity.setContent(payload);
                    
                    vertx.executeBlocking(promise -> {
                        int insert = customDebugLogMapper.insert(entity);
                        promise.complete(insert);
                    }, res -> {
                        log.debug("insert result: {}", res.result());
                    });
                }
            }


        });
        client.subscribe(topic, 1);
    }

    @Override
    protected void handleConnectStatusChange(Boolean connected) {
       
    }
}
