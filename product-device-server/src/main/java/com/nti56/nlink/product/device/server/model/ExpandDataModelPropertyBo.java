package com.nti56.nlink.product.device.server.model;


import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 类说明: 数据模型展开的属性pojo
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-21 13:21:47
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpandDataModelPropertyBo {

    //最底层名称
    // low
    private String name;

    //扩展名称
    // low
    private String expandName;

    private ThingDataTypeEnum dataType;

    private Boolean isArray;

    private Object defaultValue;
    
}
