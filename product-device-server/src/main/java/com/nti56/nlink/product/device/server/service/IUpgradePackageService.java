package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.UpgradePackageEntity;
import com.nti56.nlink.product.device.server.model.upgrade.dto.BatchUpgradePackageDTO;
import com.nti56.nlink.product.device.server.model.upgrade.dto.CreateUpgradePackageDTO;
import com.nti56.nlink.product.device.server.model.upgrade.dto.EditUpgradePackageDTO;
import com.nti56.nlink.product.device.server.model.upgrade.dto.QueryUpgradePackageDTO;
import com.nti56.nlink.product.device.server.model.upgrade.vo.UpgradePackageVO;
import com.nti56.nlink.product.device.server.model.upgrade.vo.UpgradeRangeVO;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-03-15 15:11:56
 * @since JDK 1.8
 */
public interface IUpgradePackageService extends IBaseService<UpgradePackageEntity> {
    
    Result<Page<UpgradePackageVO>> pageUpgradePackage(PageParam pageParam, QueryUpgradePackageDTO queryUpgradePackageDTO);
    
    Result<Void> createUpgradePackage(CreateUpgradePackageDTO createUpgradePackageDTO, MultipartFile file);
    
    Result<Void> editUpgradePackage(EditUpgradePackageDTO editUpgradePackageDTO);
    
    Result<String> getTargetVersion();
    
    Result<List<UpgradeRangeVO>> getUpgradeRange(Long tenantId);
    
    Result<String> updateLogView(Long edgeGatewayId, Long tenantId);
    
    Result<Void> downloadUpgradePackage(Long edgeGatewayId, Long tenantId);

    Result<UpgradePackageEntity> getUpgradePackageEntity(String upgradeVersion);

    Result<Void> executeUpgrade(Long edgeGatewayId, Long tenantId);
    
    Result<String> batchDownload(List<BatchUpgradePackageDTO> batchUpgradePackageDTOList);
    
    Result<String> batchExecuteUpgrade(List<BatchUpgradePackageDTO> batchUpgradePackageDTOList);
}
