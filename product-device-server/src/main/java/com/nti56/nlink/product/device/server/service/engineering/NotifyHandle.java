package com.nti56.nlink.product.device.server.service.engineering;

import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.client.model.dto.engineering.NotifyServiceDataDTO;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.service.IEngineeringNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/7 15:26<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class NotifyHandle {
    @Autowired
    private IEngineeringNotifyService engineeringNotifyService;

    public void handle(Long tenantId, NotifyServiceDataDTO notifyServiceData, boolean tenantIdEqual, Map<Long,Long> templateMap) {
        if(notifyServiceData == null){
            return;
        }

        HashMap<Long, Long> channelMap = new HashMap<>(notifyServiceData.getChannelList().size(),1);

        if (CollectionUtils.isNotEmpty(notifyServiceData.getChannelList())){
            notifyServiceData.getChannelList().forEach(c-> {
                long id = IdGenerator.generateId();
                channelMap.put(c.getId(),id);
                c.setId(id);
                if (!tenantIdEqual){
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(notifyServiceData.getTemplateList())){
            notifyServiceData.getTemplateList().forEach(c-> {
                long id = IdGenerator.generateId();
                templateMap.put(c.getId(),id);
                c.setId(id);
                c.setNotifyChannelId(channelMap.getOrDefault(c.getNotifyChannelId(),c.getNotifyChannelId()));
                if (!tenantIdEqual){
                    c.setTenantId(tenantId);
                }
            });
        }

        R r = R.result(engineeringNotifyService.deleteNotifyDataByTenantId(tenantId));
        if (!(boolean) r.get("ok")) {
            throw new BizException("服务器异常，请稍后重试4");
        }
        R r1 = R.result(engineeringNotifyService.createNotifyData(notifyServiceData));
        if (!(boolean) r1.get("ok")) {
            throw new BizException("服务器异常，请稍后重试5");
        }
        channelMap.clear();
        log.info("notify服务导入成功");
    }
}
