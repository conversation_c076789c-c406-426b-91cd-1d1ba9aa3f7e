package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 设备模型继承表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-27 19:24:57
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("device_model_inherit")
@ApiModel(value = "device_model_inherit", description = "设备模型继承表")
public class DeviceModelInheritEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id")
    private Long id;

    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private Long deviceId;

    /**
     * 继承的物模型id
     */
    @ApiModelProperty("继承的物模型id")
    private Long inheritThingModelId;

    /**
     * 继承顺序
     */
    @ApiModelProperty("继承顺序")
    private Integer sortNo;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "租户ID")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程id
     */
    @ApiModelProperty("工程id")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 空间id
     */
    @ApiModelProperty("空间id")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 模块id
     */
    @ApiModelProperty("模块id")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建人id
     */
    @ApiModelProperty("创建人id")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 修改人id
     */
    @ApiModelProperty("修改人id")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 修改人名称
     */
    @ApiModelProperty("修改人名称")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @Version
    private Integer version;

    /**
     * 是否删除
     */
    @ApiModelProperty("是否删除")
        @TableLogic
    private Integer deleted;

}
