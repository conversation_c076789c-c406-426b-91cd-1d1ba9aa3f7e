package com.nti56.nlink.product.device.server.scriptApi;

import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListTagRequest;
import com.nti56.nlink.product.device.server.serviceEngine.BaseService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName Thing
 * @date 2022/4/6 18:05
 * @Version 1.0
 */
public interface ThingSpi {

    /**
     * 获取物的所有属性
     * @return
     */
    Map<String,Object> readProperties();

    /**
     * 获取物服务列表
     * @return
     */
    Map<String, BaseService> getService();

    /**
     * 根据服务名称获取物服务
     * @param serviceName
     * @return
     */
    BaseService getServiceByName(String serviceName);

    /**
     * 获取物服务名列表;
     * @return
     */
    List<String> getServiceName();

    /**
     * 获取物属性的运行时数据
     * @param propertyName
     * @param <R>
     * @return
     */
    <R> R readProperty(String propertyName);

    /**
     * 获取物属性的运行时数据
     * @param propertyName
     * @param <R>
     * @return
     */
    <R> R spiReadProperty(String propertyName);

    Boolean writeTwin(String propertyName, Object value);

    /**
     * 写入属性值
     * @param propertyName
     * @return
     */
    Boolean writeProperty(String propertyName, Object value);
    
    /**
     * 写入属性值
     * @param propertyName
     * @return
     */
    Boolean writePropertyAndTwin(String propertyName, Object value);

    /**
     * 批量写入属性
     * @param properties k-属性名 v-属性值
     * @return
     */
    Map<String,Boolean> writeProperties(Map<String, Object> properties);

    /**
     * 执行本设备的物服务
     * @param inputData
     * @return
     */
    R callService(String serviceName, Map<String, Object> inputData);


    /**
     * 调用物服务
     * @param service
     * @param inputData
     * @return
     */
    R callService(BaseService service, Map<String, Object> inputData);

    R callService(String serviceName, Map<String, Object> input, DeviceServiceLogEntity logEntity);

    R callService(String serviceName, Map<String, Object> input, DeviceServiceLogEntity logEntity,Boolean isSourceSubscribe);


    /**
     * 保存数据
     */
    List queryData(String query);

    void sleep(int seconds);

    void sleep(long times,String unit);

    void initData();

    String getDeviceName();

    List<DeviceEntity> listDeviceByTag(String tagList);
}
