package com.nti56.nlink.product.device.server.verticle;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import io.netty.handler.codec.mqtt.MqttQoS;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.EventBus;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-04 11:54:40
 * @since JDK 1.8
 */
@Component
@Scope(SCOPE_PROTOTYPE)
@Slf4j
public class MqttSenderVerticle extends MqttBaseVerticle{
    
    public static final String EVENT_BUS_MQTT_SENDER_TOPIC = "EVENT_BUS_MQTT_SENDER_TOPIC";

    @Getter
    @Value("${mqtt.host}")
    private String host;

    @Getter
    @Value("${mqtt.port}")
    private Integer port;

    @Getter
    @Value("${mqtt.username}")
    private String username;

    @Getter
    @Value("${mqtt.ssl:true}")
    private Boolean ssl;
    
    @Getter
    @Value("${mqtt.password}")
    private String password;

    @Getter
    private Integer reconnectGapTime;
    
    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        log.debug("start-verticle");
        super.start(startPromise);
        
        EventBus eventBus = vertx.eventBus();
        eventBus.consumer(EVENT_BUS_MQTT_SENDER_TOPIC, msg -> {
            log.info("EVENT_BUS_MQTT_SENDER_TOPIC msg come: {}", msg.body());
            String topic = (String)msg.body();
            client.publish(topic, Buffer.buffer(), MqttQoS.AT_LEAST_ONCE, false, false);
        });

    }

    @Override
    public void subscribe() {

    }
    
    @Override
    protected void handleConnectStatusChange(Boolean connected) {
       
    }
}
