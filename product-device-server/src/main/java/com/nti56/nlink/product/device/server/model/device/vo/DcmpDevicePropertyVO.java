package com.nti56.nlink.product.device.server.model.device.vo;

import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/2/20 9:47<br/>
 * @since JDK 1.8
 */
@Data
public class DcmpDevicePropertyVO {

    @Schema(description = "属性名称")
    private String name;

    @Schema(description = "数据类型")
    private String dataType;

    @Schema(description = "是否只读")
    private Boolean readOnly;

    @Schema(description = "属性描述")
    private String descript;

    public static DcmpDevicePropertyVO getByPropertyElm(PropertyElm property){
        DcmpDevicePropertyVO dcmpDevicePropertyVO = new DcmpDevicePropertyVO();
        dcmpDevicePropertyVO.setName(property.getName());
        dcmpDevicePropertyVO.setDescript(property.getDescript());
        dcmpDevicePropertyVO.setReadOnly(property.getReadOnly());
        dcmpDevicePropertyVO.setDataType(property.getDataType().getType());
        return dcmpDevicePropertyVO;
    }
}
