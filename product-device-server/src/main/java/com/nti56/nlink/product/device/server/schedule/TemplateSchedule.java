package com.nti56.nlink.product.device.server.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.product.device.server.entity.NotifyServerLogEntity;
import com.nti56.nlink.product.device.server.entity.TemplateEntity;
import com.nti56.nlink.product.device.server.enums.AuditEnum;
import com.nti56.nlink.product.device.server.enums.NotifyServerLogEnum;
import com.nti56.nlink.product.device.server.mapper.NotifyServerLogMapper;
import com.nti56.nlink.product.device.server.service.ITemplateService;
import com.nti56.nlink.product.device.server.service.impl.NotifyStrategyContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/15 13:51<br/>
 * @since JDK 1.8
 */

@Slf4j
@Component
public class TemplateSchedule {

    @Autowired
    private ITemplateService templateService;

    @Autowired
    private NotifyStrategyContext notifyStrategyContext;

    @Autowired
    private NotifyServerLogMapper notifyServerLogMapper;

    @Scheduled(fixedDelay = 1000*60*30 , initialDelay = 1000*30)
    public void setAuditStatus(){
        log.info("查询审核中的模板的审核状态，并进行更新");
        LambdaQueryWrapper<TemplateEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TemplateEntity::getAuditStatus, AuditEnum.AUDITING.getValue());
        List<TemplateEntity> templateList = templateService.list(lqw);
        for (TemplateEntity template : templateList) {
            TenantIsolation tenantIsolation = BeanUtilsIntensifier.copyBean(template, TenantIsolation.class);
            notifyStrategyContext.setAuditStatus(template,tenantIsolation);
        }
    }

    @Scheduled(fixedDelay = 1000*60*60 , initialDelay = 1000*5)
    public void retryDeleteTemplate(){
        log.info("重新删除，原来删除失败的模板");
        List<NotifyServerLogEntity> notifyServerLogList = notifyServerLogMapper.listNewByLogType(NotifyServerLogEnum.DELETE_TEMPLATE_FAIL.getValue(),null);
        for (NotifyServerLogEntity notifyServerLogEntity : notifyServerLogList) {
            TenantIsolation tenantIsolation = BeanUtilsIntensifier.copyBean(notifyServerLogEntity, TenantIsolation.class);
            notifyStrategyContext.retryDeleteTemplate(notifyServerLogEntity,tenantIsolation);
        }
    }


}
