package com.nti56.nlink.product.device.server.util;

import cn.hutool.core.collection.CollectionUtil;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ServiceTypeEnum;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import com.nti56.nlink.product.device.server.mapper.ThingModelMapper;
import com.nti56.nlink.product.device.server.mapper.ThingServiceMapper;
import com.nti56.nlink.product.device.server.model.ThingModelSimpleBo;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明：
 *
 * @ClassName CacheUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/3/8 10:46
 * @Version 1.0
 */

@Component
@Slf4j
public class CacheUtils implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    private ThingModelMapper thingModelMapper;
    @Autowired
    private ThingServiceMapper thingServiceMapper;

    public static Map<Long, ThingModelSimpleBo> SYS_COMMON_MODEL = new HashMap<>();
    public static Map<Long, ThingServiceEntity> SYS_COMMON_THING_SERVICE = new HashMap<>();

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        
        log.info("start init common thing model dic");
        List<ThingModelSimpleBo> commonThingModels = thingModelMapper.listAllCommonThingModel(ModelTypeEnum.COMMON_MODEL.getValue());
        if(CollectionUtil.isNotEmpty(commonThingModels)){
            commonThingModels.forEach(item->{
                SYS_COMMON_MODEL.put(item.getId(),item);
            });
        }
        log.info("end init common thing model dic,size:{}",commonThingModels.size());
        log.info("-------------------------------------");
        log.info("start init common thing service dic");
        List<ThingServiceEntity> commonServices = thingServiceMapper.listAllCommonThingService(ServiceTypeEnum.COMMON_SERVICE.getValue());
        if(CollectionUtil.isNotEmpty(commonServices)){
            commonServices.forEach(item->{
                SYS_COMMON_THING_SERVICE.put(item.getId(), item);
            });
        }

        log.info("end init common thing service dic.size:{}",commonServices.size());

    }

}
