package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.CustomDriverDTO;
import com.nti56.nlink.product.device.client.model.dto.CustomDriverExportDTO;
import com.nti56.nlink.product.device.client.model.dto.CustomFieldDTO;
import com.nti56.nlink.product.device.client.model.dto.CustomMessageDTO;
import com.nti56.nlink.product.device.server.domain.thing.custom.CustomDriver;
import com.nti56.nlink.product.device.server.domain.thing.custom.CustomField;
import com.nti56.nlink.product.device.server.domain.thing.custom.CustomMessage;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.CustomDriverStatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.CustomFieldPartTypeEnum;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.CustomDriverEntity;
import com.nti56.nlink.product.device.server.entity.CustomFieldEntity;
import com.nti56.nlink.product.device.server.entity.CustomMessageEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.ChannelMapper;
import com.nti56.nlink.product.device.server.mapper.CustomDriverMapper;
import com.nti56.nlink.product.device.server.mapper.CustomFieldMapper;
import com.nti56.nlink.product.device.server.mapper.CustomMessageMapper;
import com.nti56.nlink.product.device.server.model.custom.CustomDriverBo;
import com.nti56.nlink.product.device.server.model.custom.CustomDriverDto;
import com.nti56.nlink.product.device.server.service.ICustomDriverService;
import com.nti56.nlink.product.device.server.service.ITaskService;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Nullable;

import javax.validation.constraints.NotNull;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 自定义协议表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-11-18 11:43:27
 * @since JDK 1.8
 */
@Service
@Slf4j
public class CustomDriverServiceImpl extends BaseServiceImpl<CustomDriverMapper,CustomDriverEntity> implements ICustomDriverService {

    @Autowired
    CustomDriverMapper mapper;

    @Autowired
    CustomFieldMapper customFieldMapper;

    @Autowired
    CustomMessageMapper customMessageMapper;

    @Autowired
    CommonFetcherFactory commonFetcherFactory;

    @Autowired
    ChannelMapper channelMapper;

    @Autowired
    ITaskService taskService;

    @Override
    @Transactional
    public Result<Long> create(TenantIsolation tenant, @NotNull CustomDriverDto dto) {
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenant.getTenantId());
        
        //创建CustomDriver
        Result<CustomDriver> customDriverResult = CustomDriver.checkCreate(
            dto.getCustomDriverEntity(), 
            dto.getFieldEntityList(),
            commonFetcher
        );
        if(!customDriverResult.getSignal()){
            return Result.error(customDriverResult.getMessage());
        }

        CustomDriver customDriver = customDriverResult.getResult();
        CustomDriverEntity customDriverEntity = customDriver.toEntity();

        Integer c1 = mapper.insert(customDriverEntity);
        if(c1 == null || c1 != 1){
            return Result.error("插入协议失败");
        }
        
        {
            List<CustomField> fieldList =  customDriver.getFixHeaderFieldList();

            if(fieldList == null || fieldList.size() <= 0){
                return Result.ok(customDriverEntity.getId());
            }

            //创建fixHeader
            for(CustomField field:fieldList){
                CustomFieldEntity fieldEntity = field.toEntity(false);
                fieldEntity.setTargetId(customDriverEntity.getId());
                Integer c2 = customFieldMapper.insert(fieldEntity);
                if(c2 == null || c2 != 1){
                    throw new BizException("插入协议字段失败"); //回滚
                }
            }
        }
        {
            List<CustomField> fieldList =  customDriver.getFixTailFieldList();

            if(fieldList == null || fieldList.size() <= 0){
                return Result.ok(customDriverEntity.getId());
            }

            //创建fixTail
            for(CustomField field:fieldList){
                CustomFieldEntity fieldEntity = field.toEntity(false);
                fieldEntity.setTargetId(customDriverEntity.getId());
                Integer c2 = customFieldMapper.insert(fieldEntity);
                if(c2 == null || c2 != 1){
                    throw new BizException("插入协议字段失败"); //回滚
                }
            }
        }
        return Result.ok(customDriverEntity.getId());
    }

    private void deleteSub(Long tenantId, Long customDriverId){

        //删除所有fixHeaderField
        customFieldMapper.deleteFixHeaderTailFieldByDriverId(tenantId, customDriverId);
        
        //删除所有messageField
        customFieldMapper.deleteMessageFieldByDriverId(tenantId, customDriverId);

        //删除所有message
        customMessageMapper.deleteByDriverId(tenantId, customDriverId);

    }

    @Override
    @Transactional
    public Result<Void> update(TenantIsolation tenant, @NotNull CustomDriverDto dto) {
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenant.getTenantId());
        
        //检查CustomDriver
        Result<CustomDriver> customDriverResult = CustomDriver.checkUpdate(
            dto.getCustomDriverEntity(), 
            dto.getFieldEntityList(),
            commonFetcher
        );
        if(!customDriverResult.getSignal()){
            return Result.error(customDriverResult.getMessage());
        }

        CustomDriver customDriver = customDriverResult.getResult();

        deleteSub(tenant.getTenantId(), customDriver.getId());

        //更新customDriver
        CustomDriverEntity customDriverEntity = customDriver.toEntity();
        Integer c1 = mapper.updateById(customDriverEntity);
        if(c1 == null || c1 != 1){
            throw new BizException("插入协议失败");
        }

        //插入新的fixHeaderField
        List<CustomField> fixHeaderFieldList = customDriver.getFixHeaderFieldList();
        if(fixHeaderFieldList != null && fixHeaderFieldList.size() > 0){
            //创建fixHeader
            for(CustomField field:fixHeaderFieldList){
                CustomFieldEntity fieldEntity = field.toEntity(false);
                fieldEntity.setTargetId(customDriverEntity.getId());
                Integer c2 = customFieldMapper.insert(fieldEntity);
                if(c2 == null || c2 != 1){
                    throw new BizException("插入协议字段失败"); //回滚
                }
            }
        }

        //插入新的fixHeaderField
        List<CustomField> fixTailFieldList = customDriver.getFixTailFieldList();
        if(fixTailFieldList != null && fixTailFieldList.size() > 0){
            //创建fixHeader
            for(CustomField field:fixTailFieldList){
                CustomFieldEntity fieldEntity = field.toEntity(false);
                fieldEntity.setTargetId(customDriverEntity.getId());
                Integer c2 = customFieldMapper.insert(fieldEntity);
                if(c2 == null || c2 != 1){
                    throw new BizException("插入协议字段失败"); //回滚
                }
            }
        }
        //插入新的message和messageField
        List<CustomMessage> messageList = customDriver.getMessageList();
        if(messageList != null && messageList.size() > 0){
            //创建message
            for(CustomMessage message:messageList){
                CustomMessageEntity messageEntity = message.toEntity();
                messageEntity.setCustomDriverId(customDriverEntity.getId());
                customMessageMapper.insert(messageEntity);
                List<CustomField> messageFieldList = message.getMessageFieldList();
                if(messageFieldList == null || messageFieldList.size() <= 0){
                    continue;
                }
                for(CustomField field:messageFieldList){
                    CustomFieldEntity fieldEntity = field.toEntity(false);
                    fieldEntity.setTargetId(messageEntity.getId());
                    Integer c3 = customFieldMapper.insert(fieldEntity);
                    if(c3 == null || c3 != 1){
                        throw new BizException("插入消息字段失败"); //回滚
                    }
                }
            }
        }

        checkAfterUpdate(tenant.getTenantId(), customDriver.getId());

        return Result.ok();
    }

    private void checkAfterUpdate(Long tenantId, Long customDriverId){

        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        
        //检查CustomDriver
        Result<CustomDriver> customDriverResult = CustomDriver.checkInfoToMessage(
            customDriverId, 
            commonFetcher
        );
        if(!customDriverResult.getSignal()){
            throw new BizException(customDriverResult.getMessage()); //回滚
        }
    }

    @Override
    public Result<Page<CustomDriverBo>> getPage(TenantIsolation tenant, @Nullable CustomDriverEntity entity, Page<CustomDriverEntity> page) {
        entity.setTenantId(tenant.getTenantId());
        String likeStr = entity.getDriverName();
        entity.setDriverName(null);
        
        Page<CustomDriverEntity> list = mapper.selectPage(
            page, 
            new QueryWrapper<>(entity).like(
                likeStr != null && !"".equals(likeStr),
                "driver_name",
                likeStr
            )
        );

        Page<CustomDriverBo> l = new Page<>();
        l.setTotal(list.getTotal());
        l.setCurrent(list.getCurrent());
        l.setSize(list.getSize());
        l.setPages(list.getPages());

        List<CustomDriverEntity> records = list.getRecords();
        if(records != null && records.size() > 0){
            List<Long> ids = records.stream().map(CustomDriverEntity::getId).collect(Collectors.toList());
            List<CustomMessageEntity> messageList = customMessageMapper.listByDriverIds(tenant.getTenantId(), ids);
            Map<Long, List<CustomMessageEntity>> map = messageList.stream().collect(Collectors.groupingBy(CustomMessageEntity::getCustomDriverId));
            List<CustomDriverBo> collect = records.stream().map(t -> {
                CustomDriverBo bo = new CustomDriverBo();
                bo.setDriverEntity(t);
                bo.setMessageEntityList(map.get(t.getId()));
                return bo;
            }).collect(Collectors.toList());
            
            l.setRecords(collect);
        }else{
            l.setRecords(new ArrayList<>());
        }
        
        return Result.ok(l);
    }

    @Override
    public Result<List<CustomDriverEntity>> list(TenantIsolation tenant, CustomDriverEntity entity) {
        entity.setTenantId(tenant.getTenantId());
        entity.setStatus(CustomDriverStatusEnum.ENABLED.getValue());
        List<CustomDriverEntity> list = mapper.selectList(new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    /**
     * result: true-协议被使用 false-协议没有被使用
     */
    private Result<Boolean> checkDriverUsed(Long tenantId, String driverName){
        if(driverName == null || "".equals(driverName)){
            return Result.ok(false);
        }
        List<ChannelEntity> customDriverChannelList = channelMapper.listCustomDriverChannelByName(tenantId, driverName);
        if(customDriverChannelList != null && customDriverChannelList.size() > 0){
            return Result.ok(true);
        }else{
            return Result.ok(false);
        }
    }

    @Override
    @Transactional
    public Result<Void> deleteById(TenantIsolation tenant, @NotNull Long entityId) {
        //删除自定义协议时，校验通道是否在使用
        CustomDriverEntity entity = mapper.getById(tenant.getTenantId(), entityId);
        if(entity == null){
            return Result.error("找不到自定义协议");
        }
        if(CustomDriverStatusEnum.ENABLED.getValue().equals(entity.getStatus())){
            return Result.error("自定义协议启用中，请先停用后再删除");
        }
        Result<Boolean> checkResult = checkDriverUsed(tenant.getTenantId(), entity.getDriverName());
        if(!checkResult.getSignal()){
            return Result.error(checkResult.getMessage());
        }
        Boolean driverUsed = checkResult.getResult();
        if(driverUsed){
            return Result.error("协议被通道使用，请先删除相关通道");
        }

        deleteSub(tenant.getTenantId(), entityId);

        if (mapper.deleteById(entityId) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    @Override
    public Result<CustomDriverDto> getById(TenantIsolation tenant, @NotNull Long customDriverId) {
        CustomDriverEntity entity = mapper.selectById(customDriverId);
        List<CustomFieldEntity> list = customFieldMapper.listFixHeaderTailFieldByDriverId(tenant.getTenantId(), customDriverId);

        CustomDriverDto dto = new CustomDriverDto();
        dto.setCustomDriverEntity(entity);
        dto.setFieldEntityList(list);
        return Result.ok(dto);
    }

    @Override
    public Result<CustomDriverExportDTO> exportCustomDriver(TenantIsolation tenant, Long driverId) {

        CustomDriverEntity customDriverEntity = mapper.selectById(driverId);
        CustomDriverDTO customDriverDto = BeanUtilsIntensifier.copyBean(customDriverEntity, CustomDriverDTO.class);
        if (Objects.isNull(customDriverDto)) {
            return Result.error("找不到对应协议信息");
        }
        List<CustomFieldEntity> customFieldEntities = customFieldMapper.listFixHeaderTailFieldByDriverId(tenant.getTenantId(), driverId);
        List<CustomFieldDTO> customFieldDTOS = BeanUtilsIntensifier.copyBeanList(customFieldEntities, CustomFieldDTO.class);
        customDriverDto.setFixEntityList(customFieldDTOS);

        //查找数据包和数据包字段
        List<CustomMessageEntity> messageList = customMessageMapper.listByDriverIds(tenant.getTenantId(), Lists.newArrayList(driverId));
        List<CustomMessageDTO> customMessageDTOS = Lists.newArrayList();
        Map<Long, List<CustomFieldDTO>> customFieldsMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(messageList)) {
            customMessageDTOS = BeanUtilsIntensifier.copyBeanList(messageList, CustomMessageDTO.class);
            List<Long> messageIds = messageList.stream().map(CustomMessageEntity::getId).collect(Collectors.toList());
            for (Long messageId : messageIds) {
                List<CustomFieldEntity> customFieldEntityList = customFieldMapper.listByMessageId(tenant.getTenantId(), messageId);
                customFieldsMap.put(messageId, BeanUtilsIntensifier.copyBeanList(customFieldEntityList, CustomFieldDTO.class));
            }
        }
        return Result.ok(CustomDriverExportDTO.builder()
                .customDriverDTO(customDriverDto)
                .customMessageDTOS(customMessageDTOS)
                .customFieldsMap(customFieldsMap)
                .build()
        );
    }

    @Override
    @Transactional
    public Result<Void> importCustomDriver(TenantIsolation tenantIsolation, MultipartFile file, String driverName) {

        if (file.isEmpty()) {
            throw new BizException("文件为空");
        }
        if(StringUtils.isBlank(driverName)){
            throw new BizException("自定义协议名称为空");
        }
        String originalFilename = file.getOriginalFilename();
        try {
            if (!".cpc".equals(originalFilename.substring(originalFilename.lastIndexOf(".")))) {
                throw new BizException("文件格式错误");
            }
        } catch (Exception e) {
            throw new BizException("文件格式错误");
        }
        try{
            byte[] bytes = file.getBytes();
            String customDriverStr = new String(bytes, StandardCharsets.UTF_8);
            if(StrUtil.isNotBlank(customDriverStr)){
                //转成对象
                CustomDriverExportDTO customDriverExportDTO = JSONUtil.toBean(customDriverStr, CustomDriverExportDTO.class);
                //处理插入
                CustomDriverDTO customDriverDTO = customDriverExportDTO.getCustomDriverDTO();
                if(Objects.isNull(customDriverDTO)) {
                    return Result.error("没有需要导入的协议");
                }
                //判断是否有重名协议定义 如果有改名_copy
                QueryWrapper<CustomDriverEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("driver_name", driverName);
                queryWrapper.eq("TENANT_ID", tenantIsolation.getTenantId());
                CustomDriverEntity customDriverEntity = mapper.selectOne(queryWrapper);
                if(!Objects.isNull(customDriverEntity)){
//                    customDriverDTO.setDriverName(customDriverDTO.getDriverName()+"_copy");
                    return Result.error("协议名称重复！");
                }
                //重置id
                customDriverDTO.setId(null);
                customDriverDTO.setTenantId(tenantIsolation.getTenantId());
                if(!RegexUtil.checkName(driverName)){
                    return Result.error("协议名只能是英文、字母、下划线，且英文开头");
                }
                if(driverName.length() > 255){
                    return Result.error("协议名长度不能超过255");
                }
                customDriverDTO.setDriverName(driverName);
                CustomDriverEntity newDriverEntity = BeanUtilsIntensifier.copyBean(customDriverDTO, CustomDriverEntity.class);
                newDriverEntity.setStatus(CustomDriverStatusEnum.DISABLED.getValue());
                mapper.insert(newDriverEntity);
                //插入固定头尾字段
                List<CustomFieldDTO> fixEntityList = customDriverDTO.getFixEntityList();
                List<CustomFieldEntity> fieldEntities = new ArrayList<>(fixEntityList.size());
                for (CustomFieldDTO customFieldDTO : fixEntityList) {
                    customFieldDTO.setId(null);
                    customFieldDTO.setTenantId(tenantIsolation.getTenantId());
                    customFieldDTO.setTargetId(newDriverEntity.getId());
                    fieldEntities.add(BeanUtilsIntensifier.copyBean(customFieldDTO, CustomFieldEntity.class));
                }
                customFieldMapper.insertBatchSomeColumn(fieldEntities);
                //插入数据包
                List<CustomMessageDTO> customMessageDTOS = customDriverExportDTO.getCustomMessageDTOS();
                Map<Long, List<CustomFieldDTO>> customFieldsMap = customDriverExportDTO.getCustomFieldsMap();
                if(CollectionUtil.isNotEmpty(customMessageDTOS)){
                    for (CustomMessageDTO customMessageDTO : customMessageDTOS) {
                        //获取数据包定义的字段
                        Long oldId = customMessageDTO.getId();
                        List<CustomFieldDTO> customFieldDTOS = customFieldsMap.get(oldId);
                        //插入数据包
                        customMessageDTO.setCustomDriverId(newDriverEntity.getId());
                        customMessageDTO.setId(null);
                        customMessageDTO.setTenantId(tenantIsolation.getTenantId());
                        CustomMessageEntity customMessageEntity = BeanUtilsIntensifier.copyBean(customMessageDTO, CustomMessageEntity.class);
                        customMessageMapper.insert(customMessageEntity);
                        //插入字段定义
                        List<CustomFieldEntity> dataFields = new ArrayList<>(customFieldDTOS.size());
                        for (CustomFieldDTO customFieldDTO : customFieldDTOS) {
                            customFieldDTO.setTargetId(customMessageEntity.getId());
                            customFieldDTO.setTenantId(tenantIsolation.getTenantId());
                            customFieldDTO.setId(null);
                            dataFields.add(BeanUtilsIntensifier.copyBean(customFieldDTO,CustomFieldEntity.class));
                        }
                        customFieldMapper.insertBatchSomeColumn(dataFields);
                    }
                }
                return Result.ok();
            }else {
                return Result.error("插入失败，文件中不包含协议信息");
            }
        }catch (IOException ie){
            log.error("读取自定义协议配置文件异常！异常信息：{}",ie.getMessage());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> enableById(TenantIsolation tenant, Long customDriverId) {
        CustomDriverEntity entity = mapper.getById(tenant.getTenantId(), customDriverId);
        if(entity == null){
            return Result.error("找不到自定义协议");
        }
        Integer count = mapper.updateStatusById(tenant.getTenantId(), customDriverId, CustomDriverStatusEnum.ENABLED.getValue());
        taskService.updateCustomDriverRuntimeInfoById(tenant.getTenantId(), customDriverId);
        return Result.ok();
    }

    @Override
    public Result<Void> disableById(TenantIsolation tenant, Long customDriverId) {
        CustomDriverEntity entity = mapper.getById(tenant.getTenantId(), customDriverId);
        if(entity == null){
            return Result.error("找不到自定义协议");
        }
        Integer count = mapper.updateStatusById(tenant.getTenantId(), customDriverId, CustomDriverStatusEnum.DISABLED.getValue());
        return Result.ok();
    }

}
