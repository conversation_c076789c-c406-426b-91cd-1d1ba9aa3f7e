package com.nti56.nlink.product.device.server.domain.thing.up;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: 网关上传数据
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-22 09:14:39
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpData {

    private Long timestamp;

    //TODO: 先兼容旧版，后面要删除
    private List<UpProp> prop;

    /**
     * 按分组上报
     */
    private List<UpGroup> upGroups;

    /**
     * 故障开始时间戳
     */
    private Long faultBegin;

    /**
     * 故障结束时间戳
     */
    private Long faultEnd;

    /**
     * 故障状态：true-开始、false-停止
     */
    private Boolean faultStatus;

}
