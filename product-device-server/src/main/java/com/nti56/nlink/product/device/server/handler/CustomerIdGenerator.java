package com.nti56.nlink.product.device.server.handler;


import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.nti56.nlink.common.util.IdGenerator;

import org.springframework.stereotype.Component;

@Component
public class CustomerIdGenerator implements IdentifierGenerator {
    @Override
    public Long nextId(Object entity) {
        // 填充自己的Id生成器，
        return IdGenerator.generateId();
    }
}
