package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceModelInheritEntity;
import com.nti56.nlink.product.device.server.model.DeviceDto;
import com.nti56.nlink.product.device.server.model.thingModel.vo.ThingModelVO;

import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:11:58
 * @since JDK 1.8
 */
public interface IDeviceModelInheritService extends IBaseService<DeviceModelInheritEntity> {

    Result<Void> deleteByDeviceId(Long deviceId);

    Result<Void> resetInheritRelation(TenantIsolation tenantIsolation, DeviceEntity deviceEntity, List<Long> thingModelIds);

    default void resetInheritRelation(DeviceDto dto){
        TenantIsolation tenantIsolation = BeanUtilsIntensifier.copyBean(dto,TenantIsolation.class);
        resetInheritRelation(tenantIsolation,dto, dto.getThingModelIds());
    }

    List<ThingModelVO> listByDeviceId(Long id);

    Result<Void> deleteByIdsAndDeviceId(List<Long> deleteThingModelIds, Long deviceId);

    void batchDeleteByDeviceIds(Long tenantId, List<Long> ids);

    Result<List<DeviceModelInheritEntity>> listByDeviceIds(Long tenantId, List<Long> ids);
}
