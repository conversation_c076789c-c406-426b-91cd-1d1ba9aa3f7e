package com.nti56.nlink.product.device.server.model.edgegateway.dto;

import com.nti56.nlink.product.device.server.model.connector.dto.EditGatewayConnectorDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/5/5 15:39<br/>
 * @since JDK 1.8
 */
@Data
public class EditEdgeGatewayDTO {

    @NotNull(message = "唯一标识不能为空")
    private Long id;

    @Schema(description = "名称")
    @NotBlank(message = "网关名称不能为空")
    @Length(max = 32,message = "网关名称不能超过32个字符")
    private String name;

    @Schema(description = "描述")
    @Length(max = 256,message = "网关描述不能超过256个字符")
    private String descript;

    @Schema(description = "是否访问公共MQTT接口")
    private Boolean visitPublicMqtt;
    
    @Schema(description = "外部mqtt主机")
    private String publicMqttIp;

    @Schema(description = "外部mqtt端口")
    private Integer publicMqttPort;

    @Schema(description = "网关类型 虚拟网关/网关设备 ")
    @NotNull(message = "网关类型不能为空")
    private Integer type;

    @Schema(description = "标记id列表")
    private List<Long> tagIds;

    @Schema(description = "主机")
    @Pattern(regexp = "((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$",message = "ip格式错误")
    private String host;
    /**
     * port
     */
    @Schema(description = "端口")
    @Min(value = 0,message = "管理端口应在0-65535")
    @Max(value = 65535, message = "管理端口应在0-65535")
    private Integer port;

    @Schema(description = "IMEI")
    @Pattern(regexp = "\\d{15,20}$",message = "IMEI格式错误")
    private String imei;

    @Schema(description = "流量卡号")
    private String trafficCard;

    @Schema(description = "运营商 1-移动，2-电信，3-联通")
    @Min(value = 1,message = "运营商格式错误")
    @Max(value = 3,message = "运营商格式错误")
    private Integer operators;

    @Schema(description = "type为3时,直连网关连接器信息")
    private List<EditGatewayConnectorDTO> editGatewayConnectorDTOList;

}
