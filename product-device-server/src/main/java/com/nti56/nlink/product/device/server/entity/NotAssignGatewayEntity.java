package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 未分配 网关列表
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2023-4-20 15:28:36
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "未分配网关列表")
@TableName("not_assign_gateway")
public class NotAssignGatewayEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 上报imei号
     */
    @Schema(description = "上报imei号")
    private String imei;

    /**
     * 上报端口
     */
    @Schema(description = "上报管理端口")
    private Integer adminPort;

    /**
     * 上报ip
     */
    @Schema(description = "上报ip/host")
    private String host;

    /**
     * 上报port
     */
    @Schema(description = "上报port")
    private Integer port;

    /**
     * 上报租户id
     */
    @Schema(description = "上报租户id")
    private Long tenantId;

    /**
     * 上报网关id
     */
    @Schema(description = "上报网关id")
    private Long edgeGatewayId;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    @TableField(fill = FieldFill.INSERT)
    private String creator;


    /**
     * 修改人名称
     */
    @Schema(description = "修改人名称")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改人id
     */
    @Schema(description = "修改人id")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
