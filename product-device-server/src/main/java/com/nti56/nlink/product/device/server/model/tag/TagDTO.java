package com.nti56.nlink.product.device.server.model.tag;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/4 17:35<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "tag")
public class TagDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 键
     */
    @Schema(description = "键")
    private String tagKey;
    /**
     *值
     */

    @Schema(description = "值")
    private String tagValue;

    /**
     * 租户id
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 逻辑删除，1-删除
     */
    @Schema(description = "逻辑删除，1-删除")
    private Byte deleted;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
