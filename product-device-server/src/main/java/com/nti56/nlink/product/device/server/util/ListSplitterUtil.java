package com.nti56.nlink.product.device.server.util;

import java.util.ArrayList;
import java.util.List;

public class ListSplitterUtil {

    public static <T> List<List<T>> splitList(List<T> originalList, int batchSize) {
        List<List<T>> splitLists = new ArrayList<>();

        for (int i = 0; i < originalList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, originalList.size());
            List<T> sublist = originalList.subList(i, endIndex);
            splitLists.add(sublist);
        }

        return splitLists;
    }
}
