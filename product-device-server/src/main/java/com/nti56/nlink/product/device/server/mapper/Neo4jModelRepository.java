//package com.nti56.nlink.product.device.server.mapper;
//
//import com.nti56.nlink.product.device.server.model.neo4j.Neo4jModelDTO;
//import org.neo4j.ogm.model.Result;
//import org.springframework.data.neo4j.annotation.Query;
//import org.springframework.data.neo4j.repository.Neo4jRepository;
//import org.springframework.data.repository.query.Param;
//
//
///**
// * 类说明：
// *
// * @ClassName Neo4jModelRepository
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/11/16 17:49
// * @Version 1.0
// */
//
//public interface Neo4jModelRepository extends Neo4jRepository<Neo4jModelDTO,Long> {
//
//    @Query(value = "MATCH path = (t:THING_MODEL {modelId: $modelId })-[:INHERIT*0..]->(p:THING_MODEL) WITH collect(path) AS paths CALL apoc.convert.toTree(paths) YIELD value RETURN value;")
//    Result queryModelInherit(@Param("modelId") Long modelId);
//
//    @Query(value = "MATCH path = (t:THING_MODEL {name: $modelName,tenantId: $tenantId })-[:INHERIT*0..]->(p:THING_MODEL) WITH collect(path) AS paths CALL apoc.convert.toTree(paths) YIELD value RETURN value;")
//    Result queryModelInheritByName(@Param("modelName") String modelName, @Param("tenantId") Long tenantId);
//
//    @Query(value = "MATCH path = (t:THING_MODEL {modelId: $modelId })<-[:INHERIT*0..]-(p) WITH collect(path) AS paths CALL apoc.convert.toTree(paths) YIELD value RETURN value;")
//    Result queryWhichInheritMe(@Param("modelId") long modelId);
//
//    @Query(value = "MATCH path = (t:THING_MODEL {name: $modelName,tenantId: $tenantId })<-[:INHERIT*0..]-(p) WITH collect(path) AS paths CALL apoc.convert.toTree(paths) YIELD value RETURN value;")
//    Result queryWhichInheritMeByName(@Param("modelName") String modelName, @Param("tenantId") Long tenantId);
//
//    @Query("MATCH (:THING_MODEL {modelId: $modelId })-[:INHERIT*] -> (mx) return collect(mx.modelId)")
//    Result collectorInheritModelId(@Param("modelId") Long modelId);
//
//
//    @Query("MATCH (:THING_MODEL {modelId: $id })-[r:INHERIT] -> () DELETE r")
//    Result deleteInhertedInRelationById(@Param("id") Long id);
//
//    @Query("MATCH (n:THING_MODEL {tenantId: $tenantId}) DETACH DELETE n")
//    Result deleteByTenantId(@Param("tenantId") Long tenantId);
//
//    @Query("MATCH (n:THING_MODEL {modelId: $id}), (t:THING_MODEL {modelId: $modelId}) create (n)-[r:INHERIT]->(t) return type(r)")
//    Result addModelInheritRelation(@Param("id") Long id, @Param("modelId") Long modelId);
//}
