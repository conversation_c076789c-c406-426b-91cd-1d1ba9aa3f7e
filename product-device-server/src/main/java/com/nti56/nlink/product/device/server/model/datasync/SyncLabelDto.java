package com.nti56.nlink.product.device.server.model.datasync;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/********************************************************************************
 *
 * Title: 
 * Description: 
 * <AUTHOR>
 * create date on 2023/5/11
 * @version 1.0.0
 *
 *******************************************************************************/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "标签")
public class SyncLabelDto {
    
    /**
     * id
     */
    private Long id;
    
    /**
     * 标签分组id
     */
    @Schema(description = "标签分组id")
    private Long labelGroupId;
    
    /**
     * 标签名称
     */
    @Schema(description = "标签名称")
    private String name;
    
    /**
     * 别名
     */
    @Schema(description = "别名")
    private String alias;
    
    /**
     * 标签描述
     */
    @Schema(description = "标签描述")
    private String descript;
    
    /**
     * 地址，如DB50.DBB1
     */
    @Schema(description = "地址，如DB50.DBB1")
    private String address;
    
    /**
     * 长度
     */
    @Schema(description = "长度")
    private Integer length;
    
    /**
     * 数据类型，bool/byte/short/int/float/string
     */
    @Schema(description = "数据类型，bool/byte/short/int/float/string/ushort/double")
    private String dataType;
    
    /**
     * 是否数组
     */
    @Schema(description = "是否数组")
    private Boolean isArray;
    
    /**
     * type是string类型时，表示string元素的byte长度，其他type类型放空
     */
    @Schema(description = "type是string类型时，表示string元素的byte长度，其他type类型放空")
    private Integer stringBytes;
    
    /**
     * 时间间隔，单位毫秒
     */
    @Schema(description = "时间间隔，单位毫秒")
    private Integer intervalMs;
    
    /**
     * 是否只读
     */
    @Schema(description = "是否只读")
    private Boolean readOnly;
}
