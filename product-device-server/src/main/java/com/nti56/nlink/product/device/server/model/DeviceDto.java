package com.nti56.nlink.product.device.server.model;


import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.model.thingModel.vo.ThingModelVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 类说明: 设备dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:58
 * @since JDK 1.8
 */
@Data
@Schema(description = "设备对象")
public class DeviceDto extends DeviceEntity{

    @Schema(description = "所属网关名称")
    private String edgeGatewayName;

    @Schema(description = "标记实体")
    private List<TagRsp> tags;

    @Schema(description = "标记Id数组")
    private List<String> tagIds;

    @Schema(description = "继承的物模型Id")
    private List<Long> thingModelIds;

    @Schema(description = "继承的物模型,修改时回显")
    private List<ThingModelVO> thingModels;

    private String fastThingModelName;

    /**
     * 修改前的名称
     */
    private String beforeUpdateName;

    private List<Long> idList;

    @Schema(description = "网关类型 虚拟网关/网关设备 ")
    private Integer edgeGatewayType;

}
