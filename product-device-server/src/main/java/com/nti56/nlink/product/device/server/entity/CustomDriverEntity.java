package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.nti56.nlink.product.device.client.model.dto.json.CustomDriverRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.CustomDriverSpecialConfigField;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 自定义协议表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-11-18 11:17:00
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("custom_driver")
@Schema(description = "自定义协议表")
public class CustomDriverEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @Schema(description = "id")
    @TableId(value = "id")
    private Long id;

    /**
     * 协议名称
     */
    @Schema(description = "协议名称")
    private String driverName;

    /**
     * 描述
     */
    @Schema(description = "描述")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String descript;

    @Schema(description = "状态，1-启用，0-停用")
    private Integer status;

    /**
     * 格式类型，1-固定头fixHeader
     */
    @Schema(description = "格式类型，1-固定头fixHeader")
    private Integer formatType;

    /**
     * 大小端，1-大端bigEndian，2-小端littleEndian
     */
    @Schema(description = "大小端，1-大端bigEndian，2-小端littleEndian")
    private Integer endian;

    /**
     * 总报文长度读取字段，英文逗号分隔
     */
    @Schema(description = "总报文长度读取字段，英文逗号分隔")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String readLengthFields;

    @Schema(description = "总报文长度读取字段，英文逗号分隔")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer extraLength;

    @Schema(description = "运行时信息")
    private CustomDriverRuntimeInfoField runtimeInfo;

    @Schema(description = "定制配置")
    private CustomDriverSpecialConfigField specialConfig;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

    /**
     * 删除
     */
    @Schema(description = "删除")
    @TableLogic
    private Integer deleted;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @Schema(description = "工程ID")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @Schema(description = "模块ID")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @Schema(description = "空间ID")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;



}
