package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.product.device.server.entity.CustomMessageEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nti56.nlink.common.mybatis.CommonMapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 自定义消息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18 11:17:01
 */
@Mapper
public interface CustomMessageMapper extends CommonMapper<CustomMessageEntity> {

    @Delete("DELETE FROM custom_message WHERE tenant_id = #{tenantId} AND custom_driver_id = #{customDriverId}")
    Integer deleteByDriverId(@Param("tenantId") Long tenantId, @Param("customDriverId") Long customDriverId);

    List<CustomMessageEntity> listByDriverIds(@Param("tenantId") Long tenantId, @Param("customDriverIds") List<Long> customDriverIds);

}
