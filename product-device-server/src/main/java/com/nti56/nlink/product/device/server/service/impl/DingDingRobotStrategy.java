package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.google.common.collect.Lists;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.send.NotifyDTO;
import com.nti56.nlink.product.device.server.constant.AliYunConstant;
import com.nti56.nlink.product.device.server.constant.DingDingConstant;
import com.nti56.nlink.product.device.server.constant.NotifyConstant;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.entity.NotifyChannelEntity;
import com.nti56.nlink.product.device.server.entity.NotifyServerLogEntity;
import com.nti56.nlink.product.device.server.entity.TemplateEntity;
import com.nti56.nlink.product.device.server.enums.NotifyServerLogEnum;
import com.nti56.nlink.product.device.server.enums.error.NotifyErrorEnum;
import com.nti56.nlink.product.device.server.service.INotifyServerLogService;
import com.nti56.nlink.product.device.server.service.NotifyStrategy;
import com.nti56.nlink.product.device.server.service.handler.DingChannelThreshold;
import com.nti56.nlink.product.device.server.service.handler.LeakyBucketMessageSender;
import com.nti56.nlink.product.device.server.service.handler.MessageRequest;
import com.nti56.nlink.product.device.server.util.RSAUtils;
import com.nti56.nlink.product.device.server.util.aliyun.DingDingRobot;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RSemaphore;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/7/7 10:50<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class DingDingRobotStrategy  extends NotifyStrategy {

    @Autowired
    private INotifyServerLogService logService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RedissonClient redissonClient;



    @Override
    public Result validationChannel(NotifyChannelEntity notifyChannelEntity) {
        JSONObject channelParamsJson = JSON.parseObject(notifyChannelEntity.getParams());
        DingTalkClient client;
        Integer gap = channelParamsJson.getInteger(DingDingConstant.SEND_GAP_SECOND);
        if(Objects.isNull(gap)){
            //钉钉机器人通道默认发送间隔
            channelParamsJson.put(DingDingConstant.SEND_GAP_SECOND, DingDingConstant.DEFAULT_SEND_GAP_SECOND);
            notifyChannelEntity.setParams(JSONUtil.toJsonStr(channelParamsJson));
        }
        try {
            client = DingDingRobot.createClient(channelParamsJson.getString(AliYunConstant.WEBHOOK),
                    RSAUtils.decryptByPrivateKey(channelParamsJson.getString(AliYunConstant.SECRET),NotifyConstant.PRIVATE_KEY));
        }catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error(NotifyErrorEnum.DING_DING_CREATE_CLIENT_FAIL.getCode(), NotifyErrorEnum.DING_DING_CREATE_CLIENT_FAIL.getMessage());
        }

        OapiRobotSendRequest request = DingDingRobot.createTextRequest(AliYunConstant.DEFAULT_CONNECTION_CONTEND, null, null, false);

        try {
            OapiRobotSendResponse response = client.execute(request);
            if (!response.isSuccess()) {
                return Result.error(NotifyErrorEnum.DING_DING_SEND_NOTIFY_FAIL.getCode(),response.getErrmsg());
            }
        } catch (ApiException e) {
            log.error(e.getMessage(),e);
            return Result.error(NotifyErrorEnum.DING_DING_SEND_NOTIFY_FAIL.getCode(),NotifyErrorEnum.DING_DING_SEND_NOTIFY_FAIL.getMessage());
        }

        return Result.ok();
    }

    public static void main(String[] args) throws Exception {
        DingTalkClient client = DingDingRobot.createClient("https://oapi.dingtalk.com/robot/send?access_token=0410bfe85f8c9a9f8a4cea79acc498bd9af703da8b82873949a1aea8fa146e9b",
                "SEC88f87d7408f76121bad8583039dfc15e93ad8479ce5608e7d6be944601900f22");
        OapiRobotSendRequest request = DingDingRobot.createMarkdownRequest("#### 杭州天气 @150XXXXXXXX \\n> 9度，西北风1级，空气良89，相对温度73%\\n> ![screenshot](https://img.alicdn.com/tfs/TB1NwmBEL9TBuNjy1zbXXXpepXa-2400-1218.png)\\n> ###### 10点20分发布 [天气](https://www.dingalk.com) \\n", "markdown", Lists.newArrayList("18750919203"), null, false);
        OapiRobotSendResponse response = client.execute(request);
        System.out.println(response.getErrmsg());
    }

    @Override
    public Result sendNotify(JSONObject channelParams, NotifyDTO dto, TemplateEntity templateEntity, String content, TenantIsolation tenantIsolation) {
        String receivers = dto.getReceivers();
        List<String> atMobiles = null;
        if (StringUtils.isNotBlank(receivers)){
            atMobiles = Arrays.asList(receivers.split(NotifyConstant.COMMA));
        }
        OapiRobotSendRequest request = null;
        if(StrUtil.isNotBlank(templateEntity.getTitle())){
            request = DingDingRobot.createMarkdownRequest(content, templateEntity.getTitle(), atMobiles, null, false);
        }else{
            request = DingDingRobot.createTextRequest(content, atMobiles, null, false);
        }
        JSONObject requestJson = new JSONObject();
        requestJson.put("channelParams",channelParams);
        requestJson.put("notifyDTO",dto);
        requestJson.put("templateEntity",templateEntity);
        requestJson.put("content",content);
        String requestParams = requestJson.toJSONString();
        RSemaphore semaphore = redissonClient.getSemaphore(String.format(RedisConstant.DING_CHANNEL_SEMAPHORE, templateEntity.getNotifyChannelId()));
        boolean firstSend = semaphore.tryAcquire();
        if(firstSend){
            //首次创建或修改时间间隔后，直接发
            DingTalkClient client;
            try {
                client = DingDingRobot.createClient(channelParams.getString(AliYunConstant.WEBHOOK),
                        RSAUtils.decryptByPrivateKey(channelParams.getString(AliYunConstant.SECRET),NotifyConstant.PRIVATE_KEY));
            }catch (Exception e) {
                log.error("钉钉机器人创建客户端失败。requestParams:"+requestParams,e);
                logService.createErrorLog(NotifyServerLogEnum.SEND_NOTIFY_FAIL,requestParams,e.toString() ,tenantIsolation, dto.getSubscriptionId());
                return Result.error(NotifyErrorEnum.DING_DING_CREATE_CLIENT_FAIL.getCode(), NotifyErrorEnum.DING_DING_CREATE_CLIENT_FAIL.getMessage());
            }
            try {
                OapiRobotSendResponse response = client.execute(request);
                if (!response.isSuccess()) {
                    log.error("钉钉机器人发送通知失败。requestParams:" + requestParams + ";errMsg:" + response.getErrmsg());
                    logService.createRequestFailLog(NotifyServerLogEnum.SEND_NOTIFY_FAIL, requestParams, response.getErrmsg()
                            , tenantIsolation, dto.getSubscriptionId());
                    return Result.error(NotifyErrorEnum.DING_DING_SEND_NOTIFY_FAIL.getCode(), response.getErrmsg());
                }
            } catch (Exception e) {
                log.error("钉钉机器人发送通知失败。requestParams:"+requestParams,e);
                logService.createErrorLog(NotifyServerLogEnum.SEND_NOTIFY_FAIL,requestParams,e.toString() ,tenantIsolation,dto.getSubscriptionId());
                return Result.error(NotifyErrorEnum.DING_DING_SEND_NOTIFY_FAIL.getCode(),NotifyErrorEnum.DING_DING_SEND_NOTIFY_FAIL.getMessage());
            }/*finally {
//                lock.unlock();
            }*/
            //开启限流
            DingChannelThreshold.getSender(templateEntity.getNotifyChannelId()).startProcessing();
            logService.createLog(NotifyServerLogEnum.SEND_NOTIFY_SUCCESS,requestParams ,tenantIsolation);
            return Result.ok();
        }else{
            RLock lock = redissonClient.getLock(RedisConstant.DING_DING_SEND_NOTIFY+templateEntity.getNotifyChannelId());
            try{
                if(lock.tryLock(1,1,TimeUnit.MINUTES)){
                    MessageRequest messageRequest = new MessageRequest();
                    messageRequest.setRequestParam(requestParams);
                    messageRequest.setAtMobiles(atMobiles);
                    messageRequest.setContent(content);
                    messageRequest.setChannelParams(channelParams);
                    messageRequest.setTemplateId(templateEntity.getId());
                    messageRequest.setTenantIsolation(tenantIsolation);
                    messageRequest.setTitle(templateEntity.getTitle());
                    messageRequest.setSubscriptionId(dto.getSubscriptionId());
                    LeakyBucketMessageSender sender = DingChannelThreshold.getSender(templateEntity.getNotifyChannelId());
                    if(!Objects.isNull(sender)){
                        sender.addRequest(messageRequest);
                    }else{
                        if(!stringRedisTemplate.opsForValue().setIfAbsent(String.format(RedisConstant.DING_CHANNEL_SEMAPHORE, templateEntity.getNotifyChannelId()),"0")){
                            //设置过限流器了，但是因为重启丢失了
                            log.info("===create a leak sender===");
                            LeakyBucketMessageSender newSender = new LeakyBucketMessageSender(channelParams.getInteger(DingDingConstant.SEND_GAP_SECOND), TimeUnit.SECONDS, templateEntity.getId(), channelParams);
                            DingChannelThreshold.registerSender(templateEntity.getNotifyChannelId(), newSender);
                            newSender.startProcessing();
                            newSender.addRequest(messageRequest);
                        }else{
                            log.warn("===请重新保存叮叮机器人渠道，激活限流====");
                        }
                    }
                    return Result.ok();
                }
                return Result.error();
            }catch (Exception e){
                log.error("======发送叮叮机器人消息失败！失败原因：{}",e.getMessage());
                return Result.error(e.getMessage());
            }finally {
                lock.unlock();
            }

        }

    }

    @Override
    public void retrySendFailNotify(JSONObject channelParams, NotifyServerLogEntity notifyServerLogEntity, TemplateEntity templateEntity, JSONObject requestJson, TenantIsolation tenantIsolation) {
        NotifyDTO notifyDTO = requestJson.getObject("notifyDTO", NotifyDTO.class);
        this.sendNotify(channelParams,notifyDTO,templateEntity,requestJson.getString("content"),tenantIsolation);
    }


    public void NotifyCurrentLimiting(Long channelId) throws Exception{
        String timestamp = stringRedisTemplate.opsForValue().get(RedisConstant.DING_DING_FIRST_SEND_TIMESTAMP + channelId);
        String times = stringRedisTemplate.opsForValue().get(RedisConstant.DING_DING_SEND_TIMES + channelId);
        long now = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        if (StringUtils.isBlank(timestamp) || StringUtils.isBlank(times)) {
            stringRedisTemplate.opsForValue().set(RedisConstant.DING_DING_FIRST_SEND_TIMESTAMP + channelId, now + "");
            stringRedisTemplate.opsForValue().set(RedisConstant.DING_DING_SEND_TIMES + channelId, "0");
            timestamp = now + "";
            times = "0";
        }

        long poor = now - Long.parseLong(timestamp);
        if (poor >= 61000){
            stringRedisTemplate.opsForValue().set(RedisConstant.DING_DING_FIRST_SEND_TIMESTAMP + channelId, now + "");
            stringRedisTemplate.opsForValue().set(RedisConstant.DING_DING_SEND_TIMES + channelId, "1");
            times = "1";
        }

        if (Integer.parseInt(times) >= 20) {
            Thread.sleep(61000 - (poor));
            stringRedisTemplate.opsForValue().set(RedisConstant.DING_DING_FIRST_SEND_TIMESTAMP + channelId, LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + "");
            stringRedisTemplate.opsForValue().set(RedisConstant.DING_DING_SEND_TIMES +channelId, "1");
        } else {
            stringRedisTemplate.boundValueOps(RedisConstant.DING_DING_SEND_TIMES + channelId).increment(1);
        }

    }
}
