package com.nti56.nlink.product.device.server.service.impl;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayCacheProxy;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewaySpiProxy;
import com.nti56.nlink.product.device.server.model.ConnectResult;
import com.nti56.nlink.product.device.server.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.concurrent.Future;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 14:11:44
 * @since JDK 1.8
 */
@Service
@Slf4j
public class ConnectChannelServiceImpl  implements IConnectChannelService {

    @Autowired
    private IEdgeGatewaySpiProxy edgeGatewaySpiProxy;

    @Autowired
    private IEdgeGatewayCacheProxy edgeGatewayCacheProxy;


    @Override
    @Async("collectingOnceAsyncExecutor")
    public Future<Result<List<ConnectResult>>> connectGatewayChannel(ChannelEntity channelEntity, List<ChannelElm> channelElmList) {
        Result<List<ConnectResult>> connectResult = edgeGatewaySpiProxy.connectChannel(channelEntity.getEdgeGatewayId(),channelEntity.getTenantId(),channelElmList);
        return new AsyncResult(connectResult);
    }

    @Override
    @Async("collectingOnceAsyncExecutor")
    public Future<Result<List<ConnectResult>>> connectCacheGatewayChannel(ChannelEntity channelEntity, List<ChannelElm> channelElmList) {
        Result<List<ConnectResult>> connectResult = edgeGatewayCacheProxy.channelConnection(channelEntity.getEdgeGatewayId(),channelEntity.getTenantId(),channelElmList);
        return new AsyncResult(connectResult);
    }
}
