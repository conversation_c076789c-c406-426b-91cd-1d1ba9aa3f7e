package com.nti56.nlink.product.device.server.loader;

import java.util.HashMap;
import java.util.List;

import javax.script.ScriptEngine;
import javax.script.ScriptException;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.config.ScriptEngineConfig;
import com.nti56.nlink.product.device.server.entity.ConnectorEntity;
import com.nti56.nlink.product.device.server.entity.ConnectorItemEntity;
import com.nti56.nlink.product.device.server.enums.ConnectorEnum;
import com.nti56.nlink.product.device.server.mapper.ConnectorItemMapper;
import com.nti56.nlink.product.device.server.mapper.ConnectorMapper;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ConnectorScriptLoader implements ApplicationListener<ApplicationReadyEvent> {
    
    public static final String CONNECTOR_FUNCTION_PREFIX = "connector_";

    @Autowired
    private ConnectorMapper connectorMapper;
    
    @Autowired
    private ConnectorItemMapper connectorItemMapper;

    @Autowired
    private ScriptEngine scriptEngine;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        // 加载连接器脚本
        QueryWrapper<ConnectorEntity> connectorWrapper = new QueryWrapper<ConnectorEntity>()
            .eq("connector_type", ConnectorEnum.MQTT.getCode()) //mqtt连接器
            .eq("mode_type", 0) //发送连接器
            .eq("deleted", 0) //未删除
            .eq("status", 1); //启用状态
        List<ConnectorEntity> connectorList = connectorMapper.selectList(connectorWrapper);
        if(connectorList == null || connectorList.size() <= 0){
            return;
        }
        for(ConnectorEntity connectorEntity:connectorList){
            Long connectorId = connectorEntity.getId();
            QueryWrapper<ConnectorItemEntity> itemWrapper = new QueryWrapper<ConnectorItemEntity>()
                .eq("connector_id", connectorId)
                .eq("deleted", 0); //未删除
            List<ConnectorItemEntity> connectorItemList = connectorItemMapper.selectList(itemWrapper);
            if(connectorItemList == null || connectorItemList.size() <= 0){
                continue;
            }
            Long tenantId = connectorEntity.getTenantId();
            for(ConnectorItemEntity item:connectorItemList){
                Long processorId = item.getId();
                String processCode = item.getProcessCode();
                loadScript(tenantId, connectorId, processorId, processCode);
            }

        }
    }

    public static String buildFunctionName(Long tenantId, Long connectorId, Long processorId){
        return CONNECTOR_FUNCTION_PREFIX +
                "_" + tenantId + "_" + connectorId + "_" + processorId;
    }

    public Result<Void> unloadScript(Long tenantId, Long connectorId, Long processorId){
        String functionName = ConnectorScriptLoader.buildFunctionName(tenantId, connectorId, processorId);
        log.info("unloadScript:  {}", functionName);
        String code = "function " + functionName + "(topic, payload, things){ \n" + 
            "  return null;\n" + 
            "}";
        try {
            scriptEngine.eval(code);
        } catch (ScriptException e) {
            log.error("connector unload script error: {}, {}", functionName, e);
            return Result.error("卸载失败" + e.getMessage());
        }
        return Result.ok();
    }
    
    public Result<Void> loadScript(Long tenantId, Long connectorId, Long processorId, String processCode){
        String functionName = ConnectorScriptLoader.buildFunctionName(tenantId, connectorId, processorId);
        log.info("loadScript:  {}", functionName);
        String code = "function " + functionName + "(topic, payload, things){ \n" + 
            "var result = null; \n" +
            processCode + "\n" + 
            "  return result;\n" + 
            "}";
        try {
            scriptEngine.eval(code);
        } catch (ScriptException e) {
            log.error("connector load script error: {}, {}", functionName, e);
            return Result.error("加载失败" + e.getMessage());
        }
        return Result.ok();
    }

}
