package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.product.device.server.entity.CustomFieldEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nti56.nlink.common.mybatis.CommonMapper;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 自定义字段表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18 11:17:00
 */
@Mapper
public interface CustomFieldMapper extends CommonMapper<CustomFieldEntity> {

    @Delete("DELETE FROM custom_field WHERE tenant_id = #{tenantId} AND target_type = 1 AND target_id = #{customDriverId}")
    Integer deleteFixHeaderTailFieldByDriverId(@Param("tenantId") Long tenantId, @Param("customDriverId") Long customDriverId);

    Integer deleteMessageFieldByDriverId(@Param("tenantId") Long tenantId, @Param("customDriverId") Long customDriverId);

    @Select("SELECT * FROM custom_field WHERE tenant_id = #{tenantId} AND deleted = 0 AND target_type = 2 AND target_id = #{customMessageId} ORDER BY sort_no")
    List<CustomFieldEntity> listByMessageId(@Param("tenantId") Long tenantId, @Param("customMessageId") Long customMessageId);

    @Select("SELECT * FROM custom_field WHERE tenant_id = #{tenantId} AND deleted = 0 AND target_type = 1 AND target_id = #{customDriverId} ORDER BY part_type,sort_no")
    List<CustomFieldEntity> listFixHeaderTailFieldByDriverId(@Param("tenantId") Long tenantId, @Param("customDriverId") Long customDriverId);

    Integer deleteMessageFieldByMessageId(@Param("tenantId") Long tenantId, @Param("customMessageId") Long customMessageId);

}
