package com.nti56.nlink.product.device.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.exception.BizException;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.send.NotifyDTO;
import com.nti56.nlink.product.device.server.constant.AliYunConstant;
import com.nti56.nlink.product.device.server.constant.NotifyConstant;
import com.nti56.nlink.product.device.server.entity.TemplateEntity;
import com.nti56.nlink.product.device.server.enums.NotifyServerLogEnum;
import com.nti56.nlink.product.device.server.enums.VerificationTypeEnum;
import com.nti56.nlink.product.device.server.service.INotifyServerLogService;
import com.nti56.nlink.product.device.server.service.ISendNotifyService;
import com.nti56.nlink.product.device.server.service.ITemplateService;
import com.nti56.nlink.product.device.server.util.aliyun.AliYunSmsBase;
import com.nti56.nlink.product.device.server.util.aliyun.AliYunSmsSend;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringSubstitutor;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/8 22:59<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class SendNotifyServiceImpl implements ISendNotifyService {

    @Autowired
    private ITemplateService templateService;

    @Autowired
    private NotifyStrategyContext notifyStrategyContext;

    @Autowired
    private INotifyServerLogService logService;

    @Autowired
    private INotifyServerLogService notifyServerLogService;

    @Autowired
    private Mapper dozerMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Result batchSend(List<NotifyDTO> dto, TenantIsolation tenantIsolation, String requestParams) {
        for (NotifyDTO notifyDTO : dto) {
            this.send(notifyDTO,tenantIsolation,requestParams);
        }
        return Result.ok();
    }

    @Override
    public Result send(NotifyDTO dto, TenantIsolation tenantIsolation, String requestParams) {
        Result<TemplateEntity> result = templateService.getByIdAndTenantIsolation(dto.getTemplateId(), tenantIsolation);
        TemplateEntity templateEntity = result.getResult();
        if (templateEntity == null) {
            log.error("请求参数异常，requestParams："+requestParams);
            notifyServerLogService.createLog(NotifyServerLogEnum.SEND_PARAMS_ERROR,requestParams,tenantIsolation);
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }

        Map map;

        try {
            map = JSON.parseObject(dto.getParams(), Map.class);
            map.remove(null);
        }catch (Exception e){
            log.error("params格式异常。requestParams:"+requestParams,e);
            logService.createErrorLog(NotifyServerLogEnum.SEND_PARAMS_ERROR,requestParams,e.toString() ,tenantIsolation);
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }

        if (CollectionUtils.isEmpty(map)){
            map = new HashMap();
        }

        String content = fillContent(templateEntity.getContent(),map );

        Pattern p = Pattern.compile(NotifyConstant.TEMPLATE_WILDCARD_REGEX);
        Matcher matcher = p.matcher(content);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String group = matcher.group();
            map.put(group.substring(2,group.length()-1),NotifyConstant.NONE_DEFAULT_VALUE);
            matcher.appendReplacement(sb,NotifyConstant.NONE_DEFAULT_VALUE);
        }

        matcher.appendTail(sb);
        content = sb.toString();
        dto.setParams(JSON.toJSONString(map));

        return notifyStrategyContext.sendNotify(dto,templateEntity,content,tenantIsolation);

    }

    @Value("${sms.aliyun.accessKeyId}")
    private String accessKeyId;

    @Value("${sms.aliyun.accessKeySecret}")
    private String accessKeySecret;

    @Value("${sms.aliyun.signName}")
    private String signName;

    @Override
    public Result<Object> sendVerificationCode(int type, String phone, TenantIsolation tenantIsolation) {
        VerificationTypeEnum verificationTypeEnum = VerificationTypeEnum.typeOfValue(type);
        if (verificationTypeEnum == null){
            throw new BizException("验证码类型错误");
        }

        if (!phone.matches(NotifyConstant.PHONE_REGEX)) {
            throw new BizException("手机格式异常");
        }

        //短信模板变量对应的实际值，JSON格式。{"code":"1111"}
        int code = (int)((Math.random()*9+1)*1000);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code" , code);
        try {
            Client client = AliYunSmsBase.createClient(accessKeyId, accessKeySecret);
            SendSmsResponse sendSmsResponse = AliYunSmsSend.send(client, phone, signName, verificationTypeEnum.getTemplateCode(), jsonObject.toJSONString());
            if (!com.aliyun.teautil.Common.equalString(sendSmsResponse.body.code, AliYunConstant.REQUEST_SUCCESS)) {
                log.error("发送验证码中，阿里云发送短信失败。errMsg:" + sendSmsResponse.body.message);
                throw new BizException("验证码发送失败");
            }
            stringRedisTemplate.opsForValue().set(verificationTypeEnum.getRedisKeyPre()+phone,code+"",600, TimeUnit.SECONDS);

        }catch (Exception e) {
            log.error("发送验证码中，阿里云发送短信报错。", e);
            throw new BizException("验证码发送失败");
        }
        return Result.ok();
    }

    private String fillContent(String content, Map<String,String> params){
        StringSubstitutor sub = new StringSubstitutor(params);
        return sub.replace(content);
    }

}
