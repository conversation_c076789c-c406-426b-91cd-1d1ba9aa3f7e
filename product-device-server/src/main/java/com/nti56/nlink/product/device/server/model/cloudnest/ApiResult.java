package com.nti56.nlink.product.device.server.model.cloudnest;


import java.io.Serializable;

public class ApiResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    private int code;
    private String message;
    private T data;
    private <PERSON><PERSON><PERSON> succeeded;
    private String errCode;
    private Boolean signal;

    public ApiResult(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public ApiResult(int code, String message, T data, <PERSON><PERSON><PERSON> succeeded) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.succeeded = succeeded;
    }

    public ApiResult(int code, String message, T data, <PERSON><PERSON><PERSON> succeeded, String errCode) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.succeeded = succeeded;
        this.errCode = errCode;
    }

    public static <T> ApiResult<T> succeed() {
        return new ApiResult(ApiCodeEnum.SUCCESS.getCode(), ApiCodeEnum.SUCCESS.getMessage(), (Object)null, true);
    }

    public static <T> ApiResult<T> succeed(T data) {
        return new ApiResult(ApiCodeEnum.SUCCESS.getCode(), ApiCodeEnum.SUCCESS.getMessage(), data, true);
    }

    public static <T> ApiResult<T> failed() {
        return new ApiResult(ApiCodeEnum.API_SERVER_ERROR.getCode(), ApiCodeEnum.API_SERVER_ERROR.getMessage(), (Object)null, false);
    }

    public static <T> ApiResult<T> failed(String msg) {
        return new ApiResult(ApiCodeEnum.API_SERVER_ERROR.getCode(), msg, (Object)null, false);
    }

    public static <T> ApiResult<T> failed(String errCode, String msg) {
        return new ApiResult(ApiCodeEnum.API_SERVER_ERROR.getCode(), msg, (Object)null, false, errCode);
    }

    public static <T> ApiResult<T> failed(ApiCodeEnum ApiCodeEnum) {
        return new ApiResult(ApiCodeEnum.getCode(), ApiCodeEnum.getMessage(), (Object)null, false);
    }

    public static <T> ApiResult<T> failed(ApiCodeEnum ApiCodeEnum, String message) {
        return new ApiResult(ApiCodeEnum.getCode(), message, (Object)null, false);
    }

    public static <T> ApiResult.ApiResultBuilder<T> builder() {
        return new ApiResult.ApiResultBuilder();
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public T getData() {
        return this.data;
    }

    public Boolean getSucceeded() {
        return this.succeeded;
    }

    public String getErrCode() {
        return this.errCode;
    }

    public Boolean getSignal() {
        return this.signal;
    }

    public void setCode(final int code) {
        this.code = code;
    }

    public void setMessage(final String message) {
        this.message = message;
    }

    public void setData(final T data) {
        this.data = data;
    }

    public void setSucceeded(final Boolean succeeded) {
        this.succeeded = succeeded;
    }

    public void setErrCode(final String errCode) {
        this.errCode = errCode;
    }

    public void setSignal(final Boolean signal) {
        this.signal = signal;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof ApiResult)) {
            return false;
        } else {
            ApiResult<?> other = (ApiResult)o;
            if (!other.canEqual(this)) {
                return false;
            } else if (this.getCode() != other.getCode()) {
                return false;
            } else {
                label73: {
                    Object this$succeeded = this.getSucceeded();
                    Object other$succeeded = other.getSucceeded();
                    if (this$succeeded == null) {
                        if (other$succeeded == null) {
                            break label73;
                        }
                    } else if (this$succeeded.equals(other$succeeded)) {
                        break label73;
                    }

                    return false;
                }

                Object this$signal = this.getSignal();
                Object other$signal = other.getSignal();
                if (this$signal == null) {
                    if (other$signal != null) {
                        return false;
                    }
                } else if (!this$signal.equals(other$signal)) {
                    return false;
                }

                label59: {
                    Object this$message = this.getMessage();
                    Object other$message = other.getMessage();
                    if (this$message == null) {
                        if (other$message == null) {
                            break label59;
                        }
                    } else if (this$message.equals(other$message)) {
                        break label59;
                    }

                    return false;
                }

                Object this$data = this.getData();
                Object other$data = other.getData();
                if (this$data == null) {
                    if (other$data != null) {
                        return false;
                    }
                } else if (!this$data.equals(other$data)) {
                    return false;
                }

                Object this$errCode = this.getErrCode();
                Object other$errCode = other.getErrCode();
                if (this$errCode == null) {
                    if (other$errCode != null) {
                        return false;
                    }
                } else if (!this$errCode.equals(other$errCode)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ApiResult;
    }

    public int hashCode() {
        int result = 1;
        result = result * 59 + this.getCode();
        Object $succeeded = this.getSucceeded();
        result = result * 59 + ($succeeded == null ? 43 : $succeeded.hashCode());
        Object $signal = this.getSignal();
        result = result * 59 + ($signal == null ? 43 : $signal.hashCode());
        Object $message = this.getMessage();
        result = result * 59 + ($message == null ? 43 : $message.hashCode());
        Object $data = this.getData();
        result = result * 59 + ($data == null ? 43 : $data.hashCode());
        Object $errCode = this.getErrCode();
        result = result * 59 + ($errCode == null ? 43 : $errCode.hashCode());
        return result;
    }

    public String toString() {
        return "ApiResult(code=" + this.getCode() + ", message=" + this.getMessage() + ", data=" + this.getData() + ", succeeded=" + this.getSucceeded() + ", errCode=" + this.getErrCode() + ", signal=" + this.getSignal() + ")";
    }

    public ApiResult(final int code, final String message, final T data, final Boolean succeeded, final String errCode, final Boolean signal) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.succeeded = succeeded;
        this.errCode = errCode;
        this.signal = signal;
    }

    public ApiResult() {
    }

    public static class ApiResultBuilder<T> {
        private int code;
        private String message;
        private T data;
        private Boolean succeeded;
        private String errCode;
        private Boolean signal;

        ApiResultBuilder() {
        }

        public ApiResult.ApiResultBuilder<T> code(final int code) {
            this.code = code;
            return this;
        }

        public ApiResult.ApiResultBuilder<T> message(final String message) {
            this.message = message;
            return this;
        }

        public ApiResult.ApiResultBuilder<T> data(final T data) {
            this.data = data;
            return this;
        }

        public ApiResult.ApiResultBuilder<T> succeeded(final Boolean succeeded) {
            this.succeeded = succeeded;
            return this;
        }

        public ApiResult.ApiResultBuilder<T> errCode(final String errCode) {
            this.errCode = errCode;
            return this;
        }

        public ApiResult.ApiResultBuilder<T> signal(final Boolean signal) {
            this.signal = signal;
            return this;
        }

        public ApiResult<T> build() {
            return new ApiResult(this.code, this.message, this.data, this.succeeded, this.errCode, this.signal);
        }

        public String toString() {
            return "ApiResult.ApiResultBuilder(code=" + this.code + ", message=" + this.message + ", data=" + this.data + ", succeeded=" + this.succeeded + ", errCode=" + this.errCode + ", signal=" + this.signal + ")";
        }
    }
}
