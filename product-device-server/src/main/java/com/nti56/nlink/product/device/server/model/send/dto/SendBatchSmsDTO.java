package com.nti56.nlink.product.device.server.model.send.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/19 17:25<br/>
 * @since JDK 1.8
 */
@Data
@Builder
@Schema(description = "批量发送短信参数")
public class SendBatchSmsDTO {

    @Schema(description = "电话号码json")
    private String phoneNumberJson;
    @Schema(description = "签名json")
    private String signNameJson;
    @Schema(description = "模板参数json")
    private String templateParamJson;
}
