package com.nti56.nlink.product.device.server.model.label.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 11:27<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "标签dto")
public class EditLabelAliasDTO {
    @Schema(description = "搜索词")
    private String searchStr;

    @Schema(description = "网关id")
    @NotNull(message = "网关id不能为空")
    private Long edgeGateWayId;

    @Schema(description = "勾选的标签分组")
    private List<Long> labelGroupIds;

    @Schema(description = "过滤规则集合")
    @NotNull(message = "至少有一个过滤规则")
    @Size(min = 1,message = "至少有一个过滤规则")
    private List<String> filteringRules;

    @Schema(description = "标签id集合")
    private List<Long> labelIds;

    @Schema(description = "是否过滤全部")
    private Boolean allFilter;

}
