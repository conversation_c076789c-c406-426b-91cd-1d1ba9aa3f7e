package com.nti56.nlink.product.device.server.model;

import lombok.Data;

/**
 * 类说明: 用户中心租户
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2024/2/22 14:57<br/>
 * @since JDK 1.8
 */
@Data
public class TenantVo {

    private Long id;
    /**
     * 租户ID
     */
    private String clientId;
    /**
     * 租户名称
     */
    private String clientName;
    /**
     * 关联钉钉的企业ID
     */
    private String dingCorpid;
    /**
     * 租户代码
     */
    private String clientCode;
    /**
     * 企业工商组织代码
     */
    private String orgCode;
    /**
     * 企业认证情况：0未认证,1已认证
     */
    private Integer authStatus;
    /**
     * 租户类型:1:为个人 2:为企业 3:委外企业
     */
    private Integer clientType;
    /**
     * 删除状态 0:未删除，1：已删除
     */
    private Integer deleted;
    /**
     * 企业是否有开发权限:0不展示到开发者控制台,1同时用户为开发者类型则展示
     */
    private Integer isDevelop;
    /**
     * 租户状态:0为停用,1为启用
     */
    private Integer status;
    private String createBy;
    private String modifyBy;
    private String createTime;
    private String modifyTime;

}
