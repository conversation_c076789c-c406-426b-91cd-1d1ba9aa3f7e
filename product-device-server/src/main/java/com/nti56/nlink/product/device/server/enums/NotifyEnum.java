package com.nti56.nlink.product.device.server.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/13 16:56<br/>
 * @since JDK 1.8
 */
public enum NotifyEnum {

    EMAIL(0, "邮件",
            "[{" +
            "\"fieldName\": \"userName\"," +
            "\"fieldLabel\": \"发送账号\"," +
            "\"pField\": \"params\"," +
            "\"fieldType\": \"text:1:true\"" +
            "}, {" +
            "\"fieldName\": \"password\"," +
            "\"fieldLabel\": \"账户密码\"," +
            "\"pField\": \"params\"," +
            "\"fieldType\": \"password:1:true\"" +
            "}, {" +
            "\"fieldName\": \"host\"," +
            "\"fieldLabel\": \"host\"," +
            "\"pField\": \"params\"," +
            "\"fieldType\": \"text:1:true\"" +
            "}, {" +
            "\"fieldName\": \"protocol\"," +
            "\"fieldLabel\": \"邮件协议\"," +
            "\"pField\": \"params\"," +
            "\"fieldType\": \"option:1:true\"," +
            "\"fieldValue\": \"channels/email/protocol\"" +
            "}" +
            "]",
            "[{" +
            "\"fieldName\": \"title\"," +
            "\"fieldLabel\": \"标题\"," +
            "\"fieldType\": \"text:1:true\"" +
            "}, {" +
            "\"fieldName\": \"content\"," +
            "\"fieldLabel\": \"内容\"," +
            "\"fieldType\": \"textarea:2:true\"" +
            "}]",
            ""
        ),
    SMS(1, "短信",
            "[{" +
            "\"fieldName\": \"provider\"," +
            "\"pField\": \"params\"," +
            "\"fieldLabel\": \"服务商\"," +
            "\"fieldType\": \"option:1:true\"," +
            "\"fieldValue\": \"channels/providers\"" +
            "}]",
            "[{" +
            "\"fieldName\": \"title\"," +
            "\"fieldLabel\": \"标题\"," +
            "\"fieldType\": \"text:1:true\"" +
            "}, {" +
            "\"fieldName\": \"content\"," +
            "\"fieldLabel\": \"内容\"," +
            "\"fieldType\": \"textarea:2:true\"" +
            "}]",
            "发送内容不能超过500个字符"
        ),
    DING_DING_ROBOT(2, "钉钉机器人",
            "[{" +
            "\"fieldName\": \"webhook\"," +
            "\"fieldLabel\": \"Webhook\"," +
            "\"pField\": \"params\"," +
            "\"fieldType\": \"text:1:true\"" +
            "}, {" +
            "\"fieldName\": \"secret\"," +
            "\"fieldLabel\": \"密钥\"," +
            "\"pField\": \"params\"," +
            "\"fieldType\": \"password:1:true\"" +
            "},{\"fieldName\": \"sendGapSecond\",\"fieldLabel\": \"发送间隔（s）\",\"pField\": \"params\",\"fieldType\": \"number:1:true\",\"defaultValue\":3,\"min\":3,\"max\":60}]",
            "[{\"fieldName\": \"msgType\",\"fieldLabel\": \"消息类型\",\"fieldType\":\"select::true\",\"options\":[" +
                    "{\"key\":\"text\",\"value\":\"text类型\"},{\"key\":\"markdown\",\"value\":\"markdown类型\"}]}" +
                    ",{\"fieldName\": \"title\",\"fieldLabel\": \"标题\",\"fieldType\":\"text:1:true\"}"+
                    ",{\"fieldName\": \"content\",\"fieldLabel\": \"内容\",\"fieldType\":\"textarea:5:true\"}" +
                    ",{\"fieldName\": \"content\",\"fieldLabel\": \"内容\",\"fieldType\":\"markdown:5:true\",\"isShow\":true," +
                    "\"value\":{\"link\":\"notify/index.html#消息模版支持的markdowm语法\",\"remark\":\"目前只支持markdown语法的子集,\",\"view\":\"点击查看\"}}" +
                    "]",
            "发送频率20条/分钟，如果过于频繁，建议使用多个机器人"
        )
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String channelField;

    @Getter
    private String templateField;

    @Getter
    private String tips;

    NotifyEnum(Integer value, String name ,String channelField,String templateField,String tips) {
        this.value = value;
        this.name = name;
        this.channelField = channelField;
        this.templateField = templateField;
        this.tips = tips;
    }

    public static NotifyEnum typeOfValue(Integer value){
        NotifyEnum[] values = NotifyEnum.values();
        for (NotifyEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static NotifyEnum typeOfName(String name){
        NotifyEnum[] values = NotifyEnum.values();
        for (NotifyEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static String getTemplateFieldByValue(Integer value){
        NotifyEnum[] values = NotifyEnum.values();
        for (NotifyEnum v : values) {
            if (v.value.equals(value)) {
                return v.templateField;
            }
        }
        return null;

    }

    public static List toList(){
        List<Map> result = new ArrayList<>();
        NotifyEnum[] values = NotifyEnum.values();
        Map<String,Object> map ;
        for (NotifyEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            map.put("channelField",v.channelField);
            map.put("tips",v.tips);
            result.add(map);
        }
        return result;
    }
}
