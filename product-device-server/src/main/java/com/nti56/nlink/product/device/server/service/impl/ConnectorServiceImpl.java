package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.StatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.YesNoEnum;
import com.nti56.nlink.product.device.server.entity.ConnectorEntity;
import com.nti56.nlink.product.device.server.entity.ConnectorItemEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.loader.ConnectorInstanceLoader;
import com.nti56.nlink.product.device.server.loader.ConnectorScriptLoader;
import com.nti56.nlink.product.device.server.mapper.ConnectorItemMapper;
import com.nti56.nlink.product.device.server.mapper.ConnectorMapper;
import com.nti56.nlink.product.device.server.model.connector.dto.CreateConnectorDTO;
import com.nti56.nlink.product.device.server.model.connector.dto.EditConnectorDTO;
import com.nti56.nlink.product.device.server.model.connector.dto.QueryConnectorDTO;
import com.nti56.nlink.product.device.server.model.connector.vo.ConnectorVO;
import com.nti56.nlink.product.device.server.service.IConnectorItemService;
import com.nti56.nlink.product.device.server.service.IConnectorService;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 类说明:
 * <AUTHOR>
 * @version 1.0
 * @since JDK 1.8
 */
@Service
public class ConnectorServiceImpl extends BaseServiceImpl<ConnectorMapper, ConnectorEntity> implements IConnectorService {
    
    @Autowired
    private ConnectorMapper connectorMapper;
    
    @Autowired
    private IConnectorItemService connectorItemService;

    @Autowired
    private ConnectorInstanceLoader connectorInstanceLoader;
    
    @Autowired
    private ConnectorScriptLoader connectorScriptLoader;
    
    @Autowired
    private ConnectorItemMapper connectorItemMapper;

    @Override
    public Result<Page<ConnectorVO>> pageConnector(PageParam pageParam, QueryConnectorDTO queryConnectorDTO, TenantIsolation tenantIsolation) {
        Page<ConnectorEntity> connectorEntityPage = connectorMapper.pageConnector(pageParam.toPage(ConnectorEntity.class), queryConnectorDTO, tenantIsolation.getTenantId());
        
        Page<ConnectorVO> packageVOPage = new Page<>();
        packageVOPage.setTotal(connectorEntityPage.getTotal());
        packageVOPage.setCurrent(connectorEntityPage.getCurrent());
        packageVOPage.setSize(connectorEntityPage.getSize());
        packageVOPage.setPages(connectorEntityPage.getPages());
        
        if (CollectionUtil.isNotEmpty(connectorEntityPage.getRecords())) {
            List<ConnectorVO> collect = connectorEntityPage.getRecords().stream().map(t -> {
                ConnectorVO connectorVO = new ConnectorVO();
                BeanUtil.copyProperties(t, connectorVO);

                Result<Boolean> connectStatusResult = connectorInstanceLoader.getConnectStatus(connectorVO.getId());
                if(connectStatusResult.getSignal()){
                    Boolean connectStatus = connectStatusResult.getResult();
                    connectorVO.setConnectStatus(connectStatus?1:0);
                }else{
                    connectorVO.setConnectStatus(0);
                }
                
                return connectorVO;
            }).collect(Collectors.toList());
            packageVOPage.setRecords(collect);
        } else {
            packageVOPage.setRecords(new ArrayList<>());
        }
        return Result.ok(packageVOPage);
    }
    
    @Override
    public Result<ConnectorEntity> createConnector(CreateConnectorDTO createConnectorDTO, TenantIsolation tenantIsolation) {
        Result<Void> result = uniqueName(null, createConnectorDTO.getName(), tenantIsolation);
        if (!result.getSignal()) {
            throw new BizException(result.getMessage());
        }
        ConnectorEntity connectorEntity = new ConnectorEntity();
        BeanUtil.copyProperties(createConnectorDTO, connectorEntity);
        connectorMapper.insert(connectorEntity);
        return Result.ok(connectorEntity);
    }
    
    @Override
    public Result<Void> editConnector(EditConnectorDTO editConnectorDTO, TenantIsolation tenantIsolation) {
        Result<Void> result = uniqueName(editConnectorDTO.getId(), editConnectorDTO.getName(), tenantIsolation);
        if (!result.getSignal()) {
            throw new BizException(result.getMessage());
        }
        ConnectorEntity connectorEntity = new ConnectorEntity();
        BeanUtil.copyProperties(editConnectorDTO, connectorEntity);
        connectorMapper.updateById(connectorEntity);
        if(YesNoEnum.YES.getValue().equals(connectorEntity.getStatus())){
            LambdaQueryWrapper<ConnectorItemEntity> lqw = new LambdaQueryWrapper<>();
            lqw.eq(ConnectorItemEntity::getConnectorId, editConnectorDTO.getId());
            List<ConnectorItemEntity> itemList = connectorItemMapper.selectList(lqw);
            if(itemList != null && itemList.size() > 0){
                for(ConnectorItemEntity item:itemList){
                    connectorScriptLoader.loadScript(
                        tenantIsolation.getTenantId(), 
                        editConnectorDTO.getId(), 
                        item.getId(),
                        item.getProcessCode()
                    );
                }
            }
            connectorInstanceLoader.startConnector(editConnectorDTO.getId());
        }
        return Result.ok();
    }
    
    @Override
    public Result<Void> deleteConnector(Long id, TenantIsolation tenantIsolation) {
        Result<ConnectorEntity> old = this.getByIdAndTenantIsolation(id, tenantIsolation);
        if (old.getResult() == null) {
            throw new BizException("该租户下不存在此渠道");
        }
        Result<Boolean> existResult = connectorItemService.existConnectorItem(id);
        if (existResult.getResult()) {
            throw new BizException("该连接器存在连接器详情项，请先删除详情项");
        }
        connectorMapper.deleteById(id);
        connectorInstanceLoader.closeConnector(id);
        return Result.ok();
    }

    @Override
    public Result<Void> changeStatus(Long id, Integer status, TenantIsolation tenantIsolation) {
        ConnectorEntity connectorEntity = connectorMapper.selectById(id);
        connectorEntity.setStatus(status);
        connectorMapper.updateById(connectorEntity);
        if(YesNoEnum.YES.getValue().equals(status)){
            LambdaQueryWrapper<ConnectorItemEntity> lqw = new LambdaQueryWrapper<>();
            lqw.eq(ConnectorItemEntity::getConnectorId, id);
            List<ConnectorItemEntity> itemList = connectorItemMapper.selectList(lqw);
            if(itemList != null && itemList.size() > 0){
                for(ConnectorItemEntity item:itemList){
                    connectorScriptLoader.loadScript(
                        tenantIsolation.getTenantId(), 
                        id, 
                        item.getId(), 
                        item.getProcessCode()
                    );
                }
            }
            connectorInstanceLoader.startConnector(id);
        }else{
            LambdaQueryWrapper<ConnectorItemEntity> lqw = new LambdaQueryWrapper<>();
            lqw.eq(ConnectorItemEntity::getConnectorId, id);
            List<ConnectorItemEntity> itemList = connectorItemMapper.selectList(lqw);
            if(itemList != null && itemList.size() > 0){
                for(ConnectorItemEntity item:itemList){
                    connectorScriptLoader.unloadScript(
                        tenantIsolation.getTenantId(), 
                        id, 
                        item.getId()
                    );
                }
            }
            connectorInstanceLoader.closeConnector(id);
        }
        return Result.ok();
    }

    @Override
    public Result<Void> changeStatusByEdgeGatewayId(Long edgeGatewayId, Integer status, TenantIsolation tenantIsolation) {
        List<ConnectorEntity> connectorEntityList =  getConnectorByEdgeGatewayId(edgeGatewayId).getResult();
        if(CollectionUtil.isEmpty(connectorEntityList)){
            return Result.error("该网关下未创建连接器");
        }
        for(ConnectorEntity connectorEntity:connectorEntityList){
            changeStatus(connectorEntity.getId(),status,tenantIsolation);
        }
        return Result.ok();
    }

    @Override
    public Result<ConnectorVO> getConnectorInfo(Long id) {
        ConnectorEntity connectorEntity = connectorMapper.selectById(id);
        ConnectorVO connectorVO = new ConnectorVO();
        BeanUtil.copyProperties(connectorEntity, connectorVO);
        return Result.ok(connectorVO);
    }

    @Override
    public Result<List<ConnectorEntity>> getConnectorByEdgeGatewayId(Long edgeGatewayId) {
        LambdaQueryWrapper<ConnectorEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConnectorEntity::getEdgeGatewayId,edgeGatewayId);
        List<ConnectorEntity> connectorEntityList = connectorMapper.selectList(lqw);
        return Result.ok(connectorEntityList);
    }

    /**
     * 判断名称是否唯一
     * @param id
     * @param name
     * @param tenantIsolation
     */
    private Result<Void> uniqueName(Long id, String name, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<ConnectorEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, ConnectorEntity::getId, id).eq(ConnectorEntity::getName, name).eq(ConnectorEntity::getTenantId, tenantIsolation.getTenantId());
        if (connectorMapper.selectCount(lqw) > 0) {
            return Result.error("连接器已经存在该名称的,名称：" + name);
        }
        return Result.ok();
    }
    
    private Result<ConnectorEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<ConnectorEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConnectorEntity::getId, id).eq(ConnectorEntity::getTenantId, tenantIsolation.getTenantId());
        ConnectorEntity connectorEntity = connectorMapper.selectOne(lqw);
        return Result.ok(connectorEntity);
    }

    @Override
    public Result<Void> deleteConnectorByEdgeGatewayId(Long id, TenantIsolation tenantIsolation) {
        Result<List<ConnectorEntity>> result = getConnectorByEdgeGatewayId(id);
        if (!result.getSignal()) {
            return Result.error(result.getMessage());
        }
        List<ConnectorEntity> connectorEntityList = result.getResult();
        for(ConnectorEntity connectorEntity:connectorEntityList){
            Result<Void> delResult = deleteConnector(connectorEntity.getId(),tenantIsolation);
            if (!delResult.getSignal()) {
                return Result.error(delResult.getMessage());
            }
        }
        return Result.ok();
    }

    @Override
    public Result<List<ConnectorEntity>> getAllConnectorEntity() {
        LambdaQueryWrapper<ConnectorEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConnectorEntity::getDeleted, 0);
        List<ConnectorEntity> connectorEntityList = connectorMapper.selectList(lqw);
        return Result.ok(connectorEntityList);
    }
}
