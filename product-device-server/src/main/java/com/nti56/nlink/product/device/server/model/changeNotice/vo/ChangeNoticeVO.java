package com.nti56.nlink.product.device.server.model.changeNotice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/2/17 15:39<br/>
 * @since JDK 1.8
 */
@Data
public class ChangeNoticeVO {

    private Long id;

    @Schema(description = "变动通知名称")
    private String name;

    /**
     * 变动项目(1网关,2设备)
     */
    @Schema(description = "变动项目(1网关,2设备)")
    private Integer changeSubject;

    /**
     * 变动类型(1 新建,2,删除,3更新4,状态变化)
     */
    @Schema(description = "变动类型(1 新建,2,删除,3更新4,状态变化)")
    private Integer changeType;

    /**
     * 回调id
     */
    @Schema(description = "回调id")
    private Long redirectId;

}
