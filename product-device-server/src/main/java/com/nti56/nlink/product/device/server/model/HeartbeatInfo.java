package com.nti56.nlink.product.device.server.model;

import com.nti56.nlink.product.device.server.model.edgegateway.EdgeGatewayChangeState;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

@Data
public class HeartbeatInfo {
    private String heartbeatUuid;
    private Integer gatherLabelCount;
    private Integer computeTaskCount;
    private Boolean gathering;
    private String tag;
    private Map<String,Boolean> channelStatusMap;
    private String currentVersion;
    private String downloadStatus;//(0:不处理;1:下载中;2:下载完成;3:下载失败)
    private String upgradeVersion;
    private String instance;
    private EdgeGatewayChangeState edgeGatewayChangeState;
}
