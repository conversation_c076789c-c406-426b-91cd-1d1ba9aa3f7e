package com.nti56.nlink.product.device.server.service.export.handler;

import java.util.List;

import cn.hutool.extra.spring.SpringUtil;

import org.apache.commons.collections.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.nti56.nlink.common.export.handler.AbstractExportSqlHandler;
import com.nti56.nlink.common.util.GeneratorSqlUtil;
import com.nti56.nlink.product.device.server.entity.DataModelEntity;
import com.nti56.nlink.product.device.server.entity.DataModelPropertyEntity;
import com.nti56.nlink.product.device.server.entity.ResourceRelationEntity;
import com.nti56.nlink.product.device.server.mapper.DataModelMapper;
import com.nti56.nlink.product.device.server.mapper.DataModelPropertyMapper;
import com.nti56.nlink.product.device.server.mapper.ResourceRelationMapper;

/**
 * 类说明:资源<br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/19 15:34<br/>
 * @version 1.0
 * @since JDK 1.8
 */
public class ResourceExportSqlHandler extends AbstractExportSqlHandler {
  @Override
  public void exportSqlDml(Long tenantId, List<String> sqlList) {
    exportResource(tenantId, sqlList);
    if (getNext() != null) {
      getNext().exportSqlDml(tenantId, sqlList);
    }
  }

  private void exportResource(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<ResourceRelationEntity> queryWrapper = new LambdaQueryWrapper<ResourceRelationEntity>()
            .eq(ResourceRelationEntity::getTenantId, tenantId);
    List<ResourceRelationEntity> dtoList = SpringUtil.getBean(ResourceRelationMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(ResourceRelationEntity.class, dtoList));
    }
  }



}
