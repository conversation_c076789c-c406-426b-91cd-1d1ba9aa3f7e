package com.nti56.nlink.product.device.server.model.edgegateway.vo;

import com.nti56.nlink.product.device.server.model.edgegateway.EdgeGatewayChangeState;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/25 15:11<br/>
 * @since JDK 1.8
 */
@Data
public class EdgeGatewayCurrentTimeInfoVO {


    private Long id;

    @Schema(description = "1在线 0离线")
    private Integer online;

    @Schema(description = "采集标签数量")
    private String gatherLabelCount;

    @Schema(description = "计算任务数量")
    private String computeTaskCount;

    @Schema(description = "最近一次心跳时间")
    private String heartbeatTime;

    @Schema(description = "是否采集中，true采集中，false采集暂停")
    private Boolean gathering;
    
    @Schema(description = "是否有新版本 true 有 false 无")
    private Boolean newVersion;

    @Schema(description = "ot网关变更状态")
    private EdgeGatewayChangeState otGatewayChangeState;

    @Schema(description = "边缘网关变更状态")
    private EdgeGatewayChangeState edgeGatewayChangeState;
}
