package com.nti56.nlink.product.device.server.listener;

import cn.hutool.json.JSONUtil;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.FaultRedisCache;
import com.nti56.nlink.product.device.server.service.ITaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class KeyExpireListener extends KeyExpirationEventMessageListener {


    @Autowired
    @Lazy
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    ITaskService taskService;
    // 通过构造函数注入 RedisMessageListenerContainer 给 KeyExpirationEventMessageListener
    public KeyExpireListener(RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }

    /**
     * 针对redis数据失效事件，进行数据处理
     *
     * @param message
     * @param pattern
     */
    @Override
    public void onMessage(Message message, byte[] pattern) {
        // 用户做自己的业务处理即可,注意message.toString()可以获取失效的key
        String expiredKey = message.toString();
        log.debug("onMessage --> redis 过期的key是：{}", expiredKey);
        String faultStartKey = RedisConstant.DEVICE_FAULT_LEVEL_LOCK_START.replace("%s:%s:%s", "");
        String faultEndKey = RedisConstant.DEVICE_FAULT_LEVEL_LOCK_END.replace("%s:%s:%s", "");
        try {
            if (expiredKey.startsWith(faultStartKey) || expiredKey.startsWith(faultEndKey)){
                //处理告警
                processFault(expiredKey);
            }
        } catch (Exception e) {
            log.error("处理redis 过期的key异常：{}", expiredKey, e);
        }
    }


    private void processFault(String expiredKey){
        String[] keyArray=expiredKey.split(":");
        Integer level=0;
        if(keyArray.length>=8){
            level=Integer.parseInt(keyArray[8]);
        }else{
            log.warn("过期key格式错误：{}",expiredKey);
        }

        String cacheKey = String.format(RedisConstant.DEVICE_FAULT_CACHE, keyArray[6], keyArray[7]);
        FaultRedisCache faultRedisCache = JSONUtil.toBean(stringRedisTemplate.opsForValue().get(cacheKey),FaultRedisCache.class);
        if (faultRedisCache != null){
            taskService.processFault(faultRedisCache,level);
        }
    }
}
