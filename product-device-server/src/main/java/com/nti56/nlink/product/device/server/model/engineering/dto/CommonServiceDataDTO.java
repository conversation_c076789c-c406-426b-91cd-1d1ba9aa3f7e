package com.nti56.nlink.product.device.server.model.engineering.dto;

import com.nti56.nlink.product.device.server.model.redirect.RedirectDTO;
import com.nti56.nlink.product.device.server.model.tag.TagDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/4 18:07<br/>
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommonServiceDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<RedirectDTO> redirectList;

    private List<TagDTO> tagList;
}
