package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 变动通知 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2023-2-17 10:28:31
 * @since JDK 1.8
 */
@Data
@Schema(description = "变动通知")
@TableName("change_notice")
public class ChangeNoticeEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 变动通知名称
     */
    @Schema(description = "变动通知名称")
    private String name;

    /**
     * 变动项目(1网关,2设备)
     */
    @Schema(description = "变动项目(1网关,2设备)")
    private Integer changeSubject;

    /**
     * 变动类型(1 新建,2,删除,3更新4,状态变化)
     */
    @Schema(description = "变动类型(1 新建,2,删除,3更新4,状态变化)")
    private Integer changeType;

    /**
     * 回调id
     */
    @Schema(description = "回调id")
    private Long redirectId;

    /**
     * 工程id
     */
    @Schema(description = "工程id")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块id
     */
    @Schema(description = "模块id")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间id
     */
    @Schema(description = "空间id")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    @TableField(fill = FieldFill.INSERT)
    private String creator;


    /**
     * 修改人名称
     */
    @Schema(description = "修改人名称")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改人id
     */
    @Schema(description = "修改人id")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除，1-删除
     */
    @Schema(description = "逻辑删除，1-删除")
    @TableLogic
    private Boolean deleted;


    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

}
