package com.nti56.nlink.product.device.server.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class
AdditionBindPreviewDto {

    @Schema(description = "标签分组ID")
    private Long labelGroupId;

    @Schema(description = "标签分组名称")
    private String labelGroupName;

    @Schema(description = "映射生成设备名")
    private String mappingDeviceName;

    @Schema(description = "设备是否存在")
    private boolean deviceExist;

    @Schema(description = "待绑定设备ID")
    private Long deviceId;

    @Schema(description = "通道ID")
    private Long channelId;

    @Schema(description = "通道名称")
    private String channelName;

    @Schema(description = "网关ID")
    private Long edgeGatewayId;

    @Schema(description = "实际分组名称")
    private String originalGroupName;

}
