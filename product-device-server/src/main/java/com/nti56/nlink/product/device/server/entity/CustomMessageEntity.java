package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.nti56.nlink.product.device.client.model.dto.json.AutoResponseConfigField;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 自定义消息表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-11-18 11:17:01
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("custom_message")
@Schema(description = "自定义消息表")
public class CustomMessageEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @Schema(description = "id")
    @TableId(value = "id")
    private Long id;

    /**
     * 消息名称
     */
    @Schema(description = "消息名称")
    private String messageName;

    /**
     * 描述
     */
    @Schema(description = "描述")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String descript;

    @Schema(description = "方向，1-发送和接收，2-发送，3-接收")
    private Integer direction;

    
    @Schema(description = "自动响应，0-关闭，1-开启")
    private Boolean autoResponse;
    
    @Schema(description = "自动响应消息名")
    private String autoResponseMessageName;

    @Schema(description = "自动响应配置")
    private AutoResponseConfigField autoResponseConfig;
    
    @Schema(description = "自动发送，0-关闭，1-开启")
    private Boolean autoSend;

    @Schema(description = "自动发送间隔，毫秒")
    private Long autoSendInterval;
    
    @Schema(description = "自动发送内容，json")
    private String autoSendVarStr;
    
    @Schema(description = "客户端标识字段")
    private String clientKeyFieldName;
    
    /**
     * 所属自定义协议id
     */
    @Schema(description = "所属自定义协议id")
    private Long customDriverId;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

    /**
     * 删除
     */
    @Schema(description = "删除")
    @TableLogic
    private Integer deleted;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @Schema(description = "工程ID")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @Schema(description = "模块ID")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @Schema(description = "空间ID")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;



}
