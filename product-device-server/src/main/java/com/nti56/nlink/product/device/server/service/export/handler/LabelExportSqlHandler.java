package com.nti56.nlink.product.device.server.service.export.handler;

import java.util.List;

import cn.hutool.extra.spring.SpringUtil;

import org.apache.commons.collections.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.nti56.nlink.common.export.handler.AbstractExportSqlHandler;
import com.nti56.nlink.common.util.GeneratorSqlUtil;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.mapper.*;

/**
 * 类说明:标签<br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/19 15:34<br/>
 * @version 1.0
 * @since JDK 1.8
 */
public class LabelExportSqlHandler extends AbstractExportSqlHandler {
  @Override
  public void exportSqlDml(Long tenantId, List<String> sqlList) {
    exportLabel(tenantId, sqlList);
    exportLabelGroup(tenantId, sqlList);
    exportLabelBind(tenantId, sqlList);
    if (getNext() != null) {
      getNext().exportSqlDml(tenantId, sqlList);
    }
  }

  private void exportLabelGroup(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<LabelGroupEntity> queryWrapper = new LambdaQueryWrapper<LabelGroupEntity>().
            eq(LabelGroupEntity::getTenantId, tenantId);
    List<LabelGroupEntity> dtoList = SpringUtil.getBean(LabelGroupMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(LabelGroupEntity.class, dtoList));
    }
  }

  private void exportLabel(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<LabelEntity> queryWrapper = new LambdaQueryWrapper<LabelEntity>()
            .eq(LabelEntity::getTenantId, tenantId);
    List<LabelEntity> dtoList = SpringUtil.getBean(LabelMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(LabelEntity.class, dtoList));
    }
  }

  private void exportLabelBind(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<LabelBindRelationEntity> queryWrapper = new LambdaQueryWrapper<LabelBindRelationEntity>()
            .eq(LabelBindRelationEntity::getTenantId, tenantId);
    List<LabelBindRelationEntity> dtoList = SpringUtil.getBean(LabelBindRelationMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(LabelBindRelationEntity.class, dtoList));
    }
  }

}
