package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.Tag;
import com.nti56.nlink.product.device.client.model.req.ListTagReq;
import com.nti56.nlink.product.device.client.model.req.TagReq;
import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListTagRequest;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/1 16:26<br/>
 * @since JDK 1.8
 */
public interface ITagService extends IService<Tag> {


    /**
     * 变更流程
     * @param req
     * @param id
     * @param tenantIsolation
     * @return
     */
    Result<Boolean> update(TagReq req, Long id, TenantIsolation tenantIsolation);

    /**
     * 删除流程
     *
     * @param tenantIsolation
     * @param id
     * @return
     */
    Result<Boolean> delete(TenantIsolation tenantIsolation, Long id);

    /**
     * 获取明细
     *
     * @param tenantIsolation
     * @param id
     * @return
     */
    Result<TagRsp> get(TenantIsolation tenantIsolation, Long id);

    /**
     * 分页查询
     * @param req
     * @param pageSize
     * @param pageNo
     * @param tenantIsolation
     * @return
     */
    Result<Page<TagRsp>> findByPage(TagReq req, Integer pageSize, Integer pageNo, TenantIsolation tenantIsolation);

    /**
     * 查询标记列表
     * @param req
     * @param tenantIsolation
     * @return
     */
    Result<List<TagRsp>> list(ListTagReq req, TenantIsolation tenantIsolation);

    /**
     * 查询标记列表
     *
     * @param tenantIsolation
     * @param ids
     * @return
     */
    Result<List<TagRsp>> listByIds(TenantIsolation tenantIsolation, List<Long> ids);


    /**
     * 保存
     * @param req
     * @param tenantIsolation
     * @return
     */
  Result<TagRsp> save(TagReq req, TenantIsolation tenantIsolation);

  Result<List<Tag>> listTag(TenantIsolation tenantIsolation, ListTagRequest request);

    Result<List<Tag>> listAll(TenantIsolation tenantIsolation);

    /**
     * 根据key value 批量获取标记ID
     * @param tenantId
     * @param requestList
     * @return
     */
  Result<List<Long>> listTagId(Long tenantId, List<ListTagRequest> requestList);

  Result<Boolean> verifyBind(Long tenantId, Long tagId);
}
