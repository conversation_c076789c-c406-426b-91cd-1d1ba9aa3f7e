package com.nti56.nlink.product.device.server.entity.json.element.gather;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-11 15:41:43
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GatherItemElm {

    private Long channelId;
    private String driver;
    
    private String ip;
    private Integer port;
    private Integer rack;
    private Integer slot;
    

}
