package com.nti56.nlink.product.device.server.verticle;

import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpPropertyTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;

import com.nti56.nlink.product.device.server.verticle.post.processor.PostProcessorHandler;
import com.nti56.nlink.product.device.server.verticle.post.processor.property.UpData2InfluxDBHandler;
import com.nti56.nlink.product.device.server.verticle.post.processor.property.UpData2RedisHandler;
import io.vertx.core.Promise;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import io.netty.handler.codec.mqtt.MqttQoS;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

import com.nti56.nlink.product.device.server.util.GzipUtil;
import io.vertx.core.buffer.Buffer;
import java.nio.charset.StandardCharsets;


import org.springframework.beans.factory.annotation.Value;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-21 17:19:00
 * @since JDK 1.8
 */
@Component
@Scope(SCOPE_PROTOTYPE)
@Slf4j
public class MqttPropertyConsumerVerticle extends MqttBaseVerticle {
    
    @Getter
    @Value("${mqtt.host}")
    private String host;

    @Getter
    @Value("${mqtt.port}")
    private Integer port;

    @Getter
    @Value("${mqtt.username}")
    private String username;

    @Getter
    @Value("${mqtt.password}")
    private String password;

    @Getter
    @Value("${mqtt.ssl:true}")
    private Boolean ssl;
    
    @Getter
    private Integer reconnectGapTime;
    
    @Lazy
    @Autowired
    UpData2RedisHandler upData2RedisHandler;

    @Lazy
    @Autowired
    UpData2InfluxDBHandler upData2InfluxDBHandler;


    @Override
    public void start(Promise<Void> startPromise) throws Exception{
        log.debug("start-verticle");
        super.start(startPromise);
    }

    @Override
    public void subscribe(){
        String eventTopic = GwUpPropertyTopic.createSubscribeTopic(MqttBaseVerticle.shareSubscribeGroup);
        this.client.publishHandler(s1 -> {

            String topicName = s1.topicName();
            // Buffer gzip = s1.payload();
            // byte[] bs = GzipUtil.uncompress(gzip);
            // String payload = new String(bs, StandardCharsets.UTF_8);
            String payload = s1.payload().toString();

            log.debug("topic: {}, msg: {}", topicName, payload);
            GwUpPropertyTopic.TopicInfo topicInfo = GwUpPropertyTopic.parseTopic(topicName);
            UpData upData = JSONObject.parseObject(payload, UpData.class);
            upData2InfluxDBHandler.process(topicInfo,upData);
            upData2RedisHandler.process(topicInfo,upData);
        });
        client.subscribe(eventTopic, 1);
    }


    @Override
    protected void handleConnectStatusChange(Boolean connected) {
       
    }
}
