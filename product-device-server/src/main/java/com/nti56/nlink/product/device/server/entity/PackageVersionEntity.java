package com.nti56.nlink.product.device.server.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.baomidou.mybatisplus.annotation.*;

import com.nti56.model.BaseModel;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2021/9/10 11:44<br/>
 * @since JDK 1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("pkg_package_version")
public class PackageVersionEntity implements Serializable {

    private Long id;

    @TableField("engineering_id")
    private Long engineeringId;

    @TableField("package_version")
    private String packageVersion;

    @TableField("oss_path")
    private String ossFilePath;

    @TableField("base_sys_path")
    private String baseSysPath;

    @TableField("oss_file_id")
    private Long ossFileId;

    @TableField("file_name")
    private String fileName;


    @Schema(description = "租户ID")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 模块ID
     */
    @Schema(description = "模块ID")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @Schema(description = "空间ID")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

    /**
     * 逻辑删除，1-删除
     */
    @Schema(description = "逻辑删除，1-删除")
    @TableLogic
    private Boolean deleted;


}
