package com.nti56.nlink.product.device.server.model.channel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/22 21:00<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "渠道dto")
public class NotifyChannelDTO {

    private Long id;
    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    @Length(max = 32,message = "渠道名称不能超过32个字符")
    private String name;

    @Schema(description = "描述")
    @Length(max = 256,message = "渠道描述不能超过256个字符")
    private String description;

    /**
     * 通知类型 0邮件 1短信
     */
    @Schema(description = "通知类型 0邮件 1短信 2钉钉机器人")
    @Range(max = 2,message = "通知类型数值超出范围")
    private Integer notifyType;

    /**
     * 渠道参数
     */
    @Schema(description = "渠道参数")
    private String params;


}
