package com.nti56.nlink.product.device.server.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.MessageDigest;
import org.apache.commons.codec.binary.Hex;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-17 15:51:58
 * @since JDK 1.8
 */
public class Md5CalculationUtil {
 
    /**
     * 获取一个文件的md5值(可处理大文件)
     * @return md5 value
     */
    public static String getMD5(File file) {
        FileInputStream fileInputStream = null;
        try {
            MessageDigest MD5 = MessageDigest.getInstance("MD5");
            fileInputStream = new FileInputStream(file);
            byte[] buffer = new byte[8192];
            int length;
            while ((length = fileInputStream.read(buffer)) != -1) {
                MD5.update(buffer, 0, length);
            }
            return new String(Hex.encodeHex(MD5.digest()));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            try {
                if (fileInputStream != null){
                    fileInputStream.close();
                    }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    
    public static void main(String[] args) {
        String old = "8f22707a5ac84d010a9f06ee2780d351";
        File file = new File("/Users/<USER>/Desktop/1375982395154432.zip");
        String md5 = getMD5(file);
        System.out.println(md5);
    }
    
}
