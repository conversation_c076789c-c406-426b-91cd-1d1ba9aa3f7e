package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import com.nti56.nlink.product.device.server.entity.UpgradePackageEntity;
import com.nti56.nlink.product.device.server.model.EdgeGatewayDto;
import com.nti56.nlink.product.device.server.model.VersionQueryDTO;
import com.nti56.nlink.product.device.server.model.edgegateway.vo.EdgeGatewayVO;
import com.nti56.nlink.product.device.server.model.upgrade.dto.QueryUpgradePackageDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Set;

public interface EdgeGatewayMapper extends CommonMapper<EdgeGatewayEntity> {


    List<EdgeGatewayVO> listByIds(@Param("ids") Set<Long> ids, @Param("tenantId") Long tenantId);

    Page<EdgeGatewayVO> pageEdgeGateway(IPage<EdgeGatewayVO> page,@Param("dto") EdgeGatewayDto edgeGateway,@Param("tenantIsolation") TenantIsolation tenantIsolation);

    @Select("SELECT * FROM edge_gateway WHERE deleted = 0 AND tenant_id = #{tenantId}")
    List<EdgeGatewayEntity> listAll(@Param("tenantId") Long tenantId);

    @Select("SELECT * FROM edge_gateway WHERE tenant_id = #{tenantId} AND id = #{id} AND deleted = 0")
    EdgeGatewayEntity getById(@Param("tenantId") Long tenantId, @Param("id") Long id);

    void editEdgeGateway(@Param("edgeGateway") EdgeGatewayEntity edgeGatewayEntity);

    @Select("SELECT id FROM edge_gateway WHERE deleted = 0 AND tenant_id = #{tenantId}")
    List<Long> listAllId(@Param("tenantId") Long tenantId);

    @Select("SELECT heartbeat_uuid FROM edge_gateway WHERE tenant_id = #{tenantId} AND id = #{edgeGatewayId}")
    String getHeartbeatUuidById(@Param("tenantId") Long tenantId, @Param("edgeGatewayId") Long edgeGatewayId);

    @Update("UPDATE edge_gateway SET heartbeat_uuid = #{heartbeatUuid} WHERE tenant_id = #{tenantId} AND id = #{edgeGatewayId}")
    Integer updateHeartbeatUuid(@Param("tenantId") Long tenantId, 
                                @Param("edgeGatewayId") Long edgeGatewayId, 
                                @Param("heartbeatUuid") String heartbeatUuid);

    void setEdgeGatewayNotSyncByLabelIds(@Param("labelIds") List<Long> labelIds);

    @Select("SELECT * FROM edge_gateway WHERE deleted = 0")
    List<EdgeGatewayEntity> getAllEdgeGateway();
    
    @Select({
        "<script>",
        "select ",
        " * ",
        "from edge_gateway ",
        "where DELETED = 0",
        "and id in",
        "<foreach collection='ids' item='id' open='(' separator=',' close=')'>",
        "#{id}",
        "</foreach>",
        "</script>"
    })
    List<EdgeGatewayEntity> listEdgeGatewayByIds(@Param("ids") Set<Long> ids);
    
    Page<EdgeGatewayEntity> pageEdgeGatewayEntity(IPage<EdgeGatewayEntity> edgeGatewayEntity, @Param("versionQueryDTO") VersionQueryDTO versionQueryDTO);
    
    List<Long> selectEdgeGatewayIdList(@Param("labelIds") List<Long> labelIds);

    List<EdgeGatewayEntity> getGwList(@Param("tenantId") Long tenantId,@Param("type") Integer type,@Param("deleted") Integer deleted,@Param("name") String name);
}
