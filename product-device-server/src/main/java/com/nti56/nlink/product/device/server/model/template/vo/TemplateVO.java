package com.nti56.nlink.product.device.server.model.template.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/18 17:02<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "模板vo")
public class TemplateVO {
    private Long id;

    @Schema(description = "模板名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    /**
     * 模板标题
     */
    @Schema(description = "模板标题")
    private String title;

    /**
     * 通知类型 0邮件 1短信
     */
    @Schema(description = "通知类型 0邮件 1短信")
    private Integer notifyType;

    /**
     * 模板内容
     */
    @Schema(description = "模板内容")
    private String content;

    /**
     * 模板code，短信需要
     */
    @Schema(description = "模板code，短信需要")
    private String templateCode;

    /**
     * 模板状态 0审核中 1审核通过 2审核未通过
     */
    @Schema(description = "模板状态 0审核中 1审核通过 2审核未通过")
    private Integer auditStatus;

    @Schema(description = "审核提示")
    private String auditPrompt;

    /**
     * 模板绑定的ChannelId
     */
    @Schema(description = "模板绑定的ChannelId")
    private Long notifyChannelId;



    @Schema(description = "申请模板理由")
    private String reason;

    @Schema(description = "渠道名称")
    private String channelName;

    @Schema(description = "消息类型")
    private String msgType;
}
