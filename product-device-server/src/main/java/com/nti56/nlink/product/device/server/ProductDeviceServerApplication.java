package com.nti56.nlink.product.device.server;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/2/14 17:37<br/>
 * @since JDK 1.8
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.nti56.nlink.**"})
//@EnableFeignClients(basePackages = {"com.nti56.nlink.*"})
@MapperScan({"com.nti56.nlink.product.device.server.mapper"})
@EnableScheduling
@ServletComponentScan
@EnableAsync
@EnableCaching
public class ProductDeviceServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(ProductDeviceServerApplication.class, args);
    }

}
