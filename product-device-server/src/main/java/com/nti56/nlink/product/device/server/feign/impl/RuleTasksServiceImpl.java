package com.nti56.nlink.product.device.server.feign.impl;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.rule.feign.IRuleTasksService;
import com.nti56.nlink.common.rule.model.RuleParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.rule.Rule;
import com.nti56.nlink.product.device.server.entity.ComputeTaskEntity;
import com.nti56.nlink.product.device.server.mapper.ComputeTaskMapper;
import com.nti56.nlink.product.device.server.mapper.EdgeGatewayMapper;
import com.nti56.nlink.product.device.server.mapper.LabelBindRelationMapper;
import com.nti56.nlink.product.device.server.mapper.LabelMapper;
import com.nti56.nlink.product.device.server.service.ITaskService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class RuleTasksServiceImpl implements IRuleTasksService {

    @Autowired
    LabelMapper labelMapper;

    @Autowired
    EdgeGatewayMapper edgeGatewayMapper;

    @Autowired
    ComputeTaskMapper computeTaskMapper;

    @Autowired
    LabelBindRelationMapper labelBindRelationMapper;

    @Autowired
    ITaskService taskService;

    @Autowired
    CommonFetcherFactory commonFetcherFactory;
    
    @Override
    @Transactional
    public Result<Void> upsertRuleTask(RuleParam ruleParam, TenantIsolation tenantIsolation) {
        log.info("upsertRuleTask ruleParam: {}, tenant: {}", ruleParam, tenantIsolation);
        if(tenantIsolation == null || tenantIsolation.getTenantId() == null){
            log.error("upsertRuleTask 租户参数错误");
            return Result.error("租户参数错误");
        }

        Integer deleteCount = computeTaskMapper.deleteByName(Rule.ruleTaskPrefix + ruleParam.getInstanceId(),tenantIsolation.getTenantId());
        log.info("upsertRuleTask deleteCount: {}", deleteCount);


        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        Result<Rule> ruleResult = Rule.checkInfo(ruleParam, commonFetcher);
        if(!ruleResult.getSignal()){
            return Result.error(ruleResult.getMessage());
        }
        Rule rule = ruleResult.getResult();
        List<ComputeTaskEntity> taskList = rule.generateRuleComputeTask();

        for(ComputeTaskEntity task:taskList){
            computeTaskMapper.insert(task);
        }

        log.info("upsertRuleTask finish: {}", taskList.size());
        return Result.ok();
    }

    @Override
    public Result<Void> stopRuleTask(Long instanceId, TenantIsolation tenantIsolation) {
        log.info("stopRuleTask instanceId： {}, tenant: {}", instanceId, tenantIsolation);
        if(tenantIsolation == null || tenantIsolation.getTenantId() == null){
            log.error("stopRuleTask 租户参数错误");
            return Result.error("租户参数错误");
        }
        Integer logicDeleteCount = computeTaskMapper.logicDeleteByName(Rule.ruleTaskPrefix + instanceId, tenantIsolation.getTenantId());
        log.info("stopRuleTask logicDeleteCount: {}", logicDeleteCount);
        return Result.ok();
    }

    @Override
    public Result<Void> syncRuleTask(Long instanceId, TenantIsolation tenantIsolation) {
        log.info("stopRuleTask instanceId： {}, tenant: {}", instanceId, tenantIsolation);
        if(tenantIsolation == null || tenantIsolation.getTenantId() == null){
            log.error("stopRuleTask 租户参数错误");
            return Result.error("租户参数错误");
        }
        List<ComputeTaskEntity> taskList = computeTaskMapper.listByName(Rule.ruleTaskPrefix + instanceId, tenantIsolation.getTenantId());
        if(taskList != null && taskList.size() > 0){
            for(ComputeTaskEntity task:taskList){
                taskService.syncTask(tenantIsolation, task.getEdgeGatewayId());
            }
        }
        log.info("stopRuleTask taskListSize: {}", taskList.size());
        return Result.ok();
    }
    
    @Override
    public Result<Void> enableRuleTask(Long instanceId, TenantIsolation tenantIsolation) {
        log.info("enableRuleTask instanceId： {}, tenant: {}", instanceId, tenantIsolation);
        if(tenantIsolation == null || tenantIsolation.getTenantId() == null){
            log.error("enableRuleTask 租户参数错误");
            return Result.error("租户参数错误");
        }
        Integer logicRestoreCount = computeTaskMapper.logicRestoreByName(Rule.ruleTaskPrefix + instanceId, tenantIsolation.getTenantId());
        log.info("enableRuleTask logicRestoreCount: {}", logicRestoreCount);
        return Result.ok();
    }

    @Override
    public Result<Void> deleteRuleTask(Long instanceId, TenantIsolation tenantIsolation) {
        log.info("deleteRuleTask instanceId： {}, tenant: {}", instanceId, tenantIsolation);
        if(tenantIsolation == null || tenantIsolation.getTenantId() == null){
            log.error("deleteRuleTask 租户参数错误");
            return Result.error("租户参数错误");
        }
        Integer deleteCount = computeTaskMapper.deleteByName(Rule.ruleTaskPrefix + instanceId, tenantIsolation.getTenantId());
        log.info("deleteRuleTask deleteCount: {}", deleteCount);
        return Result.ok();
    }

}
