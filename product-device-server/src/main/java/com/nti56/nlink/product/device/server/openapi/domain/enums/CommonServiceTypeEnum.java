package com.nti56.nlink.product.device.server.openapi.domain.enums;

import lombok.Getter;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ModelTypeEnum
 * @date 2022/4/29 9:54
 * @Version 1.0
 */
public enum CommonServiceTypeEnum {

    SWITCH_CONTROL_SERVICE(1,"switch_control","通用开关服务"),
    CUSTOMER_SERVICE(2,"customer_service","用户自定服务")
    ;


    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    CommonServiceTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static CommonServiceTypeEnum typeOfValue(Integer value){
        CommonServiceTypeEnum[] values = CommonServiceTypeEnum.values();
        for (CommonServiceTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static CommonServiceTypeEnum typeOfName(String name){
        CommonServiceTypeEnum[] values = CommonServiceTypeEnum.values();
        for (CommonServiceTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static CommonServiceTypeEnum typeOfNameDesc(String nameDesc){
        CommonServiceTypeEnum[] values = CommonServiceTypeEnum.values();
        for (CommonServiceTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
    
}
