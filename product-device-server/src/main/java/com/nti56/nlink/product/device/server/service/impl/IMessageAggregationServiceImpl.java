package com.nti56.nlink.product.device.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.MessageAggregationEntity;
import com.nti56.nlink.product.device.server.mapper.MessageAggregationMapper;
import com.nti56.nlink.product.device.server.service.IMessageAggregationService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【message_aggregation】的数据库操作Service实现
* @createDate 2023-08-11 09:34:16
*/
@Service
public class IMessageAggregationServiceImpl extends ServiceImpl<MessageAggregationMapper, MessageAggregationEntity>
    implements IMessageAggregationService {

    @Override
    public Result pageMessage(PageParam pageParam, TenantIsolation tenantIsolation, Long id) {

        Page<MessageAggregationEntity> pageParams = pageParam.toPage(MessageAggregationEntity.class);
        QueryWrapper<MessageAggregationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("tenant_id",tenantIsolation.getTenantId());
        wrapper.eq("aggregation_id",id);
        Page<MessageAggregationEntity> page = this.page(pageParams, wrapper);
        return Result.ok(page);
    }
}




