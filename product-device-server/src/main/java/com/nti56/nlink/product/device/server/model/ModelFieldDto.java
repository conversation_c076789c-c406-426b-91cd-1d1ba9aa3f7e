package com.nti56.nlink.product.device.server.model;

import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import com.nti56.nlink.product.device.server.entity.SubscriptionEntity;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema
public class ModelFieldDto {
    
    @Schema(description = "属性")
    private List<PropertyElm> properties;

    @Schema(description = "事件")
    private List<EventElm> events;

    @Schema(description = "服务列表")
    private List<ThingServiceEntity> services;

    @Schema(description = "订阅列表")
    private List<SubscriptionEntity> subscriptions;
}
