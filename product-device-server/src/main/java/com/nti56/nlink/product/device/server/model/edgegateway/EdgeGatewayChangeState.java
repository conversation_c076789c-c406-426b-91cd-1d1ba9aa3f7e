package com.nti56.nlink.product.device.server.model.edgegateway;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EdgeGatewayChangeState implements Serializable {
    private Boolean isEdgeGatewayChange;
    private LocalDateTime changeTime;
}
