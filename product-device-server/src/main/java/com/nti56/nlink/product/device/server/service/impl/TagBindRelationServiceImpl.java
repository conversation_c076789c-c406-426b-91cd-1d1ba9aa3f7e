package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ResourceTypeEnum;
import com.nti56.nlink.product.device.server.entity.TagBindRelationEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.TagBindRelationMapper;
import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.service.ITagBindRelationService;
import com.nti56.nlink.product.device.server.service.ITagService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 标志关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 *  2022-04-11 17:27:49
 * @since JDK 1.8
 */
@Service
public class TagBindRelationServiceImpl extends BaseServiceImpl<TagBindRelationMapper,TagBindRelationEntity> implements ITagBindRelationService {

    @Autowired
    TagBindRelationMapper mapper;

    @Override
    public Result<List<TagBindRelationEntity>> list(TagBindRelationEntity entity) {
        List<TagBindRelationEntity> list = mapper.selectList(new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    @Override
    public Result<Void> deleteById(Long tenantId, Long entityId) {
        TagBindRelationEntity entity = mapper.selectById(entityId);
        if (!entity.getTenantId().equals(tenantId)) {
            return Result.error(ServiceCodeEnum.TENANT_ERROR);
        }
        if (mapper.deleteById(entityId) == 1) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    @Transactional
    @Override
    public Result<Void> saveList(Long tenantId, Long targetId, @NotEmpty List<Long> tagIds, ResourceTypeEnum type) {
        mapper.delete(new QueryWrapper<TagBindRelationEntity>().eq("target_id",targetId).eq("tenant_id",tenantId));
        List<TagBindRelationEntity> list = new ArrayList<>();
        Optional.ofNullable(tagIds).orElse(new ArrayList<>()).forEach((tagId) ->{
            TagBindRelationEntity entity = TagBindRelationEntity.builder().tagId(tagId).resourceType(type.getValue()).targetId(targetId).build();
            entity.setTenantId(tenantId);
            list.add(entity);
        });
        if (CollectionUtils.isEmpty(list) || saveBatch(list)) {
            return Result.ok();
        }
        throw new BizException(ServiceCodeEnum.TAG_BIND_ERROR);
    }

    @Override
    public Result<List<TagBindRelationEntity>> listByTagIds(Long tenantId,List<String> tagIds, ResourceTypeEnum type) {
        TagBindRelationEntity build = TagBindRelationEntity.builder().resourceType(type.getValue()).build();
        build.setTenantId(tenantId);
        LambdaQueryWrapper<TagBindRelationEntity> wrapper = new LambdaQueryWrapper<>(build).in(Optional.ofNullable(tagIds).isPresent() && tagIds.size() > 0,TagBindRelationEntity::getTagId, tagIds);
        List<TagBindRelationEntity> list = mapper.selectList(wrapper);
        return Result.ok(list);
    }

    @Autowired
    ITagService tagService;

    @Override
    public R getTagList(TenantIsolation tenantIsolation, Integer resourceType) {
        List<Long> ids = mapper.selectIdByResourceType(resourceType,tenantIsolation.getTenantId());
        if (Optional.ofNullable(ids).isPresent() && ids.size() > 0) {
            R r =  R.ok(tagService.listByIds(ids));
            if ((Boolean)r.get("ok")) {
                return r;
            }
        }
        return R.ok(new ArrayList<>());
    }

    @Override
    public boolean deleteByTargetId(Long tenantId, ResourceTypeEnum resourceType, Long targetId) {
        return this.remove(new LambdaQueryWrapper<TagBindRelationEntity>()
            .eq(TagBindRelationEntity::getTenantId, tenantId)
            .eq(TagBindRelationEntity::getTargetId,targetId)
            .eq(TagBindRelationEntity::getResourceType, resourceType.getValue())
        );
    }

    @Override
    public List<Long> getTagIdsByTargetId(Long tenantId, ResourceTypeEnum resourceType, Long targetId) {
        LambdaQueryWrapper<TagBindRelationEntity> lqw = new LambdaQueryWrapper<>();
        lqw.select(TagBindRelationEntity::getTagId)
                .eq(TagBindRelationEntity::getTenantId, tenantId)
                .eq(TagBindRelationEntity::getTargetId,targetId)
                .eq(TagBindRelationEntity::getResourceType, resourceType.getValue());

        return this.listObjs(lqw,list -> Long.valueOf(list.toString()));
    }

    @Override
    public List<TagRsp> getTags(Long tenantId, ResourceTypeEnum resourceType, Long targetId){
        List<Long> ids = this.getTagIdsByTargetId(tenantId, resourceType, targetId);
        TenantIsolation tenantIsolation = new TenantIsolation();
        tenantIsolation.setTenantId(tenantId);
        Result<List<TagRsp>> result= tagService.listByIds(tenantIsolation, ids);
        if (result.getSignal()) {
            return result.getResult();
        }
        return null;
    }



    @Override
    public Result<List<TagBindRelationEntity>> listByTargetId(Long tenantId, Long targetId) {
        if (!Optional.ofNullable(targetId).isPresent()) {
            return Result.ok();
        }
        TagBindRelationEntity entity = TagBindRelationEntity.builder()
            .targetId(targetId)
            .build();
        entity.setTenantId(tenantId);
        return list(entity);
    }

    @Override
    public void batchDeleteByDeviceIds(Long tenantId, Set<Long> ids) {
        if (CollectionUtil.isNotEmpty(ids)) {
            LambdaQueryWrapper<TagBindRelationEntity> w = new LambdaQueryWrapper<TagBindRelationEntity>()
                    .eq(TagBindRelationEntity::getTenantId, tenantId)
                    .eq(TagBindRelationEntity::getResourceType, ResourceTypeEnum.DEVICE.getValue())
                    .in(TagBindRelationEntity::getTargetId, ids);
            mapper.delete(w);

        }
    }

    @Override
    public Map<Long, List<TagRsp>> getTagListGroupByTargetId(Long tenantId, ResourceTypeEnum resourceType, List<Long> targetIds) {
        if (Objects.isNull(targetIds) || targetIds.isEmpty()) {
            return Collections.emptyMap();
        }
        List<TagBindRelationEntity> bindList = new LambdaQueryChainWrapper<>(mapper)
                .select(TagBindRelationEntity::getTagId, TagBindRelationEntity::getTargetId)
                .eq(TagBindRelationEntity::getTenantId, tenantId)
                .in(TagBindRelationEntity::getTargetId, targetIds)
                .eq(TagBindRelationEntity::getResourceType, resourceType.getValue())
                .list();
        if (Objects.isNull(bindList) || bindList.isEmpty()) {
            return Collections.emptyMap();
        }
        List<Long> tagIds = BeanUtilsIntensifier.getIds(bindList, TagBindRelationEntity::getTagId);
        TenantIsolation tenantIsolation = new TenantIsolation();
        tenantIsolation.setTenantId(tenantId);
        Result<List<TagRsp>> result= tagService.listByIds(tenantIsolation, tagIds);
        if (!result.getSignal()) {
            return Collections.emptyMap();
        }
        Map<Long, TagRsp> tagRspMap = result.getResult().stream().collect(Collectors.toMap(TagRsp::getId, Function.identity()));
        return bindList.stream().collect(Collectors.groupingBy(TagBindRelationEntity::getTargetId, Collectors.mapping(bind -> tagRspMap.get(bind.getTagId()), Collectors.toList())));
    }

    @Override
    public Result<Void> batchSaveList(Long tenantId, Map<Long, List<Long>> deviceTagMap, ResourceTypeEnum tpye) {
        batchDeleteByDeviceIds(tenantId, deviceTagMap.keySet());
        List<TagBindRelationEntity> tagBindRelationEntities = deviceTagMap.keySet().stream().flatMap(deviceId->deviceTagMap.get(deviceId).stream().map(tagId->TagBindRelationEntity.builder().tagId(tagId).resourceType(tpye.getValue()).targetId(deviceId).tenantId(tenantId).build())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tagBindRelationEntities) || saveBatch(tagBindRelationEntities)) {
            return Result.ok();
        }
        throw new BizException(ServiceCodeEnum.TAG_BIND_ERROR);
    }

}
