package com.nti56.nlink.product.device.server.openapi.domain.request;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DoCommonServiceRequest {

    private List<Long> deviceId;
    // open or close
    private String switchType;

    // 通用服务类型 开关- switch_control
    private String serviceType;

    /**
     * 是否定时执行
     */
    private String cron;

    /**
     * 业务唯一区别id
     */
    private String businessId;

    private Map<String,Object> params;

    private List<DoServiceTaskRequest> tasks;

}
