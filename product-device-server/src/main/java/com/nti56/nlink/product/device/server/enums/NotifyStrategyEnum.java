package com.nti56.nlink.product.device.server.enums;

import lombok.Getter;


public enum NotifyStrategyEnum {

    EMAIL_STRATEGY(0, "emailStrategy"),
    ALI_YUN_SMS_STRATEGY(1, "aliYunSmsStrategy"),
    DING_DING_ROBOT_STRATEGY(2, "dingDingRobotStrategy")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    NotifyStrategyEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static NotifyStrategyEnum typeOfValue(Integer value){
        NotifyStrategyEnum[] values = NotifyStrategyEnum.values();
        for (NotifyStrategyEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static NotifyStrategyEnum typeOfName(String name){
        NotifyStrategyEnum[] values = NotifyStrategyEnum.values();
        for (NotifyStrategyEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static String getNameByValue(Integer value){
        NotifyStrategyEnum[] values = NotifyStrategyEnum.values();
        for (NotifyStrategyEnum v : values) {
            if (v.value.equals(value)) {
                return v.name;
            }
        }
        return null;

    }
}
