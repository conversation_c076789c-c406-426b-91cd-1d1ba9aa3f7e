package com.nti56.nlink.product.device.server.model.datasync;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.product.device.client.model.dto.json.CustomDriverRuntimeInfoField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: 自定义内容存储
 * <AUTHOR>
 * @version 1.0
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "自定义协议内容存储")
public class SyncCustomContentDto {
    
    /**
     * id
     */
    private Long id;
    
    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;
    
    /**
     * 内容
     */
    @Schema(description = "内容")
    private String content; //JSONObject.toJSONString(CustomDriverRuntimeInfoField)
    
}
