package com.nti56.nlink.product.device.server.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:04:57
 * @since JDK 1.8
 */
@Data
@Schema(description = "创建标签计算任务参数")
public class LabelComputeTaskParam {
    
    @Schema(description = "任务名称")
    private String name;
    
    @Schema(description = "订阅的采集任务id")
    private Long gatherTaskId;

    @Schema(description = "标签分组ids")
    private List<Long> labelGroupIds;
}
