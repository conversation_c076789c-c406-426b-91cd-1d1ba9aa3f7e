package com.nti56.nlink.product.device.server.verticle.post.processor.property;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceDataResource;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceTwin;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ChangeSubjectEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ChangeTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SubscriptionFromEnum;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpPropertyTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.service.IChangeNoticeService;
import com.nti56.nlink.product.device.server.verticle.post.processor.PostProcessorHandler;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName UpData2RedisHandler
 * @date 2022/7/26 17:09
 * @Version 1.0
 */
@Component
@Slf4j
public class UpData2RedisHandler extends PostProcessorHandler<GwUpPropertyTopic.TopicInfo> {

    DeviceTwin deviceTwin;

    @Autowired
    DeviceDataResource deviceDataResource;

    @Autowired
    IChangeNoticeService changeNoticeService;

     //@Async("propertyConsumerAsyncExecutor")
    @Override
    public void process(GwUpPropertyTopic.TopicInfo topicInfo, UpData upData){
        long start = System.currentTimeMillis();
        log.debug("上报链路，UpData2RedisHandler start:{},topic:{}",start,topicInfo);
        super.process(topicInfo,upData);
        long end = System.currentTimeMillis();
        log.debug("上报链路，UpData2RedisHandler end:{},耗时：{}ms,topic:{}",start,end - start,topicInfo);
    }

    @Override
    public void doProcess(GwUpPropertyTopic.TopicInfo topicInfo, UpData upData) {
        Long deviceId = Long.valueOf(topicInfo.getDeviceId());
        Long tenantId = Long.valueOf(topicInfo.getTenantId());
        deviceTwin = new DeviceTwin(deviceDataResource,deviceId);
        doUpData(upData, deviceTwin, deviceId, tenantId);
    }

    public void doProcess(GwUpPropertyTopic.TopicInfo topicInfo, UpData upData,DeviceTwin deviceTwin) {
        Long deviceId = Long.valueOf(topicInfo.getDeviceId());
        Long tenantId = Long.valueOf(topicInfo.getTenantId());
        doUpData(upData, deviceTwin, deviceId, tenantId);
    }

    private void doUpData(UpData upData, DeviceTwin deviceTwin, Long deviceId, Long tenantId) {
        if (ObjectUtil.isNotEmpty(upData)) {
            //写入孪生
            //触发dataChange订阅
            //触发事件，fault事件和trigger事件，及相关订阅
            Map<String,Object> map = deviceTwin.processSubscription(SubscriptionFromEnum.UP_DATA_TO_REDIS, upData.getProp(), upData.getTimestamp());
            //变动通知
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id",deviceId);
            jsonObject.put("properties",map);
            changeNoticeService.changeNotice(tenantId,jsonObject, ChangeTypeEnum.PROPERTY, ChangeSubjectEnum.DEVICE);
        }
    }

}
