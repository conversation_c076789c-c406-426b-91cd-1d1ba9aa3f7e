package com.nti56.nlink.product.device.server.verticle.post.processor;

import cn.hutool.core.util.ObjectUtil;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName PostProcessorHandler
 * @date 2022/7/26 16:47
 * @Version 1.0
 */
public abstract class PostProcessorHandler<T> {

    PostProcessorHandler next;

    public Boolean hasNext() {
        return ObjectUtil.isNotEmpty(next);
    }

    public void setNext(PostProcessorHandler postProcessorHandler) {
        this.next = postProcessorHandler;
    }

    public PostProcessorHandler getNext() {
        return next;
    }

    public void process(T topicInfo, UpData upData){
        doProcess(topicInfo,upData);
        if (hasNext()) {
            getNext().doProcess(topicInfo,upData);
        }
    }

    protected abstract void doProcess(T topicInfo, UpData upData);

}
