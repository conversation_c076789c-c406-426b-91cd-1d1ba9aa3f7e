package com.nti56.nlink.product.device.server.verticle.post.processor.property;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.WriteApiBlocking;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ReportTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.YesNoEnum;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpPropertyTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import com.nti56.nlink.product.device.server.config.InfluxDBCacheConfig;
import com.nti56.nlink.product.device.server.service.cache.InfluxDBBatchCache;
import com.nti56.nlink.product.device.server.verticle.post.processor.PostProcessorHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName UpData2InfluxDBHandler
 * @date 2022/7/26 17:01
 * @Version 1.0
 */
@Component
@Slf4j
public class UpData2InfluxDBHandler extends PostProcessorHandler<GwUpPropertyTopic.TopicInfo> {

    @Autowired
    private InfluxDBClient influxDbClient;
    
    @Autowired
    private InfluxDBCacheConfig cacheConfig;


    @Async("propertyConsumerAsyncExecutor")
    @Override
    public void process(GwUpPropertyTopic.TopicInfo topicInfo, UpData upData) {
        super.process(topicInfo, upData);
    }

    @Override
    public void doProcess(GwUpPropertyTopic.TopicInfo topicInfo, UpData upData) {
        Long start = System.currentTimeMillis();
        try {
            // 使用缓存机制替代直接写入
            addToCache(topicInfo, upData);
        } catch (Exception e) {
            log.error("Error processing data for tenant:{}, gateway:{}, device:{}", 
                topicInfo.getTenantId(), topicInfo.getEdgeGatewayId(), topicInfo.getDeviceId(), e);
            // 缓存失败时，降级为直接写入
            try {
                WriteApiBlocking writeApi = influxDbClient.getWriteApiBlocking();
                List<Point> pointList = upDataToPoints(topicInfo, upData);
                writeApi.writePoints(pointList);
                log.info("Fallback to direct write for tenant:{}, gateway:{}, device:{}", 
                    topicInfo.getTenantId(), topicInfo.getEdgeGatewayId(), topicInfo.getDeviceId());
            } catch (Exception ex) {
                log.error("Both cache and direct write failed for tenant:{}, gateway:{}, device:{}", 
                    topicInfo.getTenantId(), topicInfo.getEdgeGatewayId(), topicInfo.getDeviceId(), ex);
            }
        }
        
        Long spend = System.currentTimeMillis() - start;
        if (spend > 100) {
            log.info("upData2InfluxDBHandler tenant:{}, edgeGwId:{}, deviceId:{}, cost: {}ms", 
                topicInfo.getTenantId(), topicInfo.getEdgeGatewayId(), topicInfo.getDeviceId(), spend);
        }
    }

    private List<Point> upDataToPoints(GwUpPropertyTopic.TopicInfo topicInfo, UpData upData) {
        Long timestamp = upData.getTimestamp();
        List<Point> pointList = new ArrayList<>();
        for (UpProp item : upData.getProp()) {
            if (item.getIsArray()) {
                String propertyTag;
                for (int i = 0; i < item.getLength(); i++) {
                    Map<String, Object> fields = new HashMap<>();
                    propertyTag = item.getProperty() + "." + i;
                    if (ReportTypeEnum.CHANGE_REPORT.getName().equals(topicInfo.getReportType())) {
                        String valueFieldKey = item.getDataType() + "." + i + ".value";
                        try {
                            Object fieldValue = null;
                            if (item.getValue() instanceof ArrayList) {
                                fieldValue = ((ArrayList<?>) item.getValue()).get(i);
                            } else if(item.getValue() instanceof String) {
                                fieldValue= (String) item.getValue();
                            }else {
                                JSONArray value = (JSONArray) item.getValue();
                                fieldValue = value.get(i);
                            }
                            fields.put(valueFieldKey, fieldValue);
                            Object preFieldValue = null;
                            if (ObjectUtil.isNotEmpty(item.getPreValue())) {
                                if (item.getPreValue() instanceof ArrayList) {
                                    preFieldValue = ((ArrayList<?>) item.getPreValue()).get(i);
                                } else if(item.getPreValue() instanceof String) {
                                    preFieldValue= (String) item.getPreValue();
                                } else {
                                    JSONArray value = (JSONArray) item.getPreValue();
                                    preFieldValue = value.get(i);
                                }
                                String preValueFieldKey = item.getDataType() + "." + i + ".preValue";
                                fields.put(preValueFieldKey, preFieldValue);
                            }
                        } catch (ClassCastException e) {
                            log.error("ClassCastException class:{},tenantId:{},gatewayId:{},deviceId:{},ReportType:{},dataJson:{}", item.getValue().getClass(), topicInfo.getTenantId(), topicInfo.getEdgeGatewayId(), topicInfo.getDeviceId(), topicInfo.getReportType(), JSON.toJSONString(item));
                        }
                    } else {
                        String fieldKey = item.getDataType() + "." + i;
                        JSONArray value = new JSONArray();
                        if (item.getValue().getClass().isArray()) {
                            Object[] objects = (Object[]) item.getValue();
                            for (Object v : objects) {
                                value.add(v);
                            }
                        } else {
                            value = (JSONArray) item.getValue();
                        }
                        Object fieldValue = value.get(i);
                        fields.put(fieldKey, fieldValue);
                    }

                    Point point = Point.measurement(topicInfo.getTenantId())
                            .addTag("type", topicInfo.getReportType())
                            .addTag("eventName", topicInfo.getReportType())
                            .addTag("deviceId", topicInfo.getDeviceId())
                            .addTag("property", item.getProperty())
                            .addTag("propertyAlias", propertyTag)
                            .addTag("dataType", item.getDataType())
                            .addTag("isArray", item.getIsArray() ? YesNoEnum.YES.getName() : YesNoEnum.NO.getName())
                            .addTag("length", item.getLength().toString())
                            .addFields(fields)
                            .time(timestamp, WritePrecision.MS);

                    Long traceId = item.getTraceId();
                    if (traceId != null) {
                        log.info("trace: {}, labelId:{}, property:{}, value: {}", traceId, item.getLabelId(), item.getProperty(), item.getValue());
                        point.addTag("traceId", traceId.toString());
                    }
                    pointList.add(point);
                }
            } else {
                Map<String, Object> fields = new HashMap<>();
                if (ReportTypeEnum.CHANGE_REPORT.getName().equals(topicInfo.getReportType())) {
                    String valueFieldKey = item.getDataType() + ".value";
                    Object fieldValue = item.getValue();
                    fields.put(valueFieldKey, fieldValue);
                    Object preFieldValue = item.getPreValue();
                    if (ObjectUtil.isNotEmpty(preFieldValue)) {
                        String preValueFieldKey = item.getDataType() + ".preValue";
                        fields.put(preValueFieldKey, preFieldValue);
                    }
                } else {
                    String fieldKey = item.getDataType();
                    Object fieldValue = item.getValue();
                    fields.put(fieldKey, fieldValue);
                }
                Point point = Point.measurement(topicInfo.getTenantId())
                        .addTag("type", topicInfo.getReportType())
                        .addTag("eventName", topicInfo.getReportType())
                        .addTag("deviceId", topicInfo.getDeviceId())
                        .addTag("property", item.getProperty())
                        .addTag("propertyAlias", item.getProperty())
                        .addTag("dataType", item.getDataType())
                        .addTag("isArray", item.getIsArray() ? YesNoEnum.YES.getName() : YesNoEnum.NO.getName())
                        .addTag("length", item.getLength().toString())
                        .addFields(fields)
                        .time(timestamp, WritePrecision.MS);

                Long traceId = item.getTraceId();
                if (traceId != null) {
                    log.info("trace: {}, labelId:{}, property:{}, value: {}", traceId, item.getLabelId(), item.getProperty(), item.getValue());
                    point.addTag("traceId", traceId.toString());
                }
                pointList.add(point);
            }
        }
        return pointList;
    }


    private void addToCache(GwUpPropertyTopic.TopicInfo topicInfo, UpData upData) {
        try {
            List<Point> pointList = upDataToPoints(topicInfo, upData);
            if (!pointList.isEmpty()) {
                boolean success = InfluxDBBatchCache.push(Long.parseLong(topicInfo.getTenantId()), pointList);
                if (!success) {
                    log.warn("Failed to add {} points to cache for tenant {}, cache may be full", 
                        pointList.size(), topicInfo.getTenantId());
                    throw new RuntimeException("Cache is full");
                }
            }
        } catch (NumberFormatException e) {
            log.error("Invalid tenant ID format: {}", topicInfo.getTenantId(), e);
            throw new RuntimeException("Invalid tenant ID", e);
        }
    }


    public void writeByTenant() {
        log.info("Starting InfluxDB batch write service with config: {}", cacheConfig);
        WriteApiBlocking writeApi = influxDbClient.getWriteApiBlocking();
        
        long lastCleanupTime = System.currentTimeMillis();
        
        while (!Thread.currentThread().isInterrupted()) {
            long start = System.currentTimeMillis();
            int totalSize = 0;
            int successCount = 0;
            int errorCount = 0;
            
            try {
                Set<Long> tenantIds = InfluxDBBatchCache.getTenantIds();
                
                for (Long tenantId : tenantIds) {
                    try {
                        // 使用配置中的批量大小
                        List<Point> pointList = InfluxDBBatchCache.popBatch(tenantId, cacheConfig.getBatchSize());
                        
                        if (!pointList.isEmpty()) {
                            writeApi.writePoints(pointList);
                            totalSize += pointList.size();
                            successCount++;
                            
                            log.debug("Successfully wrote {} points for tenant {}", pointList.size(), tenantId);
                        }
                    } catch (Exception e) {
                        errorCount++;
                        log.error("Failed to write points for tenant {}", tenantId, e);
                    }
                }
                
                // 按配置间隔清理空缓存
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastCleanupTime > cacheConfig.getCleanupInterval()) {
                    InfluxDBBatchCache.cleanup();
                    lastCleanupTime = currentTime;
                }
                
                // 检查缓存健康状态
                if (InfluxDBBatchCache.isNearLimit()) {
                    log.warn("Cache is near limit, current size: {}, max: {}", 
                        InfluxDBBatchCache.getTotalCacheSize(), cacheConfig.getMaxTotalPoints());
                }
                
            } catch (Exception e) {
                log.error("Error in batch write cycle", e);
            }
            
            Long spend = System.currentTimeMillis() - start;
            if (spend > 100 || totalSize > 0) {
                log.info("Batch write completed - tenants: {}, points: {}, success: {}, errors: {}, cost: {}ms, cache: {}",
                    InfluxDBBatchCache.getTenantIds().size(), totalSize, successCount, errorCount, spend,
                    InfluxDBBatchCache.getCacheStats());
            }
            
            try {
                Thread.sleep(cacheConfig.getWriteInterval()); // 使用配置的间隔
            } catch (InterruptedException e) {
                log.info("Batch write service interrupted");
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        log.info("InfluxDB batch write service stopped");
    }


}
