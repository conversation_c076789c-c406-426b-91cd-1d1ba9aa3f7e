package com.nti56.nlink.product.device.server.job;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.config.XxlJobConfig;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.job.callble.JobCallable;
import com.nti56.nlink.product.device.server.openapi.domain.request.DoServiceTaskRequest;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.service.IJobService;
import com.nti56.nlink.product.device.server.util.XxlJobUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;

/**
 * 类说明：
 *
 * @ClassName ServiceCronJob
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/3/6 16:39
 * @Version 1.0
 */

@Component
@Slf4j
public class ServiceCronJob {

    @Autowired
    private ExecutorService executorService;

    @Autowired
    IDeviceService deviceService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @XxlJob("serviceTaskHandler")
    public void processServiceCron() {
        long jobId = XxlJobHelper.getJobId();
        String jobParam = XxlJobHelper.getJobParam();
        log.info("jobId:{}",jobId);
        executorService.submit((Callable<Object>) () -> {
            log.debug("------------------start job to do service--------------");
            log.debug("--------------{} job param: {}----------------------", XxlJobHelper.getJobId(), jobParam);
            if (StrUtil.isNotBlank(jobParam)) {
                exec(jobParam);
            }else{
                //尝试redis获取
                String params = stringRedisTemplate.opsForValue().get(String.format(RedisConstant.JOB_PARAM_CACHE, jobId));
                if(StrUtil.isNotBlank(params)){
                    exec(params);
                }
            }
            log.debug("--------------{} job execute success----------------------", XxlJobHelper.getJobId());
            return true;
        });
    }

    private void exec(String jobParam) {
        log.info("execute param:{}",jobParam);
        try{
            List<DoServiceTaskRequest> doServiceTaskRequests = JSONUtil.toList(jobParam, DoServiceTaskRequest.class);
            doServiceTaskRequests.forEach(req -> {
                DeviceServiceLogEntity logEntity = DeviceServiceLogEntity.builder()
                        .createTime(LocalDateTime.now()).callType(3).deviceId(req.getDeviceId())
                        .serviceName(req.getServiceName()).build();
                R r = deviceService.doServiceTask(req.getTenantId(), req.getDeviceId(),
                        req.getServiceName(), req.getInput(), logEntity,true);
                try {
                    Thread.sleep(30000L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                log.debug("execute device[] service[] success",req.getDeviceId(),req.getServiceName());
            });
        }catch (Exception e){
            log.error("执行通用服务异常，异常原因：{}",e.getMessage());
        }
    }
}
