package com.nti56.nlink.product.device.server.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 设备dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:58
 * @since JDK 1.8
 */
@Data
@Schema(description = "设备孪生请求对象")
public class DeviceTwinRequestBo {

    @Schema(description = "网关ID")
    private Long edgeGatewayId;

    @Schema(description = "设备Id列表")
    private List<Long> ids;

}
