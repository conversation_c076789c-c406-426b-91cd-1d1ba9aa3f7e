package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.TagBindRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 标志关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11 17:27:49
 */
@Mapper
public interface TagBindRelationMapper extends CommonMapper<TagBindRelationEntity> {

    @Select("SELECT DISTINCT tag_id FROM tag_bind_relation WHERE resource_type = #{resourceType} AND deleted = 0 AND tenant_id = #{tenantId}")
    List<Long> selectIdByResourceType(@Param("resourceType") Integer resourceType,@Param("tenantId")Long tenantId);

}
