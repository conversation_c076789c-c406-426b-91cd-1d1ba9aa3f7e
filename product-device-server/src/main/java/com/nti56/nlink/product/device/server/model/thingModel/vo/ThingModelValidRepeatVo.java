package com.nti56.nlink.product.device.server.model.thingModel.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ThingModelValidRepeatVo {

    private List<ThingModelEventVo> eventList;

    private List<ThingModelPropertyVo> propertyList;

    private List<ThingModelServiceVo> serviceList;

    private List<ThingModelSubscriptionVo> subscriptionList;

}
