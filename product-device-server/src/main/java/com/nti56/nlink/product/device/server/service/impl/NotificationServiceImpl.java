package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.JwtUserInfoUtils;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.entity.NotificationEntity;
import com.nti56.nlink.product.device.server.mapper.NotificationMapper;
import com.nti56.nlink.product.device.server.model.notification.dto.NotificationDTO;
import com.nti56.nlink.product.device.server.service.INotificationService;
import com.nti56.nlink.product.device.server.util.IdHashUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【notification(系统通知表)】的数据库操作Service实现
 * @createDate 2023-06-07 10:13:21
 */
@Service
public class NotificationServiceImpl extends ServiceImpl<NotificationMapper, NotificationEntity> implements INotificationService {
    public static final int STATUS_PASS = 2;
    public static final int ALL = 1;
    @Resource
    private NotificationMapper notificationMapper;

    @Autowired @Lazy
    RedisTemplate<String, Object> redisTemplate;

    @Override
    public Result<Page<NotificationEntity>> pageList(PageParam pageParam, NotificationDTO queryDTO) {
        if(Objects.isNull(pageParam)){
            pageParam = new PageParam();
            pageParam.setCurrent(1);
            pageParam.setSize(10);
        }
        QueryWrapper<NotificationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("create_time");
        Page<NotificationEntity> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        Page<NotificationEntity> notificationEntityPage = notificationMapper.selectPage(page, queryWrapper);
        return Result.ok(notificationEntityPage);
    }

    @Override
    public Result<Void> createNew(NotificationDTO notificationDTO) {

        String title = notificationDTO.getTitle();
        String notificationContent = notificationDTO.getNotificationContent();
//        String versionInfo = notificationDTO.getVersionInfo();
        if (StrUtil.isBlank(title)) {
            return Result.error("通知标题为空！");
        }
        if (StrUtil.isBlank(notificationContent)) {
            return Result.error("通知内容为空！");
        }
       /* if (StrUtil.isBlank(versionInfo)) {
            return Result.error("版本信息为空！");
        }*/
        NotificationEntity notificationEntity = BeanUtilsIntensifier.copyBean(notificationDTO, NotificationEntity.class);
        notificationEntity.setNotificationStatus(STATUS_PASS);
        notificationEntity.setNotificationType(ALL);
        notificationEntity.setUpdateTime(notificationDTO.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        boolean save = this.save(notificationEntity);
        if (save) {
            return Result.ok();
        } else {
            return Result.error("保存失败");
        }

    }

    @Override
    public Result<Boolean> read(TenantIsolation tenantIsolation, Long id) {
        if (Objects.isNull(id)) {
            return Result.error("通知id为空");
        }
        Long userId = JwtUserInfoUtils.getUserId();
        if (Objects.isNull(userId)) {
            return Result.error("用户id为空");
        }
        int userIdHash = IdHashUtil.hashMapping(userId);
        String key = String.format(RedisConstant.NOTIFICATION_READ_MAP, id);
        redisTemplate.opsForValue().setBit(key, userIdHash, true);
        return Result.ok(true);
    }

    @Override
    public Result<List<NotificationEntity>> listLatest4(TenantIsolation tenantIsolation) {
        QueryWrapper<NotificationEntity> queryWrapper = new QueryWrapper();
        queryWrapper.orderByDesc("create_time");
        Page<NotificationEntity> page = new Page<>(1, 4);
        Page<NotificationEntity> notificationEntityPage = notificationMapper.selectPage(page, queryWrapper);
        List<NotificationEntity> records = notificationEntityPage.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {

            Long userId = JwtUserInfoUtils.getUserId();
            records.forEach(r -> {
                String key = String.format(RedisConstant.NOTIFICATION_READ_MAP, r.getId());
                Boolean readied = redisTemplate.opsForValue().getBit(key, IdHashUtil.hashMapping(userId));
                r.setReadied(readied);
            });
        }
        return Result.ok(records);
    }

    @Override
    public Result<Boolean> deleteById(Long id) {

        int count = notificationMapper.deleteById(id);
        if (count > 0) {
            String key = String.format(RedisConstant.NOTIFICATION_READ_MAP, id);
            redisTemplate.delete(key);
            return Result.ok(true);
        } else {
            return Result.error("删除失败");
        }
    }

    @Override
    public Result<Boolean> updateNotification(TenantIsolation tenantIsolation, NotificationDTO notificationDTO, Long id) {

        NotificationEntity byId = getById(id);
        if(Objects.isNull(byId)){
            return Result.error("更新失败，找不到通知");
        }
        if (StrUtil.isNotBlank(notificationDTO.getTitle())) {
            byId.setTitle(notificationDTO.getTitle());
        }
        if (StrUtil.isNotBlank(notificationDTO.getNotificationContent())) {
            byId.setNotificationContent(notificationDTO.getNotificationContent());
        }
        if (StrUtil.isNotBlank(notificationDTO.getVersionInfo())) {
            byId.setVersionInfo(notificationDTO.getVersionInfo());
        }
        if (!Objects.isNull(notificationDTO.getUpdateTime())) {
            byId.setUpdateTime(notificationDTO.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
//        updateById(byId);
        updateNotify(byId);
        return Result.ok(true);
    }

    private void updateNotify(NotificationEntity byId) {
        notificationMapper.updateSelf(byId);
    }
}
