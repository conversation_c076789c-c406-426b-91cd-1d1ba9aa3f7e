package com.nti56.nlink.product.device.server.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:33:25
 * @since JDK 1.8
 */
public enum ActionEnum {
    CREATE("create", "创建"),
    UPDATE("update", "更新"),
    QUERY("query", "查询"),
    DELETE("delete", "删除"),
    IMPORT("import", "导入"),
    EXPORT("export", "导出"),
    SYNC("sync", "同步"),
    FETCH("fetch", "拉取"),
    ;

    @Getter
    private String value;

    @Getter
    private String desc;

    ActionEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
    public static List toList(){
        List<Map> result = new ArrayList<>();
        ActionEnum[] values = ActionEnum.values();
        Map<String,Object> map ;
        for (ActionEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.desc);
            map.put("value",v.value);
            result.add(map);
        }
        return result;
    }



}
