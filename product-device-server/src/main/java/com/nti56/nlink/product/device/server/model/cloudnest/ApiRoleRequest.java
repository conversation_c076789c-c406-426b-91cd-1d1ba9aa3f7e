package com.nti56.nlink.product.device.server.model.cloudnest;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ApiRoleRequest extends BaseRequest {
    /**角色主键*/
    @ApiModelProperty(value = "角色主键")
    private Long id;


    /**集团公司ID*/
    @ApiModelProperty(value = "集团公司ID")
    private String clientId;


    /**应用代码*/
    @ApiModelProperty(value = "应用代码")
    private String appcode;


    /**角色数字身份标识*/
    @ApiModelProperty(value = "角色数字身份标识")
    private String roleTid;


    /**角色名称*/
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    /**角色编码*/
    @ApiModelProperty(value = "角色编码")
    private String roleCode;

    /**角色类型：1.业务角色 2.普通角色*/
    @ApiModelProperty(value = "角色类型：1.业务角色 2.普通角色")
    private Integer roleType;


    /**角色描述*/
    @ApiModelProperty(value = "角色描述")
    private String roleDescribe;


    /**序号*/
    @ApiModelProperty(value = "序号")
    private Integer sortNo;


    /**创建人id*/
    @ApiModelProperty(value = "创建人id")
    private Long createBy;


    /**创建时间*/
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;


    /**修改人id*/
    @ApiModelProperty(value = "修改人id")
    private Long modifyBy;


    /**修改时间*/
    @ApiModelProperty(value = "修改时间")
    private java.util.Date modifyTime;


    /**操作人登录IP*/
    @ApiModelProperty(value = "操作人登录IP")
    private String createIp;
}
