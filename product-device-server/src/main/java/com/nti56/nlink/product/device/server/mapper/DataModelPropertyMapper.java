package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.DataModelPropertyEntity;
import com.nti56.nlink.product.device.server.model.DataModelPropertyBo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 数据模型属性表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13 11:49:35
 */
@Mapper
public interface DataModelPropertyMapper extends CommonMapper<DataModelPropertyEntity> {

    @Select("SELECT * FROM data_model_property WHERE data_model_id = #{dataModelId} AND deleted = 0")
    List<DataModelPropertyEntity> listByDataModelId(@Param("dataModelId") Long dataModelId);

    List<DataModelPropertyBo> listBoByDataModelId(@Param("tenantId") Long tenantId, 
                                                    @Param("dataModelId") Long dataModelId);

    DataModelPropertyEntity getById(@Param("tenantId") Long tenantId, @Param("id") Long id);

}
