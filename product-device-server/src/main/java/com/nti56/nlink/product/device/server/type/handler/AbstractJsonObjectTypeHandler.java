package com.nti56.nlink.product.device.server.type.handler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 类说明: mysql json类型处理器抽象类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:12:09
 * @since JDK 1.8
 */
public abstract class AbstractJsonObjectTypeHandler<T> extends BaseTypeHandler<T> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter,
                                    JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName)
            throws SQLException {
        String data = rs.getString(columnName);
        return (data == null || "".equals(data)) ? null : JSONObject.parseObject(data, getRawType());
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String data = rs.getString(columnIndex);
        return (data == null || "".equals(data)) ? null : JSONObject.parseObject(data, getRawType());
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {
        String data = cs.getString(columnIndex);
        return (data == null || "".equals(data)) ? null : JSONObject.parseObject(data, getRawType());
    }
}
