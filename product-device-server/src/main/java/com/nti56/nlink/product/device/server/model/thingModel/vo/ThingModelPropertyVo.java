package com.nti56.nlink.product.device.server.model.thingModel.vo;

import com.nti56.nlink.product.device.server.domain.thing.dpo.ThingModelDataTypeDpo;
import lombok.Data;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 08:59:09
 * @since JDK 1.8
 */
@Data
public class ThingModelPropertyVo {

    private Long deviceId;

    //直属设备名称
    private String deviceName;
    
    private Long thingModelId;

    private String thingModelName;

    private String name;
    private String descript;
    
    private Long labelId;
    private String labelName;

    private ThingModelDataTypeDpo dataType;

    private Integer reportType;

    private Boolean bindLabel;

    private Boolean readOnly;
    private Boolean persist;

    private Boolean required;

    private String edgeGatewayName;

    private String channelName;

    private String labelGroupName;

    private Boolean repeat;

    private String belongModel;
}
