//package com.nti56.nlink.product.device.server.mapper;
//
//import com.nti56.nlink.product.device.server.model.neo4j.PersonDTO;
//import org.apache.ibatis.annotations.Param;
//import org.neo4j.ogm.model.Result;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.Pageable;
//import org.springframework.data.neo4j.annotation.Query;
//import org.springframework.data.neo4j.repository.Neo4jRepository;
//
//import java.util.List;
//
///**
// * 类说明：
// *
// * @ClassName MovieRepository
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/11/16 17:49
// * @Version 1.0
// */
//
//public interface PersonRepository extends Neo4jRepository<PersonDTO,String> {
//
//    @Query(value = "MATCH (p:Person {name:{0}}) RETURN p")
//    List<PersonDTO> getActorsThatActInAmoviesFromTitle(String movieTitle );
//
//
//    @Query(value = "MATCH (p:Person)-[:ACTED_IN]->(movie:Movie {title: {0}}) return p",
//            countQuery= "MATCH (p:Person)-[:ACTED_IN]->(movie:Movie {title: {0}}) RETURN count(p)")
//    Page<PersonDTO> getActorsThatActInAmoviesFromTitlePage(@Param("title") String movieTitle, Pageable pageable );
//
//    @Query(value = "MATCH path = (p:Person {name: {0}})-[:ACTED_IN]->(movie:Movie)\n" +
//            "WITH collect(path) AS paths\n" +
//            "CALL apoc.convert.toTree(paths)\n" +
//            "YIELD value\n" +
//            "RETURN value;")
//    Result getPersonActedInTree(String name);
//}
