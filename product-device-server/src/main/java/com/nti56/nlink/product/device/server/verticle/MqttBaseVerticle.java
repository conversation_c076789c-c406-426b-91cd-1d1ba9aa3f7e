package com.nti56.nlink.product.device.server.verticle;

import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.net.PemTrustOptions;
import io.vertx.mqtt.MqttClient;
import io.vertx.mqtt.MqttClientOptions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import java.io.InputStream;

/**
 * 类说明: mqtt断线重连客户端
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-22 10:43:05
 * @since JDK 1.8
 */
@Slf4j
public abstract class MqttBaseVerticle extends AbstractVerticle {

    public final static String shareSubscribeGroup = "g1";
    
    protected MqttClient client;

    private Long timerId;

    protected Boolean connected;

    protected abstract void subscribe();

    protected abstract String getHost();

    protected abstract Integer getPort();

    protected abstract String getUsername();

    protected abstract String getPassword();

    protected abstract Boolean getSsl();

    protected abstract Integer getReconnectGapTime();

    protected abstract void handleConnectStatusChange(Boolean connected);

    @Override
    public void stop(Promise<Void> stopPromise) throws Exception {
        log.info("stop mqtt");
        if(timerId != null){
            vertx.cancelTimer(timerId);
        }
        if(client != null){
            client.disconnect();
        }
        super.stop(stopPromise);
    }

    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        log.info("mqttbase start: {}:{}", this.getHost(), this.getPort());
        
        initClient().onComplete(r -> {
            if(r.succeeded()){
                log.info("mqttbase client init success: {}:{}", this.getHost(), this.getPort());
            }else{
                log.info("mqttbase client init fail");
            }
        });
        
        Integer reconnectGapTime = getReconnectGapTime();
        if(reconnectGapTime == null){
            reconnectGapTime = 3000;
        }
        vertx.setPeriodic(reconnectGapTime, id -> {
            timerId = id;
            if(client == null){
                log.debug("reconnect");
                initClient();
            }else{
                // log.debug("connect status: {}", client.isConnected());
            }
        });
        
        startPromise.complete();
    }

    private Future<MqttClient> initClient(){
        log.info("mqttbase initClient begin");
        connected = false;
        handleConnectStatusChange(connected);
       
        Promise<MqttClient> promise = Promise.promise();
        try {
            MqttClientOptions options = new MqttClientOptions()
                    .setUsername(this.getUsername())
                    .setPassword(this.getPassword())
                    .setMaxMessageSize(1024 * 1024 * 32)
                    .setMaxInflightQueue(1000);
            Boolean ssl = getSsl();
            if(ssl == null){
                ssl = true;
            }
            if(ssl){
                InputStream stream = MqttBaseVerticle.class.getClassLoader().getResourceAsStream("tls/rootCA.crt");
                String str = IOUtils.toString(stream, "utf-8");
                Buffer buffer = Buffer.buffer(str);
                options.setSsl(true)
                    .setPemTrustOptions(new PemTrustOptions()
                            .addCertValue(buffer));
            }
            client = MqttClient.create(vertx, options);
            client.connect(this.getPort(), this.getHost(), s -> {
                if (s.succeeded()) {
                    connected = true;
                    handleConnectStatusChange(connected);

                    this.subscribe();
                    promise.complete(client);
                } else {
                    connected = false;
                    handleConnectStatusChange(connected);
                    log.error("MQTT连接失败：{}, {}, {}", this.getPort(), this.getHost(), s.cause().getMessage());
                    client = null;
                    promise.fail(s.cause());
                }
            });
            log.info("mqttbase after connect");

            // 断线重连
            client.closeHandler(r -> {
                connected = false;
                handleConnectStatusChange(connected);
                log.error("mqtt client close: {}", r);
                client = null;
            });
            client.exceptionHandler(e -> {
                connected = false;
                handleConnectStatusChange(connected);
                log.error("mqtt client error: {}", e);
                client = null;
                promise.fail(e);
            });
            log.info("mqttbase initClient end");
        }catch (Exception e){
            connected = false;
            handleConnectStatusChange(connected);
            promise.fail(e);
            log.error("mqtt连接报错：{}",e.getMessage());
        }
        return promise.future();
    }
}
