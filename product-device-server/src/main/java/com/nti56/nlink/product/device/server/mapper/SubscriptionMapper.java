package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.SubscriptionEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 订阅 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-22 15:17:38
 */
@Mapper
public interface SubscriptionMapper extends CommonMapper<SubscriptionEntity> {
    @Delete("UPDATE subscription SET deleted = 1 WHERE tenant_id = #{tenantId} AND directly_model_id = #{thingModelId}")
    Integer deleteByThingModelId(@Param("tenantId") Long tenantId,
                                 @Param("thingModelId") Long thingModelId);
}
