package com.nti56.nlink.product.device.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.exception.BizException;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.constant.DingDingConstant;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.entity.NotifyChannelEntity;
import com.nti56.nlink.product.device.server.enums.NotifyEnum;
import com.nti56.nlink.product.device.server.enums.error.NotifyErrorEnum;
import com.nti56.nlink.product.device.server.mapper.NotifyChannelMapper;
import com.nti56.nlink.product.device.server.model.channel.dto.CreateNotifyChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.EditNotifyChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.NotifyChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.vo.NotifyChannelVO;
import com.nti56.nlink.product.device.server.service.INotifyChannelService;
import com.nti56.nlink.product.device.server.service.ITemplateService;
import com.nti56.nlink.product.device.server.service.handler.DingChannelThreshold;
import com.nti56.nlink.product.device.server.service.handler.LeakyBucketMessageSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dozer.Mapper;
import org.redisson.api.RSemaphore;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/24 9:08<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class NotifyChannelServiceImpl extends ServiceImpl<NotifyChannelMapper, NotifyChannelEntity> implements INotifyChannelService {

    @Autowired
    private ITemplateService templateService;

    @Autowired
    private NotifyStrategyContext notifyStrategyContext;

    @Autowired
    private Mapper dozerMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private StringRedisTemplate redisTemplate;


    @Override
    @Transactional
    public Result createChannel(CreateNotifyChannelDTO dto, TenantIsolation tenantIsolation) {

        Result result = this.uniqueName( dto.getName(), tenantIsolation);

        if (!result.getSignal()){
            return result;
        }

        NotifyChannelEntity notifyChannelEntity = dozerMapper.map(dto, NotifyChannelEntity.class);
        Result validationChannelResult = notifyStrategyContext.validationChannel(notifyChannelEntity);
        if (!validationChannelResult.getSignal()){
            return validationChannelResult;
        }
        if (this.save(notifyChannelEntity)){
            if(NotifyEnum.DING_DING_ROBOT.getValue()== notifyChannelEntity.getNotifyType()){
                JSONObject channelParamsJson = JSON.parseObject(notifyChannelEntity.getParams());
                Integer gap = channelParamsJson.getInteger(DingDingConstant.SEND_GAP_SECOND);
                if(gap != null){
                    LeakyBucketMessageSender sender = new LeakyBucketMessageSender(gap, TimeUnit.SECONDS, notifyChannelEntity.getId(), channelParamsJson);
                    DingChannelThreshold.registerSender(notifyChannelEntity.getId(), sender);
                    RSemaphore semaphore = redissonClient.getSemaphore(String.format(RedisConstant.DING_CHANNEL_SEMAPHORE, notifyChannelEntity.getId()));
                    semaphore.trySetPermits(1);
                }
            }
            return Result.ok();
        }

        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }

    @Override
    @Transactional
    public Result editChannel(EditNotifyChannelDTO dto, TenantIsolation tenantIsolation) {


        Result<NotifyChannelEntity> oldChannel = this.getByIdAndTenantIsolation(dto.getId(), tenantIsolation);
        if (oldChannel.getResult() == null){
            throw new BizException("该租户下不存在此渠道");
        }
        JSONObject channelParamsJson = JSON.parseObject(oldChannel.getResult().getParams());
        Integer oldGap = channelParamsJson.getInteger(DingDingConstant.SEND_GAP_SECOND);
        if(StringUtils.isNotBlank(dto.getName())){

            Result result = this.uniqueName(dto.getId(), dto.getName(), tenantIsolation);

            if (!result.getSignal()){
                return result;
            }
        }

        NotifyChannelEntity notifyChannelEntity = dozerMapper.map(dto, NotifyChannelEntity.class);
        Result validationChannelResult = notifyStrategyContext.validationChannel(notifyChannelEntity);
        if (!validationChannelResult.getSignal()){
            return validationChannelResult;
        }

        if (this.updateById(notifyChannelEntity)){
            JSONObject updateParam = JSON.parseObject(notifyChannelEntity.getParams());
            Integer gap = updateParam.getInteger(DingDingConstant.SEND_GAP_SECOND);
            if(NotifyEnum.DING_DING_ROBOT.getValue()== notifyChannelEntity.getNotifyType()&&(Objects.isNull(oldGap) || !gap.equals(oldGap))){
                LeakyBucketMessageSender existSender = DingChannelThreshold.getSender(notifyChannelEntity.getId());
                if(!Objects.isNull(existSender)){
                    existSender.stopProcessing();
                }
                LeakyBucketMessageSender sender = new LeakyBucketMessageSender(gap, TimeUnit.SECONDS, notifyChannelEntity.getId(), channelParamsJson);
                DingChannelThreshold.registerSender(notifyChannelEntity.getId(), sender);
                RSemaphore semaphore = redissonClient.getSemaphore(String.format(RedisConstant.DING_CHANNEL_SEMAPHORE, notifyChannelEntity.getId()));
                semaphore.addPermits(1);
            }
            return Result.ok();
        }

        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }



    @Override
    @Transactional
    public Result deleteChannel(Long id, TenantIsolation tenantIsolation) {
        Result<NotifyChannelEntity> oldChannel = this.getByIdAndTenantIsolation(id, tenantIsolation);
        if (oldChannel.getResult() == null){
            throw new BizException("该租户下不存在此渠道");
        }

        if(templateService.countByChannelId(id,tenantIsolation).getResult() > 0){
            return Result.error(NotifyErrorEnum.CHANNEL_USED.getCode(),NotifyErrorEnum.CHANNEL_USED.getMessage());
        }

        if (this.removeById(id)){
            LeakyBucketMessageSender sender = DingChannelThreshold.getSender(id);
            if(!Objects.isNull(sender)){
                sender.stopProcessing();
            }
            DingChannelThreshold.removeSender(id);
            redisTemplate.delete(String.format(RedisConstant.DING_CHANNEL_SEMAPHORE, id));
            return Result.ok();
        }

        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    @Override
    public Result<NotifyChannelEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<NotifyChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(NotifyChannelEntity::getId,id)
                .eq(NotifyChannelEntity::getTenantId,tenantIsolation.getTenantId());
        return Result.ok(this.getOne(lqw));
    }

    @Override
    public Result<List<NotifyChannelVO>> listChannel(NotifyChannelDTO dto, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<NotifyChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(NotifyChannelEntity::getTenantId,tenantIsolation.getTenantId())
                .like(StringUtils.isNotBlank(dto.getName()), NotifyChannelEntity::getName,dto.getName())
                .eq(dto.getNotifyType() != null, NotifyChannelEntity::getNotifyType,dto.getNotifyType())
                .orderByDesc(NotifyChannelEntity::getCreateTime);
        List<NotifyChannelEntity> list = this.list(lqw);
        List<NotifyChannelVO> channelList = BeanUtilsIntensifier.copyBeanList(list, NotifyChannelVO.class);
        for (NotifyChannelVO notifyChannelVO : channelList) {
            notifyChannelVO.setTemplateFiled(NotifyEnum.getTemplateFieldByValue(notifyChannelVO.getNotifyType()));
        }
        return Result.ok(channelList);
    }

    @Override
    public Result<Page<NotifyChannelEntity>> pageChannel(PageParam pageParam, NotifyChannelDTO dto, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<NotifyChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(NotifyChannelEntity::getTenantId,tenantIsolation.getTenantId())
                .like(StringUtils.isNotBlank(dto.getName()), NotifyChannelEntity::getName,dto.getName())
                .eq(dto.getNotifyType() != null, NotifyChannelEntity::getNotifyType,dto.getNotifyType())
                .orderByDesc(NotifyChannelEntity::getCreateTime);
        return Result.ok(this.page( pageParam.toPage(NotifyChannelEntity.class),lqw));
    }

    private Result uniqueName(String name , TenantIsolation tenantIsolation){
        return this.uniqueName(null,name,tenantIsolation);
    }

    /**
     * 判断名称是否唯一
     * @param id
     * @param name
     * @param tenantIsolation
     * @return
     */
    private Result uniqueName(Long id ,String name , TenantIsolation tenantIsolation){
        LambdaQueryWrapper<NotifyChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null , NotifyChannelEntity::getId, id)
                .eq(NotifyChannelEntity::getName,name)
                .eq(NotifyChannelEntity::getTenantId,tenantIsolation.getTenantId());

        if (this.count(lqw) > 0){
            return Result.error(NotifyErrorEnum.CHANNEL_UNIQUE_NAME.getCode(),NotifyErrorEnum.CHANNEL_UNIQUE_NAME.getMessage());
        }

        return Result.ok();
    }
}
