package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import com.nti56.nlink.product.device.server.entity.AbsolutePathLabelEntity;
import com.nti56.nlink.product.device.server.entity.LabelEntity;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.model.label.dto.EditLabelAliasDTO;
import com.nti56.nlink.product.device.server.model.label.dto.LabelDTO;
import com.nti56.nlink.product.device.server.model.label.dto.PageLabelDTO;
import com.nti56.nlink.product.device.server.model.label.dto.ProofreadLabelDTO;
import com.nti56.nlink.product.device.server.model.label.vo.LabelVO;
import com.nti56.nlink.product.device.server.model.label.vo.PageLabelVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 类说明: 标签mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:14
 * @since JDK 1.8
 */
public interface AbsolutePathLabelMapper extends CommonMapper<AbsolutePathLabelEntity> {
    String querySql = "SELECT l.* ,g.name labelGroupName,c.name channelName,c.id channelId,e.name edgeGatewayName,e.id edgeGatewayId FROM label l LEFT JOIN label_group g ON l.label_group_id = g.id LEFT JOIN channel c ON g.channel_id = c.id LEFT JOIN edge_gateway e ON c.edge_gateway_id = e.id WHERE e.DELETED = 0 AND c.DELETED = 0 AND g.DELETED = 0 AND l.DELETED = 0 ";
    String wrapperSql = "SELECT * from ( " + querySql + " ) AS q ${ew.customSqlSegment}";
    /**
     * 分页查询
     */
    @Select(wrapperSql)
    Page<AbsolutePathLabelEntity> page(Page page, @Param("ew") Wrapper queryWrapper);

    /**
     * 普通查询
     */
    @Select(wrapperSql)
    List<AbsolutePathLabelEntity> list(@Param("ew") Wrapper queryWrapper);

    /**
     * 单独查询
     */
    @Select(wrapperSql)
    AbsolutePathLabelEntity one(@Param("ew") Wrapper queryWrapper);


}
