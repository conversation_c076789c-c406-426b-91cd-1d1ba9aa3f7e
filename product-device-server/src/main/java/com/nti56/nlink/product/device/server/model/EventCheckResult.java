package com.nti56.nlink.product.device.server.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 事件校验结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-06-24 09:45:29
 * @since JDK 1.8
 */
@Data
@Builder
public class EventCheckResult {
    private Boolean eventCheck;
    private String eventCheckMsg;

    private List<ConnectResult> conditionValue;
    private String conditionValueMsg;

    private List<ConnectResult> reportValue;
    private String reportValueMsg;
}
