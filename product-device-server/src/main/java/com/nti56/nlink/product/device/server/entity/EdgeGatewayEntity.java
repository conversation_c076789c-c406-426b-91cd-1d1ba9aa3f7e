package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.nti56.nlink.product.device.client.model.dto.json.EdgeGatewayRuntimeInfoField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 边缘网关表
 * 
 * author: sushangqun
 * create time: 2022-03-15 09:02:13
 */ 
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("edge_gateway")
@Schema(description = "网关")
public class EdgeGatewayEntity {

    /**
     * id
     */ 
    private Long id;
    /**
     * 边缘网关名称
     */ 
    private String name;
    /**
     * 描述
     */
    @Schema(description = "描述")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String descript;
    /**
     * 心跳uuid
     */
    @Schema(description = "心跳uuid")
    private String heartbeatUuid;
    /**
     * host
     */
    @Schema(description = "主机")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String host;
    /**
     * port
     */
    @Schema(description = "端口")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer port;

    /**
     * IMEI
     */
    @Schema(description = "IMEI")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String imei;

    /**
     * 流量卡号
     */
    @Schema(description = "流量卡号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String trafficCard;

    @Schema(description = "运营商 1-移动，2-电信，3-联通")
    private Integer operators;

    @Schema(description = "是否访问公共MQTT接口")
    @TableField(insertStrategy = FieldStrategy.NOT_NULL)
    private Boolean visitPublicMqtt;

    @Schema(description = "外部mqtt主机")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String publicMqttIp;

    @Schema(description = "外部mqtt端口")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer publicMqttPort;

    @Schema(description = "网关类型 虚拟网关/网关设备 ")
    //达梦兼容需要添加双引号，mysql必须去掉双引号
    //@TableField("\"type\"")
    private Integer type;

    @Schema(description = "同步时间")
    private LocalDateTime syncTime;

    @Schema(description = "设备同步状态，1-已最新，0-有更新")
    private Integer syncStatus;

    /**
     * 网关运行时信息
     */
    @Schema(description = "网关运行时信息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private EdgeGatewayRuntimeInfoField runtimeInfo;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

    /**
     * 逻辑删除，1-删除
     */
    @Schema(description = "逻辑删除，1-删除")
    @TableLogic
    private Boolean deleted;

    @Schema(description = "租户ID")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @Schema(description = "工程ID")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @Schema(description = "模块ID")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @Schema(description = "空间ID")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 目标版本号
     */
    @Schema(description = "目标版本号")
    private String targetVersion;
    
    /**
     * 升级开始时间
     */
    @Schema(description = "升级开始时间")
    private LocalDateTime upgradeBeginTime;
    
    /**
     * 升级状态
     */
    @Schema(description = "升级状态：0-不处理，1，下载安装包 ，2，下载中，3-立即升级，4-升级中")
    private Integer upgradeStatus;

    /**
     * 内存监控百分比
     */
    @Schema(description = "网关内存监控百分比")
    private Integer memoryMonitor;

    /**
     * 硬件空间监控百分比
     */
    @Schema(description = "网关空间监控百分比")
    private Integer spaceMonitor;

}
