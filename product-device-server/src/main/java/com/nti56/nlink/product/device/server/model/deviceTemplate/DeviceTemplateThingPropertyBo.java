package com.nti56.nlink.product.device.server.model.deviceTemplate;

import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName DeviceTemplateThingPropertyBo
 * @date 2023-12-27 10:33:58
 * @Version 1.0
 */
@Data
@Builder
@Schema(description = "设备云管获取设备模板所有物属性信息")
@AllArgsConstructor
@NoArgsConstructor
public class DeviceTemplateThingPropertyBo {

    @Schema(description = "模板id")
    private Long templateId;

    @Schema(description = "模板名称")
    private String templateName;

    @Schema(description = "通道协议名称")
    private String channelName;

    @Schema(description = "模板下所有属性信息")
    private List<PropertyElm> properties;
}
