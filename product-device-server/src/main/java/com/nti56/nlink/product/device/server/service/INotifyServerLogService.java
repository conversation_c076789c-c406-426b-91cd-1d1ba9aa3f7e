package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.product.device.server.entity.NotifyServerLogEntity;
import com.nti56.nlink.product.device.server.enums.NotifyServerLogEnum;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/18 9:23<br/>
 * @since JDK 1.8
 */
public interface INotifyServerLogService extends IService<NotifyServerLogEntity> {

    void createLog(Long id,NotifyServerLogEnum logEnum, String requestParams,String failResponse,String errorDescription,  TenantIsolation tenantIsolation, Long aggregationId, Long subscriptionId);

    void createLog(Long id,NotifyServerLogEnum logEnum, String requestParams,String failResponse,String errorDescription,  TenantIsolation tenantIsolation, Long aggregationId);

    void createLog(Long id,NotifyServerLogEnum logEnum, String requestParams,String failResponse,String errorDescription,  TenantIsolation tenantIsolation);

    void createLog(NotifyServerLogEnum logEnum, String requestParams,  TenantIsolation tenantIsolation);

    void createLog(Long id,NotifyServerLogEnum logEnum, String requestParams,  TenantIsolation tenantIsolation);

    void createRequestFailLog(NotifyServerLogEnum logEnum, String requestParams, String failResponse,  TenantIsolation tenantIsolation);

    void createErrorLog(NotifyServerLogEnum logEnum, String requestParams,String errorDescription,  TenantIsolation tenantIsolation);

    void createRequestFailLog(Long id,NotifyServerLogEnum logEnum, String requestParams, String failResponse,  TenantIsolation tenantIsolation);

    void createErrorLog(Long id,NotifyServerLogEnum logEnum, String requestParams,String errorDescription,  TenantIsolation tenantIsolation);

    void createLog(NotifyServerLogEnum logEnum, String requestParams, TenantIsolation tenantIsolation,Long subscriptionId);

    void createRequestFailLog(NotifyServerLogEnum logEnum, String requestParams, String failResponse, TenantIsolation tenantIsolation,Long subscriptionId);

    void createErrorLog(NotifyServerLogEnum logEnum, String requestParams, String errorDescription, TenantIsolation tenantIsolation,Long subscriptionId);

    void createLog(Long id, NotifyServerLogEnum logEnum, String requestParams, TenantIsolation tenantIsolation,Long subscriptionId);

    void createRequestFailLog(Long id, NotifyServerLogEnum logEnum, String requestParams, String failResponse, TenantIsolation tenantIsolation,Long subscriptionId);

    void createErrorLog(Long id, NotifyServerLogEnum logEnum, String requestParams, String errorDescription, TenantIsolation tenantIsolation,Long subscriptionId);

}
