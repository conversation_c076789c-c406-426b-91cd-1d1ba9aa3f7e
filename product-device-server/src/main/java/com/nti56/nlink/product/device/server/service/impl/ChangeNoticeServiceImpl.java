package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ChangeSubjectEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ChangeTypeEnum;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.enums.RedirectRefEnum;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.ChangeNoticeMapper;
import com.nti56.nlink.product.device.server.model.changeNotice.ChangeNoticeMsgVo;
import com.nti56.nlink.product.device.server.model.changeNotice.dto.CreateChangeNoticeDTO;
import com.nti56.nlink.product.device.server.model.changeNotice.dto.EditChangeNoticeDTO;
import com.nti56.nlink.product.device.server.model.changeNotice.dto.QueryChangeNoticeDTO;
import com.nti56.nlink.product.device.server.model.changeNotice.vo.ChangeNoticeVO;
import com.nti56.nlink.product.device.server.model.redirect.RedirectReferenceDto;
import com.nti56.nlink.product.device.server.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import java.util.ArrayList;
import java.util.List;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-02-18 16:11:55
 * @since JDK 1.8
 */
@Service
@Slf4j
public class ChangeNoticeServiceImpl extends BaseServiceImpl<ChangeNoticeMapper, ChangeNoticeEntity> implements IChangeNoticeService {

    @Autowired
    private ChangeNoticeMapper changeNoticeMapper;

    @Autowired @Lazy
    private ISubscriptionService subscriptionService;

    @Autowired
    private IInstanceRedirectService instanceRedirectService;

    @Override
    public Result<List<ChangeNoticeVO>> listChangeNotice(QueryChangeNoticeDTO queryChangeNoticeDTO, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<ChangeNoticeEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ChangeNoticeEntity::getDeleted,0);
        lqw.eq(ChangeNoticeEntity::getTenantId,tenantIsolation.getTenantId());
        if(!StringUtils.isEmpty(queryChangeNoticeDTO.getName())){
            lqw.like(ChangeNoticeEntity::getName,queryChangeNoticeDTO.getName());
        }
        if(ObjectUtil.isNotNull(queryChangeNoticeDTO.getChangeSubject())){
            lqw.eq(ChangeNoticeEntity::getChangeSubject,queryChangeNoticeDTO.getChangeSubject());
        }
        List<ChangeNoticeEntity> changeNoticeEntityList = changeNoticeMapper.selectList(lqw);
        List<ChangeNoticeVO> changeNoticeVOList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(changeNoticeEntityList)){
            for(ChangeNoticeEntity changeNoticeEntity : changeNoticeEntityList){
                ChangeNoticeVO changeNoticeVO = new ChangeNoticeVO();
                BeanUtil.copyProperties(changeNoticeEntity,changeNoticeVO);
                changeNoticeVOList.add(changeNoticeVO);
            }
        }
        return Result.ok(changeNoticeVOList);
    }

    @Override
    public Result createChangeNotice(CreateChangeNoticeDTO createChangeNoticeDTO, TenantIsolation tenantIsolation) {
        Result<Void> result = uniqueName(null,createChangeNoticeDTO.getName(),tenantIsolation);
        if (!result.getSignal()) {
            throw new BizException(result.getMessage());
        }
        ChangeNoticeEntity changeNoticeEntity = new ChangeNoticeEntity();
        BeanUtil.copyProperties(createChangeNoticeDTO,changeNoticeEntity);
        changeNoticeMapper.insert(changeNoticeEntity);
        RedirectReferenceDto referenceDto = RedirectReferenceDto.builder()
                .redirectId(createChangeNoticeDTO.getRedirectId())
                .refName(changeNoticeEntity.getName())
                .refId(changeNoticeEntity.getId())
                //新增
                .updateType(1)
                .type(RedirectRefEnum.CHANGE_NOTICE.getType())
                .typeName(RedirectRefEnum.CHANGE_NOTICE.getTypeName())
                .build();
        instanceRedirectService.updateReference(tenantIsolation,referenceDto);
        return Result.ok();
    }

    @Override
    public Result deleteChangeNotice(Long id, TenantIsolation tenantIsolation) {
        Result<ChangeNoticeEntity> oldRole = this.getByIdAndTenantIsolation(id, tenantIsolation);
        if (oldRole.getResult() == null){
            throw new BizException("该租户下不存在此渠道");
        }
        changeNoticeMapper.deleteById(id);
        RedirectReferenceDto referenceDto = RedirectReferenceDto.builder()
                .redirectId(oldRole.getResult().getRedirectId())
                .refId(oldRole.getResult().getId())
                //删除
                .updateType(0)
                .build();
        instanceRedirectService.updateReference(tenantIsolation,referenceDto);
        return Result.ok();
    }

    @Override
    public Result editChangeNotice(EditChangeNoticeDTO editChangeNoticeDTO, TenantIsolation tenantIsolation) {
        Result<Void> result = uniqueName(editChangeNoticeDTO.getId(),editChangeNoticeDTO.getName(),tenantIsolation);
        if (!result.getSignal()) {
            throw new BizException(result.getMessage());
        }
        ChangeNoticeEntity existChangeNotice = changeNoticeMapper.selectById(editChangeNoticeDTO.getId());
        RedirectReferenceDto referenceDto = RedirectReferenceDto.builder()
                .redirectId(existChangeNotice.getRedirectId())
                .refId(existChangeNotice.getId())
                //删除
                .updateType(0)
                .build();
        instanceRedirectService.updateReference(tenantIsolation,referenceDto);
        ChangeNoticeEntity changeNoticeEntity = new ChangeNoticeEntity();
        BeanUtil.copyProperties(editChangeNoticeDTO,changeNoticeEntity);
        changeNoticeMapper.updateById(changeNoticeEntity);
        RedirectReferenceDto newReference = RedirectReferenceDto.builder()
                .redirectId(editChangeNoticeDTO.getRedirectId())
                .refId(existChangeNotice.getId())
                .refName(changeNoticeEntity.getName())
                .type(RedirectRefEnum.CHANGE_NOTICE.getType())
                .typeName(RedirectRefEnum.CHANGE_NOTICE.getTypeName())
                //删除
                .updateType(1)
                .build();
        instanceRedirectService.updateReference(tenantIsolation,newReference);
        return Result.ok();
    }

    /**
     * 判断名称是否唯一
     *
     * @param id
     * @param name
     * @param tenantIsolation
     * @return
     */
    private Result<Void> uniqueName(Long id, String name, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<ChangeNoticeEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, ChangeNoticeEntity::getId, id)
                .eq(ChangeNoticeEntity::getName, name)
                .eq(ChangeNoticeEntity::getTenantId, tenantIsolation.getTenantId());

        if (changeNoticeMapper.selectCount(lqw) > 0) {
            return Result.error("已经存在该名称的,名称：" + name);
        }

        return Result.ok();
    }

    private Result<ChangeNoticeEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<ChangeNoticeEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ChangeNoticeEntity::getId,id)
                .eq(ChangeNoticeEntity::getTenantId,tenantIsolation.getTenantId());
        ChangeNoticeEntity changeNoticeEntity = changeNoticeMapper.selectOne(lqw);
        return Result.ok(changeNoticeEntity);
    }

    @Override
    public Result<List<ChangeNoticeEntity>> getChangeNoticeList() {
        LambdaQueryWrapper<ChangeNoticeEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ChangeNoticeEntity::getDeleted,0);
        List<ChangeNoticeEntity> changeNoticeEntityList = changeNoticeMapper.selectList(lqw);
        return Result.ok(changeNoticeEntityList);
    }

    @Override
    @Async
    public <T> void changeNotice(Long tenantId, T noticeMsg, ChangeTypeEnum changeType, ChangeSubjectEnum changeSubject) {
        List<ChangeNoticeEntity> list = this.lambdaQuery().eq(ChangeNoticeEntity::getChangeSubject, changeSubject.getValue())
                .eq(ChangeNoticeEntity::getChangeType, changeType.getValue())
                .eq(ChangeNoticeEntity::getTenantId, tenantId)
                .list();
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(changeNoticeEntity -> {
                ChangeNoticeMsgVo build = ChangeNoticeMsgVo.builder()
                        .changeSubject(changeSubject.getValue())
                        .changeType(changeType.getValue())
                        .msg(noticeMsg)
                        .name(changeNoticeEntity.getName())
                        .build();
                JSONObject object = new JSONObject();
                object.put("payload",build);
                subscriptionService.execRedirectWithPayload(tenantId,changeNoticeEntity.getRedirectId(),object.toJSONString());
            });
        }
    }
}
