package com.nti56.nlink.product.device.server.model.connector.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @since JDK 1.8
 */

@Data
@Schema(description = "创建连接器")
public class CreateGatewayConnectorDTO {
    
    /**
     * 模式类型 0-接收;1-发送
     */
    @Schema(description = "模式类型 0-接收;1-发送")
    @NotNull(message = "模式类型不能为空")
    private Integer modeType;
    
    /**
     * 连接器类型 0-MQTT;1-HTTP
     */
    @Schema(description = "连接器类型 0-MQTT;1-HTTP")
    @NotNull(message = "连接器类型不能为空")
    private Integer connectorType;
    
    /**
     * 连接器状态 0-关闭;1-启用
     */
    @Schema(description = "连接器状态 0-停用;1-启用")
    private Integer status;
    
    /**
     * 连接器信息明细
     */
    @Schema(description = "连接器信息明细")
    @NotBlank(message = "连接器信息明细不能为空")
    private String connectorInfo;

    /**
     * 网关ID
     */
    @Schema(description = "网关ID")
    private Long edgeGatewayId;

}
