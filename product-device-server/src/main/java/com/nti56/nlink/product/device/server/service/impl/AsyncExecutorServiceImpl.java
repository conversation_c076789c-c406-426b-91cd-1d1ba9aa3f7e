package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.*;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.entity.ChangeNoticeEntity;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import com.nti56.nlink.product.device.server.entity.LabelBindRelationEntity;
import com.nti56.nlink.product.device.server.loader.ConnectorInstanceLoader;
import com.nti56.nlink.product.device.server.model.changeNotice.SendMessage;
import com.nti56.nlink.product.device.server.model.channel.vo.ChannelVO;
import com.nti56.nlink.product.device.server.service.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-02-20 18:11:55
 * @since JDK 1.8
 */
@Service
@Slf4j
public class AsyncExecutorServiceImpl implements IAsyncExecutorService {

    @Autowired @Lazy
    private IDeviceService deviceService;
    
    @Autowired @Lazy
    private StringRedisTemplate stringRedisTemplate;
    
    @Autowired @Lazy
    private RedisTemplate redisTemplate;
    
    @Autowired
    private IEdgeGatewayService edgeGatewayService;
    
    @Autowired
    private ISubscriptionService subscriptionService;

    @Autowired
    private ConnectorInstanceLoader connectorInstanceLoader;

    @Async("edgeGatewayAsyncExecutor")
    public void edgeGatewayStatusProcess(EdgeGatewayEntity edgeGatewayEntity, Map<Long, List<ChangeNoticeEntity>> changeNoticeEntityMap,Long connectorId) {
        List<ChangeNoticeEntity> changeNoticeEntityList = changeNoticeEntityMap.get(edgeGatewayEntity.getTenantId());
        if(CollectionUtil.isEmpty(changeNoticeEntityList)){
            changeNoticeEntityList = new ArrayList<>();
        }
        List<ChangeNoticeEntity> gatewayList = changeNoticeEntityList.stream().filter(f-> ChangeSubjectEnum.EDGE_GATEWAY.getValue().equals(f.getChangeSubject())  && ChangeTypeEnum.STATUS.getValue().equals(f.getChangeType())).collect(Collectors.toList());
        //网关
        doEdgeGatewayStatusProcess(edgeGatewayEntity,gatewayList,connectorId);
    }
    
    @Async("deviceCloudAsyncExecutor")
    public void deviceStatusProcess(DeviceEntity deviceEntity, List<ChangeNoticeEntity> noticeEntityList, Boolean gwOnline, Map<String,Boolean> channelStatusMap, Map<Object,Object> deviceStatusMap, Map<Object, Object> deviceOfflineTimesMap, String key, Map<Long, List<LabelBindRelationEntity>> labelBindRelationEntityMap, Integer type) {
        //设备
        deviceService.deviceStatusProcess(deviceEntity,noticeEntityList,gwOnline,channelStatusMap,deviceStatusMap,deviceOfflineTimesMap,key,labelBindRelationEntityMap,type);
    }
    
    @Async("channelAsyncExecutor")
    public void channelStatusProcess(ChannelVO channelVO, String key, Map<String,Boolean> reportChannelStatusMap,Map<Object,Object> channelStatusMap) {
        String channelName = channelVO.getEdgeGatewayId() + "_" + channelVO.getName();
        Boolean reportChannelStatus = reportChannelStatusMap.get(channelName);
        Integer reportStatus = reportChannelStatus != null && reportChannelStatus ? 1: 0;
        Integer channelStatus = channelStatusMap.get(String.valueOf(channelVO.getId())) != null ? Integer.parseInt(String.valueOf(channelStatusMap.get(String.valueOf(channelVO.getId())))) : 0;
        if(!String.valueOf(reportStatus).equals(String.valueOf(channelStatus))){
            switch (reportStatus){
                case 0:{
                    String redisKey = String.format(RedisConstant.SUBSCRIPTION_CHANNEL_REGISTRY,channelVO.getId(),StatusEventEnum.DISCONNECT.getName());
                    Map<Object,Object> subscriptionMap = stringRedisTemplate.opsForHash().entries(redisKey);
                    if(CollectionUtil.isNotEmpty(subscriptionMap)){
                        for (Map.Entry<Object, Object> entry : subscriptionMap.entrySet()) {
                            Subscription subscription = JSONObject.parseObject(String.valueOf(entry.getValue()),Subscription.class);
                            Map<String,Object> actual = new HashMap<>();
                            actual.put("id",channelVO.getId());
                            actual.put("type",1);
                            actual.put("name",channelVO.getName());
                            actual.put("status",reportStatus);
                            subscription.edgeGatewaySubscriptionPostProcess(SubscriptionFromEnum.CHANNEL_STATUS, channelVO.getId(),channelVO.getTenantId(),actual,new Date().getTime(), subscription.getOutType());
                        }
                        log.info("通道离线订阅");
                    }
                    break;
                }
                case 1: {
                    String redisKey = String.format(RedisConstant.SUBSCRIPTION_CHANNEL_REGISTRY,channelVO.getId(),StatusEventEnum.CONNECT.getName());
                    Map<Object,Object> subscriptionMap = stringRedisTemplate.opsForHash().entries(redisKey);
                    if(CollectionUtil.isNotEmpty(subscriptionMap)){
                        for (Map.Entry<Object, Object> entry : subscriptionMap.entrySet()) {
                            Subscription subscription = JSONObject.parseObject(String.valueOf(entry.getValue()),Subscription.class);
                            Map<String,Object> actual = new HashMap<>();
                            actual.put("id",channelVO.getId());
                            actual.put("type",1);
                            actual.put("name",channelVO.getName());
                            actual.put("status",reportStatus);
                            subscription.edgeGatewaySubscriptionPostProcess(SubscriptionFromEnum.CHANNEL_STATUS, channelVO.getId(),channelVO.getTenantId(),actual,new Date().getTime(), subscription.getOutType());
                        }
                        log.info("通道在线订阅");
                    }
                    break;
                }
            }
            stringRedisTemplate.opsForHash().put(key, String.valueOf(channelVO.getId()), String.valueOf(reportStatus));
        }
    }
    
    public Result<Void> doEdgeGatewayStatusProcess(EdgeGatewayEntity edgeGatewayEntity,List<ChangeNoticeEntity> gatewayList,Long connectorId){
        String key = RedisConstant.EDGE_GATEWAY_STATUS + edgeGatewayEntity.getTenantId();
        String status = "";
        String gatewayStatus = "";
        if(edgeGatewayEntity.getType() == EdgeGatewayTypeEnum.CONNECTOR_GATEWAY.getValue()){
            Result<Boolean> connectStatusResult = connectorInstanceLoader.getConnectStatus(connectorId);
            if(connectStatusResult.getSignal()){
                Boolean connectStatus = connectStatusResult.getResult();
                gatewayStatus = connectStatus ? "1" : "0";
            }else{
                gatewayStatus = "0";
            }
        }else {
            Result<Boolean> online = edgeGatewayService.edgeGatewayOnline(edgeGatewayEntity.getTenantId(),edgeGatewayEntity.getId());
            gatewayStatus = online.getResult() ? "1" : "0";
        }

        if(stringRedisTemplate.opsForHash().hasKey(key,String.valueOf(edgeGatewayEntity.getId()))){
            status = String.valueOf(stringRedisTemplate.opsForHash().get(key,String.valueOf(edgeGatewayEntity.getId())));
        }else {
            stringRedisTemplate.opsForHash().put(key,String.valueOf(edgeGatewayEntity.getId()),gatewayStatus);
        }
       if(!gatewayStatus.equals(status)){
            stringRedisTemplate.opsForHash().put(key,String.valueOf(edgeGatewayEntity.getId()),gatewayStatus);
            //推网关状态
            Map<Long,String> needSendGatewayStatusMap = new HashMap<>();
            needSendGatewayStatusMap.put(edgeGatewayEntity.getId(),gatewayStatus);
            switch (gatewayStatus){
                case "0":{
                    String redisKey = String.format(RedisConstant.SUBSCRIPTION_EDGE_GATEWAY_REGISTRY,edgeGatewayEntity.getId(),StatusEventEnum.OFFLINE.getName());
                    Map<Object,Object> subscriptionMap = stringRedisTemplate.opsForHash().entries(redisKey);
                    if(CollectionUtil.isNotEmpty(subscriptionMap)){
                        for (Map.Entry<Object, Object> entry : subscriptionMap.entrySet()) {
                            Subscription subscription = JSONObject.parseObject(String.valueOf(entry.getValue()),Subscription.class);
                            Map<String,Object> actual = new HashMap<>();
                            actual.put("id",edgeGatewayEntity.getId());
                            actual.put("type",0);
                            actual.put("name",edgeGatewayEntity.getName());
                            actual.put("status",gatewayStatus);
                            subscription.edgeGatewaySubscriptionPostProcess(SubscriptionFromEnum.EDGE_GATEWAY_STATUS, edgeGatewayEntity.getId(),edgeGatewayEntity.getTenantId(),actual,new Date().getTime(), subscription.getOutType());
                        }
                        log.info("网关离线订阅");
                    }
                    break;
                }
                case "1":{
                    String redisKey = String.format(RedisConstant.SUBSCRIPTION_EDGE_GATEWAY_REGISTRY,edgeGatewayEntity.getId(),StatusEventEnum.ONLINE.getName());
                    Map<Object,Object> subscriptionMap = stringRedisTemplate.opsForHash().entries(redisKey);
                    if(CollectionUtil.isNotEmpty(subscriptionMap)){
                        for (Map.Entry<Object, Object> entry : subscriptionMap.entrySet()) {
                            Subscription subscription = JSONObject.parseObject(String.valueOf(entry.getValue()),Subscription.class);
                            Map<String,Object> actual = new HashMap<>();
                            actual.put("id",edgeGatewayEntity.getId());
                            actual.put("type",0);
                            actual.put("name",edgeGatewayEntity.getName());
                            actual.put("status",gatewayStatus);
                            subscription.edgeGatewaySubscriptionPostProcess(SubscriptionFromEnum.EDGE_GATEWAY_STATUS, edgeGatewayEntity.getId(),edgeGatewayEntity.getTenantId(),actual,new Date().getTime(), subscription.getOutType());
                        }
                        log.info("网关在线订阅");
                    }
                    break;
                }
            }
            //推送
            if(!StringUtils.isEmpty(needSendGatewayStatusMap) && CollectionUtil.isNotEmpty(gatewayList)){
                for (ChangeNoticeEntity changeNoticeEntity:gatewayList){
                    SendMessage sendMessage = new SendMessage();
                    sendMessage.setPayload(needSendGatewayStatusMap);
                    log.info("gateway status send and redirectId {},need send str {}",changeNoticeEntity.getRedirectId(), JSONObject.toJSONString(sendMessage));
                    subscriptionService.execRedirectWithPayload(edgeGatewayEntity.getTenantId(),changeNoticeEntity.getRedirectId(),JSONObject.toJSONString(sendMessage));
                }
            }
        }
        return Result.ok();
    }
}
