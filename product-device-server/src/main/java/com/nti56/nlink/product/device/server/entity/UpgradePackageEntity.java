package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.influxdb.annotations.Column;
import com.nti56.nlink.product.device.client.model.dto.json.UpgradePackageRangeField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 升级包配置 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2023-3-15 16:28:31
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "升级包配置")
@TableName("upgrade_package")
public class UpgradePackageEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 升级包名称
     */
    @Schema(description = "升级包名称")
    private String upgradePackageName;

    /**
     * 升级包版本
     */
    @Schema(description = "升级包版本")
    private String upgradeVersion;

    /**
     * 升级包类型(0 普通,1 紧急)
     */
    @Schema(description = "升级包类型(0 普通,1 紧急)")
    private Integer upgradeType;

    /**
     * 是否期望版本(0 否,1 是)
     */
    @Schema(description = "是否期望版本(0 否,1 是)")
    private Integer isExpectVersion;
    
    /**
     * 下载链接
     */
    @Schema(description = "下载链接")
    private String downloadLink;
    
    /**
     * 升级包描述
     */
    @Schema(description = "升级包描述")
    private String descript;
    
    /**
     * 升级包范围
     */
    @Schema(description = "升级包范围")
    private UpgradePackageRangeField upgradeRange;
    
    @Schema(description = "md5校对")
    @Column(name = "md5_proofread")
    private String md5Proofread;

    /**
     * 工程id
     */
    @Schema(description = "工程id")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块id
     */
    @Schema(description = "模块id")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间id
     */
    @Schema(description = "空间id")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    @TableField(fill = FieldFill.INSERT)
    private String creator;


    /**
     * 修改人名称
     */
    @Schema(description = "修改人名称")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改人id
     */
    @Schema(description = "修改人id")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除，1-删除
     */
    @Schema(description = "逻辑删除，1-删除")
    @TableLogic
    private Boolean deleted;


    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

}
