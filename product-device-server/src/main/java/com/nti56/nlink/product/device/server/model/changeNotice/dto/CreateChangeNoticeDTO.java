package com.nti56.nlink.product.device.server.model.changeNotice.dto;

import com.nti56.nlink.product.device.server.model.channel.dto.ChannelParamDTO;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/2/17 12:41<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "新增变动通知")
public class CreateChangeNoticeDTO {

    @Schema(description = "变动通知名称")
    @NotBlank(message = "变动通知名称不能为空")
    @Length(max = 255,message = "变动通知名称不能超过255个字符")
    @Pattern(regexp = RegexUtil.channelNamePatternString,message = "变动通知名称不能包含'.'")
    private String name;

    /**
     * 变动项目(1网关,2设备)
     */
    @Schema(description = "变动项目(1网关,2设备)")
    @NotNull(message = "变动项目不能为空")
    private Integer changeSubject;

    /**
     * 变动类型(1 新建,2,删除,3更新4,状态变化)
     */
    @Schema(description = "变动类型(1 新建,2,删除,3更新4,状态变化)")
    @NotNull(message = "变动类型不能为空")
    private Integer changeType;

    /**
     * 回调id
     */
    @Schema(description = "回调id")
    @NotNull(message = "回调id不能为空")
    private Long redirectId;

}
