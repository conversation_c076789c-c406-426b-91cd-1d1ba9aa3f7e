package com.nti56.nlink.product.device.server.openapi.convertor;

import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.common.it.ITResult;

public class ITResultConvertor {

    public static <T> ITResult<T> convert(R result) {
        int code = getCode((Integer)result.get("code"));
        String msg = result.get("message").toString();
        T data = (T) result.get("data");
        return new ITResult(data, code, msg, msg);

    }

    private static int getCode(Integer code) {
        return ServiceCodeEnum.OK.getCode().equals(code) ? ITResult.SUCCESS_CODE : ITResult.ERROR_CODE;
    }
}
