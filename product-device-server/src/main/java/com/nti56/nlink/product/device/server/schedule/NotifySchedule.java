package com.nti56.nlink.product.device.server.schedule;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.product.device.server.constant.NotifyConstant;
import com.nti56.nlink.product.device.server.entity.NotifyServerLogEntity;
import com.nti56.nlink.product.device.server.enums.NotifyServerLogEnum;
import com.nti56.nlink.product.device.server.mapper.NotifyServerLogMapper;
import com.nti56.nlink.product.device.server.service.impl.NotifyStrategyContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/19 16:23<br/>
 * @since JDK 1.8
 */

@Slf4j
@Component
public class NotifySchedule {

    @Autowired
    private NotifyServerLogMapper notifyServerLogMapper;

    @Autowired
    private NotifyStrategyContext notifyStrategyContext;


    @Scheduled(fixedDelay = 1000*60*10 , initialDelay = 1000*3)
    public void retrySendFailNotify(){
        log.info("重发失败的通知");

        boolean run = true;
        long current = 1L;
        long size = 100L;
        while (run){
            Page<NotifyServerLogEntity> notifyServerLogEntityPage = new Page<>();
            notifyServerLogEntityPage.setCurrent(current);
            notifyServerLogEntityPage.setSize(size);
            Page<NotifyServerLogEntity> notifyServerLogPage = notifyServerLogMapper.pageNewByLogType(notifyServerLogEntityPage,NotifyServerLogEnum.SEND_NOTIFY_FAIL.getValue(),NotifyConstant.RETRY_TIME);
            List<NotifyServerLogEntity> notifyServerLogList = notifyServerLogPage.getRecords();
            for (NotifyServerLogEntity notifyServerLogEntity : notifyServerLogList) {
                TenantIsolation tenantIsolation = BeanUtilsIntensifier.copyBean(notifyServerLogEntity, TenantIsolation.class);
                this.asyncRetrySendFailNotify(notifyServerLogEntity,tenantIsolation);
            }
            long total = notifyServerLogPage.getTotal();
            run = false;
            if (total > current * size){
                run = true;
                current++;
            }
        }
    }

    @Async
    public void asyncRetrySendFailNotify(NotifyServerLogEntity notifyServerLogEntity, TenantIsolation tenantIsolation){
        notifyStrategyContext.retrySendFailNotify(notifyServerLogEntity,tenantIsolation);
    }
}
