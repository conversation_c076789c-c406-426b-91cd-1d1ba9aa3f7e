package com.nti56.nlink.product.device.server.model.oss;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/20 10:15<br/>
 * @version 1.0
 * @since JDK 1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OssUpdateLoadVo {
  private String url;
  private String id;
}
