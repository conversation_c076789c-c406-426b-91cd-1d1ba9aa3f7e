package com.nti56.nlink.product.device.server.model.cloudnest;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ApiUpdateRoleMenuRequest extends BaseRequest {
    /**集团公司ID*/
    @ApiModelProperty(value = "集团公司ID")
    private String clientId;


    /**应用代码*/
    @ApiModelProperty(value = "应用代码")
    private String appcode;

    /**角色id*/
    @ApiModelProperty(value = "角色id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long roleId;

    @ApiModelProperty(value = "菜单分类：1.PC端菜单 2.移动端菜单")
    private Integer menuCategory;

    /**菜单*/
    @ApiModelProperty(value = "菜单")
    private List<ApiUpdateRoleMenuRequest.SysRoleMenuDTO> menus;

    @Data
    public static class SysRoleMenuDTO {

        /**主键ID*/
        @ApiModelProperty(value = "主键ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;


        /**集团公司ID*/
        @ApiModelProperty(value = "集团公司ID")
        private String clientId;


        /**应用代码*/
        @ApiModelProperty(value = "应用代码")
        private String appcode;


        /**角色id*/
        @ApiModelProperty(value = "角色id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long roleId;


        /**菜单id*/
        @ApiModelProperty(value = "菜单id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long menuId;

        @ApiModelProperty(value = "保留的菜单id集合")
        private  List<Long> reserveMenuId;

    }
}
