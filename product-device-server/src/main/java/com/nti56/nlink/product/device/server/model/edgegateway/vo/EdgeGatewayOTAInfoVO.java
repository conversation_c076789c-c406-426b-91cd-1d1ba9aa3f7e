package com.nti56.nlink.product.device.server.model.edgegateway.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/3/16 15:11<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "网关版本列表vo")
public class EdgeGatewayOTAInfoVO {
    
    @Schema(description = "网关ID")
    private Long id;
    
    @Schema(description = "是否在线")
    private Boolean online;
    
    @Schema(description = "是否有新版本 true 有 false 无")
    private Boolean newVersion;

    @Schema(description = "当前版本号")
    private String currentVersion;
    
    @Schema(description = "目标版本号")
    private String targetVersion;
    
    @Schema(description = "升级状态")
    private Integer upgradeStatus;
    
    @Schema(description = "下载状态 (0:不处理;1:下载中;2:下载完成;3:下载失败)")
    private Integer downloadStatus;
    
}
