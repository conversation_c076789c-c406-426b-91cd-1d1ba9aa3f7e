package com.nti56.nlink.product.device.server.model.product.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/22 10:05<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "查询产品dto")
public class QueryProductDTO {

    @Schema(description = "产品名称")
    private String name;

    @ArraySchema(schema=@Schema(implementation = Long.class,description = "标记id"))
    private List<Long> tagIds;

    @Schema(description = "产品状态，1-启用，0-关闭 ，默认关闭")
    private Boolean status;

}
