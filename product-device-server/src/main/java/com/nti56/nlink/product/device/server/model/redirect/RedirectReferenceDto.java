package com.nti56.nlink.product.device.server.model.redirect;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 类说明：
 *
 * @ClassName RedirectReferenceDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/4/6 14:03
 * @Version 1.0
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RedirectReferenceDto implements Serializable {

    private Integer type;

    private String typeName;

    private Long redirectId;

    /**
     * 引用对象名
     */
    private String refName;

    private Long refId;

    /**
     * 1-新增 0-删除
     */
    private int updateType;
}
