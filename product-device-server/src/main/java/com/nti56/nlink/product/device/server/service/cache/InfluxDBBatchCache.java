package com.nti56.nlink.product.device.server.service.cache;

import com.google.common.collect.Sets;
import com.influxdb.client.write.Point;
import com.nti56.nlink.product.device.server.config.InfluxDBCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Component
public class InfluxDBBatchCache {

    @Autowired
    private InfluxDBCacheConfig cacheConfig;
    
    // 使用ConcurrentLinkedQueue代替普通List，实现无锁并发
    private static final Map<Long, ConcurrentLinkedQueue<Point>> cache = new ConcurrentHashMap<>();
    private static final AtomicLong totalPointsCount = new AtomicLong(0);
    
    private static InfluxDBBatchCache instance;

    @PostConstruct
    public void init() {
        instance = this;
    }
    
    public static InfluxDBBatchCache getInstance() {
        return instance;
    }
    
    /**
     * 非阻塞方式添加数据点到缓存
     */
    public static boolean push(Long tenantId, List<Point> pointList) {
        if (CollectionUtils.isEmpty(pointList)) {
            return true;
        }
        
        try {
            // 检查是否启用缓存
            if (instance != null && instance.cacheConfig != null && !instance.cacheConfig.isEnabled()) {
                log.debug("Cache is disabled, dropping {} points for tenant {}", pointList.size(), tenantId);
                return false;
            }
            
            // 检查总缓存大小 - 使用原子操作避免锁
            long currentTotal = totalPointsCount.get();
            int maxTotal = instance != null && instance.cacheConfig != null ? 
                instance.cacheConfig.getMaxTotalPoints() : 100000;
            if (currentTotal + pointList.size() > maxTotal) {
                log.warn("Total cache size limit exceeded, dropping {} points for tenant {}, current total: {}, max: {}", 
                    pointList.size(), tenantId, currentTotal, maxTotal);
                return false;
            }
            
            // 获取或创建租户缓存队列
            ConcurrentLinkedQueue<Point> tenantCache = cache.computeIfAbsent(tenantId, k -> new ConcurrentLinkedQueue<>());
            
            // 检查单个租户缓存大小
            int maxPerTenant = instance != null && instance.cacheConfig != null ? 
                instance.cacheConfig.getMaxPointsPerTenant() : 10000;
            if (tenantCache.size() + pointList.size() > maxPerTenant) {
                log.warn("Tenant {} cache size limit exceeded, dropping {} points, current size: {}, max: {}", 
                    tenantId, pointList.size(), tenantCache.size(), maxPerTenant);
                return false;
            }
            
            // 原子操作添加数据
            tenantCache.addAll(pointList);
            totalPointsCount.addAndGet(pointList.size());
            
            log.debug("Added {} points for tenant {}, total cache size: {}", 
                pointList.size(), tenantId, totalPointsCount.get());
                
            return true;
        } catch (Exception e) {
           log.error("Error pushing points to cache for tenant {}", tenantId, e);
           return false;
        }
    }


    /**
     * 非阻塞方式获取所有数据点
     */
    public static List<Point> pop(Long tenantId) {
        try {
            ConcurrentLinkedQueue<Point> tenantCache = cache.remove(tenantId);
            if (tenantCache != null && !tenantCache.isEmpty()) {
                List<Point> points = new ArrayList<>(tenantCache);
                totalPointsCount.addAndGet(-points.size());
                log.debug("Popped {} points for tenant {}, remaining cache size: {}", 
                    points.size(), tenantId, totalPointsCount.get());
                return points;
            }
        } catch (Exception e) {
            log.error("Error popping points for tenant {}", tenantId, e);
        }
        return Lists.newArrayList();
    }
    
    /**
     * 非阻塞方式按批量大小获取数据
     */
    public static List<Point> popBatch(Long tenantId, int batchSize) {
        try {
            ConcurrentLinkedQueue<Point> tenantCache = cache.get(tenantId);
            if (tenantCache == null || tenantCache.isEmpty()) {
                return Lists.newArrayList();
            }
            
            List<Point> batch = new ArrayList<>();
            Point point;
            int count = 0;
            
            // 从队列头部取出指定数量的数据点
            while (count < batchSize && (point = tenantCache.poll()) != null) {
                batch.add(point);
                count++;
            }
            
            // 如果队列为空，移除该租户的缓存
            if (tenantCache.isEmpty()) {
                cache.remove(tenantId);
            }
            
            if (!batch.isEmpty()) {
                totalPointsCount.addAndGet(-batch.size());
                log.debug("Popped batch {} points for tenant {}, remaining cache size: {}", 
                    batch.size(), tenantId, totalPointsCount.get());
            }
            
            return batch;
        } catch (Exception e) {
            log.error("Error popping batch for tenant {}", tenantId, e);
            return Lists.newArrayList();
        }
    }


    /**
     * 非阻塞方式获取租户ID列表
     */
    public static Set<Long> getTenantIds() {
        try {
            Set<Long> tenantIds = Sets.newHashSet();
            for (Map.Entry<Long, ConcurrentLinkedQueue<Point>> entry : cache.entrySet()) {
                if (!entry.getValue().isEmpty()) {
                    tenantIds.add(entry.getKey());
                }
            }
            return tenantIds;
        } catch (Exception e) {
            log.error("Error getting tenant IDs", e);
            return Sets.newHashSet();
        }
    }
    
    /**
     * 获取缓存统计信息（非阻塞）
     */
    public static String getCacheStats() {
        try {
            StringBuilder stats = new StringBuilder();
            stats.append("Total cache size: ").append(totalPointsCount.get());
            stats.append(", Tenant count: ").append(cache.size());
            
            // 限制统计信息长度，避免日志过长
            int count = 0;
            for (Map.Entry<Long, ConcurrentLinkedQueue<Point>> entry : cache.entrySet()) {
                if (count++ > 10) { // 只显示前10个租户的详细信息
                    stats.append(", ...");
                    break;
                }
                stats.append(", Tenant ").append(entry.getKey())
                     .append(": ").append(entry.getValue().size());
            }
            return stats.toString();
        } catch (Exception e) {
            log.error("Error getting cache stats", e);
            return "Error getting stats";
        }
    }
    
    /**
     * 非阻塞方式清理空的租户缓存
     */
    public static void cleanup() {
        try {
            cache.entrySet().removeIf(entry -> entry.getValue().isEmpty());
            log.debug("Cache cleanup completed, remaining tenants: {}", cache.size());
        } catch (Exception e) {
            log.error("Error during cache cleanup", e);
        }
    }
    
    /**
     * 获取当前缓存大小
     */
    public static long getTotalCacheSize() {
        return totalPointsCount.get();
    }
    
    /**
     * 获取指定租户的缓存大小
     */
    public static int getTenantCacheSize(Long tenantId) {
        ConcurrentLinkedQueue<Point> tenantCache = cache.get(tenantId);
        return tenantCache != null ? tenantCache.size() : 0;
    }
    
    /**
     * 检查缓存是否接近限制
     */
    public static boolean isNearLimit() {
        int maxTotal = instance != null && instance.cacheConfig != null ? 
            instance.cacheConfig.getMaxTotalPoints() : 100000;
        return totalPointsCount.get() > (maxTotal * 0.8);
    }
    
    /**
     * 获取配置中的批量大小
     */
    public static int getBatchSize() {
        return instance != null && instance.cacheConfig != null ? 
            instance.cacheConfig.getBatchSize() : 1000;
    }






}
