package com.nti56.nlink.product.device.server.model.channel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 13:06<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "通道参数dto")
public class ChannelParamDTO {

    /**
     * id
     */
    private Long id;
    
    /**
     * 通道ID
     */
    private Long channelId;

    /**
     * 通道参数名称，如ip/port/rack/slot等
     */
    @Schema(description = "参数名称")
    @NotBlank(message = "通道参数名称不能为空")
    private String name;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String descript;

    /**
     * 通道参数值
     */
    @Schema(description = "通道参数值")
    private String value;

    @Schema(description = "是否必须")
    private Boolean necessary;

}
