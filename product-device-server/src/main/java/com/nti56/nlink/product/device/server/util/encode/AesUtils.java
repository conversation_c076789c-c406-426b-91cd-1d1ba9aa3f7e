package com.nti56.nlink.product.device.server.util.encode;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.spec.AlgorithmParameterSpec;

/**
 * 类说明: 对称加密解密AES算法 <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0.0
 * @date 2019年7月19日 下午9:24:28 <br/>
 * @since JDK 1.8
 */

@Slf4j
public class AesUtils {

    /**
     * 加解密补码
     */
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";

    /**
     * 加密算法
     */
    private static final String ALGORITHM = "AES";

    /**
     * utf-8字符集
     */
    private static final String CHARSET = "utf-8";
    /**
     * 建议为16位或32位
     */
    private static final String KEY = "abcdefghijklmnopqrstuvwxyz012345";

    /**
     * 必须16位
     * 初始化向量IV不可以为32位，否则异常java.security.InvalidAlgorithmParameterException:
     * Wrong IV length: must be 16 bytes long
     */
    private static final String IV = "9876543210abcdef";

    /**
     * 十六进制char
     */
    private final static char[] HEX_ARRAY = "0123456789abcdef".toCharArray();

    private final static int LENGTH_32 = 32;

    private final static int LENGTH_16 = 16;

    private final static int MOD=2;

    /**
     * 加密
     *
     * @param context 加密内容
     * @return 返回值
     */
    public static String encrypt(String context) {

        try {
            if(StringUtils.isEmpty(context)){
                return null;
            }
            byte[] decode = context.getBytes(CHARSET);
            byte[] bytes = createKeyAndIv(decode, Cipher.ENCRYPT_MODE);
            return byteArrToHex(bytes);
        } catch (Exception e) {
            log.error("encrypt error", e);
        }
        return null;
    }

    /**
     * 加密
     *
     * @param context 加密内容
     *
     * @param c  格式化字符
     * @return 返回值
     */
    public static String encryptFormat(String context,char c) {

        try {
            if(StringUtils.isEmpty(context)){
                return null;
            }
            byte[] decode = context.getBytes(CHARSET);
            byte[] bytes = createKeyAndIv(decode, Cipher.ENCRYPT_MODE);
            return byteArrToHexFormat(c,bytes);
        } catch (Exception e) {
            log.error("encrypt error", e);
        }
        return null;
    }

    /**
     * 解密
     *
     * @param context 解密内容
     * @return 返回值
     */
    public static String decrypt(String context) {

        try {
            if(StringUtils.isBlank(context)){
                return  null;
            }

            byte[] decode = hexToByteArr(context," ");
            byte[] bytes = createKeyAndIv(decode,
                    Cipher.DECRYPT_MODE);
            return new String(bytes, CHARSET);
        } catch (Exception e) {
            log.error("decrypt error", e);
        }
        return context;
    }


    /**
     * 解密
     *
     * @param context
     *          解密内容
     * @param regex
     *        格式化字符
     *
     * @return 返回值
     */
    public static String decryptFormat(String context,String regex) {

        try {
            if(StringUtils.isBlank(context)){
                return  null;
            }

            byte[] decode = hexToByteArr(context,regex);
            byte[] bytes = createKeyAndIv(decode,
                    Cipher.DECRYPT_MODE);
            return new String(bytes, CHARSET);
        } catch (Exception e) {
            log.error("decrypt error", e);
        }
        return context;
    }

    /**
     * 自定义密钥加密
     *
     * @param context 加密
     * @param key     key
     * @return 返回值
     */
    public static String encrypt(String context, String key) {

        try {
            if(StringUtils.isAnyBlank(context,key)){
                return null;
            }
            byte[] decode = context.getBytes(CHARSET);
            byte[] bytes = createKeyAndIv(decode, Cipher.ENCRYPT_MODE, key, IV);
            return byteArrToHex(bytes);
        } catch (Exception e) {
            log.error("encrypt error", e);
        }
        return context;
    }


    /**
     * 自定义密钥解密
     *
     * @param context 加密
     * @param key     key
     * @return 返回值
     */
    public static String decrypt(String context, String key) {

        try {
            if(StringUtils.isAnyBlank(context,key)){
                return context;
            }

            byte[] decode = hexToByteArr(context," ");
            byte[] bytes = createKeyAndIv(decode, Cipher.DECRYPT_MODE, key, IV);
            return new String(bytes, CHARSET);
        } catch (Exception e) {
            log.error("decrypt error", e);
        }
        return context;
    }

    /**
     * 获取key & iv
     *
     * @param context 内容
     * @param opMode  加解密模式
     * @param key     key
     * @param iv      iv
     * @return 返回值
     * @throws Exception 异常
     */
    private static byte[] createKeyAndIv(byte[] context, int opMode, String key, String iv) throws Exception {
        if (key.length() < LENGTH_32) {
            key = KEY;
        } else {
            key = key.substring(0, LENGTH_32);
        }
        if (iv.length() < LENGTH_16) {
            iv = IV;
        } else {
            iv = iv.substring(0, LENGTH_16);
        }
        byte[] keyByte = key.getBytes(CHARSET);
        byte[] ivByte = iv.getBytes(CHARSET);
        return cipherFilter(context, opMode, keyByte, ivByte);
    }

    /**
     * 获取key & iv
     *
     * @param context 内容
     * @param opMode  加解密模式
     * @return 返回值
     * @throws Exception 异常
     */
    private static byte[] createKeyAndIv(byte[] context, int opMode) throws Exception {

        byte[] key = KEY.getBytes(CHARSET);
        byte[] iv = IV.getBytes(CHARSET);
        return cipherFilter(context, opMode, key, iv);
    }

    /**
     * 执行操作
     *
     * @param context 内容
     * @param opmode  加解密模式
     * @param key     key
     * @param iv      iv
     * @return 返回值
     * @throws Exception 异常
     */
    private static byte[] cipherFilter(byte[] context, int opmode, byte[] key, byte[] iv) throws Exception {

        Key secretKeySpec = new SecretKeySpec(key, ALGORITHM);
        AlgorithmParameterSpec ivParameterSpec = new IvParameterSpec(iv);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(opmode, secretKeySpec, ivParameterSpec);
        return cipher.doFinal(context);
    }

    /**
     * 字节码转16进制
     *
     * @param bytes  字节码
     * @return 返回 字符串
     */

    private static String byteArrToHex(byte... bytes) {

        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * MOD] = HEX_ARRAY[v >>> 4];
            hexChars[j * MOD + 1] = HEX_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }


    /**
     * 方法说明：格式化16进制</br>
     *
     * @param c 格式化字符
     * @param bytes  字节
     * @return String
     */

    private static String byteArrToHexFormat(char c,byte... bytes) {
        char[] hexChars = new char[bytes.length * 3];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * MOD] = HEX_ARRAY[v >>> 4];
            hexChars[j * MOD + 1] = HEX_ARRAY[v & 0x0F];
            hexChars[j*MOD+2]=c;
        }
        return new String(hexChars);
    }


    /**
     * 16进制转字节码
     * @param hexString 16进制字符串
     * @return 返回 字节码
     */
    private static byte[] hexToByteArr(String hexString,String regex) {

        if (StringUtils.isEmpty(hexString)) {
            return null;
        }
        hexString=hexString.replaceAll(regex,"");
        hexString = hexString.toLowerCase();
        if(hexString.length()%MOD!=0){
            hexString+=" ";
        }
        final byte[] byteArray = new byte[hexString.length() >> 1];
        int index = 0;
        for (int i = 0; i < hexString.length(); i++) {
            if (index > hexString.length() - 1) {
                return byteArray;
            }
            byte highDit = (byte) (Character.digit(hexString.charAt(index), 16) & 0xFF);
            byte lowDit = (byte)  (Character.digit(hexString.charAt(index + 1), 16) & 0xFF);
            byteArray[i] = (byte) (highDit << 4 | lowDit);
            index += 2;
        }
        return byteArray;
    }

    /**
     * * 主方法测试 * * @param args
     */
    public static void main(String[] args) {

        try {
            String ddString = DesUtil.decode("J8lqOn/WlhTe+yAqzalwIw==", "ntitotci");
            System.out.println(ddString);
            String errorStr = "对不起，密码输入错误第(%d)次！";
            System.out.println(String.format(errorStr, 1L));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}