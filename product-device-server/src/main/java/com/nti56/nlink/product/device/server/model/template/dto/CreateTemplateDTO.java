package com.nti56.nlink.product.device.server.model.template.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/13 13:07<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "新增模板dto")
public class CreateTemplateDTO {


    @Schema(description = "模板名称")
    @NotBlank(message = "模板名称不能为空")
    @Length(max = 32,message = "模板名称不能超过32个字符")
    private String name;

    @Schema(description = "描述")
    @Length(max = 256,message = "模板描述不能超过256个字符")
    private String description;

    @Schema(description = "模板标题")
    @Length(max = 30 ,message = "模板标题不超过30个字符")
    private String title;

    /**
     * 通知类型 0邮件 1短信
     */
    @Schema(description = "通知类型 0邮件 1短信")
    @NotNull(message = "通知类型不能为空")
    @Range(max = 2,message = "通知类型数值超出范围")
    private Integer notifyType;

    /**
     * 模板内容
     */
    @Schema(description = "模板内容")
    @NotBlank(message = "模板内容不能为空")
    @Length(max = 500 ,message = "模板内容不超过500个字符")
    private String content;

    /**
     * 模板绑定的ChannelId
     */
    @Schema(description = "模板绑定的ChannelId")
    @NotNull(message = "关联渠道不能为空")
    private Long notifyChannelId;

    @Schema(description = "申请模板理由")
    private String reason;

    @Schema(description = "消息类型")
    private String msgType;

}
