package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceDataResource;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.service.cache.redis2Mem.MemoryCache;
import com.nti56.nlink.product.device.server.util.redis.RedisUtil;
import io.vertx.core.Vertx;
import io.vertx.core.eventbus.EventBus;
import io.vertx.core.json.Json;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName DeviceTwinFromRedisServiceImpl
 * @date 2022/7/26 9:20
 * @Version 1.0
 */
@Service
@Lazy
@Slf4j
public class DeviceTwinFromRedisServiceImpl implements DeviceDataResource, InitializingBean {

    @Autowired @Lazy
    RedisTemplate redisTemplate;

    RedisUtil redisUtil;

    @Autowired
    Vertx vertx;

    @Override
    public void afterPropertiesSet() throws Exception {
        redisUtil = new RedisUtil(this.redisTemplate, RedisConstant.DEVICE_TWIN_PREFIX);
    }

    @Override
    public Map<String, Object> getById(Long deviceId) {
        // Map<Object, Object> hmget = redisUtil.hmget(getDeviceActualKeyByDeviceId(deviceId));
        Map<String,Object> result = MemoryCache.getActualPropertiesValue(deviceId);//MapUtil.createMap(HashMap.class);
        // hmget.forEach((key, value) -> result.put(key.toString(), value));
        return result;
    }

    @Override
    public Object getProperty(Long deviceId, String property) {
        return MemoryCache.getActualPropValue(deviceId, property);//redisUtil.hget(getDeviceActualKeyByDeviceId(deviceId),property);
    }

    @Override
    public boolean setProperty(Long deviceId, String property, Object value) {
        Map<String,Object> propertyValue = new HashMap<>();
        propertyValue.put(property,value);
        return setProperties(deviceId,propertyValue);
    }

    @Override
    public boolean setProperties(Long deviceId, Map<String, Object> properties) {
        if (CollectionUtil.isNotEmpty(properties)) {
            Map<String,Object> timeMap = new HashMap<>();
            long timeMillis = System.currentTimeMillis();
            properties.keySet().forEach( property -> timeMap.put(property,timeMillis));
            redisUtil.hmset(getDeviceChangeTimeKeyByDeviceId(deviceId), timeMap);
        }
        boolean hmset = MemoryCache.setActualPropertiesValue(deviceId,properties); //redisUtil.hmset(getDeviceActualKeyByDeviceId(deviceId), properties);
        return hmset;
    }

    private String getDeviceChangeTimeKeyByDeviceId(Long deviceId) {
        return new StringBuilder().append("changeTime").append(":").append(deviceId).toString();
    }

    @Override
    public Set<Subscription> getSubscriptionRegistry(Long deviceId,Collection<String> properties){
        Collection<String> keySet = buildDeviceSubscriptionRegistryKeySet(deviceId, properties);
        if (keySet.isEmpty()) {
            return new HashSet<>();
        }
        return (Set<Subscription>) redisTemplate.opsForSet().union(keySet);
    }

    @Override
    public Map<String, Set<Subscription>> getNoChangeSubscriptionRegistry2Map(Long deviceId, Collection<String> properties) {
        Collection<String> keySet = buildDeviceSubscriptionRegistryKeySet(deviceId, properties);
        HashMap<String, Set<Subscription>> map = new HashMap<>();
        if (!keySet.isEmpty()) {
            keySet.forEach(s -> {
                String noChangeSuffix = RedisConstant.NO_CHANGE_EVENT_PREFIX + s;
                List<Subscription> subscriptions=MemoryCache.getSubscriptionList(deviceId,noChangeSuffix);

                // Set<String> members = redisTemplate.opsForSet().members(String.format(RedisConstant.SUBSCRIPTION_REGISTRY,deviceId,noChangeSuffix));
                if (CollectionUtil.isNotEmpty(subscriptions)) {
                    if (!map.containsKey(s)) {
                        map.put(s,new HashSet<>());
                    }
                    map.get(s).addAll(subscriptions);
                }
            });
        }
        return map;
    }

    @Override
    public Map<String,Set<Subscription>> getSubscriptionRegistry2Map(Long deviceId, Collection<String> properties){
        Collection<String> keySet = buildDeviceSubscriptionRegistryKeySet(deviceId, properties);
        HashMap<String, Set<Subscription>> map = new HashMap<>();
        if (!keySet.isEmpty()) {
            keySet.forEach(s -> {
                List<Subscription> subscriptions = MemoryCache.getSubscriptionList(deviceId, s);
                if (CollectionUtil.isNotEmpty(subscriptions)) {
                    if (!map.containsKey(s)) {
                        map.put(s,new HashSet<>());
                    }
                    map.get(s).addAll(subscriptions);
                }
            });
        }
        return map;
    }

    private Collection<String> buildDeviceSubscriptionRegistryKeySet(Long deviceId, Collection<String> targetNames) {
        Set<String> keySet = new HashSet<>();
        keySet.addAll(targetNames);
        return keySet;
    }


    @Override
    public Map<String, Object> getProperties(Long deviceId, Collection<String> properties) {
        Map<String, Object> byId = getById(deviceId);
        Map<String, Object> result = new HashMap<>();
        if (MapUtil.isNotEmpty(byId)) {
            properties.forEach(property -> {
                if (byId.containsKey(property)) {
                    result.put(property,byId.get(property));
                }
            });
        }
        return result;
    }

    @Override
    public void addDeviceServiceCallLog(DeviceServiceLogEntity logEntity) {
        EventBus eventBus = vertx.eventBus();
        eventBus.send("addDeviceServiceCallLog",Json.encode(logEntity));
    }

    @Override
    public Map<Long, Map<String, Object>> getByIds(List<Long> deviceIds) {
        Map<Long,Map<String,Object>> result = new HashMap<>();
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            deviceIds.forEach(id ->{
                Map<String, Object> properties = getById(id);
                if (MapUtil.isNotEmpty(properties)) {
                    result.put(id,properties);
                }
            });
        }
        return result;
    }

    @Override
    public Set<Long> getSubscriptionEnable(Long tenantId, Long deviceId) {
        Set<Long> members = redisTemplate.opsForSet().members(String.format(RedisConstant.SUBSCRIPTION_ENABLE,tenantId,deviceId));
        if (CollectionUtil.isEmpty(members)) {
            return new HashSet<>();
        }
        return members;
    }

    @Override
    public Map<String, Object> getChangeTimeById(Long deviceId) {
        Map<Object, Object> hmget = redisUtil.hmget(getDeviceChangeTimeKeyByDeviceId(deviceId));
        Map<String,Object> result = MapUtil.createMap(HashMap.class);
        hmget.entrySet().forEach(kv -> result.put(kv.getKey().toString(),kv.getValue()));
        return result;
    }

    // private String getDeviceActualKeyByDeviceId(Long deviceId){
    //     return new StringBuilder().append("actual").append(":").append(deviceId).toString();
    // }

    @Override
    public void updateNoChangeExpireTime(String key, Integer seconds) {
        redisTemplate.opsForValue().set(key, true, seconds, TimeUnit.SECONDS);
    }

    @Override
    public void deleteProperties(Long id, Set<String> oldPropertyNames) {
        MemoryCache.deleteActualProperties(id,oldPropertyNames);
        // redisUtil.hdel(getDeviceActualKeyByDeviceId(id),oldPropertyNames.toArray());
    }


}
