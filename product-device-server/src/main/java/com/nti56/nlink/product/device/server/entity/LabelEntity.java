package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.DataTypeElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 * 类说明: 标签表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:08:25
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("label")
@Schema(description = "标签")
public class LabelEntity {

    /**
     * id
     */ 
    private Long id;
    /**
     * 标签分组id
     */
    @Schema(description = "标签分组id")
    private Long labelGroupId;
    /**
     * 标签名称
     */
    private String name;

    @Schema(description = "别名")
    private String alias;
    /**
     * 标签描述
     */
    @Schema(description = "标签描述")
    private String descript;
    /**
     * 地址，如DB50.DBB1
     */
    @Schema(description = "地址，如DB50.DBB1")
    private String address;
    /**
     * 长度
     */
    @Schema(description = "长度")
    private Integer length;
    /**
     * 采集参数
     */
    private GatherParamField gatherParam;
    /**
     * 数据类型，bool/byte/short/int/float/string
     */
    @Schema(description = "数据类型，bool/byte/short/int/float/string/ushort/double")
    private String dataType;
    /**
     * 是否数组
     */
    @Schema(description = "是否数组")
    private Boolean isArray;
    /**
     * type是string类型时，表示string元素的byte长度，其他type类型放空
     */
    @Schema(description = "type是string类型时，表示string元素的byte长度，其他type类型放空")
    private Integer stringBytes;

    @Schema(description = "时间间隔，单位毫秒")
    private Integer intervalMs;

    @Schema(description = "是否只读")
    private Boolean readOnly;

    /**
     * 标签标识
     */
    @Schema(description = "标签标识")
    private String tag;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

    /**
     * 逻辑删除，1-删除
     */
    @Schema(description = "逻辑删除，1-删除")
    @TableLogic
    private Boolean deleted;

    @Schema(description = "租户ID")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @Schema(description = "工程ID")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @Schema(description = "模块ID")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @Schema(description = "空间ID")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    public PropertyElm toPropertyElm() {
        DataTypeElm dataTypeElm = new DataTypeElm();
        dataTypeElm.setIsArray(this.isArray);
        dataTypeElm.setType(this.dataType);
        PropertyElm propertyElm = new PropertyElm();
        propertyElm.setName(this.name);
        propertyElm.setReadOnly(this.readOnly);
        propertyElm.setBindLabel(true);
        propertyElm.setReportType(1);
        propertyElm.setDescript(this.descript);
        propertyElm.setDataType(dataTypeElm);
        return propertyElm;
    }

}
