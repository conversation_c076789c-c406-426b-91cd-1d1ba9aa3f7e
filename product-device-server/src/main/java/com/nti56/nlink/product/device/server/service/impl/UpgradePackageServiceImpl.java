package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.Upgrade.UpgradeRangeElm;
import com.nti56.nlink.product.device.server.annotation.AuditLog;
import com.nti56.nlink.product.device.server.constant.Constant;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.UpgradeStatusEnum;
import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import com.nti56.nlink.product.device.server.entity.UpgradePackageEntity;
import com.nti56.nlink.product.device.server.enums.ActionEnum;
import com.nti56.nlink.product.device.server.enums.AuditTargetEnum;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayControlProxy;
import com.nti56.nlink.product.device.server.mapper.UpgradePackageMapper;
import com.nti56.nlink.product.device.server.model.upgrade.CreateUpgradePackageRep;
import com.nti56.nlink.product.device.server.model.upgrade.dto.BatchUpgradePackageDTO;
import com.nti56.nlink.product.device.server.model.upgrade.dto.CreateUpgradePackageDTO;
import com.nti56.nlink.product.device.server.model.upgrade.dto.EditUpgradePackageDTO;
import com.nti56.nlink.product.device.server.model.upgrade.dto.QueryUpgradePackageDTO;
import com.nti56.nlink.product.device.server.model.upgrade.vo.UpgradePackageVO;
import com.nti56.nlink.product.device.server.model.upgrade.vo.UpgradeRangeVO;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;
import com.nti56.nlink.product.device.server.service.IUpgradePackageService;
import com.nti56.nlink.product.device.server.util.Md5CalculationUtil;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-02-18 16:11:55
 * @since JDK 1.8
 */
@Service
@Slf4j
public class UpgradePackageServiceImpl extends BaseServiceImpl<UpgradePackageMapper, UpgradePackageEntity> implements IUpgradePackageService {

    @Autowired
    private UpgradePackageMapper upgradePackageMapper;
    
    @Autowired @Lazy
    private IEdgeGatewayService edgeGatewayService;
    
    @Autowired
    private IEdgeGatewayControlProxy edgeGatewayControlProxy;
    
    @Getter
    @Value("${otaDownloadUrl}")
    private String otaDownloadUrl;
    
    @Autowired @Lazy
    private StringRedisTemplate stringRedisTemplate;
    
    @Override
    public Result<Page<UpgradePackageVO>> pageUpgradePackage(PageParam pageParam, QueryUpgradePackageDTO queryUpgradePackageDTO) {
    
        Page<UpgradePackageEntity> pageUpgradePackageEntity = upgradePackageMapper.pageUpgradePackage(pageParam.toPage(UpgradePackageEntity.class),queryUpgradePackageDTO);
        
        Page<UpgradePackageVO> packageVOPage = new Page<>();
        packageVOPage.setTotal(pageUpgradePackageEntity.getTotal());
        packageVOPage.setCurrent(pageUpgradePackageEntity.getCurrent());
        packageVOPage.setSize(pageUpgradePackageEntity.getSize());
        packageVOPage.setPages(pageUpgradePackageEntity.getPages());
        
        if(CollectionUtil.isNotEmpty(pageUpgradePackageEntity.getRecords())){
            List<UpgradePackageVO> collect = pageUpgradePackageEntity.getRecords().stream().map(t -> {
                UpgradePackageVO upgradePackageVO = new UpgradePackageVO();
                BeanUtil.copyProperties(t,upgradePackageVO);
                return upgradePackageVO;
            }).collect(Collectors.toList());
            packageVOPage.setRecords(collect);
        }else {
            packageVOPage.setRecords(new ArrayList<>());
        }
        return Result.ok(packageVOPage);
    }
    
    @Override
    @Transactional
    public Result<Void> createUpgradePackage(CreateUpgradePackageDTO createUpgradePackageDTO, MultipartFile file) {
        // log.info("createUpgradePackageDTO============={}" ,JSON.toJSONString(createUpgradePackageDTO));
        if (file.isEmpty()) {
            throw new BizException("文件为空");
        }
        String originalFilename = file.getOriginalFilename();
        try {
            if(StringUtils.isEmpty(originalFilename)){
                throw new BizException("文件名为空");
            }
            if (originalFilename != null && !originalFilename.isEmpty() && !".zip".equals(originalFilename.substring(originalFilename.lastIndexOf(".")))) {
                throw new BizException("文件格式错误");
            }
        } catch (Exception e) {
            throw new BizException("文件格式错误");
        }
        Result<Void> uniqueNameResult = uniqueName(null,createUpgradePackageDTO.getUpgradePackageName());
        if (!uniqueNameResult.getSignal()) {
            throw new BizException(uniqueNameResult.getMessage());
        }
        Result<Void> uniqueVersionResult = uniqueVersion(null,createUpgradePackageDTO.getUpgradeVersion());
        if (!uniqueVersionResult.getSignal()) {
            throw new BizException(uniqueVersionResult.getMessage());
        }
        CreateUpgradePackageRep templateFileRep = this.createUpgradePackageRep();
        File templateFile = templateFileRep.getFile();
        String md5 = "";
        try {
            //把上传的文件保存至本地
            file.transferTo(templateFile);
            md5 = Md5CalculationUtil.getMD5(templateFile);
        } catch (IOException e) {
            if(!templateFile.delete()){
                log.error("删除文件失败");
            }
            throw new BizException("文件传输异常");
        }
        if(createUpgradePackageDTO.getIsExpectVersion() == 1){
            upgradePackageMapper.updateExpectVersion();
        }
        UpgradePackageEntity build = UpgradePackageEntity.builder()
            .id(templateFileRep.getId())
            .upgradePackageName(createUpgradePackageDTO.getUpgradePackageName())
            .upgradeType(createUpgradePackageDTO.getUpgradeType())
            .upgradeVersion(createUpgradePackageDTO.getUpgradeVersion())
            .descript(createUpgradePackageDTO.getDescript())
            .isExpectVersion(createUpgradePackageDTO.getIsExpectVersion())
            .upgradeRange(createUpgradePackageDTO.getUpgradeRange())
            .md5Proofread(md5)
            .downloadLink(templateFileRep.getFile().getPath())
            .build();
        boolean save = this.save(build);
        if (!save){
            if(!templateFile.delete()){
                log.error("删除文件失败");
            }
            throw new BizException("服务器异常，请稍后重试10");
        }
        List<UpgradeRangeElm> upgradeRangeElmList = createUpgradePackageDTO.getUpgradeRange().getUpgradeRangeList();
        if(CollectionUtil.isNotEmpty(upgradeRangeElmList)){
            Set<Long> ids = upgradeRangeElmList.stream().map(UpgradeRangeElm::getEdgeGatewayId).collect(Collectors.toSet());
            List<EdgeGatewayEntity> edgeGatewayEntityList = edgeGatewayService.listEdgeGatewayByIds(ids).getResult();
            if(CollectionUtil.isNotEmpty(edgeGatewayEntityList)){
                edgeGatewayEntityList.forEach(edgeGatewayEntity ->{
                    edgeGatewayEntity.setUpgradeStatus(UpgradeStatusEnum.UPGRADE_STATUS_INIT.getValue());
                    edgeGatewayEntity.setTargetVersion(createUpgradePackageDTO.getUpgradeVersion());
                });
                boolean update = edgeGatewayService.saveOrUpdateBatch(edgeGatewayEntityList);
                if (!update){
                    if(!templateFile.delete()){
                        log.error("删除文件失败");
                    }
                    throw new BizException("服务器异常，请稍后重试10");
                }
            }
        }
        return Result.ok();
    }
    
    @Override
    @Transactional
    public Result<Void> editUpgradePackage(EditUpgradePackageDTO editUpgradePackageDTO) {
        UpgradePackageEntity upgradePackageEntity = new LambdaQueryChainWrapper<>(upgradePackageMapper)
            .eq(UpgradePackageEntity::getId, editUpgradePackageDTO.getId())
            .one();
        if (ObjectUtil.isEmpty(upgradePackageEntity)) {
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result<Void> uniqueNameResult = uniqueName(editUpgradePackageDTO.getId(),editUpgradePackageDTO.getUpgradePackageName());
        if (!uniqueNameResult.getSignal()) {
            throw new BizException(uniqueNameResult.getMessage());
        }
        if(editUpgradePackageDTO.getIsExpectVersion() == 1){
            upgradePackageMapper.updateExpectVersion();
        }
        upgradePackageEntity.setUpgradePackageName(editUpgradePackageDTO.getUpgradePackageName());
        upgradePackageEntity.setDescript(editUpgradePackageDTO.getDescript());
        upgradePackageEntity.setIsExpectVersion(editUpgradePackageDTO.getIsExpectVersion());
        upgradePackageEntity.setUpgradeRange(editUpgradePackageDTO.getUpgradeRange());
        upgradePackageMapper.updateById(upgradePackageEntity);
        List<UpgradeRangeElm> upgradeRangeElmList = editUpgradePackageDTO.getUpgradeRange().getUpgradeRangeList();
        if(CollectionUtil.isNotEmpty(upgradeRangeElmList)){
            Set<Long> ids = upgradeRangeElmList.stream().map(UpgradeRangeElm::getEdgeGatewayId).collect(Collectors.toSet());
            List<EdgeGatewayEntity> edgeGatewayEntityList = edgeGatewayService.listEdgeGatewayByIds(ids).getResult();
            if(CollectionUtil.isNotEmpty(edgeGatewayEntityList)){
                edgeGatewayEntityList.forEach(edgeGatewayEntity ->{
                    edgeGatewayEntity.setUpgradeStatus(UpgradeStatusEnum.UPGRADE_STATUS_INIT.getValue());
                    edgeGatewayEntity.setTargetVersion(upgradePackageEntity.getUpgradeVersion());
                });
                boolean update = edgeGatewayService.saveOrUpdateBatch(edgeGatewayEntityList);
                if (!update){
                    throw new BizException("服务器异常，请稍后重试10");
                }
            }
        }
        return Result.ok();
    }
    
    @Override
    public Result<String> getTargetVersion() {
        LambdaQueryWrapper<UpgradePackageEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UpgradePackageEntity::getIsExpectVersion,1);
        lqw.eq(UpgradePackageEntity::getDeleted, 0);
        List<UpgradePackageEntity> upgradePackageEntityList = upgradePackageMapper.selectList(lqw);
        String expectVersion = "";
        if(CollectionUtil.isNotEmpty(upgradePackageEntityList) && upgradePackageEntityList.size() > 1){
            Result.error("期望版本号应唯一");
        }
        if(CollectionUtil.isNotEmpty(upgradePackageEntityList)){
            expectVersion = upgradePackageEntityList.get(0).getUpgradeVersion();
        }
        return Result.ok(expectVersion);
    }
    
    @Override
    public Result<List<UpgradeRangeVO>> getUpgradeRange(Long tenantId) {
        Result<List<EdgeGatewayEntity>> result = edgeGatewayService.getAllEdgeGateway();
        if(!result.getSignal()){
            return Result.error("网关列表异常:" + result.getMessage());
        }
        Map<Long,String> tenantMap = edgeGatewayService.getTenantMap(tenantId).getResult();
        List<EdgeGatewayEntity> edgeGatewayEntityList = result.getResult();
        List<UpgradeRangeVO> upgradeRangeVOList = new ArrayList<>();
        for (EdgeGatewayEntity edgeGatewayEntity : edgeGatewayEntityList){
            UpgradeRangeVO upgradeRangeVO = new UpgradeRangeVO();
            upgradeRangeVO.setEdgeGatewayId(edgeGatewayEntity.getId());
            upgradeRangeVO.setTenantId(edgeGatewayEntity.getTenantId());
            upgradeRangeVO.setEdgeGatewayName(edgeGatewayEntity.getName());
            if(tenantMap.get(edgeGatewayEntity.getTenantId()) != null){
                upgradeRangeVO.setTenantName(tenantMap.get(edgeGatewayEntity.getTenantId()));
            }else {
                upgradeRangeVO.setTenantName("-");
            }
            upgradeRangeVOList.add(upgradeRangeVO);
        }
        return Result.ok(upgradeRangeVOList);
    }
    
    private CreateUpgradePackageRep createUpgradePackageRep() {
        ApplicationHome h = new ApplicationHome(this.getClass());
        File tenantFile = new File(h.getSource().getParentFile().getParent() + "/upgradePackage", "otaZip");
        //tenantFile.setExecutable(true);
        if (!tenantFile.exists()) {
            boolean mkdirs = tenantFile.mkdirs();
            if (!mkdirs) {
                throw new BizException("创建工程文件目录异常");
            }
        }
        long id = IdGenerator.generateId();
        File upgradePackageFile = new File(tenantFile, id + ".zip");
        //tenantFile.setExecutable(true);
        try {
            boolean newFile = upgradePackageFile.createNewFile();
            if (!newFile) {
                throw new BizException("创建工程文件异常");
            }
        } catch (IOException e) {
            throw new BizException("创建工程文件异常");
        }
        return CreateUpgradePackageRep.builder()
            .id(id)
            .file(upgradePackageFile)
            .build();
    }
    
    /**
     * 判断名称是否唯一
     *
     * @param id
     * @param name
     * @return
     */
    private Result<Void> uniqueName(Long id, String name) {
        LambdaQueryWrapper<UpgradePackageEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, UpgradePackageEntity::getId, id)
            .eq(UpgradePackageEntity::getUpgradePackageName, name);
        if (upgradePackageMapper.selectCount(lqw) > 0) {
            return Result.error("已经存在该名称的,名称：" + name);
        }
        return Result.ok();
    }
    
    /**
     * 判断版本号是否唯一
     *
     * @param id
     * @param version
     * @return
     */
    private Result<Void> uniqueVersion(Long id, String version) {
        LambdaQueryWrapper<UpgradePackageEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, UpgradePackageEntity::getId, id)
            .eq(UpgradePackageEntity::getUpgradeVersion, version);
        if (upgradePackageMapper.selectCount(lqw) > 0) {
            return Result.error("已经存在该版本号,版本号：" + version);
        }
        return Result.ok();
    }
    
    @Override
    public Result<String> updateLogView(Long edgeGatewayId, Long tenantId) {
        return edgeGatewayControlProxy.updateLogView(edgeGatewayId,tenantId);
    }
    
    @Override
    public Result<Void> downloadUpgradePackage(Long edgeGatewayId, Long tenantId) {
        String instance = stringRedisTemplate.opsForValue().get(RedisConstant.INSTANCE + tenantId + ":" + edgeGatewayId);
        if(StringUtils.isEmpty(instance)){
            return Result.error("执行下载，instance不能为空");
        }
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayService.getById(edgeGatewayId);
        if(StringUtils.isEmpty(edgeGatewayEntity.getTargetVersion())){
            return Result.error("执行下载，目标版本不能为空");
        }
        edgeGatewayEntity.setUpgradeStatus(UpgradeStatusEnum.DO_DOWNLOAD_STATUS.getValue());
        edgeGatewayEntity.setUpgradeBeginTime(LocalDateTime.now());
        edgeGatewayService.updateById(edgeGatewayEntity);
        UpgradePackageEntity upgradePackageEntity = getUpgradePackageEntity(edgeGatewayEntity.getTargetVersion()).getResult();
        if(ObjectUtil.isNull(upgradePackageEntity)){
            return Result.error("不存在该目标版本的升级包配置，目标版本为: " + edgeGatewayEntity.getTargetVersion());
        }
        String url = otaDownloadUrl + upgradePackageEntity.getId() + Constant.ZIP_SUFFIX;
        return edgeGatewayControlProxy.downloadUpgradePackage(edgeGatewayId,tenantId,url,edgeGatewayEntity.getTargetVersion(),upgradePackageEntity.getMd5Proofread(),instance);
    }

    @Override
    public Result<UpgradePackageEntity> getUpgradePackageEntity(String upgradeVersion){
        LambdaQueryWrapper<UpgradePackageEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UpgradePackageEntity::getUpgradeVersion, upgradeVersion)
            .eq(UpgradePackageEntity::getDeleted, 0);
        List<UpgradePackageEntity> upgradePackageEntityList = upgradePackageMapper.selectList(lqw);
        if(CollectionUtil.isEmpty(upgradePackageEntityList) || upgradePackageEntityList.size() > 1){
            return Result.ok();
        }
        return Result.ok(upgradePackageEntityList.get(0));
    }
    
    @Override
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.GATEWAY,details = "OTA网关升级")
    public Result<Void> executeUpgrade(Long edgeGatewayId, Long tenantId) {
        String instance = stringRedisTemplate.opsForValue().get(RedisConstant.INSTANCE + tenantId + ":" + edgeGatewayId);
        if(StringUtils.isEmpty(instance)){
            return Result.error("执行更新，instance不能为空");
        }
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayService.getById(edgeGatewayId);
        edgeGatewayEntity.setUpgradeStatus(UpgradeStatusEnum.DO_UPGRADE_STATUS.getValue());
        edgeGatewayService.updateById(edgeGatewayEntity);
        return edgeGatewayControlProxy.executeUpgrade(edgeGatewayId,tenantId,edgeGatewayEntity.getTargetVersion(),instance);
    }
    
    @Override
    public Result<String> batchDownload(List<BatchUpgradePackageDTO> batchUpgradePackageDTOList) {
        StringBuilder resultStr = new StringBuilder();
        for(BatchUpgradePackageDTO batchUpgradePackageDTO : batchUpgradePackageDTOList){
            Result<Void> result = downloadUpgradePackage(batchUpgradePackageDTO.getEdgeGatewayId(),batchUpgradePackageDTO.getTenantId());
            if(!result.getSignal()){
                resultStr.append(batchUpgradePackageDTO.getEdgeGatewayId() + result.getMessage() + ",");
                log.error("batchDownload fail and edgeGatewayId is {}",batchUpgradePackageDTO.getEdgeGatewayId());
            }
        }
        return Result.ok(resultStr.toString());
    }
    
    @Override
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.GATEWAY,details = "批量OTA网关升级")
    public Result<String> batchExecuteUpgrade(List<BatchUpgradePackageDTO> batchUpgradePackageDTOList) {
        StringBuilder resultStr = new StringBuilder();
        for(BatchUpgradePackageDTO batchUpgradePackageDTO : batchUpgradePackageDTOList){
            Result<Void> result = executeUpgrade(batchUpgradePackageDTO.getEdgeGatewayId(),batchUpgradePackageDTO.getTenantId());
            if(!result.getSignal()){
                resultStr.append(batchUpgradePackageDTO.getEdgeGatewayId() + result.getMessage() + ",");
                log.error("batchExecuteUpgrade fail and edgeGatewayId is {}",batchUpgradePackageDTO.getEdgeGatewayId());
            }
        }
        return Result.ok(resultStr.toString());
    }
}
