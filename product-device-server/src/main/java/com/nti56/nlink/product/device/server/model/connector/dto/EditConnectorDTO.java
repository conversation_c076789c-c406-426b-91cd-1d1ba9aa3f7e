package com.nti56.nlink.product.device.server.model.connector.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 类说明: <br/>
 * <AUTHOR> <br/>
 * @version 1.0
 * @since JDK 1.8
 */

@Data
@Schema(description = "编辑连接器")
public class EditConnectorDTO {
    
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @NotNull(message = "id不能为空")
    private Long id;
    
    /**
     * 连接器名称
     */
    @Schema(description = "连接器名称")
    @NotBlank(message = "连接器名称不能为空")
    @Length(max = 256, message = "连接器名称不能超过256个字符")
    private String name;
    
    /**
     * 连接器描述
     */
    @Schema(description = "连接器描述")
    private String descript;
    
    /**
     * 模式类型 0-接收;1-发送
     */
    @Schema(description = "模式类型 0-接收;1-发送")
    @NotNull(message = "模式类型不能为空")
    private Integer modeType;
    
    /**
     * 连接器类型 0-MQTT;1-HTTP
     */
    @Schema(description = "连接器类型 0-MQTT;1-HTTP")
    @NotNull(message = "连接器类型不能为空")
    private Integer connectorType;
    
    /**
     * 连接器状态 0-关闭;1-启用
     */
    @Schema(description = "连接器状态 0-停用;1-启用")
    private Integer status;
    
    /**
     * 连接器信息明细
     */
    @Schema(description = "连接器信息明细")
    @NotBlank(message = "连接器信息明细不能为空")
    private String connectorInfo;

    /**
     * 网关ID
     */
    @Schema(description = "网关ID")
    private Long edgeGatewayId;
}
