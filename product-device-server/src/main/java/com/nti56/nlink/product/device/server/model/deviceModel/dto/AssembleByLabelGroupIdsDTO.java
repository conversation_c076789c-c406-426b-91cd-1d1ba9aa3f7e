package com.nti56.nlink.product.device.server.model.deviceModel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@Schema(description = "批量标签创建设备参数")
public class AssembleByLabelGroupIdsDTO {



    @Schema(description = "继承物模型id列表")
    @NotNull(message = "至少选择一个模型继承")
    @Size(min = 1,message = "至少选择一个模型继承")
    private List<Long> inheritThingModelIds;

    @Schema(description = "标签分组id列表")
    @NotNull(message = "至少选择一个标签分组")
    @Size(min = 1,message = "至少选择一个标签分组")
    private List<Long> labelGroupIds;

}
