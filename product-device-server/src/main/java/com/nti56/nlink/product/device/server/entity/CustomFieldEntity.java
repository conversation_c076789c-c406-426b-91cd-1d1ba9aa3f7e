package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.nti56.nlink.product.device.client.model.dto.json.CustomFieldDynamicLengthConfigField;
import com.nti56.nlink.product.device.client.model.dto.json.CustomFieldTransformField;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 自定义字段表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-11-18 11:17:00
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("custom_field")
@Schema(description = "自定义字段表")
public class CustomFieldEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @Schema(description = "id")
    @TableId(value = "id")
    private Long id;

    /**
     * 所属类型
     */
    @Schema(description = "所属类型")
    private Integer targetType;

    /**
     * 所属id
     */
    @Schema(description = "所属id")
    private Long targetId;

    @Schema(description = "部分类型")
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Integer partType;

    /**
     * 排序号
     */
    @Schema(description = "排序号")
    private Integer sortNo;

    /**
     * 字段名称
     */
    @Schema(description = "字段名称")
    private String fieldName;

    /**
     * 描述
     */
    @Schema(description = "描述")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String fieldDescript;

    /**
     * 开始byte位置，单位byte，从0开始
     */
    @Schema(description = "开始byte位置，单位byte，从0开始")
    private Integer startByte;

    /**
     * 开始位索引，单位bit，范围0-7,null为0
     */
    @Schema(description = "开始位索引，单位bit，范围0-7,null为0")
    private Integer startBit;

    @Schema(description = "结束byte位置，单位byte")
    private Integer endByte;

    @Schema(description = "结束位索引，单位bit，范围0-7,null为0")
    private Integer endBit;

    /**
     * 数据类型，参考kep数据类型，ThingDataTypeEnum
     */
    @Schema(description = "数据类型，参考kep数据类型，ThingDataTypeEnum")
    private Integer dataType;

    /**
     * 字节长度，当dataType为string或byte才有效
     */
    @Schema(description = "字节长度，当dataType为string或byte才有效")
    private Integer bytes;

    /**
     * 字段类型
     */
    @Schema(description = "字段类型")
    private Integer fieldType;

    @Schema(description = "检查类型")
    private Integer checkType;

    /**
     * 当字段类型为常量时，指定的常量值
     */
    @Schema(description = "当字段类型为常量时，指定的常量值。当字段类型为字节长度时，需要计算的入参字段，逗号分隔。当字段类型为数量时，需要计算的入参字段，逗号分隔")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String fieldValue;

    @Schema(description = "字段内容转换")
    private CustomFieldTransformField fieldTransform;
    
    @Schema(description = "动态长度参数配置")
    private CustomFieldDynamicLengthConfigField dynamicLengthConfig;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

    /**
     * 删除
     */
    @Schema(description = "删除")
    @TableLogic
    private Integer deleted;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @Schema(description = "工程ID")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @Schema(description = "模块ID")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @Schema(description = "空间ID")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;



}
