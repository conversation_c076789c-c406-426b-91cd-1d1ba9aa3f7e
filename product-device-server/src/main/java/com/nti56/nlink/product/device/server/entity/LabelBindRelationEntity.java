package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 标签绑定关系表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-11 17:27:49
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("label_bind_relation")
@Schema
public class LabelBindRelationEntity  implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 关联的直属的物模型 方便物模型更改时查找影响
     */
    @Schema(description = "关联的直属的模型 方便物模型更改时查找影响")
    private Long directlyModelId;

    @Schema(description = "属性直属模型类型")
    private Integer modelType;

    /**
     * 绑定标签
     */
    @Schema(description = "绑定标签")
    private Long labelId;
    
    /**
     * 上一次绑定标签
     */
    @Schema(description = "上一次绑定标签")
    private Long preLabelId;

    /**
     * 所属设备
     */
    @Schema(description = "所属设备")
    private Long deviceId;

    /**
     * 属性名，应该是带层级的，可以直接匹配的
     */
    @Schema(description = "属性名，应该是带层级的，可以直接匹配的")
    private String propertyName;

    @Schema(description = "属性绑定的数据来源完整路径，labelName")
    private String labelName;

    @Schema(description = "属性绑定的数据来源完整路径，labelGroupName")
    private String labelGroupName;

    @Schema(description = "属性绑定的数据来源完整路径，channelName")
    private String channelName;

    @Schema(description = "标签所属网关id")
    private Long edgeGatewayId;

    /**
     * 关联的直属数据模型 方便数据模型更改是查找影响（如果属性是数据模型里的）
     */
    @Schema(description = "关联的直属数据模型 方便数据模型更改是查找影响（如果属性是数据模型里的）")
    private Long dataModelId;

    @Schema(description = "逻辑删除，1-删除")
    @TableLogic
    private Boolean deleted;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 工程ID
     */
    @Schema(description = "工程ID")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @Schema(description = "模块ID")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @Schema(description = "空间ID")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

}
