package com.nti56.nlink.product.device.server.util;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/10 9:29<br/>
 * @since JDK 1.8
 */
public class DateUtil {

    public static String timestamp2String(String timestamp){
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sd = sdf.format(new Date(Long.parseLong(timestamp)));      // 时间戳转换成时间
        return sd;
    }
}
