package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.model.ChannelDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-18 17:43:46
 * @since JDK 1.8
 */
public interface ChannelMapper extends CommonMapper<ChannelEntity> {

    Page<ChannelDto> getChannelByPage(IPage<ChannelDto> page, @Param("channel") ChannelDto channel,@Param("tenantIsolation") TenantIsolation tenantIsolation);

    @Select("SELECT * FROM channel WHERE tenant_id = #{tenantId} AND id = #{id} AND deleted = 0")
    ChannelEntity getById(@Param("tenantId") Long tenantId, @Param("id") Long id);
    
    @Select("SELECT id FROM channel WHERE tenant_id = #{tenantId} AND edge_gateway_id = #{edgeGatewayId} AND deleted = 0")
    List<Long> listIdByEdgeGatewayId(@Param("tenantId") Long tenantId, @Param("edgeGatewayId") Long edgeGatewayId);

    List<ChannelRuntimeInfoField> listRuntimeInfo(@Param("tenantId") Long tenantId, @Param("edgeGatewayId") Long edgeGatewayId);

    @Update("UPDATE channel SET runtime_info = #{runtimeInfo} WHERE id = #{id} AND tenant_id = #{tenantId}")
    Integer updateRuntimeInfo(@Param("tenantId") Long tenantId, 
                                @Param("id") Long id, 
                                @Param("runtimeInfo") ChannelRuntimeInfoField runtimeInfo);

    ChannelEntity getByLabelGroupId(@Param("labelGroupId") Long labelGroupId);

    ChannelEntity getByLabelId(@Param("labelId")Long labelId);

    @Update("UPDATE channel SET status = #{status} where id = #{id} AND tenant_id = #{tenantId} AND edge_gateway_id = #{edgeGatewayId} AND DELETED = 0")
    Integer statusChange(@Param("id") Long channelId,@Param("edgeGatewayId") Long edgeGatewayId,@Param("tenantId") Long tenantId,@Param("status") Integer status);

    @Update("UPDATE channel SET interval_ms = #{intervalMs} where id = #{id} AND tenant_id = #{tenantId} AND edge_gateway_id = #{edgeGatewayId} AND DELETED = 0")
    Integer modifyChannelInterval(@Param("id") Long channelId,@Param("edgeGatewayId") Long edgeGatewayId,@Param("intervalMs") Integer intervalMs,@Param("tenantId") Long tenantId);

    void deleteAllByTenantId(@Param("tenantId")Long tenantId);

    @Select("SELECT * FROM channel WHERE tenant_id = #{tenantId} AND custom_driver_name = #{driverName} AND DELETED = 0 AND driver = 100")
    List<ChannelEntity> listCustomDriverChannelByName(@Param("tenantId")Long tenantId, @Param("driverName")String driverName);

    void physicalDeleteByEdgeGatewayId(@Param("edgeGatewayId")Long edgeGatewayId);
}
