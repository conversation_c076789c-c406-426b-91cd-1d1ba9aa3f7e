package com.nti56.nlink.product.device.server.model.label.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/4/17 15:55<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "标签去重VO")
public class LabelDuplicateRemovalVO {

    private Long id;

    @Schema(description = "标签名称")
    private String name;

    @Schema(description = "数据类型，bool/byte/short/int/float/string")
    private String dataType;

    @Schema(description = "是否数组")
    private Boolean isArray;

    @Schema(description = "是否只读")
    private Boolean readOnly;

    @Schema(description = "标签名称是否重复")
    private Boolean isRepeat;

}
