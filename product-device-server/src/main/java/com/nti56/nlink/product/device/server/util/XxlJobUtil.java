package com.nti56.nlink.product.device.server.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.model.JobInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.HttpCookie;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 类说明：定时任务管理工具类
 *
 * @ClassName XxlJobUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/3/6 16:48
 * @Version 1.0
 */
@Slf4j
public class XxlJobUtil {

    /**
     * @param url
     * @param jobInfo
     * @return
     */
    public static Long add(String url, JobInfoDTO jobInfo) throws IOException {
        HttpEntity<MultiValueMap<String, String>> entity = buildHttpEntity(jobInfo);
        RestTemplate restTemplate = ApplicationContextUtil.getBean("restTemplate", RestTemplate.class);
        ResponseEntity<String> response = restTemplate.postForEntity(
                url,
                entity,
                String.class
        );
        String body = response.getBody();
        log.info("xxl-create-job-response-body:{}", body);
        JSONObject jsonObject = JSON.parseObject(body);
        int code = jsonObject.getIntValue("code");
        if (code == 200) {
            Long jobId = jsonObject.getLongValue("content");
            log.info("create job success,job id:{}", jobId);
            return jobId;
        }
        throw new BizException("create job error,code:" + code + ",msg:" + jsonObject.getString("msg"));
    }


    private static HttpEntity<MultiValueMap<String, String>> buildHttpEntity(JobInfoDTO jobInfo) {
        HttpHeaders headers = buildHeaderParamMap(jobInfo);
        MultiValueMap<String, String> paramMap = buildRequestParams(jobInfo);
        return new HttpEntity<>(paramMap, headers);
    }

    private static MultiValueMap<String, String> buildRequestParams(JobInfoDTO jobInfo) {
        MultiValueMap<String, String> paramMap = new LinkedMultiValueMap<>();
        paramMap.add("jobGroup", String.valueOf(jobInfo.getJobGroup()));
        paramMap.add("jobDesc", jobInfo.getJobDesc());
        paramMap.add("author", "system");
        paramMap.add("scheduleType", "CRON");
        paramMap.add("scheduleConf", jobInfo.getCron());
        paramMap.add("cronGen_display", jobInfo.getCron());
        paramMap.add("glueType", "BEAN");
        paramMap.add("executorHandler", jobInfo.getTaskHandler());

        paramMap.add("executorParam", jobInfo.getJobParam());
        paramMap.add("executorRouteStrategy", "FIRST");
        paramMap.add("misfireStrategy", "DO_NOTHING");
        paramMap.add("executorBlockStrategy", "SERIAL_EXECUTION");
        paramMap.add("executorTimeout", "0");
        paramMap.add("executorFailRetryCount", "0");
        if (!Objects.isNull(jobInfo.getJobId())) {
            paramMap.add("id", String.valueOf(jobInfo.getJobId()));
        }
        return paramMap;
    }

    private static HttpHeaders buildHeaderParamMap(JobInfoDTO jobInfo) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("cookie", getCookie(jobInfo.getAdminUrl(), jobInfo.getUserName(), jobInfo.getPassword()));
        headers.add("Accept", "*/*");
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        return headers;
    }

    /**
     * @param url
     * @param jobInfo
     */
    public static boolean remove(String url, JobInfoDTO jobInfo) {
        HttpHeaders headers = buildHeaderParamMap(jobInfo);
        MultiValueMap<String, String> paramMap = new LinkedMultiValueMap<>();
        paramMap.add("id", String.valueOf(jobInfo.getJobId()));

        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(paramMap, headers);
        RestTemplate restTemplate = ApplicationContextUtil.getBean("restTemplate", RestTemplate.class);
        ResponseEntity<String> response = restTemplate.postForEntity(
                url,
                entity,
                String.class
        );
        String body = response.getBody();
        JSONObject jsonObject = JSON.parseObject(body);
        int code = jsonObject.getIntValue("code");
        if (code == 200) {
            log.info("delete job success,job id:{}", jobInfo.getJobId());
            return true;
        }
        log.warn("delete job error,code:{},msg:{}", code, jsonObject.getString("msg"));
        return false;

    }

    public static String getCookie(String adminAddress, String userName, String password) {
        String path = adminAddress + "/login";
        Map<String, Object> hashMap = Maps.newHashMap();
        hashMap.put("userName", userName);
        hashMap.put("password", password);
        HttpResponse response = HttpRequest.post(path).form(hashMap).execute();
        List<HttpCookie> cookies = response.getCookies();
        StringBuilder sb = new StringBuilder();
        for (HttpCookie cookie : cookies) {
            sb.append(cookie.toString());
        }
        return sb.toString();
    }

    public static boolean start(String url, JobInfoDTO jobInfo) {
        MultiValueMap<String, String> paramMap = new LinkedMultiValueMap<>();
        paramMap.add("id", String.valueOf(jobInfo.getJobId()));


        HttpHeaders headers = buildHeaderParamMap(jobInfo);

        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(paramMap, headers);
        RestTemplate restTemplate = ApplicationContextUtil.getBean("restTemplate", RestTemplate.class);


        ResponseEntity<String> response = restTemplate.postForEntity(
                url,
                entity,
                String.class
        );
        JSONObject jsonObject = JSON.parseObject(response.getBody());
        int code = jsonObject.getIntValue("code");
        if (code != 200) {
            log.error("启动定时器异常，定时器id:{}", jobInfo.getJobId());
        }
        return code == 200;
    }
}
