package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 所属数据模型id 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 14:23:50
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("data_model_property")
@Schema( description = "数据模型属性表")
public class DataModelPropertyEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id")
    private Long id;

    /**
     * 属性名称，英文数字下划线，英文开头
     */
    @Schema(description = "属性名称，英文数字下划线，英文开头")
    private String name;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String descript;

    /**
     * 数据类型，1-bool,2-byte,3-short,4-int,5-float,6-string,7-dataModel
     */
    @Schema(description = "数据类型，1-bool,2-byte,3-short,4-int,5-float,6-string,7-dataModel")
    private Integer dataType;

    /**
     * 是否是数组，0-否，1-是
     */
    @Schema(description = "是否是数组，0-否，1-是")
    private Boolean isArray;

    /**
     * 属性数据模型id，dataModel类型才能设置
     */
    @Schema(description = "属性数据模型id，dataModel类型才能设置")
    private Long propertyDataModelId;

    /**
     * 默认值，基础类型才能设置
     */
    @Schema(description = "默认值，基础类型才能设置")
    private String defaultValue;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;
    /**
     * 工程id
     */
    @Schema(defaultValue = "工程id")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 空间id
     */
    @Schema(description = "空间id")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 模块id
     */
    @Schema(description = "模块id")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @Schema(description = "修改人id")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 修改人名称
     */
    @Schema(description = "修改人名称")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

    /**
     * 所属数据模型id
     */
    @Schema(description = "所属数据模型id")
    private Long dataModelId;

    @Schema(description = "逻辑删除，1-删除")
    @TableLogic
    private Boolean deleted;



}
