package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: 连接器表
 * <AUTHOR>
 * @version 1.0
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("connector")
@Schema(description = "连接器")
public class ConnectorEntity {
    
    /**
     * id
     */
    private Long id;
    
    /**
     * 连接器名称
     */
    @Schema(description = "连接器名称")
    private String name;
    
    /**
     * 连接器描述
     */
    @Schema(description = "连接器描述")
    private String descript;
    
    /**
     * 模式类型 0-接收;1-发送
     */
    @Schema(description = "模式类型 0-接收;1-发送")
    private Integer modeType;
    
    /**
     * 连接器类型 0-MQTT;1-HTTP
     */
    @Schema(description = "连接器类型 0-MQTT;1-HTTP")
    private Integer connectorType;
    
    /**
     * 连接器状态 0-关闭;1-启用
     */
    @Schema(description = "连接器状态 0-停用;1-启用")
    private Integer status;
    
    /**
     * 连接器信息明细
     */
    @Schema(description = "连接器信息明细")
    private String connectorInfo;

    /**
     * 网关ID
     */
    @Schema(description = "网关ID")
    private Long edgeGatewayId;
    
    /**
     * 工程ID
     */
    @Schema(description = "工程ID")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;
    
    /**
     * 空间ID
     */
    @Schema(description = "空间ID")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;
    
    /**
     * 模块ID
     */
    @Schema(description = "模块ID")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;
    
    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;
    
    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;
    
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    
    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;
    
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 逻辑删除，1-删除
     */
    @Schema(description = "逻辑删除，1-删除")
    @TableLogic
    private Boolean deleted;
    
    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;
}
