package com.nti56.nlink.product.device.server.openapi.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.it.ITResult;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DriverEnum;
import com.nti56.nlink.product.device.server.entity.DeviceTemplateEntity;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateBo;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateCreateDeviceReq;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateRespondBo;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateThingPropertyBo;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateThingPropertyReq;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateValidRep;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateValidReq;
import com.nti56.nlink.product.device.server.openapi.convertor.ITResultConvertor;
import com.nti56.nlink.product.device.server.service.IDeviceStatusManagementService;
import com.nti56.nlink.product.device.server.service.IDeviceTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 类说明: 设备controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:04
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/it/device/template")
@Tag(name = "设备模板开放接口")
public class ITDeviceTemplateController {

    @Autowired
    IDeviceStatusManagementService deviceStatusManagementService;

    @Autowired
    IDeviceTemplateService service;

    @PostMapping("list")
    @Operation(summary = "获取设备模板列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceTemplateEntity.class)
                    )})
    })
    public ITResult<List<DeviceTemplateEntity>> list(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody DeviceTemplateEntity entity) {
        BeanUtilsIntensifier.propertyInjection(tenantIsolation,entity);
        Result<List<DeviceTemplateEntity>> result = service.list(entity);
        return ITResultConvertor.convert(R.result(result));
    }

    @PostMapping("valid")
    @Operation(summary = "获取设备模板列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceTemplateEntity.class)
                    )})
    })
    public ITResult<List<DeviceTemplateValidRep>> valid(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @Validated @RequestBody DeviceTemplateValidReq validReq) {
        Result<DeviceTemplateValidRep> result = service.valid(tenantIsolation,validReq);
        return ITResultConvertor.convert(R.result(result));
    }
    @PostMapping("one/{templateId}")
    @Operation(summary = "获取设备模板参数")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceTemplateBo.class)
                    )})
    })
    public ITResult<DeviceTemplateBo> one(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable("templateId") Long templateId) {
        Result<DeviceTemplateBo> result = service.getTemplateInfo(tenantIsolation,templateId);
        return ITResultConvertor.convert(R.result(result));
    }


    @PostMapping("createByTemplate")
    @Operation(summary = "通过设备模板创建设备_新")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceTemplateBo.class)
                    )})
    })
    public ITResult<Long> createByTemplate2(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,  @RequestBody DeviceTemplateCreateDeviceReq createDeviceReq) {
        Result<Long> result = service.createByTemplate2(tenantIsolation, createDeviceReq);
        return ITResultConvertor.convert(R.result(result));
    }


    @PostMapping("getTemplateThingModel")
    @Operation(summary = "获取模板中的物模型信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceTemplateBo.class)
                    )})
    })
    public ITResult<List<DeviceTemplateThingPropertyBo>> getTemplateThingModel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,  @RequestBody DeviceTemplateThingPropertyReq req) {
        Result<List<DeviceTemplateThingPropertyBo>> result = service.getTemplateThingModel(tenantIsolation, req);
        return ITResultConvertor.convert(R.result(result));
    }

    @PostMapping("batchCopyTemplate")
    @Operation(summary = "批量复制模板")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceTemplateBo.class)
                    )})
    })
    public ITResult<Map<Long,Long>> batchCopyTemplate(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,  @RequestBody DeviceTemplateThingPropertyReq req) {
        Result<Map<Long,Long>> result = service.batchCopyTemplate(tenantIsolation, req);
        return ITResultConvertor.convert(R.result(result));
    }

    @PostMapping("create/{templateId}/{edgeGatewayId}")
    @Operation(summary = "通过设备模板创建设备")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceTemplateBo.class)
                    )})
    })
    public ITResult<DeviceTemplateBo> createByTemplate(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable("templateId") Long templateId, @PathVariable("edgeGatewayId") Long edgeGatewayId, @RequestBody DeviceTemplateBo requestBo) {
        Result<DeviceTemplateBo> result = service.createByTemplate(tenantIsolation,templateId,edgeGatewayId,requestBo);
        return ITResultConvertor.convert(R.result(result));
    }



    @PostMapping("batch/delete")
    @Operation(summary = "按条件批量删除设备、通道、及标签")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceTemplateRespondBo.class)
                    )})
    })
    public ITResult<DeviceTemplateRespondBo> deviceBatchDelete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceTemplateBo requestBo) {
        DeviceTemplateRespondBo deviceTemplateRespondBo = service.batchDelete(tenantIsolation,requestBo);
        return ITResultConvertor.convert(R.result(Result.ok(deviceTemplateRespondBo)));
    }

    @PostMapping("batch/update")
    @Operation(summary = "按条件批量修改设备、通道")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceTemplateRespondBo.class)
                    )})
    })
    @Transactional
    public ITResult<DeviceTemplateRespondBo> deviceBatchUpdate(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceTemplateBo requestBo) {
        DeviceTemplateRespondBo deviceTemplateRespondBo = service.batchUpdate(tenantIsolation,requestBo);
        return ITResultConvertor.convert(R.result(Result.ok(deviceTemplateRespondBo)));
    }

    @GetMapping("channel/driverType")
    @Operation(summary = "获取通道类型")
    public ITResult<List<Map<String,Object>>> getDriverType(){
        List<Map<String,Object>> list = DriverEnum.toList();
        return ITResultConvertor.convert(R.ok(list));
    }

}
