package com.nti56.nlink.product.device.server.model.connectorItem.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 * <AUTHOR> <br/>
 * @version 1.0
 * @since JDK 1.8
 */
@Data
@Schema(description = "连接器详情项查询")
public class QueryConnectorItemDTO {
    
    @Schema(description = "连接器详情项名称")
    private String name;
    
    @Schema(description = "连接器Id")
    private Long connectorId;
}
