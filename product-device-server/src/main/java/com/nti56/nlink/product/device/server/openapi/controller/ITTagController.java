package com.nti56.nlink.product.device.server.openapi.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.it.ITResult;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.openapi.convertor.ITResultConvertor;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListTagRequest;
import com.nti56.nlink.product.device.server.service.ITagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/12/19 9:58<br/>
 * @since JDK 1.8
 */
@RestController
@RequestMapping("it/tags")
@Tag( name = "标记模块")
public class ITTagController {

    @Autowired
    private ITagService tagService;


    @PostMapping("/list")
    @Operation(summary = "获取tag列表" )
    public ITResult<List<com.nti56.nlink.product.device.server.entity.Tag>> listTag(@RequestHeader("ot_headers") TenantIsolation tenantIsolation
            , @RequestBody @Validated ListTagRequest request){
        return ITResultConvertor.convert(R.result(tagService.listTag(tenantIsolation,request)));
    }


    @PostMapping("/listAll")
    @Operation(summary = "获取全部tag列表" )
    public ITResult<List<com.nti56.nlink.product.device.server.entity.Tag>> listTag(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return ITResultConvertor.convert(R.result(tagService.listAll(tenantIsolation)));
    }
}
