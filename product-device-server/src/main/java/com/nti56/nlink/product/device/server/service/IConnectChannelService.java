package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.model.ConnectResult;
import java.util.List;
import java.util.concurrent.Future;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-11-30 15:15:15
 * @since JDK 1.8
 */
public interface IConnectChannelService {

    Future<Result<List<ConnectResult>>> connectGatewayChannel(ChannelEntity channelEntity, List<ChannelElm> channelElmList);

    Future<Result<List<ConnectResult>>> connectCacheGatewayChannel(ChannelEntity channelEntity, List<ChannelElm> channelElmList);
}
