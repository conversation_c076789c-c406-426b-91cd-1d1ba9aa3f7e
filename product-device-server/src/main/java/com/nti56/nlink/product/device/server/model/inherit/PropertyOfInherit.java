package com.nti56.nlink.product.device.server.model.inherit;

import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PropertyOfInherit extends PropertyElm {

    @Schema(description = "直属设备模型id")
    private Long baseDeviceId;

    @Schema(description = "直属设备模型名称")
    private String baseDeviceName;
    
    @Schema(description = "直属物模型id")
    private Long baseThingModelId; 

    @Schema(description = "直属物模型名称")
    private String baseThingModelName;


}
