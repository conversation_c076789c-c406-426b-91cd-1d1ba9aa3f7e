package com.nti56.nlink.product.device.server.model.edgegateway.dto;

import com.nti56.nlink.product.device.server.model.connector.dto.EditGatewayConnectorDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/10/13 15:39<br/>
 * @since JDK 1.8
 */
@Data
public class EditEdgeGatewayMonitorDTO {

    private Long edgeGatewayId;

    /**
     * 内存监控百分比
     */
    @Schema(description = "网关内存监控百分比")
    private Integer memoryMonitor;

    /**
     * 硬件空间监控百分比
     */
    @Schema(description = "网关空间监控百分比")
    private Integer spaceMonitor;

}
