package com.nti56.nlink.product.device.server.model.deviceTemplate;

import com.nti56.nlink.product.device.server.model.ThingModelDto;
import com.nti56.nlink.product.device.server.model.channel.dto.CreateChannelDTO;
import com.nti56.nlink.product.device.server.model.label.dto.ImportLabelDTO;
import com.nti56.nlink.product.device.server.model.label.dto.LabelDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "设备模板创建数据传输对象")
public class DeviceTemplateBuildDTO {
    @Schema(description = "设备模板名称")
    private String name;

    @Schema(description = "模板描述")
    private String descript;

    @Schema(description = "协议类型驱动")
    private Integer driver;

    @Schema(description = "选择导入文件的标签列表")
    private List<LabelDTO> labelList;

    @Schema(description = "设备模板要绑定的物模型id集合")
    private List<Long> thingModelIdList;

}
