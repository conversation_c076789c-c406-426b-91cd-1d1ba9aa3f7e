package com.nti56.nlink.product.device.server.verticle;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwSyncTopic;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;
import com.nti56.nlink.product.device.server.service.ITaskService;

import io.vertx.core.Promise;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import java.util.concurrent.Executor;

import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

import org.springframework.beans.factory.annotation.Value;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-21 17:19:00
 * @since JDK 1.8
 */
@Component
@Scope(SCOPE_PROTOTYPE)
@Slf4j
public class MqttSyncConsumerVerticle extends MqttBaseVerticle {
    
    @Getter
    @Value("${mqtt.host}")
    private String host;

    @Getter
    @Value("${mqtt.port}")
    private Integer port;

    @Getter
    @Value("${mqtt.username}")
    private String username;

    @Getter
    @Value("${mqtt.password}")
    private String password;

    @Getter
    @Value("${mqtt.ssl:true}")
    private Boolean ssl;
    
    @Getter
    private Integer reconnectGapTime;
    
    @Autowired
    private IEdgeGatewayService edgeGatewayService;

    @Autowired
    Executor collectingOnceAsyncExecutor;

    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        log.debug("start-verticle");
        super.start(startPromise);
    }

    @Override
    public void subscribe(){
        String syncTopic = GwSyncTopic.createSubscribeTopic(MqttBaseVerticle.shareSubscribeGroup);
        this.client.publishHandler(s1 -> {
            log.debug("synctopic");
            String topicName = s1.topicName();
            log.debug("synctopic1: {}", topicName);
            log.debug("synctopic2: {}", s1.payload());
            GwSyncTopic.TopicInfo topicInfo = GwSyncTopic.parseTopic(topicName);
            Long tenantId = topicInfo.getTenantId();
            Long edgeGatewayId = topicInfo.getEdgeGatewayId();
            TenantIsolation tenant = new TenantIsolation();
            tenant.setTenantId(tenantId);
            collectingOnceAsyncExecutor.execute(() -> edgeGatewayService.edgeGatewaySyncById(edgeGatewayId,tenant));
        });
        client.subscribe(syncTopic, 1);
    }

    @Override
    protected void handleConnectStatusChange(Boolean connected) {
       
    }
}
