package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.engineering.ProductDeviceServerDataDTO;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/4 9:48<br/>
 * @since JDK 1.8
 */
public interface IEngineeringProductService {
    Result<Void> deleteProductDataByTenantId(Long tenantId);

    Result<ProductDeviceServerDataDTO> getProductDataByTenantId(Long tenantId);

    Result<Void> createProductData(ProductDeviceServerDataDTO dto);

    Result<Void> initProductData(ProductDeviceServerDataDTO dto ,Long tenantId);
}
