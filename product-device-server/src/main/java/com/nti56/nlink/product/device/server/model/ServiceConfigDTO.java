package com.nti56.nlink.product.device.server.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 通用物服务配置表
 * @TableName service_config
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "ServiceConfigDTO对象")
public class ServiceConfigDTO implements Serializable {
    /**
     * 
     */
    private Long id;

    /**
     * 物服务ID
     */
    @Schema(description = "物服务ID")
    private Long thingServiceId;

    /**
     * 关联的直属的物模型 方便物模型更改时查找影响
     */
    @Schema(description = "关联的直属的物模型 方便物模型更改时查找影响")
    private Long thingModelId;

    /**
     * 所属设备
     */
    @Schema(description = "关联设备Id")
    private Long deviceId;

    /**
     * 服务配置 json string
     */
    @Schema(description = "服务配置")
    private JSONObject configMap;


    private static final long serialVersionUID = 1L;


}