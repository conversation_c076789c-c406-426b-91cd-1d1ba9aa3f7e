package com.nti56.nlink.product.device.server.model.upgrade.dto;

import com.nti56.nlink.product.device.client.model.dto.json.UpgradePackageRangeField;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/3/17 12:56<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "新增升级包配置")
public class CreateUpgradePackageDTO {
    
    /**
     * 升级包名称
     */
    @Schema(description = "升级包名称")
    @NotBlank(message = "升级包名称不能为空")
    @Length(max = 255,message = "升级包名称不能超过255个字符")
    private String upgradePackageName;
    
    /**
     * 升级包类型(0 普通,1 紧急)
     */
    @Schema(description = "升级包类型(0 普通,1 紧急)")
    @NotNull(message = "升级包类型不能为空")
    private Integer upgradeType;
    
    /**
     * 升级包版本
     */
    @Schema(description = "升级包版本")
    @NotBlank(message = "升级包名称不能为空")
    private String upgradeVersion;
    
    /**
     * 升级包描述
     */
    @Schema(description = "升级包描述")
    private String descript;
    
    /**
     * 是否期望版本(0 否,1 是)
     */
    @Schema(description = "是否期望版本(0 否,1 是)")
    @NotBlank(message = "是否期望版本不能为空")
    private Integer isExpectVersion;
    
    /**
     * 升级包范围
     */
    @Schema(description = "升级包范围")
    @NotEmpty(message = "至少选择一个范围")
    private UpgradePackageRangeField upgradeRange;

}
