package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.engineering.NotifyServiceDataDTO;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/4 13:40<br/>
 * @since JDK 1.8
 */
public interface IEngineeringNotifyService {

    Result<Void> deleteNotifyDataByTenantId(Long tenantId);

    Result<NotifyServiceDataDTO> getNotifyDataByTenantId(Long tenantId);

    Result<Void> createNotifyData(NotifyServiceDataDTO dto);
}
