package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.product.device.server.entity.ChangeNoticeEntity;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import com.nti56.nlink.product.device.server.entity.LabelBindRelationEntity;
import com.nti56.nlink.product.device.server.model.channel.vo.ChannelVO;
import java.util.List;
import java.util.Map;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-2-20 17:15:15
 * @since JDK 1.8
 */
public interface IAsyncExecutorService {
    
    void edgeGatewayStatusProcess(EdgeGatewayEntity edgeGatewayEntity, Map<Long, List<ChangeNoticeEntity>> changeNoticeEntityMap,Long connectorId);
    
    void deviceStatusProcess(DeviceEntity deviceEntity, List<ChangeNoticeEntity> noticeEntityList, Boolean gwOnline, Map<String,Boolean> channelStatusMap, Map<Object,Object> deviceStatusMap, Map<Object, Object> deviceOfflineTimesMap, String key, Map<Long, List<LabelBindRelationEntity>> groupedByDeviceId, Integer type);
    
    void channelStatusProcess(ChannelVO channelVO, String key, Map<String, Boolean> reportChannelStatusMap, Map<Object, Object> channelStatusMap);
    
}
