package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Stopwatch;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.DeviceRuntimeMetadataField;
import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.PropertyMetadataItem;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import com.nti56.nlink.product.device.server.annotation.AuditLog;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.domain.thing.dpo.PropertyDpo;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DriverEnum;
import com.nti56.nlink.product.device.server.domain.thing.label.Label;
import com.nti56.nlink.product.device.server.domain.thing.label.LabelGroup;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.enums.ActionEnum;
import com.nti56.nlink.product.device.server.enums.AuditTargetEnum;
import com.nti56.nlink.product.device.server.enums.ErrorEnum;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewaySpiProxy;
import com.nti56.nlink.product.device.server.mapper.AbsolutePathLabelMapper;
import com.nti56.nlink.product.device.server.mapper.ChannelMapper;
import com.nti56.nlink.product.device.server.mapper.LabelGroupMapper;
import com.nti56.nlink.product.device.server.mapper.LabelMapper;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.model.label.dto.*;
import com.nti56.nlink.product.device.server.model.label.vo.LabelDuplicateRemovalVO;
import com.nti56.nlink.product.device.server.model.label.vo.LabelVO;
import com.nti56.nlink.product.device.server.model.label.vo.PageLabelVO;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import com.nti56.nlink.common.util.JwtUserInfoUtils;
import java.util.Map.Entry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 14:11:44
 * @since JDK 1.8
 */
@Service
@Slf4j
public class LabelServiceImpl extends BaseServiceImpl<LabelMapper, LabelEntity> implements ILabelService {

    @Autowired
    LabelMapper labelMapper;

    @Autowired
    private Mapper dozerMapper;

    @Autowired
    private ILabelBindRelationService labelBindRelationService;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private ILabelGroupService labelGroupService;

    @Lazy
    @Autowired
    private IChannelService channelService;

    @Autowired
    private IEdgeGatewaySpiProxy edgeGatewaySpiProxy;

    @Autowired
    private CommonFetcherFactory commonFetcherFactory;

    @Lazy
    @Autowired
    private IEdgeGatewayService edgeGatewayService;

    @Autowired
    private ChannelMapper channelMapper;

    @Autowired
    private LabelGroupMapper labelGroupMapper;

    @Autowired
    private IThingModelService thingModelService;

    @Autowired
    private TransactionDefinition transactionDefinition;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Override
    public Result<Void> batchUpdateGatherParam(Long tenantId, List<GatherParamField> gatherParamList){
        if(gatherParamList == null || gatherParamList.size() <= 0){
            return Result.ok();
        }
        List<GatherParamField> subList = new ArrayList<>();
        int i = 0;
        for(GatherParamField item:gatherParamList){
            subList.add(item);
            i++;
            if(i > 100){
                labelMapper.batchUpdateGatherParam(tenantId, subList);
                subList = new ArrayList<>();
                i = 0;
            }
        }
        if(subList.size() > 0){
            labelMapper.batchUpdateGatherParam(tenantId, subList);
        }
        return Result.ok();
    }

    @Override
    @Transactional
    public Result<Void> createOrEditLabelList(Long labelGroupId, List<LabelDTO> labelList, TenantIsolation tenantIsolation) {
        log.info("开始批量创建标签，labelGroupId:{},labelList:{}", labelGroupId, labelList);
        StringBuilder uniqueNameSb = new StringBuilder();
        List<LabelEntity> addLabelEntities = new ArrayList<>();
        boolean error = false;
        HashSet<String> names = new HashSet<>();
        int setSize = 0;
        Set<String> nameSet = new LambdaQueryChainWrapper<>(labelMapper)
                .select(LabelEntity::getName)
                .eq(LabelEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(LabelEntity::getLabelGroupId, labelGroupId)
                .list().stream().map(LabelEntity::getName).collect(Collectors.toSet());

        for (LabelDTO labelDTO : labelList) {

            names.add(labelDTO.getName());
            if (++setSize != names.size()) {
                uniqueNameSb.append(labelDTO.getName()).append(";\n");
                setSize--;
                error = true;
                continue;
            }
            if (nameSet.contains(labelDTO.getName())) {
                uniqueNameSb.append(labelDTO.getName()).append(";\n");
                error = true;
                continue;
            }
            LabelEntity labelEntity = dozerMapper.map(labelDTO, LabelEntity.class);
            labelEntity.setLabelGroupId(labelGroupId);
            addLabelEntities.add(labelEntity);
        }
        if (error) {
            String errorMsg = "";
            if (uniqueNameSb.length() > 0) {
                errorMsg = errorMsg + "标签名称存在重复：\n" + uniqueNameSb.toString();
            }
            throw new BizException(errorMsg);
        }
        this.saveBatch(addLabelEntities);

        this.setEdgeGatewayNotSyncByLabelGroupId(labelGroupId);
        return Result.ok();
    }

    private void setEdgeGatewayNotSyncByLabelGroupId(Long labelGroupId) {
        Result<ChannelEntity> channelResult = channelService.getByLabelGroupId(labelGroupId);
        if (channelResult.getSignal() && channelResult.getResult() != null ){
            Long edgeGatewayId = channelResult.getResult().getEdgeGatewayId();
            if (edgeGatewayId != null){
                edgeGatewayService.setNotSyncById(edgeGatewayId);
            }
        }
    }

    @Transactional
    @Override
    @AuditLog(action = ActionEnum.CREATE,target = AuditTargetEnum.LABEL, details = "创建新标签")
    public Result<Void> createLabel(LabelDTO label, TenantIsolation tenantIsolation) {
        Result<ChannelEntity> channelEntityResult = channelService.getByIdAndTenantIsolation(label.getChannelId(), tenantIsolation);
        if (!channelEntityResult.getSignal() || !Optional.ofNullable(channelEntityResult.getResult()).isPresent()) {
            throw new BizException(ServiceCodeEnum.CHANNEL_PARAMS_FORMAT_FAIL);
        }
        if (!Optional.ofNullable(label.getLabelGroupId()).isPresent()) {
            Result<LabelGroup> longResult = labelGroupService.creatLabelGroup(
                    label.getChannelId(), label.getLabelGroupName(),
                    tenantIsolation,
                    LabelGroupDTO.builder().build());
            label.setLabelGroupId(longResult.getResult().getId());
        }
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        Result<Void> uniqueResult = Label.checkCreateLabelNameUnique(label.getLabelGroupId(), label.getName(), commonFetcher);
        if (!uniqueResult.getSignal()) {
            throw new BizException(ServiceCodeEnum.LABEL_NAME_REPEAT);
        }
        LabelEntity labelEntity = BeanUtilsIntensifier.copyBean(label, LabelEntity.class);
        BeanUtilsIntensifier.propertyInjection(tenantIsolation, labelEntity);
        labelMapper.insert(labelEntity);
        Result<Label> labelResult = Label.checkInfoToBase(labelEntity, DriverEnum.typeOfValue(channelEntityResult.getResult().getDriver()));
        if (!labelResult.getSignal()) {
            throw new BizException(labelResult.getServiceCode(), labelResult.getMessage());
        }
        edgeGatewayService.setNotSyncById(channelEntityResult.getResult().getEdgeGatewayId());
        return Result.ok();
    }

    @Override
    public Result<List<LabelVO>> listVOByLabelGroupId(Long labelGroupId) {
        return Result.ok(labelMapper.listVOByLabelGroupId(labelGroupId));
    }
    
    @Transactional
    @Override
    @AuditLog(action = ActionEnum.DELETE,target = AuditTargetEnum.LABEL, details = "单个或批量删除标签")
    public Result<Void> deleteByIdsAndTenantIsolation(List<Long> deleteLabelIds, TenantIsolation tenantIsolation) {
        if (CollectionUtils.isEmpty(deleteLabelIds)) {
            return Result.ok();
        }
        edgeGatewayService.setEdgeGatewayNotSyncByLabelIds(deleteLabelIds);
        Result<List<Long>> result = edgeGatewayService.selectEdgeGatewayIdList(deleteLabelIds);
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }
        List<Long> edgeGatewayIds = result.getResult();
        for(Long id : edgeGatewayIds){
            edgeGatewayService.setNotSyncById(id);
        }
        LambdaQueryWrapper<LabelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.in(LabelEntity::getId, deleteLabelIds)
                .eq(LabelEntity::getTenantId, tenantIsolation.getTenantId());
        labelMapper.delete(lqw);
        return Result.ok();
    }

    @Override
    public Result<List<CountByIdDTO>> countByLabelGroupIds(TenantIsolation tenantIsolation, List<Long> labelGroupIds) {
        return Result.ok(labelMapper.countByLabelGroupIds(tenantIsolation.getTenantId(), labelGroupIds));
    }

    @Override
    public Result<LabelEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<LabelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LabelEntity::getId, id)
                .eq(LabelEntity::getTenantId, tenantIsolation.getTenantId());
        return Result.ok(labelMapper.selectOne(lqw));
    }

    @Override
    public Result<List<ProofreadLabelDTO>> listProofreadDataByLabelGroupId(Long labelGroupId) {
        return Result.ok(labelMapper.listProofreadDataByLabelGroupId(labelGroupId));
    }


    @Override
    public Result connectTest(TenantIsolation tenantIsolation, List<Long> labelIds) {
        List<LabelBo> labelBoList = labelMapper.listLabelByLabelIds(tenantIsolation.getTenantId(), labelIds);
        List<Long> channelIds = labelBoList.parallelStream().map(LabelBo::getChannelId).distinct().collect(Collectors.toList());
        List<ChannelEntity> channelList = channelService.getChannelListByChannelIds(tenantIsolation, channelIds);
        List<ChannelElm> channelElmList = channelService.getChannelELmList(tenantIsolation, channelIds, channelList);
        Long edgeGatewayId = channelList.get(0).getEdgeGatewayId();
        List<AccessElm> accessElmList = getAccessElmList(labelBoList);
        Result<List<ConnectResult>> result = edgeGatewaySpiProxy.connectLabel(edgeGatewayId, tenantIsolation.getTenantId(), accessElmList, channelElmList);
        return result;
    }

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.CREATE,target = AuditTargetEnum.LABEL, details = "更新标签")
    public Result<Void> editLabel(EditLabelDTO dto, TenantIsolation tenantIsolation) {
        Result<LabelEntity> labelResult = this.getByIdAndTenantIsolation(dto.getId(), tenantIsolation);
        if (labelResult.getResult() == null) {
            throw new BizException("该租户下不存在此标签");
        }
        boolean labelNameChange = false;
        LabelEntity oldLabel = labelResult.getResult();
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        if (!oldLabel.getName().equals(dto.getName())) {
            Result<Void> uniqueResult = Label.checkUpdateLabelNameUnique(oldLabel.getLabelGroupId(), dto.getName(), oldLabel.getId(), commonFetcher);
            if (!uniqueResult.getSignal()) {
                throw new BizException(ServiceCodeEnum.LABEL_NAME_REPEAT);
            }
            labelNameChange = true;
        }
        LabelEntity labelEntity = dozerMapper.map(dto, LabelEntity.class);
        if (ObjectUtil.isEmpty(labelEntity.getIntervalMs())) {
            labelEntity.setIntervalMs(0);
        }
        this.updateById(labelEntity);

        Result<Label> labelCheckResult = Label.checkInfoToChannel(labelEntity, commonFetcher);
        if (!labelCheckResult.getSignal()) {
            throw new BizException(labelCheckResult.getMessage());
        }
        if (labelNameChange) {
            labelBindRelationService.updateByLabel(labelCheckResult.getResult(),tenantIsolation);
        }
        edgeGatewayService.setEdgeGatewayNotSyncByLabelGroupId(dto.getLabelGroupId());
        return Result.ok();
    }

    @Override
    public Result<Page<PageLabelVO>> pageLabel(PageLabelDTO dto, TenantIsolation tenantIsolation) {
        if (StringUtils.isNotBlank(dto.getDataType()) && ThingDataTypeEnum.typeOfName(dto.getDataType()) == null) {
            return Result.ok(dto.getPageParam().toPage(PageLabelVO.class));
        }
        dto.setSearchStr(this.escapeSearchStr(dto.getSearchStr()));
        Page<PageLabelVO> pageLabelVOList = labelMapper.pageLabel(dto.getPageParam().toPage(PageLabelVO.class), dto, tenantIsolation);
        return getPageLabelVOListByLabelAllow(pageLabelVOList);
    }

    @Override
    @Transactional
    public Result<List<LabelEntity>> editLabelAlias(EditLabelAliasDTO dto, TenantIsolation tenantIsolation) {

        if (dto.getAllFilter() == null) {
            dto.setAllFilter(false);
        }
        if (!dto.getAllFilter() && CollectionUtils.isEmpty(dto.getLabelIds())) {
            throw new BizException("至少勾选一个标签");
        }
        dto.setSearchStr(this.escapeSearchStr(dto.getSearchStr()));
        List<LabelEntity> labelList = labelMapper.listByEditLabelAlias(dto, tenantIsolation);
        if (CollectionUtils.isEmpty(labelList)) {
            return Result.ok();
        }
        String newAlias;
        List<LabelEntity> needUpdateLabelList = new ArrayList<>();
        List<LabelEntity> result = new ArrayList<>();
        List<Long> deviceNeedSetSyncLabelIdList = new ArrayList<>();
        StringBuilder errorMsg = new StringBuilder();

        for (LabelEntity label : labelList) {
            newAlias = StringUtils.isBlank(label.getAlias()) ? label.getName() : label.getAlias();
            List<String> filteringRules = dto.getFilteringRules();
            for (String filteringRule : filteringRules) {
                newAlias = StringUtils.replaceOnce(newAlias, filteringRule, "");
            }
            if (newAlias.length() > 32) {
                errorMsg.append(label.getName()).append(";\n");
                continue;
            }
            if (errorMsg.length() > 0) {
                continue;
            }

            if (!newAlias.equals(label.getAlias())) {
                LabelEntity labelEntity = new LabelEntity();
                labelEntity.setId(label.getId());
                labelEntity.setAlias(newAlias);
                label.setAlias(newAlias);
                needUpdateLabelList.add(labelEntity);
                deviceNeedSetSyncLabelIdList.add(label.getId());
            }
            if (dto.getLabelIds().contains(label.getId())) {
                result.add(label);
            }

        }
        if (errorMsg.length() > 0) {
            throw new BizException("以下标签生成的标签别名超过32个字符：\n" + errorMsg.toString());
        }
        this.updateBatchById(needUpdateLabelList);
        deviceService.setNotSyncByLabelIdList(deviceNeedSetSyncLabelIdList);
        return Result.ok(result);
    }

    @Override
    public Result<Void> checkLabel(CheckLabelDTO dto, TenantIsolation tenantIsolation) {
        StringBuilder uniqueNameSb = new StringBuilder();
//        StringBuilder bindRelation = new StringBuilder();
        StringBuilder checkMsg = new StringBuilder();
        List<LabelEntity> addLabelEntities = new ArrayList<>();
        List<LabelEntity> updateLabelEntities = new ArrayList<>();
        List<LabelDTO> labelList = dto.getLabels();
        DriverEnum driver = DriverEnum.typeOfValue(dto.getDriver());
        HashSet<String> names = new HashSet<>();
        HashSet<String> checkMsgSet = new HashSet<>();
        int setSize = 0;
        for (LabelDTO labelDTO : labelList) {

            names.add(labelDTO.getName());
            if (++setSize != names.size()) {
                uniqueNameSb.append(labelDTO.getName()).append(";\n");
                setSize--;
                continue;
            }
            LabelEntity labelEntity = dozerMapper.map(labelDTO, LabelEntity.class);

            if (labelDTO.getId() == null) {
                addLabelEntities.add(labelEntity);
            } else {
                updateLabelEntities.add(labelEntity);
            }
            Result<Label> labelResult = Label.checkInfoToBase(labelEntity, driver);
            if (!labelResult.getSignal() && checkMsgSet.add(labelResult.getMessage())) {
                checkMsg.append(labelResult.getMessage()).append(";\n");
            }
        }

        if (CollectionUtils.isNotEmpty(addLabelEntities)) {
            List<String> nameList = addLabelEntities.stream().map(LabelEntity::getName).collect(Collectors.toList());
            List<String> uniqueNameList = this.uniqueNameList(nameList, tenantIsolation).getResult();
            if (CollectionUtils.isNotEmpty(uniqueNameList)) {
                for (String uniqueName : uniqueNameList) {
                    uniqueNameSb.append(uniqueName).append(";\n");
                }
            }
        }

        if (CollectionUtils.isNotEmpty(updateLabelEntities)) {
            for (LabelEntity updateLabelEntity : updateLabelEntities) {
                Long id = updateLabelEntity.getId();
                Result<Void> result = this.uniqueName(updateLabelEntity.getId(), updateLabelEntity.getName(), tenantIsolation);
                if (!result.getSignal()) {
                    uniqueNameSb.append(updateLabelEntity.getName()).append(";\n");
                }
            }
        }

        String errorMsg = "";
        if (uniqueNameSb.length() > 0) {
            errorMsg = errorMsg + "以下标签名称存在重复：\n" + uniqueNameSb.toString();
        }
        if (checkMsg.length() > 0) {
            errorMsg = errorMsg + checkMsg.toString();
        }

        if (StringUtils.isNotBlank(errorMsg)) {
            throw new BizException(errorMsg);
        }

        return Result.ok();
    }

    @Override
    public Result connectByLabelGroupId(TenantIsolation tenantIsolation, Long labelGroupId) {
        List<LabelBo> labelBoList = labelMapper.listLabelByLabelGroupId(tenantIsolation.getTenantId(), labelGroupId);
        List<AccessElm> accessElmList = getAccessElmList(labelBoList);
        List<Long> channelIds = labelBoList.parallelStream().map(LabelBo::getChannelId).distinct().collect(Collectors.toList());
        List<ChannelEntity> channelList = channelService.getChannelListByChannelIds(tenantIsolation, channelIds);
        List<ChannelElm> channelElmList = channelService.getChannelELmList(tenantIsolation, channelIds, channelList);
        Long edgeGatewayId = channelList.get(0).getEdgeGatewayId();
        Result<List<ConnectResult>> result = edgeGatewaySpiProxy.connectLabel(edgeGatewayId, tenantIsolation.getTenantId(), accessElmList, channelElmList);
        return result;
    }

    @Override
    public List<LabelEntity> listByLabelGroupId(Long labelGroupId, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<LabelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LabelEntity::getLabelGroupId, labelGroupId)
                 .eq(LabelEntity::getDeleted,0)
                .eq(LabelEntity::getTenantId, tenantIsolation.getTenantId());
        return this.list(lqw);
    }

    @Override
    public List<LabelEntity> listByLabelGroupIds(List<Long> labelGroupIds, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<LabelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.in(LabelEntity::getLabelGroupId, labelGroupIds)
                 .eq(LabelEntity::getDeleted,0)
                .eq(LabelEntity::getTenantId, tenantIsolation.getTenantId());
        return this.list(lqw);
    }

    @Override
    public List<LabelEntity> listLabelUnderLabelGroupId(
        Long labelGroupId,
        Long channelId, 
        String labelGroupName,
        TenantIsolation tenantIsolation
    ) {
        LambdaQueryWrapper<LabelGroupEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LabelGroupEntity::getChannelId, channelId)
            .likeRight(LabelGroupEntity::getName, labelGroupName)
            .eq(LabelGroupEntity::getDeleted,0)
            .eq(LabelGroupEntity::getTenantId, tenantIsolation.getTenantId());
        List<LabelGroupEntity> labelGroupList = labelGroupService.list(lqw);
        
        List<Long> labelGroupIdList = labelGroupList.stream().map(LabelGroupEntity::getId).collect(Collectors.toList());
        labelGroupIdList.add(labelGroupId);

        LambdaQueryWrapper<LabelEntity> lqw2 = new LambdaQueryWrapper<>();
        lqw2.in(LabelEntity::getLabelGroupId, labelGroupIdList)
            .eq(LabelEntity::getDeleted,0)
            .eq(LabelEntity::getTenantId, tenantIsolation.getTenantId());
        return this.list(lqw2);
    }

    @Override
    public List<LabelEntity> listByIds(List<Long> labelIds, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<LabelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.in(LabelEntity::getId, labelIds)
                .eq(LabelEntity::getTenantId, tenantIsolation.getTenantId());
        return this.list(lqw);
    }

    @Autowired
    AbsolutePathLabelMapper absolutePathLabelMapper;

    @Override
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.LABEL, details = "批量标签解绑")
    public Result<Void> labelBatchUnbind(TenantIsolation tenantIsolation,LabelRequestBo requestBo) {
        if (Objects.isNull(tenantIsolation) || Objects.isNull(tenantIsolation.getTenantId()) || CollectionUtil.isEmpty(requestBo.getIds())) {
            return Result.ok();
        }
        LambdaQueryWrapper<AbsolutePathLabelEntity> in = new LambdaQueryWrapper<AbsolutePathLabelEntity>()
                .eq(AbsolutePathLabelEntity::getTenantId, tenantIsolation.getTenantId())
                .in(AbsolutePathLabelEntity::getId, requestBo.getIds());
        List<AbsolutePathLabelEntity> list = absolutePathLabelMapper.list(in);
        if (CollectionUtil.isEmpty(list)) {
            return Result.ok();
        }
        return labelBindRelationService.unbind(list,tenantIsolation.getTenantId());
    }
    
    
    @Override
    public ModelField toModelFieldByLabels(List<LabelEntity> labels) {
        List<PropertyElm> propertyElms = new ArrayList<>();
        for (LabelEntity label : labels) {
            propertyElms.add(label.toPropertyElm());
        }
        ModelField modelField = new ModelField();
        modelField.setProperties(propertyElms);
        return modelField;
    }

    @Transactional
    @Override
    @AuditLog(action = ActionEnum.IMPORT,target = AuditTargetEnum.LABEL, details = "通道批量标签导入")
    public Result<Void> labelBatchInput(TenantIsolation tenantIsolation, Long channelId, List<LabelDTO> list) {
        List<String> nameSet = list.stream().map(LabelDTO::getName).collect(Collectors.toList());
        if (nameSet.size() != list.size()) {
            throw new BizException(ServiceCodeEnum.INPUT_LABEL_NAME_REPEAT);
        }
        for (int i = 0; i < nameSet.size(); i++) {
            String name = nameSet.get(i);
            int index = name.lastIndexOf(LabelGroup.LABEL_GROUP_GRADE_SYMBOL);
            if (index == -1) {
                nameSet.remove(i);
                continue;
            }
            nameSet.set(i, name.substring(0, index));
        }
        if (!RegexUtil.checkLabelGroupLevelNames(nameSet).getSignal()) {
            throw new BizException(ServiceCodeEnum.LABEL_GROUP_LEVEL_NAME_ERROR);
        }
        Result<ChannelEntity> channelEntityResult = channelService.getByIdAndTenantIsolation(channelId, tenantIsolation);
        if (!channelEntityResult.getSignal()) {
            throw new BizException(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        DriverEnum driverEnum = DriverEnum.typeOfValue(channelEntityResult.getResult().getDriver());
        List<LabelGroup> labelGroups = LabelGroup.inputLabels(list, channelId, commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId()));
        List<LabelGroup> addGroups = labelGroups.stream().filter(labelGroup -> !Optional.ofNullable(labelGroup.getId()).isPresent()).collect(Collectors.toList());
        labelGroupService.batchAdd(channelId, addGroups, tenantIsolation);
        labelGroups.forEach(labelGroup -> createLabelList(labelGroup, driverEnum, tenantIsolation));
        labelGroupService.setEdgeGatewayNotSyncByChannelId(channelId);
        return Result.ok();
    }
    
    private void buildExcelLabelDTO(List<ExcelLabelDTO> excelLabelDTOList,String messageName,String messageContent){
        if(excelLabelDTOList.size() >= 200){
            return;
        }
        ExcelLabelDTO excelLabelDTO = new ExcelLabelDTO();
        excelLabelDTO.setMessageName(messageName);
        excelLabelDTO.setMessageContent(messageContent);
        excelLabelDTOList.add(excelLabelDTO);
    }

    @Override
    public Result<Page<PageLabelVO>> pageLabelWithFullName(PageLabelDTO dto, TenantIsolation tenantIsolation) {
        if (StringUtils.isNotBlank(dto.getDataType()) && ThingDataTypeEnum.typeOfName(dto.getDataType()) == null) {
            return Result.ok(dto.getPageParam().toPage(PageLabelVO.class));
        }
        List<Long> labelGroupIds = LabelGroup.getLabelGroupIds(dto.getLabelGroupName(), dto.getChannelId(), commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId()));
        if (CollectionUtil.isEmpty(labelGroupIds)) {
            return Result.ok(dto.getPageParam().toPage(PageLabelVO.class));
        }
        dto.setLabelGroupIds(labelGroupIds);
        Page<PageLabelVO> pageLabelVOList = labelMapper.pageLabelWithFullName(dto.getPageParam().toPage(PageLabelVO.class), dto, tenantIsolation);
        return getPageLabelVOListByLabelAllow(pageLabelVOList);
    }

    private Result getPageLabelVOListByLabelAllow(Page<PageLabelVO> pageLabelVOList){
        if(StringUtils.isBlank(JwtUserInfoUtils.userJson())){
            return Result.error(ErrorEnum.MENU_USER_INFO_NOT_EXIST.getCode(), ErrorEnum.MENU_USER_INFO_NOT_EXIST.getMessage());
        }
        JSONObject json = JSON.parseObject(JwtUserInfoUtils.userJson());
        Integer labelAllow = json.getInteger("labelAllow");
        if(labelAllow!=null && labelAllow == 0){
            pageLabelVOList.getRecords().forEach(pageLabelVO->{
                pageLabelVO.setAddress("");
                pageLabelVO.setDescript("");
            });
        }
        return Result.ok(pageLabelVOList);
    }

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.LABEL, details = "移动或复制标签")
    public Result<Void> moveOrCopyLabel(MoveOrCopyLabelDTO dto, TenantIsolation tenantIsolation) {
        LabelGroupEntity sourceLabelGroup = labelGroupMapper.getByLabelId(dto.getLabelIds().get(0));
        if (dto.getTargetLabelGroupId().equals(sourceLabelGroup.getId())) {
            throw new BizException("操作源和操作目标一样，操作无效");
        }

        ChannelEntity sourceChannel = channelMapper.getByLabelId(dto.getLabelIds().get(0));
        if (sourceChannel == null) {
            throw new BizException("参数异常");
        }

        Result<ChannelEntity> targetChannelResult = channelService.getByLabelGroupId(dto.getTargetLabelGroupId());
        if (!targetChannelResult.getSignal() || targetChannelResult.getResult() == null) {
            throw new BizException("参数异常");
        }

        if (!sourceChannel.getDriver().equals(targetChannelResult.getResult().getDriver())) {
            throw new BizException("通道类型不同，操作失败");
        }


        if (dto.getType().equals(1)) {
            labelMapper.updateLabelGroupIdAndSetGatherParamNullByLabelIds(dto.getLabelIds(), dto.getTargetLabelGroupId(), tenantIsolation);
            List<LabelBo> labelBoList = labelMapper.listLabelByIds(dto.getLabelIds(),tenantIsolation.getTenantId());
            List<LabelEntity> labelEntityList = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(labelBoList)){
                for(LabelBo labelBo:labelBoList){
                    LabelEntity labelEntity = new LabelEntity();
                    BeanUtil.copyProperties(labelBo,labelEntity);
                    labelEntityList.add(labelEntity);
                }
            }
            if(CollectionUtil.isNotEmpty(labelEntityList)) {
                labelBindRelationService.clearLabelBindRelation(tenantIsolation,labelEntityList);
            }
            edgeGatewayService.setNotSyncById(sourceChannel.getEdgeGatewayId());
        } else if (dto.getType().equals(2)) {
            List<LabelEntity> labelEntities = this.listByIds(dto.getLabelIds(), tenantIsolation);
            if (CollectionUtils.isNotEmpty(labelEntities)) {
                for (LabelEntity labelEntity : labelEntities) {
                    labelEntity.setId(null);
                    labelEntity.setGatherParam(null);
                    labelEntity.setLabelGroupId(dto.getTargetLabelGroupId());
                }
                this.saveBatch(labelEntities);
            }
        }
        List<String> repeatNames = labelMapper.listRepeatNameByLabelGroupId(dto.getTargetLabelGroupId(), tenantIsolation);
        if (CollectionUtils.isNotEmpty(repeatNames)) {
            throw new BizException("以下标签名称存在重复：\n" + repeatNames.toString());
        }
        edgeGatewayService.setNotSyncById(targetChannelResult.getResult().getEdgeGatewayId());
        return Result.ok();
    }

    @Override
    public Result<Void> saveBatchByNative(List<LabelEntity> list, int batchSize) {

        try {
            int size = list.size();
            int idxLimit = Math.min(batchSize, size);
            int i = 1;
            //保存单批提交的数据集合
            List<LabelEntity> oneBatchList = new ArrayList<>();
            for (Iterator<LabelEntity> var7 = list.iterator(); var7.hasNext(); ++i) {
                LabelEntity element = var7.next();
                oneBatchList.add(element);
                if (i == idxLimit) {
                    labelMapper.saveBatchByNative(oneBatchList);
                    //每次提交后需要清空集合数据
                    oneBatchList.clear();
                    idxLimit = Math.min(idxLimit + batchSize, size);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("保存失败");
        }
        return Result.ok();
    }

    private void createLabelList(LabelGroup labelGroup, DriverEnum driverEnum, TenantIsolation tenantIsolation) {
        if (CollectionUtil.isEmpty(labelGroup.getLabels())) {
            return;
        }
        log.info("开始批量创建标签，labelGroupId:{},labelList:{}", labelGroup);
        StringBuilder uniqueNameSb = new StringBuilder();
        List<LabelEntity> addLabelEntities = new ArrayList<>();
        boolean error = false;
        HashSet<String> names = new HashSet<>();
        int setSize = 0;
        Set<String> nameSet = new LambdaQueryChainWrapper<>(labelMapper)
                .select(LabelEntity::getName)
                .eq(LabelEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(LabelEntity::getLabelGroupId, labelGroup.getId())
                .list().stream().map(LabelEntity::getName).collect(Collectors.toSet());

        for (LabelDTO labelDTO : labelGroup.getLabels()) {

            names.add(labelDTO.getName());
            if (++setSize != names.size()) {
                uniqueNameSb.append(labelDTO.getName()).append(";\n");
                setSize--;
                error = true;
                continue;
            }
            if (nameSet.contains(labelDTO.getName())) {
                uniqueNameSb.append(labelDTO.getName()).append(";\n");
                error = true;
                continue;
            }
            LabelEntity labelEntity = dozerMapper.map(labelDTO, LabelEntity.class);
            Result<Label> labelResult = Label.checkInfoToBase(labelEntity, driverEnum);
            if (!labelResult.getSignal()) {
                throw new BizException(labelResult.getServiceCode(), labelResult.getMessage() + ",labelName:{0}", labelDTO.getName());
            }
            if (Optional.ofNullable(labelEntity.getIntervalMs()).isPresent() && Integer.valueOf(10).compareTo(labelEntity.getIntervalMs()) > 0 && Integer.valueOf(0).compareTo(labelEntity.getIntervalMs()) != 0) {
                labelEntity.setIntervalMs(10);
            }
            labelEntity.setLabelGroupId(labelGroup.getId());
            addLabelEntities.add(labelEntity);
        }
        if (error) {
            String errorMsg = "";
            if (uniqueNameSb.length() > 0) {
                errorMsg = errorMsg + "标签名称存在重复：\n" + uniqueNameSb.toString();
            }
            throw new BizException(errorMsg);
        }
        this.saveBatch(addLabelEntities);
    }

    private void createLabelList(LabelGroup labelGroup, DriverEnum driverEnum, TenantIsolation tenantIsolation,List<ExcelLabelDTO> excelLabelDTOList) {
        if (CollectionUtil.isEmpty(labelGroup.getLabels())) {
            return;
        }
        log.info("开始批量创建标签，labelGroupId:{},labelList:{}", labelGroup);
       // StringBuilder uniqueNameSb = new StringBuilder();
        List<LabelEntity> addLabelEntities = new ArrayList<>();
        //boolean error = false;
        HashSet<String> names = new HashSet<>();
        //int setSize = 0;
        Set<String> nameSet = new LambdaQueryChainWrapper<>(labelMapper)
                .select(LabelEntity::getName)
                .eq(LabelEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(LabelEntity::getLabelGroupId, labelGroup.getId())
                .list().stream().map(LabelEntity::getName).collect(Collectors.toSet());

        for (LabelDTO labelDTO : labelGroup.getLabels()) {

            if(excelLabelDTOList.size()>=200){
                return;
            }

            names.add(labelDTO.getName());
//            if (++setSize != names.size()) {
//                uniqueNameSb.append(labelDTO.getName()).append(";\n");
//                setSize--;
//                error = true;
//                continue;
//            }
            if (nameSet.contains(labelDTO.getName())) {
                ExcelLabelDTO excelLabelDTO = new ExcelLabelDTO();
                excelLabelDTO.setMessageName("labelName:"+labelDTO.getName());
                excelLabelDTO.setMessageName("已存在该标签名");
                excelLabelDTOList.add(excelLabelDTO);
//                uniqueNameSb.append(labelDTO.getName()).append(";\n");
//            error = true;
                continue;
            }
            LabelEntity labelEntity = dozerMapper.map(labelDTO, LabelEntity.class);
            Result<Label> labelResult = Label.checkInfoToBase(labelEntity, driverEnum);
            if (!labelResult.getSignal()) {
                //
                ExcelLabelDTO excelLabelDTO = new ExcelLabelDTO();
                excelLabelDTO.setMessageName("labelName check err :"+labelDTO.getName());
                excelLabelDTO.setMessageContent(labelResult.getMessage());
                excelLabelDTOList.add(excelLabelDTO);
                continue;
                //throw new BizException(labelResult.getServiceCode(), labelResult.getMessage() + ",labelName:{0}", labelDTO.getName());
            }
            if (Optional.ofNullable(labelEntity.getIntervalMs()).isPresent() && Integer.valueOf(10).compareTo(labelEntity.getIntervalMs()) > 0 && Integer.valueOf(0).compareTo(labelEntity.getIntervalMs()) != 0) {
                labelEntity.setIntervalMs(10);
            }
            labelEntity.setLabelGroupId(labelGroup.getId());
            addLabelEntities.add(labelEntity);
        }
//        if (error) {
//            String errorMsg = "";
//            if (uniqueNameSb.length() > 0) {
//                errorMsg = errorMsg + "标签名称存在重复：\n" + uniqueNameSb.toString();
//            }
//            throw new BizException(errorMsg);
//        }
        if(CollectionUtil.isNotEmpty(excelLabelDTOList)){
            return;
        }
        this.saveBatch(addLabelEntities);
    }

    private List<AccessElm> getAccessElmList(List<LabelBo> labelBoList) {
        List<AccessElm> accessElmList = new ArrayList<>(labelBoList.size());
        labelBoList.forEach(labelBo -> {
            AccessElm accessElm = new AccessElm();
            accessElm.setLabelId(labelBo.getId());
            accessElm.setLabelName(labelBo.getName());
            accessElm.setAddress(labelBo.getAddress());
            accessElm.setDataType(labelBo.getDataType());
            accessElm.setIsArray(labelBo.getIsArray());
            accessElm.setLength(labelBo.getLength());
            accessElm.setStringBytes(labelBo.getStringBytes());
            accessElm.setChannelId(labelBo.getChannelId());
            accessElmList.add(accessElm);
        });
        return accessElmList;
    }

    private String escapeSearchStr(String searchStr) {
        if (StringUtils.isNotBlank(searchStr) && (searchStr.contains("_") || searchStr.contains("%"))) {
            char[] chars = searchStr.toCharArray();
            StringBuilder result = new StringBuilder();
            for (char ch : chars) {
                if (ch == '_' || ch == '%') {
                    result.append("\\");
                }
                result.append(ch);
            }
            return result.toString();
        }
        return searchStr;

    }


    private Result<Void> uniqueName(String name, TenantIsolation tenantIsolation) {
        return this.uniqueName(null, name, tenantIsolation);
    }

    /**
     * 判断名称是否唯一
     *
     * @param id
     * @param name
     * @param tenantIsolation
     * @return
     */
    private Result<Void> uniqueName(Long id, String name, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<LabelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, LabelEntity::getId, id)
                .eq(LabelEntity::getName, name)
                .eq(LabelEntity::getTenantId, tenantIsolation.getTenantId());

        if (labelMapper.selectCount(lqw) > 0) {
            return Result.error("已经存在名称为：" + name + "的标签");
        }

        return Result.ok();
    }


    /**
     * 判断名称是否唯一
     *
     * @param name
     * @param tenantIsolation
     * @return
     */
    private Result<List<String>> uniqueNameList(List<String> name, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<LabelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.select(LabelEntity::getName)
                .in(LabelEntity::getName, name)
                .eq(LabelEntity::getTenantId, tenantIsolation.getTenantId());

        return Result.ok(this.listObjs(lqw, Object::toString));
    }


    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    @Transactional
    public void jdbcTemplateBatchSave(List<LabelEntity> list, int batchSize) {
        Stopwatch sw = Stopwatch.createStarted();
        jdbcTemplate.batchUpdate("INSERT INTO `label`(`id`,`name`,`descript`,`address`,`length`,`data_type`, `gather_param`,  `interval_ms`, " +
                        " `is_array`,`label_group_id`,`string_bytes`,`tag`,`create_time`,`ENGINEERING_ID`,`SPACE_ID`,`MODULE_ID`,`CREATOR`, " +
                        " `CREATOR_ID`,`UPDATOR_ID`,`UPDATOR`,`UPDATE_TIME`,`VERSION`,`DELETED`,`tenant_id`,`alias`,`read_only`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
                list,
                batchSize,
                (PreparedStatement ps, LabelEntity label) -> {
                    ps.setLong(1, label.getId());
                    ps.setString(2, label.getName());
                    if (label.getDescript() != null) {
                        ps.setString(3, label.getDescript());
                    } else {
                        ps.setObject(3, null);
                    }
                    ps.setString(4, label.getAddress());

                    if (label.getLength() != null) {
                        ps.setInt(5, label.getLength());
                    } else {
                        ps.setObject(5, null);
                    }
                    ps.setString(6, label.getDataType());
                    if (label.getGatherParam() != null) {
                        ps.setString(7, label.getGatherParam().toSqlString());
                    } else {
                        ps.setObject(7, null);
                    }
                    if (label.getIntervalMs() != null) {
                        ps.setInt(8, label.getIntervalMs());
                    } else {
                        ps.setObject(8, null);
                    }
                    if (label.getIsArray() != null) {
                        ps.setBoolean(9, label.getIsArray());
                    }else {
                        ps.setObject(9,null);
                    }
                    if (label.getLabelGroupId() != null) {
                        ps.setLong(10, label.getLabelGroupId());
                    }else {
                        ps.setObject(10,null);
                    }
                    if (label.getStringBytes() != null) {
                        ps.setInt(11, label.getStringBytes());
                    }else {
                        ps.setObject(11,null);
                    }
                    if (label.getTag() != null) {
                        ps.setString(12, label.getTag());
                    } else {
                        ps.setObject(12, null);
                    }
                    if (label.getCreateTime() != null) {
                        ps.setTimestamp(13, Timestamp.valueOf(label.getCreateTime()));
                    } else {
                        ps.setObject(13, null);
                    }
                    if (label.getEngineeringId() != null) {
                        ps.setLong(14, label.getEngineeringId());
                    } else {
                        ps.setObject(14, null);
                    }
                    if (label.getSpaceId() != null) {
                        ps.setLong(15, label.getSpaceId());
                    } else {
                        ps.setObject(15, null);
                    }
                    if (label.getModuleId() != null) {
                        ps.setLong(16, label.getModuleId());
                    } else {
                        ps.setObject(16, null);
                    }
                    if (label.getCreator() != null) {
                        ps.setString(17, label.getCreator());
                    } else {
                        ps.setObject(17, null);
                    }
                    if (label.getCreatorId() != null) {
                        ps.setLong(18, label.getCreatorId());
                    } else {
                        ps.setObject(18, null);
                    }
                    if (label.getUpdatorId() != null) {
                        ps.setLong(19, label.getUpdatorId());
                    } else {
                        ps.setObject(19, null);
                    }
                    if (label.getUpdator() != null) {
                        ps.setString(20, label.getUpdator());
                    } else {
                        ps.setObject(20, null);
                    }
                    if (label.getUpdateTime() != null) {
                        ps.setTimestamp(21, Timestamp.valueOf(label.getUpdateTime()));
                    } else {
                        ps.setObject(21, null);
                    }

                    ps.setInt(22, 1);
                    if (label.getDeleted() != null) {
                        ps.setInt(23, label.getDeleted() ? 1 : 0);
                    } else {
                        ps.setInt(23, 0);
                    }

                    if (label.getTenantId() != null) {
                        ps.setLong(24, label.getTenantId());
                    } else {
                        ps.setObject(24, null);
                    }
                    if (label.getAlias() != null) {
                        ps.setString(25, label.getAlias());
                    } else {
                        ps.setObject(25, null);
                    }

                    if (label.getReadOnly() != null) {
                        ps.setBoolean(26, label.getReadOnly());
                    } else {
                        ps.setObject(26, null);
                    }
                });
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>> label :" + list.size() + ":" + sw.elapsed(TimeUnit.SECONDS) + "s<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
        sw.stop();

    }

    @Override
    public Result<LabelBindRelationBo> getLabelDetail(Long labelId) {
        return Result.ok(labelMapper.getLabelDetail(labelId));
    }

    @Override
    @AuditLog(action=ActionEnum.UPDATE,target = AuditTargetEnum.LABEL, details = "批量标签轮询间隔修改")
    public Result<Void> batchUpdateInterval(TenantIsolation tenantIsolation, LabelRequestBo requestBo) {
        if(requestBo.getIds() == null){
            return Result.error("标签ID为空");
        }
        labelMapper.batchUpdateInterval(requestBo.getIds(),requestBo.getIntervalMs(),tenantIsolation.getTenantId());
        edgeGatewayService.setEdgeGatewayNotSyncByLabelIds(requestBo.getIds());
        return Result.ok();
    }

    @Override
    public Result<List<LabelDuplicateRemovalVO>> listByLabelGroupIds(TenantIsolation tenantIsolation, ModelLabelRequestBo modelLabelRequestBo) {
        if(CollectionUtil.isEmpty(modelLabelRequestBo.getLabelGroupIds())){
            return Result.ok(new ArrayList<>());
        }
        List<LabelEntity> labelEntityList = labelMapper.listByLabelGroupIds(modelLabelRequestBo,tenantIsolation);
        if(CollectionUtil.isEmpty(labelEntityList)){
            return Result.ok(new ArrayList<>());
        }
        Map<String, List<PropertyDpo>> propertyMap = null;
        Map<String,Long> propertyNameMap = null;
        if(ObjectUtil.isNotNull(modelLabelRequestBo.getModelId())){
            Result<ThingModelDto> entityResult = thingModelService.getThingModelById(tenantIsolation, modelLabelRequestBo.getModelId());
            if(!entityResult.getSignal()){
                return Result.error(entityResult.getMessage());
            }
            ModelDpo modelDpo = entityResult.getResult().getFullModel();
            if(ObjectUtil.isNull(modelDpo)){
                return Result.ok(new ArrayList<>());
            }
            List<PropertyDpo> propertyDpoList = modelDpo.getProperties();
            if(CollectionUtil.isNotEmpty(propertyDpoList)){
                propertyMap = propertyDpoList.stream().collect(
                    Collectors.groupingBy(
                        property -> property.getName() + ":" + property.getDataType().getType() + ":" + property.getDataType().getIsArray() + ":" + property.getReadOnly()
                    ));
                propertyNameMap = propertyDpoList.stream().collect(Collectors.groupingBy(p->p.getName(),Collectors.counting()));
            }
        }

        Map<String,Long> nameMap = labelEntityList.stream().collect(Collectors.groupingBy(l->l.getName(),Collectors.counting()));
        List<LabelDuplicateRemovalVO> labelDuplicateRemovalVOList = new ArrayList<>(labelEntityList.size());
        for(LabelEntity labelEntity :labelEntityList){
            String key = labelEntity.getName() + ":" + labelEntity.getDataType() + ":" + labelEntity.getIsArray() + ":" + labelEntity.getReadOnly();
            if(propertyMap != null && propertyMap.get(key) != null){
               continue; //如果属性中有相同的则过滤掉
            }
            LabelDuplicateRemovalVO labelDuplicateRemovalVO = new LabelDuplicateRemovalVO();
            BeanUtil.copyProperties(labelEntity,labelDuplicateRemovalVO);
            if(nameMap.get(labelEntity.getName()) != null && nameMap.get(labelEntity.getName()) > 1L){
                labelDuplicateRemovalVO.setIsRepeat(true);
            }else {
                labelDuplicateRemovalVO.setIsRepeat(false);
            }
            if(propertyNameMap != null && propertyNameMap.get(labelEntity.getName()) != null){ //跟原模型的属性名做比较，有给红色提示
                labelDuplicateRemovalVO.setIsRepeat(true);
            }
            labelDuplicateRemovalVOList.add(labelDuplicateRemovalVO);
        }
        return Result.ok(labelDuplicateRemovalVOList);
    }


    @Override
    public Result<List<AbsolutePathLabelEntity>> listByAbsolutionName(TenantIsolation tenantIsolation,Long edgeGatewayId, String channelName, String groupName) {
        List<AbsolutePathLabelEntity> list = labelMapper.listByAbsolutionName(tenantIsolation.getTenantId(),edgeGatewayId,channelName,groupName);
        return Result.ok(list);
    }
    
    @Override
    public List<LabelEntity> listByLabelGroupIds(Set<Long> labelGroupIds) {
        LambdaQueryWrapper<LabelEntity> queryWrapper = new LambdaQueryWrapper<LabelEntity>()
            .in(LabelEntity::getLabelGroupId,labelGroupIds)
            .eq(LabelEntity::getDeleted,0);
        List<LabelEntity> labelEntityList = labelMapper.selectList(queryWrapper);
        return labelEntityList;
    }



    @Override
    @AuditLog(action = ActionEnum.IMPORT,target = AuditTargetEnum.LABEL, details = "通道批量标签导入")
    public Result<List<ExcelLabelDTO>> labelExcelBatchInput(TenantIsolation tenantIsolation, Long channelId, List<LabelDTO> list) {
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);
        List<ExcelLabelDTO> excelLabelDTOList = new ArrayList<>();
        //导入同名的，取最后一个
        list = list.stream().collect(Collectors.toMap(
                LabelDTO::getName, // 作为键
                labelDTO -> labelDTO, // 作为值
                (existing, replacement) -> replacement // 解决冲突时选择最后一个出现的元素
        )).values().stream().collect(Collectors.toList());
        List<String> nameSet = list.stream().map(LabelDTO::getName).collect(Collectors.toList());

        List<String> labelNameList = new ArrayList<>();
        for (int i = 0; i < nameSet.size(); i++) {
            String name = nameSet.get(i);
            int index = name.lastIndexOf(LabelGroup.LABEL_GROUP_GRADE_SYMBOL);
            if (index == -1) {
                nameSet.remove(i);
                continue;
            }
            nameSet.set(i, name.substring(0, index));
            labelNameList.add(name.substring(index + 1));
        }

        RegexUtil.checkLabelGroupLevelNames(nameSet,excelLabelDTOList);

        compareAndClear(list,labelNameList,channelId,tenantIsolation);

        Result<ChannelEntity> channelEntityResult = channelService.getByIdAndTenantIsolation(channelId, tenantIsolation);
        if (!channelEntityResult.getSignal()) {
            buildExcelLabelDTO(excelLabelDTOList,"channelEntity","该租户下通道不存在");
        }
        DriverEnum driverEnum = DriverEnum.typeOfValue(channelEntityResult.getResult().getDriver());
        List<LabelGroup> labelGroups = LabelGroup.inputLabels(list, channelId, commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId()),excelLabelDTOList);
        List<LabelGroup> addGroups = labelGroups.stream().filter(labelGroup -> !Optional.ofNullable(labelGroup.getId()).isPresent()).collect(Collectors.toList());
        labelGroupService.batchAdd(channelId, addGroups, tenantIsolation);
        labelGroups.forEach(labelGroup -> createLabelList(labelGroup, driverEnum, tenantIsolation,excelLabelDTOList));
        labelGroupService.setEdgeGatewayNotSyncByChannelId(channelId);
        if(CollectionUtil.isEmpty(excelLabelDTOList)){
            platformTransactionManager.commit(transactionStatus);
        }else {
            platformTransactionManager.rollback(transactionStatus);
        }
        return Result.ok(excelLabelDTOList);
    }

    private Result<Void> compareAndClear(List<LabelDTO> labelDTOS,List<String> labelNameList,Long channelId,TenantIsolation tenantIsolation){
        // 导入数据按labelGroupName分组
        List<LabelDTO> list = new ArrayList<>();
        for(LabelDTO labelDTO :labelDTOS){
            int index = labelDTO.getName().lastIndexOf(LabelGroup.LABEL_GROUP_GRADE_SYMBOL);
            if (index == -1) {
                labelDTO.setName("default." + labelDTO.getName());
            }
            list.add(labelDTO);
        }
        Map<String, List<LabelDTO>> groupedByNameMap = list.stream().collect(Collectors.groupingBy(label -> label.getName().substring(0, label.getName().lastIndexOf(LabelGroup.LABEL_GROUP_GRADE_SYMBOL))));

        //把通道下的数据查找出来
        Result<List<LabelGroupDto>> listResult = labelGroupService.listLabelGroupByChannelId(channelId,tenantIsolation);
        if(!listResult.getSignal()){
            log.error("labelBatchInput err and message is {}",listResult.getMessage());
            return Result.error(listResult.getMessage());
        }
        //先查找通道下分组
        List<LabelGroupDto> groupDtoList = listResult.getResult();
        List<Long> delIds = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(groupDtoList)) {
            //映射分组名称和分组ID关系
            Map<String,Long> nameToIdMap = groupDtoList.stream().collect(Collectors.toMap(LabelGroupDto::getName, LabelGroupDto::getId,(entity1, entity2) -> entity1));
            Set<Long> groupIds = groupDtoList.stream().map(LabelGroupDto::getId).collect(Collectors.toSet());
            //再分组下的标签集合
            List<LabelEntity> labelEntityList = listByLabelGroupIds(groupIds);
            if (CollectionUtil.isNotEmpty(labelEntityList)) {
                //按分组ID分组
                Map<Long, List<LabelEntity>> group = labelEntityList.stream().collect(Collectors.groupingBy(LabelEntity::getLabelGroupId));
                //按导入分组进行校验各个分组下是否有标签重复，重复则先清除表数据后面做覆盖
                for(Entry<String, List<LabelDTO>> entry : groupedByNameMap.entrySet()){
                    if(nameToIdMap.get(entry.getKey()) != null){
                        Long labelGroupId = nameToIdMap.get(entry.getKey());
                        List<LabelEntity> labelEntities = group.get(labelGroupId);
                        for(LabelEntity labelEntity :labelEntities){
                            if(labelNameList.contains(labelEntity.getName())){
                                delIds.add(labelEntity.getId());
                            }
                        }
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(delIds)) {
                labelMapper.deleteBatchIds(delIds);
            }
        }
        return Result.ok();
    }

}
