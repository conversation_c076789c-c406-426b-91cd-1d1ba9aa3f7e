package com.nti56.nlink.product.device.server.model.product.dto;

import com.nti56.nlink.product.device.client.model.dto.json.InputDataField;
import com.nti56.nlink.product.device.client.model.dto.json.OutputDataField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/22 10:05<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "校对设备服务dto")
public class ProofreadDeviceServiceDTO {

    private Long id;

    @Schema(description = "服务名称")
    private String serviceName;

    @Schema(description = "所属设备ID")
    private Long deviceId;

    @Schema(description = "是否允许覆盖")
    private Boolean override;

    @Schema(description = "调用方式:0-sync（同步调用） 1-async（异步调用）")
    private Boolean async;

    @Schema(description = "输入参数 JSON对象")
    private InputDataField[] inputData;

    @Schema(description = "结果 JSON对象")
    private OutputDataField outputData;

    @Schema(description = "结果描述")
    private String outputDataDescript;

    @Schema(description = "代码文本 需要限制代码长度")
    private String serviceCode;

}
