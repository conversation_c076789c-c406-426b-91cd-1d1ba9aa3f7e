package com.nti56.nlink.product.device.server.model.connector.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @since JDK 1.8
 */
@Data
@Schema(description = "连接器VO")
public class ConnectorVO {
    
    /**
     * id
     */
    private Long id;
    
    /**
     * 连接器名称
     */
    @Schema(description = "连接器名称")
    private String name;
    
    /**
     * 连接器描述
     */
    @Schema(description = "连接器描述")
    private String descript;
    
    /**
     * 模式类型 0-接收;1-发送
     */
    @Schema(description = "模式类型 0-接收;1-发送")
    private Integer modeType;
    
    /**
     * 连接器类型 0-MQTT;1-HTTP
     */
    @Schema(description = "连接器类型 0-MQTT;1-HTTP")
    private Integer connectorType;
    
    /**
     * 连接器状态 0-关闭;1-启用
     */
    @Schema(description = "连接器状态 0-停用;1-启用")
    private Integer status;
    
    /**
     * 连接器信息明细
     */
    @Schema(description = "连接器信息明细")
    private String connectorInfo;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    
    /**
     * 连接状态
     */
    @Schema(description = "连接状态")
    private Integer connectStatus;

}
