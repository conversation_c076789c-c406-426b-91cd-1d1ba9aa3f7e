package com.nti56.nlink.product.device.server.service.export.handler;

import java.util.List;

import cn.hutool.extra.spring.SpringUtil;

import org.apache.commons.collections.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.nti56.nlink.common.export.handler.AbstractExportSqlHandler;
import com.nti56.nlink.common.util.GeneratorSqlUtil;
import com.nti56.nlink.product.device.server.entity.DataModelEntity;
import com.nti56.nlink.product.device.server.entity.DataModelPropertyEntity;
import com.nti56.nlink.product.device.server.entity.TagBindRelationEntity;
import com.nti56.nlink.product.device.server.mapper.DataModelMapper;
import com.nti56.nlink.product.device.server.mapper.DataModelPropertyMapper;
import com.nti56.nlink.product.device.server.mapper.TagBindRelationMapper;

/**
 * 类说明:标记关系<br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/19 15:34<br/>
 * @version 1.0
 * @since JDK 1.8
 */
public class TagExportSqlHandler extends AbstractExportSqlHandler {
  @Override
  public void exportSqlDml(Long tenantId, List<String> sqlList) {
    exportTagRelation(tenantId, sqlList);
    if (getNext() != null) {
      getNext().exportSqlDml(tenantId, sqlList);
    }
  }

  private void exportTagRelation(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<TagBindRelationEntity> queryWrapper = new LambdaQueryWrapper<TagBindRelationEntity>()
            .eq(TagBindRelationEntity::getTenantId, tenantId);
    List<TagBindRelationEntity> dtoList = SpringUtil.getBean(TagBindRelationMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(TagBindRelationEntity.class, dtoList));
    }
  }


}
