package com.nti56.nlink.product.device.server.model.thingModel.vo;

import com.nti56.nlink.product.device.client.model.dto.json.dpo.EventDefineDpo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 08:58:38
 * @since JDK 1.8
 */
@Data
public class ThingModelEventVo implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long thingModelId;

    private String thingModelName;
    
    private String name;
    private String type;
    private String descript;
    private EventDefineDpo eventDefine;

    private Long thingServiceId;

    private Long eventId;

    private Boolean repeat;

    private String belongModel;
}
