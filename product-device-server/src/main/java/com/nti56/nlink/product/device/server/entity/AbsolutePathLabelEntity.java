package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.DataTypeElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 * 类说明: 标签表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "绝对路径标签")
public class AbsolutePathLabelEntity {

    /**
     * 标签id
     */ 
    private Long id;
    /**
     * 标签分组id
     */
    @Schema(description = "标签分组id")
    private Long labelGroupId;

    /**
     * 通道Id
     */
    private Long channelId;

    private String channelName;

    private String labelGroupName;

    private String edgeGatewayName;

    @Schema(description = "标签所属网关id")
    private Long edgeGatewayId;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签绝对名称
     */
    private String absoluteName;

    @Schema(description = "别名")
    private String alias;
    /**
     * 标签描述
     */
    @Schema(description = "标签描述")
    private String descript;
    /**
     * 地址，如DB50.DBB1
     */
    @Schema(description = "地址，如DB50.DBB1")
    private String address;
    /**
     * 长度
     */
    @Schema(description = "长度")
    private Integer length;
    /**
     * 采集参数
     */
    private GatherParamField gatherParam;
    /**
     * 数据类型，bool/byte/short/int/float/string
     */
    @Schema(description = "数据类型，bool/byte/short/int/float/string/ushort/double")
    private String dataType;
    /**
     * 是否数组
     */
    @Schema(description = "是否数组")
    private Boolean isArray;
    /**
     * type是string类型时，表示string元素的byte长度，其他type类型放空
     */
    @Schema(description = "type是string类型时，表示string元素的byte长度，其他type类型放空")
    private Integer stringBytes;

    @Schema(description = "时间间隔，单位毫秒")
    private Integer intervalMs;

    @Schema(description = "是否只读")
    private Boolean readOnly;


    @Schema(description = "租户ID")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;




    public PropertyElm toPropertyElm() {
        DataTypeElm dataTypeElm = new DataTypeElm();
        dataTypeElm.setIsArray(this.isArray);
        dataTypeElm.setType(this.dataType);
        PropertyElm propertyElm = new PropertyElm();
        propertyElm.setName(this.name);
        propertyElm.setReadOnly(this.readOnly);
        propertyElm.setBindLabel(true);
        propertyElm.setReportType(1);
        propertyElm.setDescript(this.descript);
        propertyElm.setDataType(dataTypeElm);
        return propertyElm;
    }

}
