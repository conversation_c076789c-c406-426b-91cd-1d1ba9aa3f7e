package com.nti56.nlink.product.device.server.model.channel.vo;

import com.nti56.nlink.product.device.server.model.custom.CustomDriverBo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ChannelDebugInfoVo extends ChannelVO{
    
    @Schema(description = "通道ip")
    private String ip;

    @Schema(description = "通道port")
    private Integer port;

    @Schema(description = "模式，sync-同步，async-异步")
    private String mode;

    @Schema(description = "自定义协议")
    private CustomDriverBo customDriver;

    @Schema(description = "mqtt username")
    private String mqttUsername;

    @Schema(description = "mqtt password")
    private String mqttPassword;

    @Schema(description = "mqtt ws ip")
    private String mqttWsHost;

    @Schema(description = "mqtt ws port")
    private Integer mqttWsPort;

    @Schema(description = "mqtt ws url")
    private String mqttWsUrl;

    @Schema(description = "订阅mqtt发送topic")
    private String sendTopic;

    @Schema(description = "订阅mqtt接收topic")
    private String receiveTopic;

    @Schema(description = "订阅mqtt自动响应topic")
    private String autoResponseTopic;

    @Schema(description = "订阅mqtt调试topic")
    private String debugTopic;

    @Schema(description = "mqtt发送topic前缀")
    private String sendTopicPrefix;
    
}
