package com.nti56.nlink.product.device.server.model;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * 类说明: <br/>
 * <AUTHOR> <br/>
 * @version 1.0
 * @since JDK 1.8
 */
@Data
public class VersionQueryDTO {
    
    @Schema(description = "网关名称")
    private String name;
    
    @Schema(description = "-1:全部;0:离线;1:在线")
    private Integer online;
    
    /**
     * 网关ID集合 用于查询过来
     */
    @Schema(description = "网关ID集合")
    private List<Long> IdList;


    private Integer type;
}
