package com.nti56.nlink.product.device.server.model.device.dto;

import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 类说明：trigger事件触发实例
 *
 * @ClassName TriggerEventInstance
 * <AUTHOR>
 * @Date 2023/8/10 13:24
 * @Version 1.0
 */

@Data
public class TriggerEventInstance implements Serializable {

    private String eventName;
    private String eventDesc;
    private String triggerCondition;
    private UpData upData;
    /**
     * 调用物服务的ID
     */
    private Long thingServiceId;

}
