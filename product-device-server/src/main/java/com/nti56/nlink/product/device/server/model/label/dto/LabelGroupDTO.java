package com.nti56.nlink.product.device.server.model.label.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 10:47<br/>
 * @since JDK 1.8
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "标签分组dto")
public class LabelGroupDTO {

    private Long id;

    @Schema(description = "名字")
    @NotBlank(message = "标签分组名称不能为空")
    @Length(max = 32,message = "标签分组名称不能超过32个字符")
    private String name;

    @Schema(description = "描述")
    @Length(max = 256,message = "标签分组描述不能超过256个字符")
    private String descript;

    @Schema(description = "标签列表")
    @Valid
    private List<LabelDTO> updateLabelList;

    @Schema(description = "新分组名称")
    private String newLevelName;

}
