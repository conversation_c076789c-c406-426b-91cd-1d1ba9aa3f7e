package com.nti56.nlink.product.device.server.util.aliyun;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.util.List;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/7/7 10:30<br/>
 * @since JDK 1.8
 */
public class DingDingRobot {

    public static DingTalkClient createClient(String webhook,String secret)  throws Exception {
        Long timestamp = System.currentTimeMillis();
        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes(UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes(UTF_8));
        String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)),"UTF-8");
        return new DefaultDingTalkClient(webhook+
                                                    "&timestamp="+timestamp+
                                                    "&sign="+sign);
    }

    public static OapiRobotSendRequest createTextRequest(String content, List<String> atMobiles,List<String> atUserIds,boolean atAll) {
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("text");
        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent(content);
        request.setText(text);
        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        at.setAtMobiles(atMobiles);
        at.setIsAtAll(atAll);
        at.setAtUserIds(atUserIds);
        request.setAt(at);
        return request;

    }

    public static OapiRobotSendRequest createMarkdownRequest(String content, String title, List<String> atMobiles, List<String> atUserIds, boolean atAll) {
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("markdown");
        OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
        markdown.setTitle(title);
        markdown.setText(content);
        request.setMarkdown(markdown);
        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        at.setAtMobiles(atMobiles);
        at.setIsAtAll(atAll);
        at.setAtUserIds(atUserIds);
        request.setAt(at);
        return request;
    }
}
