package com.nti56.nlink.product.device.server.model.product.vo;

import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.model.thingModel.vo.ThingModelVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/22 10:05<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "设备模型vo")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceVO {

    private Long id;

    @Schema(description = "设备名称")
    private String name;

    @Schema(description = "继承的物模型id和名称")
    private List<ThingModelVO> thingModels;

    @Schema(description = "继承的物模型定义")
    private List<ModelDpo> modelContainsInherits;

    @Schema(description = "自己的模型定义")
    private ModelDpo model;
    
    @Schema(description = "完整的模型定义")
    private ModelDpo fullModel;

    @Schema(description = "属性当前值")
    private Map<String,Object> deviceTwinActual;

    @Schema(description = "属性当前值")
    private Map<String,Object> deviceChangeTime;

    @Schema(description = "通用故障模型")
    private ModelDpo commonFaultModel;

    @Schema(description = "设备基础模型")
    private ModelDpo deviceBaseModel;

}
