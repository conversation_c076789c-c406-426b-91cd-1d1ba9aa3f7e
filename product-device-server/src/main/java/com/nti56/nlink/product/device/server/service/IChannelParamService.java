package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;
import com.nti56.nlink.product.device.server.model.ChannelParamDto;
import com.nti56.nlink.product.device.server.model.channel.dto.ChannelParamDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.CheckChannelParamsDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.ProofreadChannelParamDTO;

import java.util.List;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 14:11:44
 * @since JDK 1.8
 */
public interface IChannelParamService extends IBaseService<ChannelParamEntity> {

    Result<List<ChannelParamEntity>> listChannelParam(ChannelParamDto channel, TenantIsolation tenantIsolation);

    Result<Void> deleteChannelParamByChannelId(Long channelId);

    Result<Void> createOrEditChannelParamList(Long id,Integer driver,Boolean isServer, List<ChannelParamDTO> channelParamList, TenantIsolation tenantIsolation);

    Result<Void> checkChannelParams(CheckChannelParamsDTO dto);

    Result<List<ChannelParamEntity>> getByChannelId(Long id,Integer driver);

    Result<Void> deleteByIdsAndTenantIsolation(List<Long> deleteChannelParamIds, TenantIsolation tenantIsolation);

    Result<List<ProofreadChannelParamDTO>> listProofreadDataByChannelId(Long channelId, TenantIsolation tenantIsolation);
    
    Result<List<ChannelParamDTO>> listChannelParamByEdgeGatewayId(Long edgeGatewayId,TenantIsolation tenantIsolation);
    
    Result<List<ChannelParamDTO>> listChannelParamByChannelIds(List<Long> channelIds, TenantIsolation tenantIsolation);
}
