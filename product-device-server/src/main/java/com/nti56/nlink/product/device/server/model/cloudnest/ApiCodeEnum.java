package com.nti56.nlink.product.device.server.model.cloudnest;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public enum ApiCodeEnum {
    SUCCESS(200, "操作成功"),
    API_SUCCESS(0, "操作成功"),
    API_SERVER_ERROR(-1, "系统错误"),
    UNAUTHORIZED(401, "未授权"),
    INTERNAL_SERVER_ERROR(500, "系统异常");

    private Integer code;
    private String message;

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap();
        map.put("code", this.code);
        map.put("message", this.message);
        return map;
    }

    public static ApiCodeEnum getCodeEnum(Integer code) {
        Optional optional = Arrays.stream(values()).filter((e) -> {
            return e.getCode().equals(code);
        }).findFirst();
        return optional.isPresent() ? valueOf(optional.get().toString()) : null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    private ApiCodeEnum(final Integer code, final String message) {
        this.code = code;
        this.message = message;
    }
}
