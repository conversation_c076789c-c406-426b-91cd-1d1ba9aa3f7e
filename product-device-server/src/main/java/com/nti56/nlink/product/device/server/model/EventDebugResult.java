package com.nti56.nlink.product.device.server.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 类说明: 连通性测试结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-06-24 09:45:29
 * @since JDK 1.8
 */
@Data
@Builder
public class EventDebugResult {
    private Boolean ok;
    private String message;
    private Boolean logicResult;
    private List<Map<String,String>>  checkErrorMsg;

}
