package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.server.entity.AbsolutePathLabelEntity;
import com.nti56.nlink.product.device.server.entity.LabelEntity;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.model.label.dto.*;
import com.nti56.nlink.product.device.server.model.label.vo.LabelDuplicateRemovalVO;
import com.nti56.nlink.product.device.server.model.label.vo.LabelVO;
import com.nti56.nlink.product.device.server.model.label.vo.PageLabelVO;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import java.util.List;
import java.util.Set;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 14:11:44
 * @since JDK 1.8
 */
public interface ILabelService extends IBaseService<LabelEntity> {

    Result<Void> batchUpdateGatherParam(Long tenantId, List<GatherParamField> gatherParamList);

    Result<Void> createOrEditLabelList(Long id, List<LabelDTO> labelList, TenantIsolation tenantIsolation);

    Result<Void> createLabel(LabelDTO label, TenantIsolation tenantIsolation);

    Result<List<LabelVO>> listVOByLabelGroupId(Long labelGroupId);

    Result<Void> deleteByIdsAndTenantIsolation(List<Long> deleteLabelIds, TenantIsolation tenantIsolation);

    Result<List<CountByIdDTO>> countByLabelGroupIds(TenantIsolation tenantIsolation, List<Long> labelGroupIds);

    Result<LabelEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation);

    Result<List<ProofreadLabelDTO>> listProofreadDataByLabelGroupId(Long labelGroupId);

    
    Result connectTest(TenantIsolation tenantIsolation, List<Long> labelIds);

    Result<Void> editLabel(EditLabelDTO dto, TenantIsolation tenantIsolation);

    Result<Page<PageLabelVO>> pageLabel(PageLabelDTO dto, TenantIsolation tenantIsolation);

    Result<List<LabelEntity>> editLabelAlias(EditLabelAliasDTO dto, TenantIsolation tenantIsolation);

    Result<Void> checkLabel(CheckLabelDTO dto, TenantIsolation tenantIsolation);

    Result connectByLabelGroupId(TenantIsolation tenantIsolation, Long labelGroupId);

    List<LabelEntity> listByLabelGroupId(Long labelGroupId ,TenantIsolation tenantIsolation);

    List<LabelEntity> listByLabelGroupIds(List<Long> labelGroupId ,TenantIsolation tenantIsolation);

    List<LabelEntity> listByIds(List<Long> labelIds, TenantIsolation tenantIsolation);

    ModelField toModelFieldByLabels(List<LabelEntity> labels);

    Result<Void> labelBatchInput(TenantIsolation tenantIsolation, Long channelId, List<LabelDTO> list);

    Result<Page<PageLabelVO>> pageLabelWithFullName(PageLabelDTO dto, TenantIsolation tenantIsolation);

    Result<Void> moveOrCopyLabel(MoveOrCopyLabelDTO dto, TenantIsolation tenantIsolation);

    Result<Void> saveBatchByNative(List<LabelEntity> list,int batchSize);

    void jdbcTemplateBatchSave(List<LabelEntity> list, int batchSize);

    Result<LabelBindRelationBo> getLabelDetail(Long labelId);

    Result<Void> batchUpdateInterval(TenantIsolation tenantIsolation, LabelRequestBo requestBo);

    Result<List<LabelDuplicateRemovalVO>> listByLabelGroupIds(TenantIsolation tenantIsolation, ModelLabelRequestBo modelLabelRequestBo);

    Result<List<AbsolutePathLabelEntity>> listByAbsolutionName(TenantIsolation tenantIsolation, Long edgeGatewayId, String channelName, String groupName);

    Result<Void> labelBatchUnbind(TenantIsolation tenantIsolation, LabelRequestBo requestBo);
    
    List<LabelEntity> listByLabelGroupIds(Set<Long> labelGroupIds);

    Result<List<ExcelLabelDTO>> labelExcelBatchInput(TenantIsolation tenantIsolation, Long channelId, List<LabelDTO> list);

    List<LabelEntity> listLabelUnderLabelGroupId(Long labelGroupId, Long channelId, String labelGroupName, TenantIsolation tenantIsolation);
    
    
}
