package com.nti56.nlink.product.device.server.serviceEngine;

import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;

import javax.script.ScriptEngine;

/**
 * 说明：服务引擎
 *
 * <AUTHOR>
 * @ClassName serviceEngine
 * @date 2022/4/14 16:23
 * @Version 1.0
 */
public abstract class ServiceEngine {

    protected ScriptEngine engine;

    protected ServiceThreadPoolTaskExecutor serviceTaskExecutor;

    public R doService(BaseService service){
        if (service.serviceDefined.getIsAsync()) {
            AsyncServiceTask asyncServiceTask = (AsyncServiceTask) service;
            serviceTaskExecutor.execute(asyncServiceTask);
            return R.ok();
        }
        return R.result((Result<Object>) service.doTask());
    }




}
