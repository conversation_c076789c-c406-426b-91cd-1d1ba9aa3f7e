package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.ResourceRelationEntity;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <p>
 * 资源关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-11 17:27:49
 * @since JDK 1.8
 */
public interface IResourceRelationService extends IBaseService<ResourceRelationEntity> {

    Result<ResourceRelationEntity> saveResourceRelation(ResourceRelationEntity device);

    Result<Page<ResourceRelationEntity>> getPage(@Nullable ResourceRelationEntity device, Page<ResourceRelationEntity> page);

    Result<List<ResourceRelationEntity>> list(ResourceRelationEntity device);

    Result<Void> update(ResourceRelationEntity device, TenantIsolation tenantIsolation);

    Result<Void> deleteById(Long id, TenantIsolation tenantIsolation);

    Result<ResourceRelationEntity> getByIdAndTenantIsolation(Long entityId, TenantIsolation tenantIsolation);
}
