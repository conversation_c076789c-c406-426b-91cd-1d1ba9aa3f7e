package com.nti56.nlink.product.device.server.domain.thing.up;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-22 09:17:11
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpProp {

    private String property;
    private Long deviceId;
    private Long labelId;
    private String labelName;
    private String dataType;
    private Boolean isArray;
    private Integer length;
    private Object preValue;
    private Object value;
    private String faultInput;
    private Long traceId;

}
