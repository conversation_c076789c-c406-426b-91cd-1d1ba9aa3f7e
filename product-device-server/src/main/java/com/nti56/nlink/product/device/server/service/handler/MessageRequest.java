package com.nti56.nlink.product.device.server.service.handler;

import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.dto.TenantIsolation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 类说明：
 *
 * @ClassName MessageRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/6/19 15:20
 * @Version 1.0
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageRequest {

    private Long templateId;

//    private OapiRobotSendRequest request;

    private String title;

    private String content;

    private List<String> atMobiles;

    private String requestParam;

    private TenantIsolation tenantIsolation;

    private JSONObject channelParams;

    private Long subscriptionId;
}
