package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nti56.model.BaseModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 实例回调操作
 * @TableName re_instance_redirect
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName(value ="redirect")
public class InstanceRedirectEntity extends BaseModel {

    /**
     * 工程id
     */
    @Schema(description = "工程id")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块id
     */
    @Schema(description = "模块id")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间id
     */
    @Schema(description = "空间id")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 回调名称
     */
    @Schema(description = "回调名称")
    private String redirectName;

    /**
     * 回调类型 0-webhook
     */
    @Schema(description = "回调类型 0-webhook")
    private Integer redirectType;

    /**
     * 回调请求超时时长
     */
    @Schema(description = "回调请求超时时长")
    private Integer redirectRequestTimeout;

    /**
     * 回调请求连接超时时长
     */
    @Schema(description = "回调请求连接超时时长")
    @TableField("request_connect_timeout")
    private Integer redirectRequestConnectTimeout;

    /**
     * 回调被调用次数
     */
    @Schema(description = "回调被调用次数")
    private Integer redirectInvokeTime;

    /**
     * 回调方法明细 json
     */
    @Schema(description = "回调方法明细", example = "{\n" +
            "    \"method\": \"get\",\n" +
            "    \"url\": \"http://xxxxxx\",\n" +
            "    \"headers\": [\n" +
            "        {\n" +
            "            \"key\": \"key1\",\n" +
            "            \"value\": \"value1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"key\": \"key2\",\n" +
            "            \"value\": \"value2\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"params\": [\n" +
            "        {\n" +
            "            \"key\": \"key1\",\n" +
            "            \"value\": \"value1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"key\": \"key2\",\n" +
            "            \"value\": \"value2\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"body\": {\n" +
            "        \n" +
            "    }\n" +
            "}\t\n" +
            "\t")
    private String redirectFn;

    @Schema(description = "描述")
    private String description;

    @TableField(exist = false)
    private String lastInvokeTime;

}