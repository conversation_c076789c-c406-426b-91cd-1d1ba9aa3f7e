package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.ServiceConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【service_config(通用物服务配置表)】的数据库操作Mapper
* @createDate 2023-03-06 14:51:50
* @Entity generator.domain.ServiceConfig
*/
@Mapper
public interface ServiceConfigMapper extends CommonMapper<ServiceConfigEntity> {


    ServiceConfigEntity selectByServiceIdAndDeviceId(@Param("tenantId") Long tenantId, @Param("serviceId") Long serviceId, @Param("deviceId") Long deviceId);

    ServiceConfigEntity selectByServiceIdAndModel(@Param("tenantId") Long tenantId, @Param("serviceId") Long serviceId, @Param("modelId") Long modelId);

    List<ServiceConfigEntity> selectByServiceIdAndDeviceIds(@Param("serviceId") Long serviceId, @Param("deviceIds") List<Long> deviceIds, @Param("tenantId") Long tenantId);



}
