package com.nti56.nlink.product.device.server.model.channel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/25 10:07<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "根据id查询通道vo")
public class QueryChannelByIdVO {

    private Long id;

    @Schema(description = "通道名字")
    private String name;

    @Schema(description = "通道状态：0-停用，1-启用")
    private Integer status;

    @Schema(description = "自定义协议名")
    private String customDriverName;

    @Schema(description = "是否是服务端通道")
    private Boolean isServer;

    @Schema(description = "所属驱动类型，1-Snap7，2-Modbus，3-OPC UA，4-ZigBee，5-BLE")
    private Integer driver;

    @Schema(description = "时间间隔，单位毫秒")
    private Integer intervalMs;

    @Schema(description = "驱动名称")
    private String driverName;

    @Schema(description = "描述")
    private String descript;

    @Schema(description = "通道参数列表")
    private List<ChannelParamVO> channelParamList;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;


}
