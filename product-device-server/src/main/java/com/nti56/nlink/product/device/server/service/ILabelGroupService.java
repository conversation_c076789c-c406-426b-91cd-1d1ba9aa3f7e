package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.label.LabelGroup;
import com.nti56.nlink.product.device.server.entity.LabelGroupEntity;
import com.nti56.nlink.product.device.server.model.LabelGroupDto;
import com.nti56.nlink.product.device.server.model.label.CopyLabelGroupReq;
import com.nti56.nlink.product.device.server.model.label.dto.LabelDTO;
import com.nti56.nlink.product.device.server.model.label.dto.LabelGroupDTO;
import com.nti56.nlink.product.device.server.model.label.dto.MoveOrCopyLabelGroupDTO;
import com.nti56.nlink.product.device.server.model.label.vo.LabelGroupVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 14:11:44
 * @since JDK 1.8
 */
public interface ILabelGroupService extends IBaseService<LabelGroupEntity> {

    Result<List<LabelGroupDto>> listLabelGroupByChannelId(Long channelId , TenantIsolation tenantIsolation);

    Result<List<LabelGroupEntity>> listLabelGroupByChannelIds(List<Long> channelIds, TenantIsolation tenant);
    
    Result<List<LabelGroupDto>> labelAndGroupTree(String name,Long channelId , TenantIsolation tenantIsolation,Boolean getAll);

    Result<List<LabelGroupVO>> getByChannelId(Long id);

    Result<Integer> countByChannelId(Long channelId);

    Result<Void> deleteByIdsAndTenantIsolation(List<Long> deleteLabelGroupIds, TenantIsolation tenantIsolation);

    @Transactional
    Result<LabelGroup> creatLabelGroup(Long channelId, String newLevelName, TenantIsolation tenantIsolation, LabelGroupDTO labelGroupDTO);

    Result<LabelGroup> updateLabelGroup(String newLevelName, Long channelId, TenantIsolation tenantIsolation, LabelGroupDTO labelGroupDTO);

    Result<Integer> unbindByLabelGroup(Long channelId, TenantIsolation tenantIsolation, LabelGroupDTO labelGroupDTO);

    Result<Void> delete(Long channelId, TenantIsolation tenantIsolation, LabelGroupDTO labelGroupDTO);

    Result<List<LabelGroup>> getLabelGroupTree(Long channelId, TenantIsolation tenantIsolation, String searchName);

    Result<LabelGroupEntity> getByIdAndTenantIsolation(Long labelGroupId, TenantIsolation tenant);

    Result<Void> batchAdd(Long channelId, List<LabelGroup> addGroups, TenantIsolation tenantIsolation);

    List<LabelDTO> exportLabels(Long channelId, TenantIsolation tenantIsolation, String name);

    Result<Void> moveOrCopyLabelGroup(MoveOrCopyLabelGroupDTO dto, TenantIsolation tenantIsolation);

    Result<Void> copyLabelGroup(CopyLabelGroupReq req, TenantIsolation tenantIsolation);

    Result<Void> setEdgeGatewayNotSyncByChannelId(Long channelId);
}
