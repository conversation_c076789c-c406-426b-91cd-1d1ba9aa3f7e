package com.nti56.nlink.product.device.server.model.edgegateway;

import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import lombok.Data;

@Data
public class WriteParam {

    private AccessElm access;

    private Boolean boolValue;
    private Byte byteValue;
    private Short shortValue;
    private Integer intValue;
    private Float floatValue;
    private Double doubleValue;
    private String stringValue;
    private Long longValue;


    private Boolean[] boolArrayValue;
    private byte[] byteArrayValue;
    private Short[] shortArrayValue;
    private Integer[] intArrayValue;
    private Float[] floatArrayValue;
    private Double[] doubleArrayValue;
    private String[] stringArrayValue;
    private Long[] longArrayValue;

    public WriteParam(){}

    public WriteParam(AccessElm access, Object value){
        this.access = access;
        String typeStr = access.getDataType();
        ThingDataTypeEnum type = ThingDataTypeEnum.typeOfName(typeStr);
        switch (type) {
            case BOOLEAN: {
                if(access.getIsArray()){
                    boolArrayValue = (Boolean[])value;
                }else{
                    boolValue = (Boolean)value;
                }
                break;
            }
            case CHAR: {
                if(access.getIsArray()){
                    byteArrayValue = (byte[])value;
                }else{
                    byteValue = (Byte)value;
                }
                break;
            }
            case BYTE:
            case SHORT: {
                if(access.getIsArray()){
                    shortArrayValue = (Short[])value;
                }else{
                    shortValue = (Short)value;
                }
                break;
            }
            case BCD:
            case LBCD:
            case WORD:
            case LONG: {
                if(access.getIsArray()){
                    intArrayValue = (Integer[])value;
                }else{
                    intValue = (Integer)value;
                }
                break;
            }
            case DWORD: {
                if(access.getIsArray()){
                    longArrayValue = (Long[])value;
                }else{
                    longValue = (Long)value;
                }
                break;
            }
            case FLOAT: {
                if(access.getIsArray()){
                    floatArrayValue = (Float[])value;
                }else{
                    floatValue = (Float)value;
                }
                break;
            }
            case DOUBLE: {
                if(access.getIsArray()){
                    doubleArrayValue = (Double[])value;
                }else{
                    doubleValue = (Double)value;
                }
                break;
            }
            case STRING: {
                if(access.getIsArray()){
                    stringArrayValue = (String[])value;
                }else{
                    stringValue = (String)value;
                }
                break;
            }

            default:
                throw new RuntimeException("类型错误");
        }
    }
}
