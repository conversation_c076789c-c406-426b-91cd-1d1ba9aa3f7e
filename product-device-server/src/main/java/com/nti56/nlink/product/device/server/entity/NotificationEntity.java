package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统通知表
 * @TableName notification
 */
@TableName(value ="notification")
@Data
public class NotificationEntity implements Serializable {
    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 描述
     */
    private String notificationContent;

    /**
     * 通知类型，1-全员，2-指定
     */
    private Integer notificationType;

    /**
     * 版本信息
     */
    private String versionInfo;

    /**
     * 通知状态，1-待审，2-通过，3-未通过
     */
    private Integer notificationStatus;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
//    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除，1-删除")
    @TableLogic
    private Boolean deleted;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 工程ID
     */
    private Long engineeringId;

    /**
     * 模块ID
     */
    private Long moduleId;

    /**
     * 空间ID
     */
    private Long spaceId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private boolean readied;
}