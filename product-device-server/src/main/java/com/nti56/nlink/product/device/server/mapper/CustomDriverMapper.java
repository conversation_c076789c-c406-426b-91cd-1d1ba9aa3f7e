package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.product.device.client.model.dto.json.CustomDriverRuntimeInfoField;
import com.nti56.nlink.product.device.server.entity.CustomDriverEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nti56.nlink.common.mybatis.CommonMapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 * 自定义协议表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18 11:17:00
 */
@Mapper
public interface CustomDriverMapper extends CommonMapper<CustomDriverEntity> {

    @Select("SELECT * FROM custom_driver WHERE tenant_id = #{tenantId} AND deleted = 0 AND id = #{id}")
    CustomDriverEntity getById(@Param("tenantId") Long tenantId, @Param("id") Long id);

    @Select("SELECT * FROM custom_driver WHERE tenant_id = #{tenantId} AND deleted = 0")
    List<CustomDriverEntity> listAll(@Param("tenantId") Long tenantId);

    @Select("SELECT * FROM custom_driver WHERE tenant_id = #{tenantId} AND deleted = 0 AND status = 1")
    List<CustomDriverEntity> listAllEnabled(@Param("tenantId") Long tenantId);

    @Update("UPDATE custom_driver SET runtime_info = #{runtimeInfo} WHERE tenant_id = #{tenantId} AND id = #{id} AND deleted = 0")
    Integer updateRuntimeInfo(@Param("tenantId") Long tenantId, @Param("id") Long id, @Param("runtimeInfo") CustomDriverRuntimeInfoField runtimeInfo);

    @Select("SELECT runtime_info FROM custom_driver WHERE tenant_id = #{tenantId} AND deleted = 0")
    List<CustomDriverRuntimeInfoField> listAllRuntimeInfo(@Param("tenantId") Long tenantId);

    @Select("SELECT runtime_info FROM custom_driver WHERE tenant_id = #{tenantId} AND deleted = 0 AND status = 1")
    List<CustomDriverRuntimeInfoField> listAllEnabledRuntimeInfo(@Param("tenantId") Long tenantId);

    @Update("UPDATE custom_driver SET status = #{status} WHERE tenant_id = #{tenantId} AND id = #{id} ")
    Integer updateStatusById(@Param("tenantId") Long tenantId, @Param("id") Long id, @Param("status") Integer status);

}
