package com.nti56.nlink.product.device.server.domain.thing.topic;

import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;

import lombok.Builder;
import lombok.Data;

/**
 * 类说明: 网关同步事件topic
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-08-11 08:33:52
 * @since JDK 1.8
 */
public class GwHeartbeatTopic {
    
    @Data
    @Builder
    public static class TopicInfo{
        private Long tenantId;
        private Long edgeGatewayId;
    }

    public static TopicInfo parseTopic(String topic){
        String[] split = topic.split("/");
        String tenantId = split[2];
        String edgeGatewayId = split[3];

        return TopicInfo.builder()
            .tenantId(Long.parseLong(tenantId))
            .edgeGatewayId(Long.parseLong(edgeGatewayId))
            .build();
    }

    /**
     * 创建心跳topic
     */
    public static String createSubscribeTopic(String group){
        return "$share/" + group + "/" + MqttTopicEnum.GW_HEARTBEAT.getPrefix() 
                + "+/+";
    }

    public static String createEchoTopic(Long tenantId, Long edgeGatewayId) {
        return MqttTopicEnum.OT_HEARTBEAT_ECHO.getPrefix()
                + tenantId + "/" + edgeGatewayId;
    }

    /**
     * 创建未分配心跳topic
     */
    public static String createNotAssignHeartbeatTopic(){
        return MqttTopicEnum.GW_NOT_ASSIGN_HEARTBEAT.getPrefix();
    }

}
