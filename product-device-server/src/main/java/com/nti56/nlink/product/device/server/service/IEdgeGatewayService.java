package com.nti56.nlink.product.device.server.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.EdgeGatewayRuntimeInfoField;
import com.nti56.nlink.product.device.server.domain.thing.channel.Channel;
import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import com.nti56.nlink.product.device.server.model.EdgeGatewayDto;
import com.nti56.nlink.product.device.server.model.HeartbeatInfo;
import com.nti56.nlink.product.device.server.model.IdListDTO;
import com.nti56.nlink.product.device.server.model.LabelGroupDto;
import com.nti56.nlink.product.device.server.model.VersionQueryDTO;
import com.nti56.nlink.product.device.server.model.datasync.SyncEdgeGatewayDto;
import com.nti56.nlink.product.device.server.model.device.vo.DcmpEdgeGatewayVO;
import com.nti56.nlink.product.device.server.model.edgegateway.EdgeGatewayExcel;
import com.nti56.nlink.product.device.server.model.edgegateway.Traffic5gInfo;
import com.nti56.nlink.product.device.server.model.edgegateway.dto.*;
import com.nti56.nlink.product.device.server.model.edgegateway.vo.*;
import com.nti56.nlink.product.device.server.openapi.domain.request.GetEdgeGatewayRequest;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListDcmpEdgeGatewayRequest;
import org.springframework.scheduling.annotation.Async;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 14:11:44
 * @since JDK 1.8
 */
public interface IEdgeGatewayService extends IBaseService<EdgeGatewayEntity> {
    
    Result<CreateEdgeGatewayVO> createEdgeGateway(CreateEdgeGatewayDTO dto, TenantIsolation tenantIsolation);

    Result<Page<EdgeGatewayVO>> getEdgeGatewayPage(EdgeGatewayDto edgeGateway, Page<EdgeGatewayVO> page, TenantIsolation tenantIsolation);

    Result<List<EdgeGatewayEntity>> listEdgeGateway(EdgeGatewayEntity edgeGateway, TenantIsolation tenantIsolation);

    Result<Void> updateEdgeGateway(EditEdgeGatewayDTO dto, TenantIsolation tenantIsolation);

    Result<EdgeGatewayEntity> deleteEdgeGatewayById(Long edgeGatewayId, TenantIsolation tenantIsolation);

    Result<EdgeGatewayVO> getEdgeGatewayById(Long edgeGatewayId, TenantIsolation tenantIsolation);

    Result<EdgeGatewayEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation);

    Result<List<LabelGroupDto>> listLabelGroupByEdgeGateway(Long edgeGatewayId, TenantIsolation tenantIsolation);

    Result<Void> edgeGatewaySyncById(Long id, TenantIsolation tenantIsolation);
    
    Result<Void> syncCustomDriver(TenantIsolation tenant, Long edgeGatewayId);

    Result<Void> batchEdgeGatewaySync(EdgeGatewayDto edgeGatewayDto,TenantIsolation tenantIsolation);

    Result<Void> setNotSyncById(Long edgeGatewayId);

    Result<Void> setEdgeGatewayNotSyncByChannelId(Long channelId);

    Result<Void> setEdgeGatewayNotSyncByLabelGroupId(Long labelGroupId);

    Result<Void> setEdgeGatewayNotSyncByLabelIds(List<Long> labelIds);

    Result<List<EdgeGatewayCurrentTimeInfoVO>> listEdgeGatewayCurrentTimeInfo(IdListDTO dto, TenantIsolation tenantIsolation);

    Result<String> heartbeat(TenantIsolation tenant, Long edgeGatewayId, HeartbeatInfo info);
    
    Result<Boolean> edgeGatewayOnline(Long tenantId, Long edgeGatewayId);


    Result<List<Channel>> listChannelByEdgeGatewayId(Long edgeGatewayId, TenantIsolation tenantIsolation);

    Result<Void> startGather(Long edgeGatewayId, TenantIsolation tenantIsolation);

    Result<Void> stopGather(Long edgeGatewayId, TenantIsolation tenantIsolation);


    Result<EdgeGatewayEntity> edgeGatewayDeleteCheck(Long id, TenantIsolation tenantIsolation);

    Result<DcmpEdgeGatewayVO> getEdgeGatewayForDcmp(TenantIsolation tenantIsolation, GetEdgeGatewayRequest request);
    Result<EdgeGatewayRuntimeInfoField> calculationGatewayDevices(Long edgeGatewayId, TenantIsolation tenantIsolation);

    Result<Void> updateGatewayRunTimeInfo(Long id, TenantIsolation tenantIsolation);

    Result<List<EdgeGatewayEntity>> getAllEdgeGateway();

    Result<List<EdgeGatewayEntity>> getAllEdgeGatewayByTenantId(Long tenantId);

    Result<List<DcmpEdgeGatewayVO>> listEdgeGatewayForDcmp(TenantIsolation tenantIsolation, ListDcmpEdgeGatewayRequest request);
    
    Result<Page<EdgeGatewayVersionVO>> listEdgeGatewayVersion(PageParam pageParam,VersionQueryDTO versionQueryDTO,Long tenantId);
    
    Result<EdgeGatewayOTAInfoVO> getOtaInfo(Long edgeGatewayId, TenantIsolation tenantIsolation);
    
    Result<List<EdgeGatewayEntity>> listEdgeGatewayByIds(Set<Long> ids);
    
    Result<Traffic5gInfo> getTraffic5g(TenantIsolation tenantIsolation, Long edgeGatewayId);
    
    Result<Void> updateEdgeGatewayInfo(EditEdgeGatewayDTO dto, TenantIsolation tenantIsolation);
    
    Result<Map<Long,String>> getTenantMap(Long tenantId);
    
    Result<EdgeGatewayOTAInfoVO> checkEdgeGatewayOTAInfoVO(EdgeGatewayEntity edgeGatewayEntity,String currentVersion);

    Result<EdgeGatewayEntity> buildEdgeGateway(CreateEdgeGatewayDTO dto,Long tenantId);

    @Async
    default void batchUpdateGatewayRunTimeInfo(Set<Long> keySet, TenantIsolation tenant){
        if (CollectionUtil.isNotEmpty(keySet)) {
            keySet.forEach(id -> updateGatewayRunTimeInfo(id,tenant));
        }
    };

    void syncLabelConfiguration(Long edgeGatewayId, SyncEdgeGatewayDto info, Long tenantId);

    Result<SyncEdgeGatewayDto> edgeGatewayPullById(Long id, TenantIsolation tenantIsolation);
    
    Result<List<Long>> selectEdgeGatewayIdList(List<Long> labelIds);

    Result<HardwareInfo> hardwareInfo(TenantIsolation tenantIsolation, Long edgeGatewayId);

    Result<Void> updateEdgeGatewayMonitor(EditEdgeGatewayMonitorDTO dto,Integer type, TenantIsolation tenantIsolation);

    Result<List<ExcelMessageDTO>> edgeGatewayBatchInput(TenantIsolation tenantIsolation, Integer exportType, List<EdgeGatewayExcel> list);

    void exportEdgeGateway(HttpServletResponse response,Long tenantId,Set<Long> edgeGatewayIds) throws IOException;

    Result<List<EdgeGatewayEntity>> notConnectList(Long tenantId);
}
