package com.nti56.nlink.product.device.server.model.inherit;

import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventElm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class EventOfInherit extends EventElm {
    @Schema(description = "直属物模型id")
    private Long baseThingModelId;

    @Schema(description = "直属物模型名称")
    private String baseThingModelName;
    
}
