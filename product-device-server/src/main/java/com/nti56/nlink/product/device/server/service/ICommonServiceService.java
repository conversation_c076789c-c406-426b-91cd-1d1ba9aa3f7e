package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.ServiceConfigEntity;
import com.nti56.nlink.product.device.server.model.ServiceConfigDTO;
import com.nti56.nlink.product.device.server.openapi.domain.request.DoCommonServiceRequest;

/**
 * <AUTHOR>
 * @description 基础服务业务接口
 * @createDate 2023-03-06 14:51:50
 */
public interface ICommonServiceService {

    Result execute(TenantIsolation tenantIsolation, DoCommonServiceRequest request);

    Result deleteServiceTask(TenantIsolation tenantIsolation, Long bId);
}
