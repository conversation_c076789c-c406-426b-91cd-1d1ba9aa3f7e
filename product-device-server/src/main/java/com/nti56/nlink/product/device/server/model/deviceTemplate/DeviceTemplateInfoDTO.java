package com.nti56.nlink.product.device.server.model.deviceTemplate;

import com.nti56.nlink.product.device.client.model.dto.engineering.ProductDeviceServerDataDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName DeviceTemplateInfoDTO
 * @date 2023/3/7 19:34
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "设备模板dto")
public class DeviceTemplateInfoDTO implements Serializable {

    private String name;

    private String platformVersion;

    private ProductDeviceServerDataDTO dataDTO;




}
