package com.nti56.nlink.product.device.server.model.notAssign.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: lql
 * @Date: 2023/04/21/15:56
 * @Description:
 */
@Data
public class ConnectGatewayDto {

    @JSONField(ordinal=1)
    private String host;

    @JSONField(ordinal=2)
    private Integer port;

    @JSONField(ordinal=3)
    private Integer adminPort;

    @JSONField(ordinal=4)
    private ConnectMqtt mqtt;

    @JSONField(ordinal=5)
    private ConnectOt ot;

    @JSONField(ordinal=6)
    private String tenantId;

    @JSONField(ordinal=7)
    private String edgeGatewayId;

    @JSONField(ordinal=8)
    private String heartbeatUuid;

    @JSONField(ordinal=9)
    private Metric metric;

    @JSONField(ordinal=10)
    private Boolean enableProxy;

    @JSONField(ordinal=11)
    private Boolean configIsOk;

    @JSONField(ordinal=12)
    private Boolean connectOt;

    @JSONField(ordinal=13)
    private Boolean gathering;

    public ConnectGatewayDto() {
        this.enableProxy = true;
        this.configIsOk = true;
        this.connectOt = true;
        this.gathering = true;
        this.metric = new Metric();
    }

    @Data
    class Metric {

    }
}
