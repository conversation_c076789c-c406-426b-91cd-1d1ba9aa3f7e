package com.nti56.nlink.product.device.server.model.deviceTemplate;

import com.nti56.nlink.product.device.server.model.channel.dto.CreateChannelDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName DeviceTemplateValidRep
 * @date 2023-12-27 10:33:58
 * @Version 1.0
 */
@Data
@Builder
@Schema(description = "设备云管校验设备模板请求对象")
@AllArgsConstructor
@NoArgsConstructor
public class DeviceTemplateValidReq {

    @Schema(description = "已选中过的模板id")
    private List<Long> selectedList;

    @Schema(description = "当前选中模板id")
    @NotNull(message = "选中模板id为空")
    private Long id;




}
