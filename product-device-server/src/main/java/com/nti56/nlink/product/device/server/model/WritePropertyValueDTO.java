package com.nti56.nlink.product.device.server.model;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 类说明: <br/>
 * <AUTHOR> <br/>
 * @version 1.0
 * @since JDK 1.8
 */
@Data
@Schema(description = "设备属性写值")
public class WritePropertyValueDTO {
    
    @Schema(description = "设备id")
    @NotNull(message = "设备id不能为空")
    private Long deviceId;
    
    @Schema(description = "属性名")
    @NotBlank(message = "属性名不能为空")
    private String propertyName;
    
    @Schema(description = "写的值")
    @NotBlank(message = "写的值不能为空")
    private String writeValue;
}
