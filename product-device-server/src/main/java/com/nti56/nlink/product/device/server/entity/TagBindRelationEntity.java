package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 标志关系表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-11 17:27:49
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tag_bind_relation")
@Schema( description = "标志关系表")
public class TagBindRelationEntity  implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 关联标识ID表
     */
    @Schema(description = "关联标识ID表")
    private Long tagId;

    /**
     * 资源类型:1-产品 2-设备 3-网关
     */
    @Schema(description = "资源类型:1-产品 2-设备 3-网关 4-标签 5-物模型")
    private Integer resourceType;

    /**
     * 目标对象ID
     */
    @Schema(description = "目标对象ID")
    private Long targetId;

    @Schema(description = "逻辑删除，1-删除")
    @TableLogic
    private Boolean deleted;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 工程ID
     */
    @Schema(description = "工程ID")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @Schema(description = "模块ID")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @Schema(description = "空间ID")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

}
