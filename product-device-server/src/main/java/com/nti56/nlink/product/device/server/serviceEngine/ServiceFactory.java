package com.nti56.nlink.product.device.server.serviceEngine;

import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.client.model.dto.json.scriptApi.Service;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Optional;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ServiceFactory
 * @date 2022/4/22 15:34
 * @Version 1.0
 */
@Component
@Slf4j
public class ServiceFactory  implements InitializingBean{

    private HashMap<String, Class<? extends BaseService>> nameAssociations;

    @Override
    public void afterPropertiesSet() throws Exception {
        nameAssociations = new HashMap<>();
        nameAssociations.put("thing", ThingService.class);
    }

    //默认使用Nashorn
    public BaseService getService(){
        return getService("thing");
    }

    public BaseService getService(Service service, DeviceServiceLogEntity logEntity){
        return getService("thing",service, logEntity);
    }

    public BaseService getService(String serviceName, Service service, DeviceServiceLogEntity logEntity){
        BaseService baseService = getService(serviceName);
        if (!Optional.ofNullable(baseService).isPresent()) {
            return null;
        }
        baseService.definedBaseService(service);
        baseService.setServiceName(service.getServiceName());
        baseService.setLogEntity(logEntity);
        return baseService;
    }

    public BaseService getService(String serviceName){
        BaseService baseService = null;
        try {
            baseService = nameAssociations.get(serviceName).getDeclaredConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            log.error(e.getMessage());
        }
        return baseService;
    }
}
