package com.nti56.nlink.product.device.server.model.cloudnest;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class ApiRoleResponse extends BaseResponse {
    /**
     * 角色主键
     */
    @ApiModelProperty(value = "角色主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 集团公司ID
     */
    @ApiModelProperty(value = "集团公司ID")
    private String clientId;

    /**
     * 应用代码
     */
    @ApiModelProperty(value = "应用代码")
    private String appcode;

    /**
     * 角色数字身份标识
     */
    @ApiModelProperty(value = "角色数字身份标识")
    private String roleTid;

    /**
     * 钉钉角色id
     */
    @ApiModelProperty(value = "钉钉角色id")
    private Long dingRoleId;

    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    /**
     * 角色编码
     */
    @ApiModelProperty(value = "角色编码")
    private String roleCode;

    /**
     * 角色类型：1.业务角色 2.普通角色
     */
    @ApiModelProperty(value = "角色类型：1.业务角色 2.普通角色")
    private Integer roleType;

    /**
     * 角色描述
     */
    @ApiModelProperty(value = "角色描述")
    private String roleDescribe;

    /**
     * 角色来源 1:系统创建，2：租户创建
     */
    @ApiModelProperty(value = "角色来源 1:系统创建，2：租户创建")
    private Integer source;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer sortNo;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人id
     */
    @ApiModelProperty(value = "修改人id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long modifyBy;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * 操作人登录IP
     */
    @ApiModelProperty(value = "操作人登录IP")
    private String createIp;
}
