package com.nti56.nlink.product.device.server.exception;

import java.util.ArrayList;
import java.util.List;

import com.nti56.nlink.common.util.ServiceCodeEnum;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BizDetailException extends RuntimeException {
    
    @Getter
    private int code;

    @Getter
    private List<String> messageList;

    public BizDetailException(List<String> messageList) {
        super();
        this.code = ServiceCodeEnum.CODE_UNKNOWN_ERROR.getCode();
        this.messageList = messageList;
    }

}
