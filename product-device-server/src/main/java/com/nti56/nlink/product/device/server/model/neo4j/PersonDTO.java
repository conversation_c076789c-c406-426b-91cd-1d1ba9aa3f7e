//package com.nti56.nlink.product.device.server.model.neo4j;
//
//import lombok.Data;
//import org.neo4j.ogm.annotation.Id;
//import org.neo4j.ogm.annotation.NodeEntity;
//import org.neo4j.ogm.annotation.Property;
//import org.neo4j.ogm.annotation.Relationship;
//
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//
///**
// * 类说明：
// *
// * @ClassName PersonDTO
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/11/17 10:04
// * @Version 1.0
// */
//
//@Data
//@NodeEntity("Person")
//public class PersonDTO {
//
//    @Id
//    private String name;
//
//    @Property
//    private Integer born;
//
//    @Relationship(type = "ACTED_IN",direction = Relationship.OUTGOING)
//    private Set<MovieDTO> actedIns = new HashSet<>();
//
// /*   @Relationship(type = "ACTED_IN",direction = Relationship.OUTGOING)
//    private List<ActedIn> actedRole;*/
//}
