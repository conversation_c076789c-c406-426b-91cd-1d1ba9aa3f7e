package com.nti56.nlink.product.device.server.model.notification.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 系统通知表
 * @TableName notification
 */
@Data
@Schema(description = "notification")
public class NotificationDTO implements Serializable {

    /**
     * 通知标题
     */
    @Schema(description = "通知标题")
    private String title;

    @Schema(description = "通知内容")
    private String notificationContent;

    /**
     * 通知类型，1-全员，2-指定
     */
    private Integer notificationType;

    /**
     * 版本信息
     */
    @Schema(description = "版本信息")
    private String versionInfo;

    /**
     * 通知状态，1-待审，2-通过，3-未通过
     */
    private Integer notificationStatus;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date createTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String creator;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    private Long updatorId;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updator;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date updateTime;



    private static final long serialVersionUID = 1L;
}