package com.nti56.nlink.product.device.server.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:33:25
 * @since JDK 1.8
 */
public enum AuditTargetEnum {
    GATEWAY("edge-gateway", "网关"),
    CHANNEL("channel", "通道"),
    LABEL("label", "标签"),
    SUBSCRIBE("subscribe", "订阅"),
    ;

    @Getter
    private String value;

    @Getter
    private String desc;

    AuditTargetEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
    public static List toList(){
        List<Map> result = new ArrayList<>();
        AuditTargetEnum[] values = AuditTargetEnum.values();
        Map<String,Object> map ;
        for (AuditTargetEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.desc);
            map.put("value",v.value);
            result.add(map);
        }
        return result;
    }



}
