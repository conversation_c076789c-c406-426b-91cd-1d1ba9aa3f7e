package com.nti56.nlink.product.device.server.manager.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.nti56.nlink.product.device.server.entity.Tag;
import com.nti56.nlink.product.device.server.manager.ITagManager;
import com.nti56.nlink.product.device.server.mapper.TagMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/1 11:34<br/>
 * @since JDK 1.8
 */
@Component
@Slf4j
public class TagManagerImpl extends ServiceImpl<TagMapper, Tag> implements ITagManager {
}
