package com.nti56.nlink.product.device.server.model.redirect;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/4 17:38<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "回调对象")
public class RedirectDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 回调名称
     */
    @Schema(description = "回调名称")
    private String redirectName;

    /**
     * 回调类型 0-webhook
     */
    @Schema(description = "回调类型 0-webhook")
    private Integer redirectType;

    /**
     * 回调请求超时时长
     */
    @Schema(description = "回调请求超时时长")
    private Integer redirectRequestTimeout;

    /**
     * 回调请求连接超时时长
     */
    @Schema(description = "回调请求连接超时时长")
    private Integer redirectRequestConnectTimeout;

    /**
     * 回调被调用次数
     */
    @Schema(description = "回调被调用次数")
    private Integer redirectInvokeTime;

    /**
     * 回调方法明细 json
     */
    @Schema(description = "回调方法明细")
    private String redirectFn;

    @Schema(description = "描述")
    private String description;

    /**
     * 逻辑删除，1-删除
     */
    @Schema(description = "逻辑删除，1-删除")
    private Byte deleted;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String lastInvokeTime;

    /**
     * 请求地址
     */
    private String targetUrl;
}
