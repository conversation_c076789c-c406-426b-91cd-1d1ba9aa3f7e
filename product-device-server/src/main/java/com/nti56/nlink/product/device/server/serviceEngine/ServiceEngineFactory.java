package com.nti56.nlink.product.device.server.serviceEngine;

import com.nti56.nlink.product.device.server.serviceEngine.jsEngine.NashornEngine;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ServiceEngineFactory
 * @date 2022/4/20 11:44
 * @Version 1.0
 */
@Component
@Slf4j
public class ServiceEngineFactory  implements InitializingBean{

    final ScriptEngineManager manager = new ScriptEngineManager();

    private volatile static ScriptEngine engine;

    private HashMap<String, Class<? extends ServiceEngine>> nameAssociations;

    private ConcurrentHashMap<String, ScriptEngine> scriptEngineAssociations;

    private ConcurrentHashMap<String, ServiceEngine> serviceEngineAssociations;

    @Autowired
    private ServiceThreadPoolTaskExecutor serviceTaskExecutor;

    @Override
    public void afterPropertiesSet() throws Exception {
        nameAssociations = new HashMap<>();
        nameAssociations.put("nashorn", NashornEngine.class);
        scriptEngineAssociations = new ConcurrentHashMap<>();
        serviceEngineAssociations = new ConcurrentHashMap<>();
    }

    //默认使用Nashorn
    public ServiceEngine getServiceEngine(){
        return getServiceEngine("nashorn");
    }

    public ServiceEngine getServiceEngine(String scriptEngineName){
        ServiceEngine serviceEngine = getSingletonServiceEngine(scriptEngineName);
        return serviceEngine;
    }


    private ServiceEngine getSingletonServiceEngine(String serviceEngineName){
        if (!nameAssociations.containsKey(serviceEngineName)) {
            return null;
        }
        Class<? extends ServiceEngine> serviceEngineClass = nameAssociations.get(serviceEngineName);
        if (this.serviceEngineAssociations.containsKey(serviceEngineName)) {
            return this.serviceEngineAssociations.get(serviceEngineName);
        }
        ServiceEngine serviceEngine;
        synchronized (this.serviceEngineAssociations) {
            serviceEngine = this.serviceEngineAssociations.get(serviceEngineName);
            if (serviceEngine == null) {
                try {
                    serviceEngine = nameAssociations.get(serviceEngineName).newInstance();
                    ScriptEngine scriptEngine = getSingletonScriptEngine(serviceEngineName);
                    serviceEngine.engine = scriptEngine;
                    serviceEngine.serviceTaskExecutor = serviceTaskExecutor;
                    serviceEngineAssociations.put(serviceEngineName,serviceEngine);
                } catch (InstantiationException e) {
                    log.error(e.getMessage());
                } catch (IllegalAccessException e) {
                    log.error(e.getMessage());
                }
            }
        }
        return serviceEngine;

    }

    private ScriptEngine getSingletonScriptEngine(String scriptEngineName){
        if (scriptEngineAssociations.containsKey(scriptEngineName)) {
            return scriptEngineAssociations.get(scriptEngineName);
        }
        ScriptEngine scriptEngine;
        synchronized (this.scriptEngineAssociations) {
            scriptEngine = this.scriptEngineAssociations.get(scriptEngineName);
            if (scriptEngine == null) {
                scriptEngine = manager.getEngineByName(scriptEngineName);
                scriptEngineAssociations.put(scriptEngineName,scriptEngine);
            }
        }
        return scriptEngine;
    }

}
