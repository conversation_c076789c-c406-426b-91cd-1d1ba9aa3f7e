//package com.nti56.nlink.product.device.server.model.neo4j;
//
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import org.neo4j.ogm.annotation.Id;
//import org.neo4j.ogm.annotation.NodeEntity;
//import org.neo4j.ogm.annotation.Property;
//import org.neo4j.ogm.annotation.Relationship;
//
//import java.util.Set;
//
///**
// * 类说明：
// *
// * @ClassName MovieDTO
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/11/17 9:03
// * @Version 1.0
// */
//@NodeEntity("Device")
//@Data
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
//public class Neo4jDeviceDTO {
//
//
//    @Id
//    private Long deviceId;
//    @Property
//    private String name;
//    @Property
//    private String description;
//    @Property
//    private String updateTime;
//    @Property
//    private Integer status;
//    @Property
//    private Long tenantId;
//    @Property
//    private Long gatewayId;
//
//    @Relationship(type = "INHERIT", direction = Relationship.OUTGOING)
//    private Set<Neo4jModelDTO> inheritModels;
//}
