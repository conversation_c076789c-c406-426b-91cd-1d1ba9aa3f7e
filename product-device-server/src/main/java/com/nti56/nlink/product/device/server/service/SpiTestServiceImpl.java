package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DriverEnum;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewaySpiProxy;
import com.nti56.nlink.product.device.server.model.ConnectResult;
import com.nti56.nlink.product.device.server.model.edgegateway.WriteParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;

/**
 * 类说明: spi测试用
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-07-14 13:32:04
 * @since JDK 1.8
 */
@Service
public class SpiTestServiceImpl implements ISpiTestService {

    @Autowired
    private IEdgeGatewaySpiProxy edgeGatewaySpiProxy;

    @Override
    public Result connectChannel(TenantIsolation tenantIsolation) {
        List<ChannelElm> channelList = getChannelElmList();
        Result<List<ConnectResult>> result = edgeGatewaySpiProxy.connectChannel(1372781655425024L,tenantIsolation.getTenantId(),channelList);
        return result;
    }

    private List<ChannelElm> getChannelElmList(){
        List<ChannelElm> channelList = new ArrayList<>();
        ChannelElm channelElm = new ChannelElm();
        channelElm.setChannelId(1382818109235202L);
        channelElm.setDriver(DriverEnum.MELSEC.getName());
        channelElm.setIp("*************");
        channelElm.setPort(6000);
        channelList.add(channelElm);
        return channelList;
    }

    @Override
    public Result connectLabel(TenantIsolation tenantIsolation) {
        List<AccessElm> labelList = getLabelList();
        List<ChannelElm> channelList = getChannelElmList();
        Result<List<ConnectResult>> result = edgeGatewaySpiProxy.connectLabel(1372781655425024L,tenantIsolation.getTenantId(),labelList,channelList);
        return result;
    }

    private List<AccessElm> getLabelList(){
        List<AccessElm> accessElmList = new ArrayList<>();
        AccessElm accessElm1 = new AccessElm();
        accessElm1.setLabelId(1359902970445824L);
        accessElm1.setProperty("property_d100");
        accessElm1.setLabelName("D100");
        accessElm1.setDataType(ThingDataTypeEnum.SHORT.getName());
        accessElm1.setAddress("D100");
        accessElm1.setIsArray(false);
        accessElm1.setLength(1);
        accessElm1.setStringBytes(0);
        accessElm1.setChannelId(1382818109235202L);
        accessElmList.add(accessElm1);
       /* AccessElm accessElm2 = new AccessElm();
        accessElm2.setLabelId(1357493217017856L);
        accessElm2.setProperty("property_2");
        accessElm2.setLabelName("bool_1");
        accessElm2.setDataType("bool");
        accessElm2.setAddress("id=1;f=1;address=2");
        accessElm2.setIsArray(false);
        accessElm2.setLength(1);
        accessElm2.setStringBytes(0);
        accessElm2.setChannelId(1382818109235202L);
        accessElmList.add(accessElm2);*/
        return accessElmList;
    }

    @Override
    public Result writeBool(TenantIsolation tenantIsolation) {

        AccessElm accessElm = getBoolAccessElm();
        Result<Void> result = edgeGatewaySpiProxy.writeBool(1382248732934144L,tenantIsolation.getTenantId(),accessElm,true);
        return result;
    }

    private AccessElm getBoolAccessElm(){
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017856L);
        accessElm.setProperty("property_1");
        accessElm.setLabelName("bool_1");
        accessElm.setDataType(ThingDataTypeEnum.BOOLEAN.getName());
        accessElm.setAddress("INFO[0].RFIDFault");
        accessElm.setIsArray(false);
        accessElm.setLength(1);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);
        return accessElm;
    }

    private ChannelElm getRTUChannelElm(){
        ChannelElm channelElm = new ChannelElm();
        channelElm.setChannelId(1382818109235202L);
        channelElm.setDriver(DriverEnum.ModbusRtu.getName());
        channelElm.setComId(1);
        channelElm.setBaudRate(9600);
        channelElm.setDataBits(8);
        channelElm.setStopBits(1);
        channelElm.setParity(0);
        channelElm.setSlaveId(1);
        channelElm.setEndianness("DCBA");
        channelElm.setFirstAddress("是");
        return channelElm;
    }

    private ChannelElm getChannelElm(){
        ChannelElm channelElm = new ChannelElm();
        channelElm.setChannelId(1382818109235202L);
        channelElm.setDriver(DriverEnum.ETHER_IP.getName());
        channelElm.setIp("**************");
        channelElm.setPort(44818);
        channelElm.setSlot(0);
        channelElm.setReconnectGapMs(3000);
        channelElm.setMaxConnection(1);
        channelElm.setDelayIdleMs(0);
        channelElm.setChannelKey("**************44818");
        return channelElm;
    }

    private ChannelElm getOpcuaChannelElm(){
        ChannelElm channelElm = new ChannelElm();
        channelElm.setChannelId(1382818109235202L);
        channelElm.setDriver(DriverEnum.OPCUA.getName());
        channelElm.setIp("*************");
        channelElm.setPort(49320);
        channelElm.setUserName("opcua");
        channelElm.setPassword("opcua");
        return channelElm;
    }

    @Override
    public Result writeShort(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1359902970445824L);
        accessElm.setProperty("property_D");
        accessElm.setLabelName("D_2");
        accessElm.setDataType(ThingDataTypeEnum.SHORT.getName());
        accessElm.setAddress("INFO[0].TaskNum");
        accessElm.setIsArray(false);
        accessElm.setLength(1);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);
        Result<Void> result = edgeGatewaySpiProxy.writeShort(1382248732934144L,tenantIsolation.getTenantId(),accessElm, (short) 33);
        return result;
    }

    @Override
    public Result writeInt(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017856L);
        accessElm.setProperty("property_3");
        accessElm.setLabelName("int_3");
        accessElm.setDataType(ThingDataTypeEnum.LONG.getName());
        accessElm.setAddress("INFO[0].TestDINT");
        accessElm.setIsArray(false);
        accessElm.setLength(1);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);
        Result<Void> result = edgeGatewaySpiProxy.writeInt(1382248732934144L,tenantIsolation.getTenantId(),accessElm,  55);
        return result;
    }

    @Override
    public Result writeFloat(TenantIsolation tenantIsolation) {
        AccessElm accessElm = getAccessElmFloat();
        Result<Void> result = edgeGatewaySpiProxy.writeFloat(1382248732934144L,tenantIsolation.getTenantId(),accessElm,  55.6f);
        return result;
    }

    @Override
    public Result writeString(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017856L);
        accessElm.setProperty("property_5");
        accessElm.setLabelName("string_5");
        accessElm.setDataType(ThingDataTypeEnum.STRING.getName());
        accessElm.setAddress("INFO[0].TestSTRING");
        accessElm.setIsArray(false);
        accessElm.setLength(1);
        accessElm.setStringBytes(1);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);
        Result<Void> result = edgeGatewaySpiProxy.writeString(1382248732934144L,tenantIsolation.getTenantId(),accessElm,  "g");
        return result;
    }

    private AccessElm getAccessElmFloat(){
        ChannelElm channelElm = getChannelElm();
        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017856L);
        accessElm.setProperty("property_4");
        accessElm.setLabelName("INFO[0].TestREAL");
        accessElm.setDataType(ThingDataTypeEnum.FLOAT.getName());
        accessElm.setAddress("INFO[0].TestREAL");
        accessElm.setIsArray(false);
        accessElm.setLength(1);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);
        return accessElm;
    }

    @Override
    public Result writeByte(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017856L);
        accessElm.setProperty("property_6");
        accessElm.setLabelName("byte_6");
        accessElm.setDataType(ThingDataTypeEnum.BYTE.getName());
        accessElm.setAddress("INFO[0].TestSINT");
        accessElm.setIsArray(false);
        accessElm.setLength(1);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);
        Byte a = 51;
        Result<Void> result = edgeGatewaySpiProxy.writeByte(1382248732934144L,tenantIsolation.getTenantId(),accessElm,  a);
        return result;
    }

    @Override
    public Result writeBoolArray(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1362579214639104L);
        accessElm.setProperty("property_array_bool");
        accessElm.setLabelName("bool_array");
        accessElm.setDataType(ThingDataTypeEnum.BOOLEAN.getName());
        accessElm.setAddress("D100");
        accessElm.setIsArray(true);
        accessElm.setLength(3);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);

        Boolean[] value = new Boolean[]{true,true,true};
        Result<Void> result = edgeGatewaySpiProxy.writeBoolArray(1372781655425024L,tenantIsolation.getTenantId(),accessElm,value);
        return result;
    }

    @Override
    public Result writeShortArray(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1362579214639104L);
        accessElm.setProperty("property_array_short");
        accessElm.setLabelName("short_array");
        accessElm.setDataType(ThingDataTypeEnum.SHORT.getName());
        accessElm.setAddress("INFO[0].TestINT20");
        accessElm.setIsArray(true);
        accessElm.setLength(3);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);

        Short[] value = new Short[]{1,2,3};
        Result<Void> result = edgeGatewaySpiProxy.writeShortArray(1382248732934144L,tenantIsolation.getTenantId(),accessElm,value);
        return result;
    }

    @Override
    public Result writeIntArray(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017858L);
        accessElm.setProperty("property_array_int");
        accessElm.setLabelName("int_array");
        accessElm.setDataType(ThingDataTypeEnum.LONG.getName());
        accessElm.setAddress("INFO[0].TestDINT16");
        accessElm.setIsArray(true);
        accessElm.setLength(3);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);

        Integer[] value = new Integer[]{1,2,3};
        Result<Void> result = edgeGatewaySpiProxy.writeIntArray(1382248732934144L,tenantIsolation.getTenantId(),accessElm,value);
        return result;
    }

    @Override
    public Result writeFloatArray(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017859L);
        accessElm.setProperty("property_array_float");
        accessElm.setLabelName("float_array");
        accessElm.setDataType(ThingDataTypeEnum.FLOAT.getName());
        accessElm.setAddress("INFO[0].TestREAL16");
        accessElm.setIsArray(true);
        accessElm.setLength(3);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);

        Float[] value = new Float[]{1.1f,1.2f,1.3f};
        Result<Void> result = edgeGatewaySpiProxy.writeFloatArray(1382248732934144L,tenantIsolation.getTenantId(),accessElm,value);
        return result;
    }

    @Override
    public Result writeStringArray(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017859L);
        accessElm.setProperty("property_array_string");
        accessElm.setLabelName("string_array");
        accessElm.setDataType(ThingDataTypeEnum.STRING.getName());
        accessElm.setAddress("D100");
        accessElm.setIsArray(true);
        accessElm.setLength(3);
        accessElm.setStringBytes(2);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);

        String[] value = new String[]{"aa","bb","ab"};
        Result<Void> result = edgeGatewaySpiProxy.writeStringArray(1372781655425024L,tenantIsolation.getTenantId(),accessElm,value);
        return result;
    }

    @Override
    public Result writeByteArray(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017860L);
        accessElm.setProperty("property_array_byte");
        accessElm.setLabelName("byte_array");
        accessElm.setDataType(ThingDataTypeEnum.BYTE.getName());
        accessElm.setAddress("D100");
        accessElm.setIsArray(true);
        accessElm.setLength(6);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);

        byte[] value = new byte[]{51,51,52,52,53,53};
        Result<Void> result = edgeGatewaySpiProxy.writeByteArray(1382248732934144L,tenantIsolation.getTenantId(),accessElm,value);
        return result;
    }

    @Override
    public Result writeWordArray(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017860L);
        accessElm.setProperty("property_array_word");
        accessElm.setLabelName("word_array");
        accessElm.setDataType(ThingDataTypeEnum.WORD.getName());
        accessElm.setAddress("D100");
        accessElm.setIsArray(true);
        accessElm.setLength(3);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);
        Integer[] value = new Integer[]{2,3,4};
        Result<Void> result = edgeGatewaySpiProxy.writeIntArray(1382248732934144L,tenantIsolation.getTenantId(),accessElm,value);
        return result;
    }

    @Override
    public Result writeWord(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017860L);
        accessElm.setProperty("property_word");
        accessElm.setLabelName("word");
        accessElm.setDataType(ThingDataTypeEnum.WORD.getName());
        accessElm.setAddress("INFO[0].TaskNum");
        accessElm.setIsArray(false);
        accessElm.setLength(1);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);
        Result<Void> result = edgeGatewaySpiProxy.writeInt(1382248732934144L,tenantIsolation.getTenantId(),accessElm,66);
        return result;
    }

    @Override
    public Result writeDouble(TenantIsolation tenantIsolation) {
        AccessElm accessElm = getDoubleAccessElm();
        Result<Void> result = edgeGatewaySpiProxy.writeDouble(1382248732934144L,tenantIsolation.getTenantId(),accessElm,1.88);
        return result;
    }

    private AccessElm getDoubleAccessElm(){
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017859L);
        accessElm.setProperty("property_double");
        accessElm.setLabelName("double");
        accessElm.setDataType(ThingDataTypeEnum.DOUBLE.getName());
        accessElm.setAddress("D100");
        accessElm.setIsArray(false);
        accessElm.setLength(1);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);
        return accessElm;
    }

    @Override
    public Result writeDoubleArray(TenantIsolation tenantIsolation) {
        AccessElm accessElm = getDoubleArrayAccessElm();

        Double[] value = new Double[]{1.123,1.223,1.33};
        Result<Void> result = edgeGatewaySpiProxy.writeDoubleArray(1382248732934144L,tenantIsolation.getTenantId(),accessElm,value);
        return result;
    }

    private AccessElm getDoubleArrayAccessElm(){
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017859L);
        accessElm.setProperty("property_array_double");
        accessElm.setLabelName("double_array");
        accessElm.setDataType(ThingDataTypeEnum.DOUBLE.getName());
        accessElm.setAddress("D100");
        accessElm.setIsArray(true);
        accessElm.setLength(3);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);
        return accessElm;
    }

    @Override
    public Result multiWrite(TenantIsolation tenantIsolation) {
      /*  List<WriteParam> params = new ArrayList<>();
        WriteParam writeParam1 = new WriteParam();
        AccessElm accessElm1 = getDoubleAccessElm();
        writeParam1.setAccess(accessElm1);
        writeParam1.setDoubleValue(33.248);
        params.add(writeParam1);

        WriteParam writeParam2 = new WriteParam();
        AccessElm accessElm2 = getDoubleArrayAccessElm();
        writeParam2.setAccess(accessElm2);
        Double[] value = new Double[]{5.55,5.56,5.57};
        writeParam2.setDoubleArrayValue(value);
        params.add(writeParam2);*/
        List<WriteParam> params = getMultiWriteParam();
        Result<List<ConnectResult>> result = edgeGatewaySpiProxy.multiWrite(1372781655425024L,tenantIsolation.getTenantId(),params);
        return result;
    }

    private List<WriteParam> getMulti(){
        List<WriteParam> params = new ArrayList<>();
        AccessElm accessElm = getBoolAccessElm();
        WriteParam writeParam = new WriteParam();
        writeParam.setAccess(accessElm);
        writeParam.setBoolValue(true);
        params.add(writeParam);
        return params;
    }

    private List<WriteParam> getMultiWriteParam() {
        List<WriteParam> params = new ArrayList<>();
        ChannelElm channelElm = getChannelElm();
        WriteParam writeParam1 = new WriteParam();
        AccessElm accessElm1 = new AccessElm();
        accessElm1.setLabelId(1359902970445824L);
        accessElm1.setProperty("property_d100");
        accessElm1.setLabelName("D100");
        accessElm1.setDataType(ThingDataTypeEnum.BOOLEAN.getName());
        accessElm1.setAddress("D100");
        accessElm1.setIsArray(false);
        accessElm1.setLength(1);
        accessElm1.setStringBytes(0);
        accessElm1.setChannelId(1382818109235202L);
        accessElm1.setChannel(channelElm);
        writeParam1.setAccess(accessElm1);
        writeParam1.setBoolValue(false);

        WriteParam writeParam2 = new WriteParam();
        AccessElm accessElm2 = new AccessElm();
        accessElm2.setLabelId(1360302062018578L);
        accessElm2.setProperty("property_d105");
        accessElm2.setLabelName("D105");
        accessElm2.setDataType(ThingDataTypeEnum.SHORT.getName());
        accessElm2.setAddress("D105");
        accessElm2.setIsArray(false);
        accessElm2.setLength(1);
        accessElm2.setStringBytes(0);
        accessElm2.setChannelId(1382818109235202L);
        accessElm2.setChannel(channelElm);
        writeParam2.setAccess(accessElm2);
        writeParam2.setShortValue((short)91);

        WriteParam writeParam3 = new WriteParam();
        AccessElm accessElm3 = new AccessElm();
        accessElm3.setLabelId(1359902970445824L);
        accessElm3.setProperty("property_d100");
        accessElm3.setLabelName("D100");
        accessElm3.setDataType(ThingDataTypeEnum.LONG.getName());
        accessElm3.setAddress("D100");
        accessElm3.setIsArray(false);
        accessElm3.setLength(1);
        accessElm3.setStringBytes(0);
        accessElm3.setChannelId(1382818109235202L);
        accessElm3.setChannel(channelElm);
        writeParam3.setAccess(accessElm3);
        writeParam3.setIntValue(88);

        WriteParam writeParam4 = new WriteParam();
        AccessElm accessElm4 = new AccessElm();
        accessElm4.setLabelId(1359902970445827L);
        accessElm4.setProperty("property_d100");
        accessElm4.setLabelName("D108");
        accessElm4.setDataType(ThingDataTypeEnum.DWORD.getName());
        accessElm4.setAddress("D108");
        accessElm4.setIsArray(false);
        accessElm4.setLength(1);
        accessElm4.setStringBytes(0);
        accessElm4.setChannelId(1382818109235202L);
        accessElm4.setChannel(channelElm);
        writeParam4.setAccess(accessElm4);
        writeParam4.setLongValue((long) 123);


        params.add(writeParam1);
        params.add(writeParam2);
//        params.add(writeParam2);
//        params.add(writeParam3);
//        params.add(writeParam4);
        return params;
    }

    private ChannelElm getMelsecChannelElm(){
        ChannelElm channelElm = new ChannelElm();
        channelElm.setChannelId(1382818109235202L);
        channelElm.setDriver(DriverEnum.MELSEC.getName());
        channelElm.setIp("*************");
        channelElm.setPort(6000);
        return channelElm;
    }

    @Override
    public Result writeDWordArray(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017860L);
        accessElm.setProperty("property_array_word");
        accessElm.setLabelName("dword_array");
        accessElm.setDataType(ThingDataTypeEnum.DWORD.getName());
        accessElm.setAddress("D108");
        accessElm.setIsArray(true);
        accessElm.setLength(3);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);
        Long[] value = new Long[]{6l,2l,3l};
        Result<Void> result = edgeGatewaySpiProxy.writeLongArray(1382248732934144L,tenantIsolation.getTenantId(),accessElm,value);
        return result;
    }

    @Override
    public Result writeDWord(TenantIsolation tenantIsolation) {
        ChannelElm channelElm = getChannelElm();

        AccessElm accessElm = new AccessElm();
        accessElm.setLabelId(1357493217017860L);
        accessElm.setProperty("property_word");
        accessElm.setLabelName("dword");
        accessElm.setDataType(ThingDataTypeEnum.DWORD.getName());
        accessElm.setAddress("INFO[0].TestDINT");
        accessElm.setIsArray(false);
        accessElm.setLength(1);
        accessElm.setStringBytes(0);
        accessElm.setChannelId(1382818109235202L);
        accessElm.setChannel(channelElm);
        Result<Void> result = edgeGatewaySpiProxy.writeLong(1382248732934144L,tenantIsolation.getTenantId(),accessElm,66L);
        return result;
    }

}
