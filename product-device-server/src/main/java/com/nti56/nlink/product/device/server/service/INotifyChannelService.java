package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.NotifyChannelEntity;
import com.nti56.nlink.product.device.server.model.channel.dto.CreateNotifyChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.EditNotifyChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.NotifyChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.vo.NotifyChannelVO;

import java.util.List;


public interface INotifyChannelService extends IService<NotifyChannelEntity> {

    Result createChannel(CreateNotifyChannelDTO dto, TenantIsolation tenantIsolation);

    Result editChannel(EditNotifyChannelDTO dto, TenantIsolation tenantIsolation);

    Result deleteChannel(Long id, TenantIsolation tenantIsolation);

    Result<NotifyChannelEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation);

    Result<List<NotifyChannelVO>> listChannel(NotifyChannelDTO dto, TenantIsolation tenantIsolation);

    Result<Page<NotifyChannelEntity>> pageChannel(PageParam pageParam, NotifyChannelDTO dto, TenantIsolation tenantIsolation);
}
