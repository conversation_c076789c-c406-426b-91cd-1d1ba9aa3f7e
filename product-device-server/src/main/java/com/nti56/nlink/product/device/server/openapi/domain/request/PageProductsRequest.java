package com.nti56.nlink.product.device.server.openapi.domain.request;

import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.product.device.server.model.product.dto.QueryProductDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PageProductsRequest {
    @Schema(description = "分页参数")
    private PageParam pageParam;
    
    @Schema(description = "查询条件")
    private QueryProductDTO dto;
}
