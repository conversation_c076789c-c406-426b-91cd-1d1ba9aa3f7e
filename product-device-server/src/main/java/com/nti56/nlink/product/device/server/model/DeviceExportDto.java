package com.nti56.nlink.product.device.server.model;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.model.thingModel.vo.ThingModelVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 设备dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:58
 * @since JDK 1.8
 */
@Data
public class DeviceExportDto {
    /**
     * 设备名称
     */
    @ColumnWidth(25)
    @ExcelProperty("设备名称")
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER,horizontalAlignment = HorizontalAlignmentEnum.CENTER
            ,borderTop = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN,borderLeft = BorderStyleEnum.THIN,borderRight = BorderStyleEnum.THIN)

    private String name;
    /**
     * 所属标记
     */
    @ColumnWidth(25)
    @ExcelProperty("标记")
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER,horizontalAlignment = HorizontalAlignmentEnum.CENTER
            ,borderTop = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN,borderLeft = BorderStyleEnum.THIN,borderRight = BorderStyleEnum.THIN)

    private String tagName;


    /**
     * 所属网关
     */
    @ColumnWidth(25)
    @ExcelProperty("所属网关")
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER,horizontalAlignment = HorizontalAlignmentEnum.CENTER
            ,borderTop = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN,borderLeft = BorderStyleEnum.THIN,borderRight = BorderStyleEnum.THIN)

    private String edgeGatewayName;

}
