package com.nti56.nlink.product.device.server.model.cloudnest;

import java.io.Serializable;

public abstract class BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    public BaseRequest() {
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof BaseRequest)) {
            return false;
        } else {
            BaseRequest other = (BaseRequest) o;
            return other.canEqual(this);
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof BaseRequest;
    }


    public String toString() {
        return "BaseRequest()";
    }

}