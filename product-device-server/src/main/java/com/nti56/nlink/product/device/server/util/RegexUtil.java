package com.nti56.nlink.product.device.server.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.InputDataField;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.Snap7DataTypeEnum;
import com.nti56.nlink.product.device.server.model.label.dto.ExcelLabelDTO;
import javax.sound.midi.Soundbank;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:23:17
 * @since JDK 1.8
 */
public class RegexUtil {

    private static final Pattern ipv4Pattern = Pattern.compile(
            "^((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}$"
    );
    private static final Pattern portPattern = Pattern.compile(
            "^([0-9]|[1-9]\\d{1,3}|[1-5]\\d{4}|6[0-4]\\d{3}|65[0-4]\\d{2}|655[0-2]\\d|6553[0-5])$"
    );
    private static final Pattern intPattern = Pattern.compile(
            "^-?\\d+$"
    );
    private static final Pattern ushortPattern = Pattern.compile(
            "^\\d+$"
    );
    private static final Pattern uintPattern = Pattern.compile(
            "^\\d+$"
    );
    private static final Pattern shortPattern = Pattern.compile(
            "^-?\\d+$"
    );
    private static final Pattern floatPattern = Pattern.compile(
            "^-?\\d+(.\\d+)?$"
    );
    public static final Pattern snap7AddressPattern = Pattern.compile(
            "^(DB|M|I|Q)(\\d{0,10})\\.(DBX|BIT|X|DBB|BYTE|B|DBW|INT|I|WORD|W|DBD|DWORD|DINT|REAL|D|STRING|S)(\\d{1,10})(\\.(\\d{0,1}))?$"
    );

    private static Pattern iec104AddressPattern = Pattern.compile(
            "^(M_ME_SV|M_ME_FV|M_ME_NV|M_IT|M_SP|C_SC|C_SE_FV|M_DP|C_DC)\\.[1-9](\\d{1,10})?$"
    );

    private static Pattern iec104AsduPublicAddressPattern = Pattern.compile(
            "^[1-9]\\d?$"
    );

    private static final Pattern snap7ImqAddressPattern1 = Pattern.compile(
            "^(M|I|Q)(DBX|BIT|X|DBB|BYTE|B|DBW|INT|I|WORD|W|DBD|DWORD|DINT|REAL|D|STRING|S)(\\d{1,10})(\\.(\\d{0,1}))?$"
    );
    private static final Pattern snap7ImqBitAddressPattern2 = Pattern.compile(
            "^(M|I|Q)(\\d{1,10})(\\.(\\d{0,1}))?$"
    );
    private static final Pattern opcUaAddressPattern = Pattern.compile(
            "(ns=)(.+)(;s=)(.+)"
    );

    private static final Pattern bacnetIpAddressPattern = Pattern.compile(
            "^([a-zA-Z])+\\.([0-9])+\\.([a-zA-Z])+(\\[([0-9])+\\])?$"
    );
    private static final Pattern modbusBoolAddressPattern = Pattern.compile(
            "^(0|1)[0-9]+[0-9]*$"
    );
    private static final Pattern modbusNumberAddressPattern = Pattern.compile(
            "^(3|4)[0-9]+[0-9]*$"
    );
    private static final Pattern namePattern = Pattern.compile(
            "^[a-zA-Z][a-zA-Z0-9_]*$"
    );

    private static final Pattern namePattern2 = Pattern.compile(
            "^[a-zA-Z0-9_]+$"
    );

    private static final Pattern melsecAddressPattern = Pattern.compile(
            "^(SD|D|W|TN|SN|CN|SW|Z)(\\d+)$"
    );

    private static final Pattern melsecBoolAddressPattern = Pattern.compile(
            "^(X|Y|M|L|F|V|B|SM|TS|TC|SS|SC|CS|CC|SB|S|DX|DY)(\\d+)$"
    );

    private static final Pattern keyenceNanoUnSignedShortAddressPattern = Pattern.compile(
            "^(DM|W|TM|Z|CM)(\\d+)$"
    );

    private static final Pattern keyenceBoolAddressPattern = Pattern.compile(
            "^(R|B|MR|LR|CR|VB|VM)(\\d+)$"
    );

    private static final Pattern keyenceNanoIntAddressPattern = Pattern.compile(
            "^(T|TC|TS|C|CC|CS|CTH|CTC|AT)(\\d+)$"
    );

    private static final Pattern labelGroupLevelNamePattern = Pattern.compile(
            "^([^\\.\\f\\n\\r\\t\\v ]+\\.?)*[^\\.]$"
    );

    private static final Pattern labelGroupNamePattern = Pattern.compile(
            "[^\\.\\f\\n\\r\\t\\v ]+"
    );

    private static final Pattern labelNamePattern = namePattern;

    public static final Pattern theWildcardPattern = Pattern.compile(
            "\\$\\{\\w+\\}"
    );

    private static final Pattern serialPortPattern = Pattern.compile(
            "^(COM)(\\d+)$"
    );

    private static final Pattern baudRatePattern = Pattern.compile(
            "^(300|600|1200|2400|4800|9600|14400|19200|28800|38400|56000|57600|115200|128000|256000)"
    );

    private static final Pattern dataBitsPattern = Pattern.compile(
            "^(5|6|7|8)"
    );

    private static final Pattern stopBitsPattern = Pattern.compile(
            "^(1|2)"
    );

    private static final Pattern parityPattern = Pattern.compile(
            "^(0|1|2)"
    );

    private static final Pattern finsAddressPattern = Pattern.compile(
            "^(D|C|W|H|A|L|E)(\\d{1,10})(\\.(\\d{0,1}))?$"
    );

    private static final Pattern inovanceColAddressPattern = Pattern.compile(
            "^[IQ][0-9]+$"
    );

    private static final Pattern inovanceRegistryAddressPattern = Pattern.compile(
            "^M[0-9]+$"
    );

    public static final String channelNamePatternString = "^[\\u4e00-\\u9fa5_a-zA-Z0-9-]+$";

    private static final Pattern panasonicAddressPattern = Pattern.compile(
            "^(X|Y|R|T|C|L|D|V|WX|WY|WR|WL|DT|LD|SV|EV|I)[A-F0-9]+$"
    );

    public static boolean checkIpv4(String ipStr) {
        if (ipStr == null) {
            return false;
        }
        Matcher m = ipv4Pattern.matcher(ipStr);
        boolean matches = m.matches();
        return matches;
    }

    public static boolean checkPort(String portStr) {
        if (portStr == null) {
            return false;
        }
        Matcher m = portPattern.matcher(portStr);
        boolean matches = m.matches();
        return matches;
    }

    public static boolean checkInt(String str) {
        if (str == null) {
            return false;
        }
        Matcher m = intPattern.matcher(str);
        boolean matches = m.matches();
        return matches;
    }

    /**
     * @return dataType
     */
    public static Result<Snap7DataTypeEnum> checkSnap7Address(String addressStr) {
        if (addressStr == null) {
            return Result.error();
        }
        {
            Matcher m = snap7AddressPattern.matcher(addressStr);
            boolean matches = m.matches();
            if (matches) {
                String dataTypeStr = m.group(3);
                Snap7DataTypeEnum dataType = Snap7DataTypeEnum.typeOfAlias(dataTypeStr);
                if (dataType != null) {
                    return Result.ok(dataType);
                }
            }
        }
        {
            Matcher m = snap7ImqAddressPattern1.matcher(addressStr);
            boolean matches = m.matches();
            if (matches) {
                String dataTypeStr = m.group(2);
                Snap7DataTypeEnum dataType = Snap7DataTypeEnum.typeOfAlias(dataTypeStr);
                if (dataType != null) {
                    return Result.ok(dataType);
                }
            }
        }
        {
            Matcher m = snap7ImqBitAddressPattern2.matcher(addressStr);
            boolean matches = m.matches();
            if (matches) {
                return Result.ok(Snap7DataTypeEnum.BIT);
            }
        }

        return Result.error();
    }

    public static Result<Void> checkMelsecAddress(String addressStr, String dataType) {
        if (addressStr == null) {
            return Result.error();
        }
        ThingDataTypeEnum thingDataTypeEnum = ThingDataTypeEnum.typeOfName(dataType);
        if (Objects.isNull(thingDataTypeEnum)) {
            return Result.error("暂不支持的类型：" + dataType);
        }
        Matcher m = null;
        if (ThingDataTypeEnum.BOOLEAN.equals(thingDataTypeEnum)) {
            m = melsecBoolAddressPattern.matcher(addressStr);
        } else {
            m = melsecAddressPattern.matcher(addressStr);
        }
        boolean matches = m.matches();
        if (matches) {
            return Result.ok();
        }
        if(ThingDataTypeEnum.BOOLEAN.equals(thingDataTypeEnum)){
            return Result.error(dataType+ "类型地址应以X|Y|M|L|F|V|B|SM|TS|TC|SS|SC|CS|CC|SB|S|DX|DY开头");
        }
        return Result.error(dataType + "类型地址应以SD|D|W|TN|SN|CN|SW|Z开头");
    }

    public static Result<Void> checkLabelName(String name){
        if(name == null || name.length() > 128){
            return Result.error();
        }
        Matcher m = labelNamePattern.matcher(name);
        boolean matches = m.matches();
        if(matches){
            return Result.ok();
        }
        return Result.error();
    }

    public static Result<Void> checkLabelGroupName(String name){
        if(name == null || name.length() > 128){
            return Result.error();
        }
        Matcher m = labelGroupNamePattern.matcher(name);
        boolean matches = m.matches();
        if(matches){
            return Result.ok();
        }
        return Result.error();
    }

    public static Result<Void> checkLabelGroupLevelName(String name){
        if(name == null || name.length() > 128){
            return Result.error();
        }
        Matcher m = labelGroupLevelNamePattern.matcher(name);
        boolean matches = m.matches();
        if(matches){
            return Result.ok();
        }
        return Result.error();
    }

    public static Result<Void> checkLabelGroupLevelNames(Collection<String> names){
        if (CollectionUtil.isEmpty(names)) {
            return Result.ok();
        }
        for (String name : names) {
            Result<Void> result = checkLabelGroupLevelName(name);
            if (!result.getSignal()) {
                return Result.error();
            }
        }
        return Result.ok();
    }
    
    
    public static List<ExcelLabelDTO> checkLabelGroupLevelNames(Collection<String> names,List<ExcelLabelDTO> excelLabelDTOList){
        if (CollectionUtil.isEmpty(names)) {
            return excelLabelDTOList;
        }
        for (String name : names) {
            if( excelLabelDTOList.size() >= 200){
                return excelLabelDTOList;
            }
            Result<Void> result = checkLabelGroupLevelName(name);
            if (!result.getSignal()) {
                ExcelLabelDTO excelLabelDTO = new ExcelLabelDTO();
                excelLabelDTO.setMessageName("labelGroup:"+name);
                excelLabelDTO.setMessageContent("labelGroupName 格式校验失败");
                excelLabelDTOList.add(excelLabelDTO);
            }
        }
        return excelLabelDTOList;
    }
    
    public static boolean checkName(String nameStr) {
        if(nameStr == null){
            return false;
        }
        if("".equals(nameStr)){
            return false;
        }
        Matcher m = namePattern.matcher(nameStr);
        boolean matches = m.matches();
        return matches;
    }

    public static boolean checkName2(String nameStr) {
        if(nameStr == null){
            return false;
        }
        if("".equals(nameStr)){
            return false;
        }
        Matcher m = namePattern2.matcher(nameStr);
        boolean matches = m.matches();
        return matches;
    }

    public static boolean checkValueString(String value, ThingDataTypeEnum dataType){
        switch (dataType) {
            case BOOLEAN:{
                if("true".equals(value) || "false".equals(value)){
                    return true;
                }
                return false;
            }
            case CHAR:{
                return true;
            }
            case BYTE:{
                return true;
            }
            case SHORT:{
                Matcher m = shortPattern.matcher(value);
                boolean matches = m.matches();
                return matches;
            }
            case WORD:{
                Matcher m = ushortPattern.matcher(value);
                boolean matches = m.matches();
                return matches;
            }
            case LONG:{
                Matcher m = intPattern.matcher(value);
                boolean matches = m.matches();
                return matches;
            }
            case DWORD:{
                Matcher m = uintPattern.matcher(value);
                boolean matches = m.matches();
                return matches;
            }
            case FLOAT:{
                Matcher m = floatPattern.matcher(value);
                boolean matches = m.matches();
                return matches;
            }
            case DOUBLE:{
                Matcher m = floatPattern.matcher(value);
                boolean matches = m.matches();
                return matches;
            }
            case BCD:{
                Matcher m = intPattern.matcher(value);
                boolean matches = m.matches();
                return matches;
            }
            case LBCD:{
                Matcher m = intPattern.matcher(value);
                boolean matches = m.matches();
                return matches;
            }
            case STRING:{

                return true;
            }
            case DATA_MODEL:{
                return false;
            }
            default:
                return false;
        }
    }

    public static boolean checkInputNameRepeat(InputDataField[] inputData) {
        Set<String> nameSet = new HashSet<>();
        if (Optional.ofNullable(inputData).isPresent()) {
            Iterator<InputDataField> iterator = Arrays.stream(inputData).iterator();
            while (iterator.hasNext()) {
                InputDataField next = iterator.next();
                if (nameSet.contains(next.getName())) {
                    return true;
                }
                nameSet.add(next.getName());
            }
        }
        return false;
    }

    /**
     * opcUa地址校验
     *
     * @param addressStr
     * @return
     */
    public static Result<Void> checkOpcuaAddress(String addressStr) {
        if (ObjectUtil.isNull(addressStr) || "".equals(addressStr.trim())) {
            return Result.error();
        }
        Matcher m = opcUaAddressPattern.matcher(addressStr);
        boolean matches = m.matches();
        if (matches) {
            return Result.ok();
        }
        return Result.error();
    }
    
    /**
     * modbus地址校验
     *
     * @param addressStr
     * @return
     */
    public static Result<Void> checkModbusAddress(String addressStr,String dataType,Boolean isArray) {
        if (StringUtils.isBlank(addressStr)) {
            return Result.error();
        }
        ThingDataTypeEnum thingDataTypeEnum = ThingDataTypeEnum.typeOfName(dataType);
        if (Objects.isNull(thingDataTypeEnum)) {
            return Result.error("暂不支持的类型：" + dataType);
        }
        switch (thingDataTypeEnum){
            case CHAR:
            case BYTE:
                if(isArray){
                    return checkModbus(addressStr,dataType);
                }
                String firstStr = addressStr.substring(0,1);
                if(!"H".equals(firstStr) && !"L".equals(firstStr)){
                    return Result.error("Byte 类型应以如H40001 ｜ ：L40001" + dataType);
                }
                String byteAddress = addressStr.substring(1);
                return checkModbus(byteAddress,dataType);
            case BOOLEAN:
                if((addressStr.startsWith("3") || addressStr.startsWith("4"))){
                    if(isArray){
                        return Result.error(dataType + "3|4类型没有数组类型");
                    }
                    try {
                        String booleanStr = addressStr.substring(addressStr.indexOf(".")+1);
                        if(Integer.valueOf(booleanStr) > 15){
                            return Result.error(dataType + "类型.后面地址值超过最大范围");
                        }
                        if(Integer.valueOf(addressStr.substring(1,addressStr.indexOf("."))) > 65533){
                            return Result.error(dataType + "类型地址值超过最大范围");
                        }
                    }catch (Exception e){
                        return Result.error(dataType + "类型3|4开头地址应包含.");
                    }
                }else {
                    Matcher m = modbusBoolAddressPattern.matcher(addressStr);
                    if(!m.matches()){
                        return Result.error(dataType+ "类型地址应以0|1开头");
                    }
                    String address  = addressStr.substring(1);
                    try {
                        if(Integer.valueOf(address) > 65533){
                            return Result.error(dataType + "类型地址值超过最大范围");
                        }
                    }catch (Exception e){
                        return Result.error(dataType + "类型地址值超过最大范围");
                    }
                }
                return Result.ok();
            case SHORT:
            case WORD:
                if((addressStr.startsWith("0") || addressStr.startsWith("1"))){
                    if(isArray){
                        return Result.error(dataType + "0|1类型没有数组类型");
                    }
                    try {
                        String wordBitStr = addressStr.substring(addressStr.indexOf("#")+1);
                        if(Integer.valueOf(wordBitStr) < 1){
                            return Result.error(dataType + "类型#面地址值应大于1");
                        }
                        if(Integer.valueOf(wordBitStr) > 16){
                            return Result.error(dataType + "类型#后面地址值超过最大范围");
                        }
                        if(Integer.valueOf(addressStr.substring(1,addressStr.indexOf("#"))) > 65533){
                            return Result.error(dataType + "类型地址值超过最大范围");
                        }
                    }catch (Exception e){
                        return Result.error(dataType + "类型0|1开头地址应包含#");
                    }
                    return Result.ok();
                }
            case LONG:
            case DWORD:
            case FLOAT:
            case DOUBLE:
            case STRING:
                return checkModbus(addressStr,dataType);
            default:
                return Result.error("暂不支持的类型：" + dataType);
        }
    }
    
    private static Result<Void> checkModbus(String addressStr,String dataType){
        Matcher m  = modbusNumberAddressPattern.matcher(addressStr);
        boolean matches = m.matches();
        if(!matches){
            return Result.error(dataType + "类型地址应以3|4开头");
        }
        String address  = addressStr.substring(1);
        try {
            if(Integer.valueOf(address) > 65533){
                return Result.error(dataType + "类型地址值超过最大范围");
            }
        }catch (Exception e){
            return Result.error(dataType + "类型地址值超过最大范围");
        }
        return Result.ok();
    }

    public static Result<Void> checkKeyenceNanoAddress(String addressStr, String dataType) {

        if(StringUtils.isBlank(addressStr)){
            return Result.error();
        }
        ThingDataTypeEnum thingDataTypeEnum = ThingDataTypeEnum.typeOfName(dataType);
        if(Objects.isNull(thingDataTypeEnum)){
            return Result.error("暂不支持的类型：" + dataType);
        }
        Matcher m = null;
        boolean matches = false;
        switch (thingDataTypeEnum){
            case SHORT:
            case WORD:
                m = keyenceNanoUnSignedShortAddressPattern.matcher(addressStr);
                matches = m.matches();
                if(matches){
                    return Result.ok();
                }else{
                    return Result.error(dataType+ "类型地址应以DM|W|TM|Z|CM开头");
                }
            case DWORD:
                m = keyenceNanoIntAddressPattern.matcher(addressStr);
                matches = m.matches();
                if(matches){
                    return Result.ok();
                }else{
                    return Result.error(dataType+ "类型地址应以T|TC|TS|C|CC|CS|CTH|CTC|AT开头");
                }
            case BOOLEAN:
                m = keyenceBoolAddressPattern.matcher(addressStr);
                matches = m.matches();
                if(matches){
                    return Result.ok();
                }else{
                    return Result.error(dataType+ "类型地址应以R|B|MR|LR|CR|VB|VM开头");
                }
            default:
                return Result.error("暂不支持的类型：" + dataType + "，支持的类型有：SHORT, WORD, DWORD, BOOLEAN ");
        }
    }

    public static boolean checkSerialPort(String serialPort) {
        Matcher m = serialPortPattern.matcher(serialPort);;
        return m.matches();
    }

    public static boolean checkBaudRate(String baudRateStr) {
        Matcher m = baudRatePattern.matcher(baudRateStr);;
        return m.matches();
    }

    public static boolean checkDataBits(String dataBitsStr) {
        Matcher m = dataBitsPattern.matcher(dataBitsStr);;
        return m.matches();
    }

    public static boolean checkStopBits(String stopBitsStr) {
        Matcher m = stopBitsPattern.matcher(stopBitsStr);;
        return m.matches();
    }

    public static boolean checkParity(String parityStr) {
        Matcher m = parityPattern.matcher(parityStr);;
        return m.matches();
    }

    public static Result<Void> checkBacnetIpAddress(String addressStr) {
        if (StringUtils.isBlank(addressStr)) {
            return Result.error();
        }
        Matcher m = bacnetIpAddressPattern.matcher(addressStr);
        boolean matches = m.matches();
        if (matches) {
            return Result.ok();
        }
        return Result.error();
    }


    public static Result<Void> checkPanasonicAddress(String addressStr) {
        if (StringUtils.isBlank(addressStr)){
            return Result.error();
        }
        Matcher m = panasonicAddressPattern.matcher(addressStr);
        boolean matches = m.matches();
        if(matches){
            return Result.ok();
        }
        return Result.error();
    }

    public static Result<Void> checkIec104AsduPublicAddress(String addressStr) {
        if (StringUtils.isBlank(addressStr)) {
            return Result.error();
        }
        Matcher m = iec104AsduPublicAddressPattern.matcher(addressStr);
        boolean matches = m.matches();
        if (matches) {
            return Result.ok();
        }
        return Result.error();
    }

    public static Result<Void> checkIec104Address(String addressStr, ThingDataTypeEnum dataTypeEnum) {
        if (StringUtils.isBlank(addressStr)) {
            return Result.error();
        }
        Matcher m = iec104AddressPattern.matcher(addressStr);
        boolean matches = m.matches();
        if (matches) {
            String[] split = addressStr.split("\\.");
            switch (dataTypeEnum) {
                case WORD: {
                    if (!split[0].equals("M_ME_SV")) return Result.error("WORD 类型仅支持 M_ME_SV 地址！");
                    break;
                }
                case DWORD: {
                    if (!split[0].equals("M_IT")) return Result.error("DWORD 类型仅支持 M_IT 地址！");
                    break;
                }
                case BOOLEAN: {
                    if (!(split[0].equals("C_SC") || split[0].equals("M_SP")))
                        return Result.error("BOOLEAN 类型仅支持 C_SC、M_SP 地址！");
                    break;
                }
                case FLOAT: {
                    if (!(split[0].equals("M_ME_FV") || split[0].equals("M_ME_NV") || split[0].equals("C_SE_FV")))
                        return Result.error("FLOAT 类型仅支持 M_ME_FV、M_ME_NV和C_SE_FV 地址！");
                    break;
                }
                case BYTE: {
                    if (!(split[0].equals("M_DP") || split[0].equals("C_DC")))
                        return Result.error("Byte 类型仅支持 C_DC、M_DP 地址！");
                    break;
                }
                default:
                    return Result.error();
            }

            return Result.ok();
        }
        return Result.error();
    }

    public static Result<Void> checkFinsAddress(String addressStr, String dataType) {
        if (StringUtils.isBlank(addressStr)) {
            return Result.error();
        }
        ThingDataTypeEnum thingDataTypeEnum = ThingDataTypeEnum.typeOfName(dataType);
        if (Objects.isNull(thingDataTypeEnum)) {
            return Result.error("暂不支持的类型：" + dataType);
        }
        switch (thingDataTypeEnum){
            case BOOLEAN:
            case SHORT:
            case WORD:
            case LONG:
            case DWORD:
            case FLOAT:
            case DOUBLE:
            case STRING:
                Matcher m = finsAddressPattern.matcher(addressStr);
                boolean matches = m.matches();
                if(matches){
                    return Result.ok();
                }
                return Result.error(dataType + "类型地址应以D|C|W|H|A|L|E开头");
            default:
                return Result.error("暂不支持的类型：" + dataType);
        }

    }

    public static Result<Void> checkInovanceAddress(String addressStr, String dataType) {
        if (StrUtil.isBlank(addressStr)) {
            return Result.error("标签地址不能为空");
        }
        if (StrUtil.isBlank(dataType)) {
            return Result.error("标签地址类型不能为空");
        }
        ThingDataTypeEnum thingDataTypeEnum = ThingDataTypeEnum.typeOfName(dataType);
        if (Objects.isNull(thingDataTypeEnum)) {
            return Result.error("暂不支持的类型：" + dataType);
        }
        switch (thingDataTypeEnum) {
            case BOOLEAN:
                Matcher rMatcher = inovanceRegistryAddressPattern.matcher(addressStr);
                Matcher cMatcher = inovanceColAddressPattern.matcher(addressStr);
                if (rMatcher.matches() || cMatcher.matches()) {
                    try{
                        int num = Integer.parseInt(addressStr.substring(1));
                        if (num >= 0 && num <= 65535) {
                            return Result.ok();
                        } else {
                            return Result.error("地址范围只能取0-65535间");
                        }
                    }catch (Exception e){
                        return Result.error("点位地址格错误，范围在0-65535");
                    }

                } else {
                    return Result.error("Boolean类型地址只能以I|Q|M开头");
                }
            case BYTE:
            case SHORT:
            case CHAR:
            case WORD:
            case DWORD:
            case STRING:
                Matcher matcher = inovanceRegistryAddressPattern.matcher(addressStr);
                if (matcher.matches() || matcher.matches()) {
                    try{
                        int num = Integer.parseInt(addressStr.substring(1));
                        if (num >= 0 && num <= 65535) {
                            return Result.ok();
                        } else {
                            return Result.error("地址范围只能取0-65535间");
                        }
                    }catch (Exception e){
                        return Result.error("点位地址格错误，范围在0-65535");
                    }

                }else{
                    return Result.error(dataType + "类型地址只能以M开头");
                }
            default:
                return Result.error("暂不支持的类型：" + dataType);
        }
    }

    public static Result<Void> checkAbPLCAddress(String addressStr, String dataType) {
        if (StringUtils.isBlank(addressStr)) {
            return Result.error();
        }
        ThingDataTypeEnum thingDataTypeEnum = ThingDataTypeEnum.typeOfName(dataType);
        if (Objects.isNull(thingDataTypeEnum)) {
            return Result.error("暂不支持的类型：" + dataType);
        }
        switch (thingDataTypeEnum){
            case CHAR:
            case BOOLEAN:
            case SHORT:
            case LONG:
            case FLOAT:
            case DOUBLE:
            case STRING:
                return Result.ok();
            default:
                return Result.error("暂不支持的类型：" + dataType);
        }
    }
}
