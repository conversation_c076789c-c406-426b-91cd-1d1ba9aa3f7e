package com.nti56.nlink.product.device.server.model;


import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.model.thingModel.vo.ThingModelVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 批量打标签
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-07-14 14:09:58
 * @since JDK 1.8
 */
@Data
@Schema(description = "批量打标签")
public class BatchDeviceTagDto{

    @Schema(description = "设备ID")
    private List<Long> idList;

    @Schema(description = "标记实体")
    private List<TagRsp> tags;

}
