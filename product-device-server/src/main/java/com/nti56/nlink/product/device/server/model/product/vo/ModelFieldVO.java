package com.nti56.nlink.product.device.server.model.product.vo;

import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import com.nti56.nlink.product.device.server.entity.DeviceServiceEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/28 11:39<br/>
 * @since JDK 1.8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema
public class ModelFieldVO {
    @Schema(description = "属性")
    private List<PropertyElm> properties;

    @Schema(description = "事件")
    private List<EventElm> events;

    @Schema(description = "服务列表")
    private List<DeviceServiceEntity> services;
}
