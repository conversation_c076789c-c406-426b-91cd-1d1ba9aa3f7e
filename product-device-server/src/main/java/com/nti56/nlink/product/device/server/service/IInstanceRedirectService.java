package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.InstanceRedirectEntity;
import com.nti56.nlink.product.device.server.model.edgegateway.dto.ExcelMessageDTO;
import com.nti56.nlink.product.device.server.model.redirect.ExcelRedirectDTO;
import com.nti56.nlink.product.device.server.model.redirect.RedirectDTO;
import com.nti56.nlink.product.device.server.model.redirect.RedirectReferenceDto;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Set;

/**
 * 类说明：
 *
 * @ClassName InstanceRedirectService
 * @Description 针对表【re_instance_redirect(实例回调操作)】的数据库操作Service
 * <AUTHOR>
 * @Date 2022/6/24 15:52
 * @Version 1.0
 */

public interface IInstanceRedirectService extends IService<InstanceRedirectEntity> {

    Result<Page<RedirectDTO>> getPage(InstanceRedirectEntity entity, Page<InstanceRedirectEntity> page);

    Result<List<InstanceRedirectEntity>> list(InstanceRedirectEntity entity);

    Result<InstanceRedirectEntity> save(TenantIsolation tenantIsolation, InstanceRedirectEntity entity);


    Result update(InstanceRedirectEntity entity, TenantIsolation tenantIsolation);


    Result deleteById(Long redirectId, TenantIsolation tenantIsolation);


    Result<InstanceRedirectEntity> getByIdAndTenantIsolation(Long redirectId, TenantIsolation tenantIsolation);


    Result<Object> testRedirect(InstanceRedirectEntity entity, TenantIsolation tenantIsolation);

//    Result<Boolean> execRedirect(Long redirectId, TenantIsolation tenantIsolation);

    Result<Boolean> execRedirectWithPayload(Long redirectId, TenantIsolation tenantIsolation, Object payload);
    
    Result<Integer> redirectCount(TenantIsolation tenantIsolation);

    Result<Boolean> updateReference(TenantIsolation toBean, RedirectReferenceDto referenceDto);

    Result<List<Object>> getReference(Long redirectId, TenantIsolation tenantIsolation);

    Result<List<ExcelMessageDTO>> instanceRedirectBatchInput(TenantIsolation tenantIsolation, Integer exportType, List<ExcelRedirectDTO> list);

    void exportRedirect(HttpServletResponse response, Long tenantId, Set<Long> referenceIds) throws IOException;
}
