package com.nti56.nlink.product.device.server.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/13 16:56<br/>
 * @since JDK 1.8
 */
public enum NotifyServerLogEnum {
    SEND_NOTIFY_SUCCESS(100,"通知发送成功"),
    //该枚举日志会重发
    SEND_NOTIFY_FAIL(101,"通知发送失败"),
    SMS_LENGTH_OVERSIZE(102,"短信长度超出限制"),
    SEND_PARAMS_ERROR(103,"发送参数异常"),
    NONE_RECEIVER_INFO_ERROR(104,"没有接收人信息"),
    RECEIVER_FORMAT_ERROR(105,"接收人格式异常"),



    SEND_EMAIL_FAIL(200,"发送邮件失败"),


    CREATE_CLIENT_FAIL(300,"创建客户端失败"),
    DELETE_TEMPLATE_SUCCESS(301,"删除模板成功"),
    DELETE_TEMPLATE_FAIL(302, "删除模板失败"),
    BATCH_SEND_SMS_FAIL(303,"批量发送短信失败"),



    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    NotifyServerLogEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static NotifyServerLogEnum typeOfValue(Integer value){
        NotifyServerLogEnum[] values = NotifyServerLogEnum.values();
        for (NotifyServerLogEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static NotifyServerLogEnum typeOfName(String name){
        NotifyServerLogEnum[] values = NotifyServerLogEnum.values();
        for (NotifyServerLogEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static String getNameByValue(Integer value){
        NotifyServerLogEnum[] values = NotifyServerLogEnum.values();
        for (NotifyServerLogEnum v : values) {
            if (v.value.equals(value)) {
                return v.name;
            }
        }
        return null;

    }


    public static List toList(){
        List<Map> result = new ArrayList<>();
        NotifyServerLogEnum[] values = NotifyServerLogEnum.values();
        Map<String,Object> map ;
        for (NotifyServerLogEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            result.add(map);
        }
        return result;
    }
}
