package com.nti56.nlink.product.device.server.service.impl;

import com.nti56.nlink.common.rule.feign.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.service.IExportSqlService;
import com.nti56.nlink.product.device.server.service.IFeignExportService;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/5/19 18:14<br/>
 * @since JDK 1.8
 */
@Service
public class FeignExportServiceImpl implements IFeignExportService {
//    @Autowired
//    private IFeignRuleEngineService exportRuleEngineService;
//
//    @Autowired
//    private IFeignAuthService authService;
//
//    @Autowired
//    private IFeignUserService userService;
//
//    @Autowired
//    private IFeignNotifyService feignNotifyService;

    @Autowired
    private IExportSqlService exportSqlService;

    @Override
    public R export(TenantIsolation tenantIsolation, Integer type) {
        switch (type) {
//            case 1:
//                return R.result(exportService.export(tenantIsolation));
//            case 2:
//                return exportRuleEngineService.export(tenantIsolation);
//            case 3:
//                return feignNotifyService.export(tenantIsolation);
            case 4:
                return exportSqlService.export(tenantIsolation);
//            case 5:
//                return userService.export(tenantIsolation);
//            case 6:
//                return authService.export(tenantIsolation);
            default:
                return R.error("未定义");
        }
    }
}
