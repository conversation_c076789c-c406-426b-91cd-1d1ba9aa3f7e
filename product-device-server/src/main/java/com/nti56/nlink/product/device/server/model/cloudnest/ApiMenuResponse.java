package com.nti56.nlink.product.device.server.model.cloudnest;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class ApiMenuResponse {
    /**
     * 菜单主键
     */
    @ApiModelProperty(value = "菜单主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用代码
     */
    @ApiModelProperty(value = "应用代码")
    private String appcode;

    /**
     * 菜单数字身份标识
     */
    @ApiModelProperty(value = "菜单数字身份标识")
    private String menuTid;

    /**
     * 上级菜单ID
     */
    @ApiModelProperty(value = "上级菜单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pId;

    /**
     * 菜单类型  0:目录  1:菜单 2:按钮
     */
    @ApiModelProperty(value = "菜单类型  0:目录  1:菜单 2:按钮")
    private Integer menuType;

    /**
     * 菜单分类：1.PC端菜单 2.移动端菜单
     */
    @ApiModelProperty(value = "菜单分类：1.PC端菜单 2.移动端菜单")
    private Integer menuCategory;

    /**
     * 菜单是否缓存：1是 0否
     */
    @ApiModelProperty(value = "菜单是否缓存：1是 0否")
    private Integer enableCache;

    /**
     * 功能名称
     */
    @ApiModelProperty(value = "功能名称")
    private String menuName;

    /**
     * 功能编码
     */
    @ApiModelProperty(value = "功能编码")
    private String menuCode;

    /**
     * 静态页面地址
     */
    @ApiModelProperty(value = "静态页面地址")
    private String staticAddress;

    /**
     * 外部页面
     */
    @ApiModelProperty(value = "外部页面")
    private String externalPage;

    /**
     * 外部链接接入方式
     */
    @ApiModelProperty(value = "外部链接接入方式：1 iframe，2 qiankun")
    private Integer externalUriType;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortNo;


    /**
     * 菜单图标
     */
    @ApiModelProperty(value = "菜单图标")
    private String icon;

    /**未选中菜单图标  */
    @ApiModelProperty(value = "选中菜单图标")
    private String checkedIcon;

    /**描述  */
    @ApiModelProperty(value = "描述")
    private String menuDescribe;

    /**
     * 标签
     */
    @ApiModelProperty(value = "标签")
    private String tag;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;

}
