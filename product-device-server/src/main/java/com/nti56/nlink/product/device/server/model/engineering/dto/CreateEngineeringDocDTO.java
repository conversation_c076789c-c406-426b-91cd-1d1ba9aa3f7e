package com.nti56.nlink.product.device.server.model.engineering.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/21 13:53<br/>
 * @since JDK 1.8
 */
@Data
public class CreateEngineeringDocDTO {
    @NotBlank(message = "文件名称不能为空")
    @Length(max = 256,message = "文件名称长度不超过256字")
    private String docName;

    private String password;

    private String confirmPassword;

    private String desc;

}
