package com.nti56.nlink.product.device.server.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:05:36
 * @since JDK 1.8
 */
@Data
@Schema(description = "任务心跳参数")
public class TaskHeartbeatParam {

    @Schema(description = "计算任务ids")
    private List<Long> computeTaskIdList;
    
    @Schema(description = "采集任务ids")
    private List<Long> gatherTaskIdList;
    
}
