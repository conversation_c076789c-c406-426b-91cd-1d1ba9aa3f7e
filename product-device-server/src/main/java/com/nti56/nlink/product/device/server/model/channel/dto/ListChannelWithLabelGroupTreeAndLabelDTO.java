package com.nti56.nlink.product.device.server.model.channel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/10/12 10:41<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "根据网关id查询channel列表")
public class ListChannelWithLabelGroupTreeAndLabelDTO {

    @NotNull(message = "网关标识不能为空")
    @Schema(description = "网关id")
    private Long edgeGatewayId;

    @Schema(description = "需要展开的通道")
    private List<Long> channelIds;

}
