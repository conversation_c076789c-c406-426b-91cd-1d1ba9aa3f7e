package com.nti56.nlink.product.device.server.model;


import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 网关dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:58
 * @since JDK 1.8
 */
@Data
@Schema(description = "网关模型")
public class EdgeGatewayDto extends EdgeGatewayEntity {

    @Schema(description = "创建时间开始")
    private String createTimeStart;

    @Schema(description = "创建时间结束")
    private String createTimeEnd;

    @Schema(description = "标记id列表")
    private List<Long> tagIds;

    /**
     * 通道列表
     */
    @Schema(description = "通道列表")
    private List<Long> channelIdList;
    
    @Schema(description = "-1:全部;0:离线;1:在线")
    private Integer online;
    
    /**
     * 网关ID集合 用于查询过来
     */
    @Schema(description = "网关ID集合")
    private List<Long> IdList;

    @Schema(description = "网关类型 -1:全部;1:网关设备;2:虚拟网关;3:直连网关")
    private Integer type;
    
}
