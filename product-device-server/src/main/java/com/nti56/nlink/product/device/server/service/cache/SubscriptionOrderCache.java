package com.nti56.nlink.product.device.server.service.cache;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.Data;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

public class SubscriptionOrderCache {


    private static ReentrantLock lock = new ReentrantLock();


    //deviceId，subscriptionId，timestamp
    private static Table<Long, Long, Long> subscriptionOrderTable = HashBasedTable.create();

    private static Map<String, DeviceSubscribeLockTime> deviceSubsLockTimeMap = new ConcurrentHashMap<>();

    public static boolean isEmpty(Long deviceId, Long subscriptionId) {
        lock.lock();
        try {
            Long timeStamp = subscriptionOrderTable.get(deviceId, subscriptionId);
            return timeStamp==null;
        } finally {
            lock.unlock();
        }
    }


    public static Long pop(Long deviceId, Long subscriptionId) {
        lock.lock();
        try {
            return subscriptionOrderTable.remove(deviceId, subscriptionId);
        } finally {
            lock.unlock();
        }
   }

    public static boolean push(Long deviceId, Long subscriptionId, Long newTimestamp) {
        lock.lock();
        try {
            Long timeStamp = subscriptionOrderTable.get(deviceId, subscriptionId);
            if(timeStamp==null || newTimestamp>timeStamp){
                subscriptionOrderTable.put(deviceId, subscriptionId, newTimestamp);
                return true;
            }
            return false;
        } finally {
            lock.unlock();
        }
    }


    /**
     * 每个设备id和订阅id对应一个锁，避免资源争抢
     * @param deviceId
     * @param subscriptionId
     * @param newTimestamp
     * @return
     */

    public static boolean push2(Long deviceId, Long subscriptionId, Long newTimestamp) {
        String key = String.format("%d_%d", deviceId, subscriptionId);
        DeviceSubscribeLockTime deviceSubscribeLockTime =  deviceSubsLockTimeMap.putIfAbsent(key, new DeviceSubscribeLockTime());
        if (deviceSubscribeLockTime == null) {
            deviceSubscribeLockTime = deviceSubsLockTimeMap.get(key);
        }
        deviceSubscribeLockTime.getLock().lock();
        try{
            if(deviceSubscribeLockTime.getTimestamp()==null || newTimestamp>deviceSubscribeLockTime.getTimestamp()){
                deviceSubscribeLockTime.setTimestamp(newTimestamp);
                return true;
            }
            return false;
        }finally {
            deviceSubscribeLockTime.getLock().unlock();
        }
    }



    @Data
    public static class DeviceSubscribeLockTime{

        private Long timestamp=0L;

        private final ReentrantLock lock=new ReentrantLock();




    }






}
