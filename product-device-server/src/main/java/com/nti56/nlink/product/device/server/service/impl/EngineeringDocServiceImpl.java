package com.nti56.nlink.product.device.server.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.util.encode.KernelEncryptDecodeUtils;
import com.nti56.nlink.product.device.client.model.dto.engineering.NotifyServiceDataDTO;
import com.nti56.nlink.product.device.client.model.dto.engineering.ProductDeviceServerDataDTO;
import com.nti56.nlink.product.device.server.entity.EngineeringDocEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.EngineeringDocMapper;
import com.nti56.nlink.product.device.server.model.engineering.CreateEngineeringDocFileRep;
import com.nti56.nlink.product.device.server.model.engineering.dto.CommonServiceDataDTO;
import com.nti56.nlink.product.device.server.model.engineering.dto.CreateEngineeringDocDTO;
import com.nti56.nlink.product.device.server.model.engineering.dto.EngineeringDocInfoDTO;
import com.nti56.nlink.product.device.server.service.IEngineeringCommonService;
import com.nti56.nlink.product.device.server.service.IEngineeringDocService;
import com.nti56.nlink.product.device.server.service.IEngineeringNotifyService;
import com.nti56.nlink.product.device.server.service.IEngineeringProductService;
import com.nti56.nlink.product.device.server.service.engineering.*;
import com.nti56.nlink.product.device.server.util.FileUtil;
import com.nti56.nlink.common.util.JwtUserInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;


/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/24 9:08<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class EngineeringDocServiceImpl extends ServiceImpl<EngineeringDocMapper, EngineeringDocEntity> implements IEngineeringDocService {

    @Autowired
    private IEngineeringProductService engineeringProductService;

    @Autowired
    private IEngineeringNotifyService engineeringNotifyService;

//    @Autowired
//    private EngineeringRuleEngineServiceClient engineeringRuleEngineServiceClient;


    @Autowired
    private IEngineeringCommonService engineeringCommonService;

    @Autowired
    private ProductDeviceHandle productDeviceHandle;

    //    @Autowired
//    private AlarmHandle alarmHandle;
//
//    @Autowired
//    private CommonHandle commonHandle;
//
    @Autowired
    private NotifyHandle notifyHandle;
//
//    @Autowired
//    private RuleEngineHandle ruleEngineHandle;

    @Value("${platformVersion}")
    private String platformVersion;


    @Override
    public Result<EngineeringDocEntity> getTheLatestAutoData(TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<EngineeringDocEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(EngineeringDocEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(EngineeringDocEntity::getGenerateType, 0)
                .orderByDesc(EngineeringDocEntity::getCreateTime, EngineeringDocEntity::getUpdateTime)
                .last("limit 1");
        return Result.ok(this.getOne(lqw));
    }

    @Override
    public Result<List<EngineeringDocEntity>> listEngineeringDoc(TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<EngineeringDocEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(EngineeringDocEntity::getTenantId, tenantIsolation.getTenantId());
        return Result.ok(this.list(lqw));
    }

    @Override
    @Transactional
    public Result<Void> createEngineeringDoc(HttpServletResponse response, TenantIsolation tenantIsolation, CreateEngineeringDocDTO dto) {

        String password = dto.getPassword();
        if (StringUtils.isNotBlank(password)) {
            if (!password.equals(dto.getConfirmPassword())) {
                throw new BizException("两次输入密码不一致");
            }
            password = KernelEncryptDecodeUtils.encodeDes(password);
        }

        this.backupEngineeringDoc(tenantIsolation, dto.getDocName(), password, 1, dto.getDesc());
        return Result.ok();
    }

    @Override
    public Result<Void> importEngineeringDoc(TenantIsolation tenantIsolation, MultipartFile file, String password, String desc) {

        if (file.isEmpty()) {
            throw new BizException("文件为空");
        }

        String originalFilename = file.getOriginalFilename();
        try {
            if (!".nti".equals(originalFilename.substring(originalFilename.lastIndexOf(".")))) {
                throw new BizException("文件格式错误");
            }
        } catch (Exception e) {
            throw new BizException("文件格式错误");
        }

        CreateEngineeringDocFileRep engineeringDocFileRep = this.createEngineeringDocFile(tenantIsolation);
        File engineeringDocFile = engineeringDocFileRep.getFile();

        try {
            //把上传的文件保存至本地
            file.transferTo(engineeringDocFile);
        } catch (IOException e) {
            engineeringDocFile.delete();
            throw new BizException("文件传输异常");
        }
        EngineeringDocInfoDTO engineeringDocInfo;

        try (FileInputStream fos = new FileInputStream(engineeringDocFile);
             GZIPInputStream gos = new GZIPInputStream(fos);
             ObjectInputStream oos = new ObjectInputStream(gos)) {

            engineeringDocInfo = (EngineeringDocInfoDTO) oos.readObject();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            engineeringDocFile.delete();
            throw new BizException("文件序列化异常");
        }

        String engineeringDocPassword = engineeringDocInfo.getPassword();
        if (StringUtils.isNotBlank(engineeringDocPassword)) {
            if (StringUtils.isBlank(password)) {
                engineeringDocFile.delete();
                throw new BizException("密码为空");
            }
            if (!engineeringDocPassword.equals(KernelEncryptDecodeUtils.encodeDes(password))) {
                engineeringDocFile.delete();
                throw new BizException("密码错误");
            }

        } else {
            if (StringUtils.isNotBlank(password)) {
                engineeringDocFile.delete();
                throw new BizException("解压失败");
            }
        }

        //备份当前数据
        Result<EngineeringDocEntity> theLatestDataResult = this.getTheLatestAutoData(tenantIsolation);
        int versionNum = 1;
        EngineeringDocEntity theLatestData = theLatestDataResult.getResult();
        if (theLatestData != null) {
            versionNum = Integer.parseInt(theLatestData.getName().replace("version", "")) + 1;
            if (versionNum > 99) {
                versionNum = 1;
            }
        }
        this.backupEngineeringDoc(tenantIsolation, "version" + versionNum, null, 0, desc);

        boolean tenantIdEqual = false;
        if (tenantIsolation.getTenantId().equals(engineeringDocInfo.getTenantId())) {
            tenantIdEqual = true;
        }

        try {
            ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            RequestContextHolder.setRequestAttributes(sra, true);
            HashMap<Long, Long> tagMap = Maps.newHashMap();//new HashMap<>(engineeringDocInfo.getCommonServiceData().getTagList().size(), 1);
            HashMap<Long, Long> redirectMap = Maps.newHashMap();//new HashMap<>(engineeringDocInfo.getCommonServiceData().getRedirectList().size(), 1);
            HashMap<Long, Long> templateMap = new HashMap<>(engineeringDocInfo.getNotifyServiceData() == null || CollectionUtils.isEmpty(engineeringDocInfo.getNotifyServiceData().getTemplateList()) ? 0 : engineeringDocInfo.getNotifyServiceData().getTemplateList().size(), 1);
            HashMap<Long, Long> deviceMap = new HashMap<>(engineeringDocInfo.getProductDeviceServerData().getDeviceList().size(), 1);
            HashMap<Long, Long> serviceMap = new HashMap<>(1024);
            boolean finalTenantIdEqual = tenantIdEqual;
            String userJson = JwtUserInfoUtils.userJson();
            JSONObject jsonObject = JSON.parseObject(userJson);
            Long userId = jsonObject.getLong("userId");
            String userName = jsonObject.getString("realname");

            // CompletableFuture<Void> commonFuture = CompletableFuture.runAsync(() ->
            //         commonHandle.handle(tenantIsolation.getTenantId(), engineeringDocInfo.getCommonServiceData(), finalTenantIdEqual,
            //                 tagMap, redirectMap));
            CompletableFuture<Void> productDeviceFuture = CompletableFuture.runAsync(() ->
                    productDeviceHandle.handle(tenantIsolation.getTenantId(), engineeringDocInfo.getProductDeviceServerData(), userId, userName, finalTenantIdEqual,
                            deviceMap, serviceMap, tagMap, redirectMap));
//            CompletableFuture<Void> alarmFuture = CompletableFuture.runAsync(() ->
//                    alarmHandle.handle(tenantIsolation.getTenantId(), engineeringDocInfo.getAlarmServerData(), finalTenantIdEqual));
            CompletableFuture<Void> notifyFuture = CompletableFuture.runAsync(() ->
                    notifyHandle.handle(tenantIsolation.getTenantId(), engineeringDocInfo.getNotifyServiceData(), finalTenantIdEqual, templateMap));
//            CompletableFuture<Void> ruleEngineFuture = CompletableFuture.runAsync(() ->
//                    ruleEngineHandle.handle(tenantIsolation.getTenantId(), engineeringDocInfo.getRuleEngineServerData(), finalTenantIdEqual,
//                            deviceMap,serviceMap,tagMap, redirectMap, templateMap));
            CompletableFuture.allOf(
                    // commonFuture,
                    /* alarmFuture,*/notifyFuture/* ,ruleEngineFuture*/, productDeviceFuture).get();
        } catch (Exception e) {
            log.error(String.format("服务器异常%s", e.getMessage()), e);
            engineeringDocFile.delete();
            throw new BizException("服务器异常，请稍后重试9" + e.getMessage());
        }
        String docName = originalFilename.substring(0, originalFilename.lastIndexOf("."));
        EngineeringDocEntity engineeringDocEntity = new EngineeringDocEntity();
        engineeringDocEntity.setId(engineeringDocFileRep.getId());
        engineeringDocEntity.setName(docName);
        engineeringDocEntity.setGenerateType(2);
        engineeringDocEntity.setDocSize(engineeringDocFile.length());
        engineeringDocEntity.setDownloadLink(engineeringDocFile.getPath());
        engineeringDocEntity.setPlatformVersion(engineeringDocInfo.getPlatformVersion());
        engineeringDocEntity.setTenantId(tenantIsolation.getTenantId());
        engineeringDocEntity.setRemark(desc);
        try {
            boolean save = this.save(engineeringDocEntity);
            if (!save) {
                engineeringDocFile.delete();
                throw new BizException("服务器异常，请稍后重试10");
            }
        } catch (Exception e) {
            engineeringDocFile.delete();
            throw new BizException("服务器异常，请稍后重试11");
        }
        return Result.ok();
    }

    @Override
    public void downloadEngineeringDoc(HttpServletResponse response, TenantIsolation tenantIsolation, Long id) {
        Result<EngineeringDocEntity> engineeringDocEntityResult = this.getByIdAndTenantIsolation(id, tenantIsolation);
        EngineeringDocEntity engineeringDocEntity = engineeringDocEntityResult.getResult();
        if (!engineeringDocEntityResult.getSignal() || engineeringDocEntity == null) {
            throw new BizException("该租户下不存在此工程文件");
        }
        try {
            FileUtil.exportFile(response, new File(engineeringDocEntity.getDownloadLink()), engineeringDocEntity.getName(), "nti");
        } catch (IOException e) {
            throw new BizException("下载失败");
        }

    }

    @Override
    public Result<Page<EngineeringDocEntity>> pageEngineeringDoc(TenantIsolation tenantIsolation, PageParam pageParam) {
        LambdaQueryWrapper<EngineeringDocEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(EngineeringDocEntity::getTenantId, tenantIsolation.getTenantId())
                .orderByDesc(EngineeringDocEntity::getCreateTime);
        return Result.ok(this.page(pageParam.toPage(EngineeringDocEntity.class), lqw));
    }


    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public EngineeringDocEntity backupEngineeringDoc(TenantIsolation tenantIsolation, String docName, String password, Integer generateType, String desc) {
        //查询工程文件数据
        Result<CommonServiceDataDTO> commonDataByTenantId;

        Result<ProductDeviceServerDataDTO> productDataByTenantId;

        R getNotifyDataByTenantId;

        R getRuleEngineDataByTenantId;

        try {

//            CompletableFuture<Result<CommonServiceDataDTO>> commonFuture = CompletableFuture.supplyAsync(() ->
//                    engineeringCommonService.getCommonDataByTenantId(tenantIsolation.getTenantId()));

            CompletableFuture<Result<ProductDeviceServerDataDTO>> productDeviceFuture = CompletableFuture.supplyAsync(() ->
                    engineeringProductService.getProductDataByTenantId(tenantIsolation.getTenantId()));


            CompletableFuture<R> notifyFuture = CompletableFuture.supplyAsync(() ->
                    R.result(engineeringNotifyService.getNotifyDataByTenantId(tenantIsolation.getTenantId())));

//            CompletableFuture<R> ruleEngineFuture = CompletableFuture.supplyAsync(() ->
//                    engineeringRuleEngineServiceClient.getRuleEngineDataByTenantId(tenantIsolation.getTenantId()));


//            commonDataByTenantId = commonFuture.get();
//            if (!commonDataByTenantId.getSignal() || commonDataByTenantId.getResult() == null) {
//                throw new BizException("数据备份异常");
//            }

            productDataByTenantId = productDeviceFuture.get();
            if (!productDataByTenantId.getSignal()) {
                throw new BizException("数据备份异常");
            }

            getNotifyDataByTenantId = notifyFuture.get();
            if (!(boolean) getNotifyDataByTenantId.get("ok")) {
                throw new BizException("数据备份异常");
            }

//            getRuleEngineDataByTenantId = ruleEngineFuture.get();
//            if (!(boolean) getRuleEngineDataByTenantId.get("ok")) {
//                throw new BizException("数据备份异常");
//            }
        } catch (Exception e) {
            throw new BizException("数据备份异常");
        }

        //查询数据
        EngineeringDocInfoDTO engineeringDocInfo = EngineeringDocInfoDTO.builder()
                .tenantId(tenantIsolation.getTenantId())
                .password(password)
                .platformVersion(platformVersion)
//                .commonServiceData(commonDataByTenantId.getResult())
                .productDeviceServerData(JSONObject.parseObject(JSONObject.toJSONString(productDataByTenantId.getResult()), ProductDeviceServerDataDTO.class))
                .notifyServiceData(JSONObject.parseObject(JSONObject.toJSONString(getNotifyDataByTenantId.get("data")), NotifyServiceDataDTO.class))
//                .ruleEngineServerData(JSONObject.parseObject(JSONObject.toJSONString(getRuleEngineDataByTenantId.get("data")), RuleEngineServerDataDTO.class))
                .build();

        //创建文件
        CreateEngineeringDocFileRep engineeringDocFileRep = this.createEngineeringDocFile(tenantIsolation);
        File file = engineeringDocFileRep.getFile();

        try (FileOutputStream fos = new FileOutputStream(file);
             GZIPOutputStream gos = new GZIPOutputStream(fos);
             ObjectOutputStream oos = new ObjectOutputStream(gos)) {

            oos.writeObject(engineeringDocInfo);
            oos.flush();
        } catch (IOException e) {
            e.printStackTrace();
            file.delete();
            throw new BizException("数据备份异常");
        }

        EngineeringDocEntity engineeringDocEntity = new EngineeringDocEntity();
        engineeringDocEntity.setId(engineeringDocFileRep.getId());
        engineeringDocEntity.setName(docName);
        engineeringDocEntity.setGenerateType(generateType);
        engineeringDocEntity.setDocSize(file.length());
        engineeringDocEntity.setDownloadLink(file.getPath());
        engineeringDocEntity.setPlatformVersion(platformVersion);
        engineeringDocEntity.setTenantId(tenantIsolation.getTenantId());
        engineeringDocEntity.setRemark(desc);
        try {
            boolean save = this.save(engineeringDocEntity);
            if (!save) {
                file.delete();
                throw new BizException("数据备份异常");
            }
        } catch (Exception e) {
            file.delete();
            throw new BizException("数据备份异常");
        }

        return engineeringDocEntity;
    }


    public Result<EngineeringDocEntity> getByIdAndTenantIsolation(Long channelId, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<EngineeringDocEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(EngineeringDocEntity::getId, channelId)
                .eq(EngineeringDocEntity::getTenantId, tenantIsolation.getTenantId());
        return Result.ok(this.getOne(lqw));
    }

    private CreateEngineeringDocFileRep createEngineeringDocFile(TenantIsolation tenantIsolation) {
        ApplicationHome h = new ApplicationHome(this.getClass());
        File tenantFile = new File(h.getSource().getParentFile().getParent() + "/engineeringDoc", tenantIsolation.getTenantId() + "");
        if (!tenantFile.exists()) {
            boolean mkdirs = tenantFile.mkdirs();
            if (!mkdirs) {
                throw new BizException("创建工程文件目录异常");
            }
        }
        long id = IdGenerator.generateId();
        File engineerDocFile = new File(tenantFile, id + ".nti");
        try {
            boolean newFile = engineerDocFile.createNewFile();
            if (!newFile) {
                throw new BizException("创建工程文件异常");
            }
        } catch (IOException e) {
            throw new BizException("创建工程文件异常");
        }
        return CreateEngineeringDocFileRep.builder()
                .id(id)
                .file(engineerDocFile)
                .build();
    }
}
