package com.nti56.nlink.product.device.server.model.label.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 10:47<br/>
 * @since JDK 1.8
 */

@Data
@ToString
@Schema(description = "标签分组dto")
public class ProofreadLabelGroupDTO implements Serializable {

    private Long id;

    private Long strategyId;

}
