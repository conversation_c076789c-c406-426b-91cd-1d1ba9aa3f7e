package com.nti56.nlink.product.device.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.entity.ResourceRelationEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.DeviceModelMapper;
import com.nti56.nlink.product.device.server.mapper.ResourceRelationMapper;
import com.nti56.nlink.product.device.server.mapper.ThingModelMapper;
import com.nti56.nlink.product.device.server.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 资源关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-11 17:27:49
 * @since JDK 1.8
 */
@Service
public class ResourceRelationServiceImpl extends BaseServiceImpl<ResourceRelationMapper, ResourceRelationEntity> implements IResourceRelationService {

    @Autowired
    ResourceRelationMapper mapper;

    @Autowired
    IThingModelService thingModelService;

    @Autowired
    IThingServiceService thingServiceService;

    @Autowired
    ThingModelMapper thingModelMapper;

    @Autowired
    IThingModelInheritService thingModelInheritService;

    @Autowired
    DeviceModelMapper deviceModelMapper;

    @Autowired
    IDeviceModelInheritService productModelInheritService;

    @Autowired
    IDeviceServiceService productServiceService;

    @Override
    public Result<ResourceRelationEntity> saveResourceRelation(@NotNull ResourceRelationEntity entity) {
        if (mapper.insert(entity) == 1) {
            return Result.ok(entity);
        }
        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }

    @Override
    public Result<Page<ResourceRelationEntity>> getPage(ResourceRelationEntity entity, Page<ResourceRelationEntity> page) {
        Page<ResourceRelationEntity> list = mapper.selectPage(page, new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    @Override
    public Result<List<ResourceRelationEntity>> list(ResourceRelationEntity entity) {
        QueryWrapper<ResourceRelationEntity> wrapper = new QueryWrapper<>(entity);

        List<ResourceRelationEntity> list = mapper.selectList(wrapper);
        return Result.ok(list);
    }

    @Override
    public Result update(@NotNull ResourceRelationEntity entity, TenantIsolation tenantIsolation) {
        ResourceRelationEntity resourceRelationEntity = this.getByIdAndTenantIsolation(entity.getId(), tenantIsolation).getResult();
        if (resourceRelationEntity == null){
            throw new BizException("该租户下找不到该资源关系");
        }

        if (mapper.updateById(entity) == 1) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    @Override
    public Result deleteById(@NotNull Long entityId, TenantIsolation tenantIsolation) {
        ResourceRelationEntity resourceRelationEntity = this.getByIdAndTenantIsolation(entityId, tenantIsolation).getResult();
        if (resourceRelationEntity == null){
            throw new BizException("该租户下找不到该资源关系");
        }

        if (mapper.deleteById(entityId) == 1) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    @Override
    public Result<ResourceRelationEntity> getByIdAndTenantIsolation(Long entityId, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<ResourceRelationEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ResourceRelationEntity::getId,entityId)
                .eq(ResourceRelationEntity::getTenantId,tenantIsolation.getTenantId());
        return Result.ok(mapper.selectOne(lqw));
    }

}
