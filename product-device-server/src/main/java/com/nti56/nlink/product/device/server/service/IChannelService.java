package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;
import com.nti56.nlink.product.device.server.model.ChannelDto;
import com.nti56.nlink.product.device.server.model.ChannelRequestBo;
import com.nti56.nlink.product.device.server.model.channel.dto.CopyChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.CreateChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.EditChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.ListChannelWithLabelGroupTreeAndLabelDTO;
import com.nti56.nlink.product.device.server.model.channel.vo.*;
import org.springframework.web.socket.handler.ConcurrentWebSocketSessionDecorator;

import javax.validation.Valid;
import java.util.List;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 14:11:44
 * @since JDK 1.8
 */
public interface IChannelService  extends IBaseService<ChannelEntity> {

    Result<Page<ChannelDto>> getChannelPage(ChannelDto channel, Page<ChannelDto> page, TenantIsolation tenantIsolation);

    Result<List<ChannelVO>> listChannel(ChannelEntity channel, TenantIsolation tenantIsolation);

    Result<ChannelEntity> getChannelById(Long channelId);

    Result<ChannelEntity> getByIdAndTenantIsolation(Long channelId, TenantIsolation tenantIsolation);

    Result<List<ChannelVO>>  listChannelNoEdgeGateway(TenantIsolation tenantIsolation);

    Result unbindEdgeGateway(List<Long> oldChannelIds);

    Result bingEdgeGateway(List<Long> newChannelIds, Long edgewayId);

    Result deleteChannel(Long id, TenantIsolation tenantIsolation);

    Result<Integer> countByEdgeGatewayId(Long edgeGatewayId);

    Result<List<ChannelEntity>> listByEdgeGatewayId(Long edgeGatewayId, Long tenantId, boolean onlyOnline);

    Result<Void> unbindByEdgeGatewayId(Long edgeGatewayId);

    Result<List<ChannelEntity>> listChannelByEdgeGatewayId(Long edgeGatewayId, Long tenantId,Boolean runtime);
    
    Result<List<ChannelCheckoutOutVO>> channelConnectTestList(TenantIsolation tenantIsolation, Long edgeGatewayId);
    
    Result bingEdgeGateway(Long channelId, Long edgeGatewayId, TenantIsolation tenantIsolation);
    
    Result connectTest(TenantIsolation tenantIsolation, List<Long> channelIds);
    
    List<ChannelEntity> getChannelListByChannelIds(TenantIsolation tenantIsolation, List<Long> channelIds);
    
    List<ChannelElm> getChannelELmList(TenantIsolation tenantIsolation, List<Long> channelIds, List<ChannelEntity> channelList);

    Result<CreateChannelAllVO> createChannel(@Valid CreateChannelDTO dto, TenantIsolation tenantIsolation);

    Result<EditChannelAllVO> editChannel(EditChannelDTO dto, TenantIsolation tenantIsolation);

    Result<QueryChannelByIdVO> getChannel(Long id, TenantIsolation tenantIsolation);

    Result<ChannelDebugInfoVo> getChannelDebugInfo(Long id, TenantIsolation tenantIsolation);
    
    Result<QueryChannelAllByIdVO> getChannelAll(Long id, TenantIsolation tenantIsolation);

    void collectingOnce(String sessionId, String key, ConcurrentWebSocketSessionDecorator session, ChannelEntity channelEntity, List<AccessElm> accessElms, List<ChannelParamEntity> paramEntities);

    Result<ChannelEntity> getByLabelGroupId(Long labelGroupId);

    Result<List<ListChannelWithLabelGroupTreeAndLabelVO>> listChannelWithLabelGroupTreeAndLabel(ListChannelWithLabelGroupTreeAndLabelDTO dto, TenantIsolation tenantIsolation);

    Result<Void> statusChange(Long channelId, Long edgeGatewayId, TenantIsolation tenantIsolation);

    Result<Void> modifyChannelInterval(Long id, Long edgeGatewayId, Integer intervalMs, TenantIsolation tenantIsolation);

    Result<Void> deleteByEdgeGatewayId(Long edgeGatewayId);

    Result<CreateChannelAllVO> copyChannel(CopyChannelDTO dto, TenantIsolation tenantIsolation);

    Result<List<ChannelStatusVO>> channelStatus(Long edgeGatewayId, TenantIsolation tenantIsolation);

    Result<Void> batchEditChannel(TenantIsolation tenantIsolation, ChannelRequestBo requestBo, Long edgeGatewayId);
    
    Result<List<ChannelVO>> listAllChannel();
    
    Result<List<ChannelEntity>> listByEdgeGatewayId(Long edgeGatewayId,Long tenantId);

    Result<List<ChannelEntity>> listChannelByTenantId(Long tenantId, boolean onlyOnline);
}
