package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.ServiceConfigEntity;
import com.nti56.nlink.product.device.server.model.ServiceConfigDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【service_config(通用物服务配置表)】的数据库操作Service
 * @createDate 2023-03-06 14:51:50
 */
public interface IServiceConfigService extends IBaseService<ServiceConfigEntity> {

    Result getConfigByDeviceId(TenantIsolation tenantIsolation, Long serviceId, Long deviceId);


    Result getConfigByModelId(TenantIsolation tenantIsolation, Long serviceId, Long modelId);

    /**
     * 保存或修改服务配置
     *
     * @param serviceConfig
     * @return
     */
    Result saveOrUpdateConfig(ServiceConfigDTO serviceConfig, TenantIsolation tenantIsolation);

    List<ServiceConfigEntity> queryConfigByServiceIdAndDeviceIds(Long serviceId, List<Long> deviceIds, Long tenantId);

    ServiceConfigEntity getConfigByServiceIdAndModelId(Long serviceId, Long modelId, Long tenantId);
}
