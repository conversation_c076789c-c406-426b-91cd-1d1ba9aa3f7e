package com.nti56.nlink.product.device.server.model.deviceTemplate;

import lombok.Data;

import java.util.List;

/**
 * 通过设备模板创建设备请求参数
 */
@Data
public class DeviceTemplateCreateDeviceReq {

    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 所属客户名称
     */
    private String customerName;
    /**
     * 选择的网关id
     */
    private Long edgeGatewayId;
    /**
     * 选中的模板信息
     */
    private List<DeviceTemplateChannelDto> channelList;


}
