package com.nti56.nlink.product.device.server.domain.thing.topic;

import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;

import lombok.Builder;
import lombok.Data;

public class GwDebugTopic {
    
    @Data
    @Builder
    public static class TopicInfo{
        private Long tenantId;
        private Long edgeGatewayId;
        private String socketType;
        private String type;
    }

    public static TopicInfo parseTopic(String topic){
        String[] split = topic.split("/");
        String tenantId = split[2];
        String edgeGatewayId = split[3];
        String socketType = split[5];
        String type = split[6];

        return TopicInfo.builder()
            .tenantId(Long.parseLong(tenantId))
            .edgeGatewayId(Long.parseLong(edgeGatewayId))
            .socketType(socketType)
            .type(type)
            .build();
    }

    public static String createSubscribeDebugCustomTopic(Long tenantId, Long edgeGatewayId){
        return MqttTopicEnum.GW_DEBUG.getPrefix()
            + tenantId + "/"
            + edgeGatewayId + "/"
            + "custom" + "/" + "#"
            ;
    }

    public static String createShareSubscribeTopic(String group){
        return  "$share/" + group + "/" 
            + MqttTopicEnum.GW_DEBUG.getPrefix()
            + "+/" //tenantId
            + "+/" //edgeGatewayId
            + "custom" + "/" + "#"
            ;
    }
}
