package com.nti56.nlink.product.device.server.model;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "物模型信息")
public class ThingModelInfoDto {
    
    @Schema(description = "物模型主键ID")
    private Long id;
    
    @Schema(description = "物模型名称")
    private String name;
    
    @Schema(description = "继承的物模型id列表")
    private List<Long> inheritThingModelIds;

    @Schema(description = "标记id列表")
    private List<Long> tagIds;

    @Schema(description = "描述")
    private String descript;

    @Schema(description = "模型类型 1-物模型 2-设备模型 3-故障模型 4-通用模型")
    private Integer modelType;

}
