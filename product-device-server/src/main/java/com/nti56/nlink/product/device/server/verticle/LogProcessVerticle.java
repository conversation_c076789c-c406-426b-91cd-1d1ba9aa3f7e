package com.nti56.nlink.product.device.server.verticle;

import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.mapper.DeviceServiceLogMapper;
import com.nti56.nlink.product.device.server.util.redis.RedisUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.Json;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName logProcessVerticle
 * @date 2022/8/12 10:07
 * @Version 1.0
 */
@Component
@Scope(SCOPE_PROTOTYPE)
@Slf4j
public class LogProcessVerticle extends AbstractVerticle {

    @Autowired @Lazy
    RedisTemplate redisTemplate;

    @Autowired
    DeviceServiceLogMapper deviceServiceLogMapper;

    RedisUtil redisUtil;

    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        redisUtil = new RedisUtil(this.redisTemplate, null);
        super.start(startPromise);
        vertx.eventBus().consumer("addDeviceServiceCallLog",logEntity -> processLog(Json.decodeValue((String) logEntity.body(),DeviceServiceLogEntity.class)));
    }

    private void processLog(DeviceServiceLogEntity logEntity) {
        if (redisTemplate.opsForValue().setIfAbsent(RedisConstant.DEVICE_SERVICE_LOG_LOCK,1,1, TimeUnit.MINUTES)) {
            saveLogs(logEntity);
            return;
        }
        long size = redisUtil.sGetSetSize(RedisConstant.DEVICE_SERVICE_LOG);
        if (size < 1000) {
            redisUtil.sSet(RedisConstant.DEVICE_SERVICE_LOG,logEntity);
        }else {
            saveLogs(logEntity);
        }
    }

    private void saveLogs(DeviceServiceLogEntity body) {
        SetOperations<String,DeviceServiceLogEntity> setOperations = redisTemplate.opsForSet();
        List<DeviceServiceLogEntity> list = setOperations.pop(RedisConstant.DEVICE_SERVICE_LOG, 1000);
        list.add(body);
        deviceServiceLogMapper.saveLogs(list);
    }

}
