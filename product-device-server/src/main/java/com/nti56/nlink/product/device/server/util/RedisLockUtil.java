package com.nti56.nlink.product.device.server.util;

/**
 * @Description: redis实现分布式锁
 * <AUTHOR>
 * @Date 2023/2/6 14:40
 */

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * Redis 分布式锁
 **/
@Component
@Slf4j
public class RedisLockUtil {

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 加锁
     **/
    public Boolean getLock(String key, String value, Long expiredTime) {
        Boolean lockStatus = this.redisTemplate.opsForValue().setIfAbsent(key, value, Duration.ofSeconds(expiredTime));
        return lockStatus;
    }

    public <T> T getCacheObject(String key) {
        ValueOperations<String, T> operation = this.redisTemplate.opsForValue();
        return operation.get(key);
    }
    /**
     * 匹配值才释放锁
     **/
    public Long releaseLock(String key, String value) {
        String luaScript = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        RedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);
        Long releaseStatus = (Long) this.redisTemplate.execute(redisScript, Collections.singletonList(key), value);
        return releaseStatus;
    }

    public boolean tryLock(String key, String value, long expireTime, long timeout, long interval) {
        // 默认重试间隔设置为30ms
        interval = interval <= 0L ? 30L : interval;

        // 带超时的获取锁逻辑
        if (timeout > 0L) {
            final long begin = System.currentTimeMillis();

            while (System.currentTimeMillis() - begin < timeout) {
                // 尝试获取锁
                if (acquireLock(key, value, expireTime)) {
                    return true;
                }

                // 等待指定间隔后重试
                waitForRetry(interval);
            }
            return false;
        }
        // 不带超时的获取锁逻辑
        else {
            return acquireLock(key, value, expireTime);
        }
    }

    /**
     * 根据key直接释放锁
     * @param key
     */
    public void unLock(String key ) {
        try {
            Boolean deleted = redisTemplate.delete(key);
            if (Boolean.TRUE.equals(deleted)) {
                log.debug("{}:{}:解锁成功", Thread.currentThread().getName(), key);
            }
        } catch (Exception e) {
            log.error("解锁异常: {}", e.getMessage(), e);
        }
    }
    /**
     * 尝试获取锁的核心方法
     */
    private boolean acquireLock(String key, String value, long expireTime) {
        Boolean acquired = redisTemplate.opsForValue()
                .setIfAbsent(key, value, expireTime, TimeUnit.MILLISECONDS);

        if (Boolean.TRUE.equals(acquired)) {
            log.info("{}:{}:上锁成功", Thread.currentThread().getName(), key);
            return true;
        }
        return false;
    }

    /**
     * 等待重试的同步方法
     */
    private void waitForRetry(long interval) {
        synchronized (Thread.currentThread()) {
            try {
                log.info("{}:等待{}ms后重试", Thread.currentThread().getName(), interval);
                Thread.currentThread().wait(interval);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("锁获取被中断: {}", e.getMessage());
            }
        }
    }


}