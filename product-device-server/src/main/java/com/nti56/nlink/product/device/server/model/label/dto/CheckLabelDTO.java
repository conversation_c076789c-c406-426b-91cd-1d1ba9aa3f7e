package com.nti56.nlink.product.device.server.model.label.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/5/25 9:35<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "校验标签dto")
public class CheckLabelDTO {

    @Schema(description = "通道类型")
    private Integer driver;

    @Valid
    @Schema(description = "校验标签集合")
    private List<LabelDTO> labels;
}
