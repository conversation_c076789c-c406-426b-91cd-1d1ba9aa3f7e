package com.nti56.nlink.product.device.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.product.device.server.entity.NotifyServerLogEntity;
import com.nti56.nlink.product.device.server.enums.NotifyServerLogEnum;
import com.nti56.nlink.product.device.server.mapper.NotifyServerLogMapper;
import com.nti56.nlink.product.device.server.service.INotifyServerLogService;
import org.springframework.stereotype.Service;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/18 9:25<br/>
 * @since JDK 1.8
 */

@Service
public class NotifyServerLogServiceImpl extends ServiceImpl<NotifyServerLogMapper, NotifyServerLogEntity> implements INotifyServerLogService {

    @Override
    public void createLog(Long id, NotifyServerLogEnum logEnum, String requestParams, String failResponse, String errorDescription, TenantIsolation tenantIsolation, Long aggregationId, Long subscriptionId) {
        NotifyServerLogEntity log = NotifyServerLogEntity.builder().logType(logEnum.getValue())
                .logDescription(logEnum.getName())
                .requestParams(requestParams)
                .failResponse(failResponse)
                .errorDescription(errorDescription)
                .tenantId(tenantIsolation.getTenantId())
                .engineeringId(tenantIsolation.getEngineeringId())
                .spaceId(tenantIsolation.getSpaceId())
                .moduleId(tenantIsolation.getModuleId())
                .messageAggregationId(aggregationId)
                .subscriptionId(subscriptionId)
                .build();

        if (id == null){
            id = IdGenerator.generateId();
            log.setId(id);
        }

        log.setSourceId(id);

        this.save(log);
    }

    @Override
    public void createLog(Long id, NotifyServerLogEnum logEnum, String requestParams, String failResponse, String errorDescription, TenantIsolation tenantIsolation, Long aggregationId) {
        this.createLog(id,logEnum,requestParams,failResponse,errorDescription,tenantIsolation,aggregationId,null);
    }

    @Override
    public void createLog(Long id, NotifyServerLogEnum logEnum, String requestParams, String failResponse, String errorDescription, TenantIsolation tenantIsolation) {
       this.createLog(id,logEnum,requestParams,failResponse,errorDescription,tenantIsolation,null);

    }

    @Override
    public void createLog(NotifyServerLogEnum logEnum, String requestParams, TenantIsolation tenantIsolation,Long subscriptionId) {
        this.createLog(null,logEnum,requestParams,"","",tenantIsolation,null,subscriptionId);
    }


    @Override
    public void createLog(NotifyServerLogEnum logEnum, String requestParams, TenantIsolation tenantIsolation) {
        this.createLog(null,logEnum,requestParams,"","",tenantIsolation);
    }

    @Override
    public void createLog(Long id, NotifyServerLogEnum logEnum, String requestParams, TenantIsolation tenantIsolation) {
        this.createLog(id,logEnum,requestParams,"","",tenantIsolation);
    }

    @Override
    public void createLog(Long id, NotifyServerLogEnum logEnum, String requestParams, TenantIsolation tenantIsolation,Long subscriptionId) {
        this.createLog(id,logEnum,requestParams,"","",tenantIsolation,null,id);
    }

    @Override
    public void createRequestFailLog(NotifyServerLogEnum logEnum, String requestParams, String failResponse, TenantIsolation tenantIsolation) {
        this.createLog(null,logEnum,requestParams,failResponse,"",tenantIsolation);
    }

    @Override
    public void createErrorLog(NotifyServerLogEnum logEnum, String requestParams, String errorDescription, TenantIsolation tenantIsolation) {
        this.createLog(null,logEnum,requestParams,"",errorDescription,tenantIsolation);
    }

    @Override
    public void createRequestFailLog(Long id, NotifyServerLogEnum logEnum, String requestParams, String failResponse, TenantIsolation tenantIsolation) {
        this.createLog(id,logEnum,requestParams,failResponse,"",tenantIsolation);
    }

    @Override
    public void createErrorLog(Long id, NotifyServerLogEnum logEnum, String requestParams, String errorDescription, TenantIsolation tenantIsolation) {
        this.createLog(id,logEnum,requestParams,"",errorDescription,tenantIsolation);
    }

    @Override
    public void createRequestFailLog(Long id, NotifyServerLogEnum logEnum, String requestParams, String failResponse, TenantIsolation tenantIsolation,Long subscriptionId) {
        this.createLog(id,logEnum,requestParams,failResponse,"",tenantIsolation,null,subscriptionId);
    }

    @Override
    public void createErrorLog(Long id, NotifyServerLogEnum logEnum, String requestParams, String errorDescription, TenantIsolation tenantIsolation,Long subscriptionId) {
        this.createLog(id,logEnum,requestParams,"",errorDescription,tenantIsolation,null,subscriptionId);
    }

    @Override
    public void createRequestFailLog(NotifyServerLogEnum logEnum, String requestParams, String failResponse, TenantIsolation tenantIsolation,Long subscriptionId) {
        this.createLog(null,logEnum,requestParams,failResponse,"",tenantIsolation,null,subscriptionId);
    }

    @Override
    public void createErrorLog(NotifyServerLogEnum logEnum, String requestParams, String errorDescription, TenantIsolation tenantIsolation,Long subscriptionId) {
        this.createLog(null,logEnum,requestParams,"",errorDescription,tenantIsolation,null,subscriptionId);
    }
}
