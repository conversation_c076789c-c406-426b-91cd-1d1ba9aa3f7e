package com.nti56.nlink.product.device.server.model.changeNotice;

import lombok.Builder;
import lombok.Data;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ChangeNoticeMsgVo
 * @date 2023/2/22 11:46
 * @Version 1.0
 */
@Builder
@Data
public class ChangeNoticeMsgVo <T> {

    private String name;

    /**
     * 变动项目(1网关,2设备)
     */
    private Integer changeSubject;

    /**
     * 变动类型(1 新建,2,删除,3更新,4状态变化,5设备模型更新)
     */
    private Integer changeType;

    private T msg;
}
