package com.nti56.nlink.product.device.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.req.ListTagReq;
import com.nti56.nlink.product.device.client.model.req.TagReq;
import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.domain.tag.TagDomain;
import com.nti56.nlink.product.device.server.entity.Tag;
import com.nti56.nlink.product.device.server.entity.TagBindRelationEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.manager.ITagManager;
import com.nti56.nlink.product.device.server.mapper.TagMapper;

import com.nti56.nlink.product.device.server.openapi.domain.request.ListTagRequest;
import com.nti56.nlink.product.device.server.service.ITagBindRelationService;
import com.nti56.nlink.product.device.server.service.ITagService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/1 16:27<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements ITagService {

  @Autowired
  private ITagManager tagManager;

  @Autowired
  private Mapper dozerMapper;

  @Autowired
  private ITagBindRelationService tagBindRelationService;

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Result<Boolean> update(TagReq req, Long id, TenantIsolation tenantIsolation) {
    Tag tag = tagManager.getById(id);
    if (!Objects.equals(tag.getTenantId(), tenantIsolation.getTenantId())) {
      return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }
    if (!StringUtils.equals(String.format("%s%s", req.getTagKey(), req.getTagValue())
            , String.format("%s%s", tag.getTagKey(), tag.getTagValue()))) {
      Result<Tag> tagD = TagDomain.checkTag(tenantIsolation, req, tagManager, dozerMapper);
      if (!tagD.getSignal()){
        return Result.error(tagD.getMessage());
      }
    }
    if (StringUtils.isNotBlank(req.getTagKey())) {
      tag.setTagKey(req.getTagKey());
    }
    if (StringUtils.isNotBlank(req.getTagValue())) {
      tag.setTagValue(req.getTagValue());
    }
    if (tagManager.updateById(tag)) {
      return Result.ok();
    }
    return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);

  }

  @Override
  public Result<Boolean> delete(TenantIsolation tenantIsolation, Long id) {
    Tag tag = tagManager.getById(id);
    if (Objects.isNull(tag)) {
      return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }
    if (!Objects.equals(tenantIsolation.getTenantId(), tag.getTenantId())) {
      return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }
    if (tagManager.removeById(id)) {
      tagBindRelationService.remove(new LambdaQueryWrapper<TagBindRelationEntity>()
              .eq(TagBindRelationEntity::getTenantId, tenantIsolation.getTenantId())
              .eq(TagBindRelationEntity::getTagId, id));
      return Result.ok();
    }
    return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
  }


  @Override
  public Result<TagRsp> get(TenantIsolation tenantIsolation, Long id) {
    Tag tag = tagManager.getById(id);
    if (Objects.isNull(tag)) {
      return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
    }
    if (!Objects.equals(tenantIsolation.getTenantId(), tag.getTenantId())) {
      return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
    }
    return Result.ok(dozerMapper.map(tag, TagRsp.class));

  }


  @Override
  public Result<Page<TagRsp>> findByPage(TagReq req, Integer pageSize, Integer pageNo, TenantIsolation tenantIsolation) {
    QueryWrapper<Tag> queryWrapper = new QueryWrapper<>();
    if (tenantIsolation.getTenantId() != null) {
      queryWrapper.eq("tenant_id", tenantIsolation.getTenantId());
    }
    if (StringUtils.isNotBlank(req.getTagKey()) && StringUtils.isNotBlank(req.getTagValue())) {
      queryWrapper.likeRight("tag_key", req.getTagKey()).likeRight("tag_value", req.getTagValue());
    } else {
      if (StringUtils.isNotBlank(req.getTagValue())) {
        queryWrapper.likeRight("tag_value", req.getTagValue());
      } else if (StringUtils.isNotBlank(req.getTagKey())) {
        queryWrapper.likeRight("tag_key", req.getTagKey());
      }
    }
    Page<Tag> page = new Page<>();
    page.setCurrent(pageNo);
    page.setSize(pageSize);
    page = tagManager.page(page, queryWrapper);
    Page<TagRsp> resultPage = new Page<>();
    resultPage.setPages(0);
    if (page.getPages() > 0) {
      List<TagRsp> list = new ArrayList<>();
      resultPage.setPages(page.getPages());
      resultPage.setSize(pageSize);
      resultPage.setCurrent(page.getCurrent());
      resultPage.setTotal(page.getTotal());
      page.getRecords().forEach(dto -> {
        list.add(dozerMapper.map(dto, TagRsp.class));
      });
      resultPage.setRecords(list);
    }
    return Result.ok(resultPage);
  }

  @Override
  public Result<List<TagRsp>> list(ListTagReq req, TenantIsolation tenantIsolation) {
    QueryWrapper<Tag> queryWrapper = new QueryWrapper<>();
    if (tenantIsolation.getTenantId() != null) {
      queryWrapper.eq("tenant_id", Objects.requireNonNull(tenantIsolation.getTenantId(), "租户id为空"));
    }
    if (StringUtils.isNotBlank(req.getSearch())) {
      queryWrapper.and(i -> i.likeRight("tag_key", req.getSearch()).or().likeRight("tag_value", req.getSearch()));
    }
    List<Tag> tags = tagManager.list(queryWrapper);
    List<TagRsp> result = Lists.newArrayList();
    tags.forEach(dto -> {
      result.add(dozerMapper.map(dto, TagRsp.class));
    });
    return Result.ok(result);
  }

  @Override
  public Result<List<TagRsp>> listByIds(TenantIsolation tenantIsolation, List<Long> ids) {
    if(CollectionUtils.isEmpty(ids)){
      return Result.error(ServiceCodeEnum.CODE_GET_LIST_FAIL);
    }
    List<Tag> tags = tagManager.list(new LambdaQueryWrapper<Tag>()
            .eq(Tag::getTenantId, Objects.requireNonNull(tenantIsolation.getTenantId(), "租户id为空"))
            .in(Tag::getId, ids));
    if (CollectionUtils.isNotEmpty(tags)) {
      List<TagRsp> result = Lists.newArrayList();
      tags.forEach(dto -> {
        result.add(dozerMapper.map(dto, TagRsp.class));
      });
      return Result.ok(result);
    }
    return Result.error(ServiceCodeEnum.CODE_GET_LIST_FAIL);
  }


  @Override
  @Transactional(rollbackFor = Exception.class)
  public Result<TagRsp> save(TagReq req, TenantIsolation tenantIsolation) {
    Result<Tag> tagD = TagDomain.checkTag(tenantIsolation, req, tagManager, dozerMapper);
    if (!tagD.getSignal()) {
      return Result.error(tagD.getMessage());
    }
    Tag tag = tagD.getResult();
    if (tagManager.save(tag)) {
      return Result.ok(dozerMapper.map(tag, TagRsp.class));
    }
    return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
  }

  @Override
  public Result<List<Tag>> listTag(TenantIsolation tenantIsolation, ListTagRequest request) {
    if (StringUtils.isBlank(request.getTagKey()) && StringUtils.isBlank(request.getTagValue())){
      throw new BizException("key和value至少有一个条件不能为空");
    }

    LambdaQueryWrapper<Tag> lqw = new LambdaQueryWrapper<>();
    lqw.like(StringUtils.isNotBlank(request.getTagKey()),Tag::getTagKey,request.getTagKey())
            .like(StringUtils.isNotBlank(request.getTagValue()),Tag::getTagValue,request.getTagValue())
            .eq(Tag::getTenantId,tenantIsolation.getTenantId());
    return Result.ok(this.list(lqw));
  }

  @Override
  public Result<List<Tag>> listAll(TenantIsolation tenantIsolation) {
    LambdaQueryWrapper<Tag> lqw = new LambdaQueryWrapper<>();
    lqw.eq(Tag::getTenantId,tenantIsolation.getTenantId());
    return Result.ok(this.list(lqw));
  }

  public Result<List<Long>> listTagId(Long tenantId, List<ListTagRequest> requestList) {
    List<Long> returnList = new ArrayList<>();
    if(CollectionUtils.isEmpty(requestList)){
      return Result.ok(returnList);
    }
    LambdaQueryWrapper<Tag> lqw = new LambdaQueryWrapper<>();
    lqw.eq(Tag::getTenantId,tenantId);
    lqw.eq(Tag::getDeleted,0);
    List<Tag> tagList = this.list(lqw);
    if(CollectionUtils.isEmpty(tagList)){
       return Result.ok(returnList);
    }
    for(Tag tag : tagList){
      for(ListTagRequest request :requestList){
        if(request.getTagKey().equals(tag.getTagKey()) && request.getTagValue().equals(tag.getTagValue())){
            returnList.add(tag.getId());
        }
      }
    }
    return Result.ok(returnList);
  }

  @Override
  public Result<Boolean> verifyBind(Long tenantId, Long tagId) {
    QueryWrapper<TagBindRelationEntity> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("tag_id", tagId);
    queryWrapper.eq("tenant_id", tenantId);
    List<TagBindRelationEntity> tagBindRelationList = tagBindRelationService.list(queryWrapper);
    if (CollectionUtils.isNotEmpty(tagBindRelationList)) {
      return Result.ok(true);
    }
    return Result.ok(false);
  }


}
