package com.nti56.nlink.product.device.server.model.thingModel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/4/17 19:11<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "根据标签追加物模型")
public class AppendThingModelByLabelIdsDTO {

    @Schema(description = "追加模型id")
    private Long modelId;

    @Schema(description = "标签id列表")
    @NotNull(message = "至少选择一个标签")
    @Size(min = 1,message = "至少选择一个标签")
    private List<Long> labelIds;
}
