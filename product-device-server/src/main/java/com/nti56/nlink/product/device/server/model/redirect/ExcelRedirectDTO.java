package com.nti56.nlink.product.device.server.model.redirect;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/11/1 19:27<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "excel回调dto")
@ExcelIgnoreUnannotated
public class ExcelRedirectDTO {

    /**
     * 回调名称
     */
    @Schema(description = "回调名称")
    @Length(max = 64,message = "网关名称不能超过64个字符")
    @ExcelProperty(value = "redirectName",index = 0)
    private String redirectName;

    /**
     * 回调类型 0-webhook
     */
    @Schema(description = "回调类型 0-webhook")
    @ExcelProperty(value = "redirectType",index = 1)
    private Integer redirectType;

    /**
     * 回调请求超时时长
     */
    @Schema(description = "回调请求超时时长")
    @ExcelProperty(value = "redirectRequestTimeout",index = 2)
    private Integer redirectRequestTimeout;

    /**
     * 回调请求连接超时时长
     */
    @Schema(description = "回调请求连接超时时长")
    @ExcelProperty(value = "redirectRequestConnectTimeout",index = 3)
    private Integer redirectRequestConnectTimeout;

    /**
     * 回调方法明细 json
     */
    @Schema(description = "回调方法明细", example = "{\n" +
            "    \"method\": \"get\",\n" +
            "    \"url\": \"http://xxxxxx\",\n" +
            "    \"headers\": [\n" +
            "        {\n" +
            "            \"key\": \"key1\",\n" +
            "            \"value\": \"value1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"key\": \"key2\",\n" +
            "            \"value\": \"value2\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"params\": [\n" +
            "        {\n" +
            "            \"key\": \"key1\",\n" +
            "            \"value\": \"value1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"key\": \"key2\",\n" +
            "            \"value\": \"value2\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"body\": {\n" +
            "        \n" +
            "    }\n" +
            "}\t\n" +
            "\t")
    @ExcelProperty(value = "redirectFn",index = 4)
    private String redirectFn;

    @Schema(description = "描述")
    @ExcelProperty(value = "description",index = 5)
    private String description;

    @TableField(exist = false)
    @ExcelProperty(value = "lastInvokeTime",index = 6)
    private String lastInvokeTime;

}
