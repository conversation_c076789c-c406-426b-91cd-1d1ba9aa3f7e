package com.nti56.nlink.product.device.server.service.impl.strategy;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.redirect.RedirectFn;
import com.nti56.nlink.product.device.server.handler.RedirectHandler;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 类说明：
 *
 * @ClassName WebhookRedirectHandler
 * @Description webhook handler
 * <AUTHOR>
 * @Date 2022/6/27 14:50
 * @Version 1.0
 */
@Slf4j
public class RedirectHandlerImpl implements RedirectHandler {

    @Getter
    private RedirectFn redirectFn;

    private String redirectType;

    public RedirectHandlerImpl(RedirectFn redirectFn, String redirectType) {
        this.redirectFn = redirectFn;
        this.redirectType = redirectType;
    }

    @Override
    public Result<Boolean> invokeRedirect(Long redirectId) {

        if(Objects.isNull(redirectFn)){
            log.error("invoke {} redirect error. redirectFn is null,redirect id:{}", redirectId);
            return Result.error("null webhookRedirectFn");
        }
        log.debug("start invoke {} redirect function,redirect id:{},currentTime:{}", redirectId,
                System.currentTimeMillis());
        Result<Object> result = this.redirectFn.execFn();
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }
        log.debug("end invoke {} redirect function,redirect id:{},currentTime:{}", redirectId,
                System.currentTimeMillis());
        return Result.ok(true);
    }

}
