package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.channel.ChannelParam;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DriverEnum;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.ChannelParamMapper;
import com.nti56.nlink.product.device.server.model.ChannelParamDto;
import com.nti56.nlink.product.device.server.model.channel.dto.ChannelParamDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.CheckChannelParamsDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.ProofreadChannelParamDTO;
import com.nti56.nlink.product.device.server.service.IChannelParamService;
import com.nti56.nlink.product.device.server.service.IChannelService;
import org.apache.commons.collections.CollectionUtils;
import org.dozer.Mapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-15 14:11:44
 * @since JDK 1.8
 */
@Service
public class ChannelParamServiceImpl extends BaseServiceImpl<ChannelParamMapper,ChannelParamEntity> implements IChannelParamService {
    
    @Autowired
    private ChannelParamMapper channelParamMapper;

    @Lazy
    @Autowired
    private IChannelService channelService;

    @Autowired
    private Mapper dozerMapper;

    @Override
    public Result<List<ChannelParamEntity>> listChannelParam(ChannelParamDto channelParam, TenantIsolation tenantIsolation) {
        ChannelEntity channelEntity = channelService.getByIdAndTenantIsolation(channelParam.getChannelId(), tenantIsolation).getResult();
        if (channelEntity == null){
            throw new BizException("该租户下找不到该渠道");
        }

        LambdaQueryChainWrapper<ChannelParamEntity> wrapper = new LambdaQueryChainWrapper<>(channelParamMapper);
        if (Optional.ofNullable(channelParam).isPresent() && Optional.ofNullable(channelParam.getChannelId()).isPresent()) {
            wrapper.eq(ChannelParamEntity::getChannelId,channelParam.getChannelId());
        }
        wrapper.orderByDesc(ChannelParamEntity::getCreateTime);
        List<ChannelParamEntity> channelParamEntities = wrapper.list();
        return Result.ok(channelParamEntities);
    }

    @Override
    public Result<Void> deleteChannelParamByChannelId(Long channelId) {
        QueryWrapper<ChannelParamEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("channel_id",channelId);
        channelParamMapper.delete(wrapper);
        return Result.ok();
    }

    @Override
    @Transactional
    public Result<Void> createOrEditChannelParamList(Long channelId,Integer driver, Boolean isServer, List<ChannelParamDTO> channelParamList, TenantIsolation tenantIsolation) {
        
        DriverEnum driverEnum = DriverEnum.typeOfValue(driver);
        if (driverEnum == null){
            return Result.error("驱动类型参数异常");
        }
        if (CollectionUtils.isEmpty(channelParamList)){
            return Result.ok();
        }
        List<ChannelParamEntity> entityList = channelParamList.stream().map(t -> {
            ChannelParamEntity e = new ChannelParamEntity();
            BeanUtils.copyProperties(t,e);
            return e;
        }).collect(Collectors.toList());

        Result<ChannelParam> result = ChannelParam.checkParam(driverEnum, isServer, entityList);
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }
        ChannelParam channelParam = result.getResult();
        List<ChannelParamEntity> list = channelParam.toEntityList();
        list.forEach(t -> {
            t.setChannelId(channelId);
            t.setTenantId(tenantIsolation.getTenantId());
            t.setId(null);
        });
        deleteChannelParamByChannelId(channelId);
        this.saveBatch(list);
        return Result.ok();
    }

    @Override
    public Result<Void> checkChannelParams(CheckChannelParamsDTO dto) {
        DriverEnum driverEnum = DriverEnum.typeOfValue(dto.getDriver());
        if (driverEnum == null){
            return Result.error("驱动类型参数异常");
        }
        List<ChannelParamDTO> channelParamList = dto.getChannelParamList();
        if(channelParamList == null || channelParamList.size() <= 0){
            return Result.error("参数不能为空");
        }
        List<ChannelParamEntity> entityList = channelParamList.stream()
            .map(t -> {
                ChannelParamEntity entity = new ChannelParamEntity();
                BeanUtils.copyProperties(t, entity);
                return entity;
            })
            .collect(Collectors.toList());

        Result<ChannelParam> checkParamsResult = ChannelParam.checkParam(driverEnum, dto.getIsServer(), entityList);
        if (!checkParamsResult.getSignal()){
            return Result.error(checkParamsResult.getMessage());
        }

        return Result.ok();
    }

    @Override
    public Result<List<ChannelParamEntity>> getByChannelId(Long channelId,Integer driver) {
        LambdaQueryWrapper<ChannelParamEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ChannelParamEntity::getChannelId,channelId)
                .orderByDesc(ChannelParamEntity::getCreateTime);
        List<ChannelParamEntity> channelParamEntities = channelParamMapper.selectList(lqw);
        ArrayList<ChannelParamEntity> result = new ArrayList<>();
        DriverEnum driverEnum = DriverEnum.typeOfValue(driver);
        if (driverEnum != null){
            String[] requiredParam = driverEnum.getRequiredParam();
            if (ArrayUtil.isNotEmpty(requiredParam)){
                List<String> requiredParamList = new ArrayList<>(Arrays.asList(requiredParam));
                for (int i = 0; i < requiredParamList.size(); i++) {
                    requiredParamList.set(i,requiredParamList.get(i).split(":")[0]);
                }
                Iterator<ChannelParamEntity> iterator = channelParamEntities.iterator();
                while (iterator.hasNext()) {
                    ChannelParamEntity next = iterator.next();
                    String name = next.getName();
                    if (requiredParamList.contains(name)){
                        result.add(next);
                        iterator.remove();
                    }
                }
            }

        }
        result.addAll(channelParamEntities);
        return Result.ok(result);
    }

    @Override
    public Result<Void> deleteByIdsAndTenantIsolation(List<Long> deleteChannelParamIds, TenantIsolation tenantIsolation) {
        if (CollectionUtils.isEmpty(deleteChannelParamIds)){
            return Result.ok();
        }

        LambdaQueryWrapper<ChannelParamEntity> lqw = new LambdaQueryWrapper<>();
        lqw.in(ChannelParamEntity::getId,deleteChannelParamIds)
                .eq(ChannelParamEntity::getTenantId,tenantIsolation.getTenantId());
        channelParamMapper.delete(lqw);
        return Result.ok();
    }

    @Override
    public Result<List<ProofreadChannelParamDTO>> listProofreadDataByChannelId(Long channelId, TenantIsolation tenantIsolation) {
        return Result.ok(channelParamMapper.listProofreadDataByChannelId(channelId, tenantIsolation));
    }
    
    @Override
    public Result<List<ChannelParamDTO>> listChannelParamByEdgeGatewayId(Long edgeGatewayId, TenantIsolation tenantIsolation) {
        return Result.ok(channelParamMapper.listChannelParamByEdgeGatewayId(edgeGatewayId,tenantIsolation));
    }
    
    @Override
    public Result<List<ChannelParamDTO>> listChannelParamByChannelIds(List<Long> channelIds, TenantIsolation tenantIsolation) {
        return Result.ok(channelParamMapper.listChannelParamByChannelIds(channelIds,tenantIsolation));
    }
    
    private Result<Void> uniqueName(Long channelId ,String name , TenantIsolation tenantIsolation){
        return this.uniqueName(null,channelId,name,tenantIsolation);
    }
    /**
     * 判断名称是否唯一
     * @param id
     * @param name
     * @param tenantIsolation
     * @return
     */
    private Result<Void> uniqueName(Long id ,Long channelId,String name , TenantIsolation tenantIsolation){
        LambdaQueryWrapper<ChannelParamEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null ,ChannelParamEntity::getId, id)
                .eq(ChannelParamEntity::getName,name)
                .eq(ChannelParamEntity::getChannelId,channelId)
                .eq(ChannelParamEntity::getTenantId,tenantIsolation.getTenantId());

        if (channelParamMapper.selectCount(lqw) > 0){
            return Result.error("该通道已经存在该名称的通道参数,参数名称："+name);
        }

        return Result.ok();
    }
}
