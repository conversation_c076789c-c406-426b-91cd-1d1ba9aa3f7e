package com.nti56.nlink.product.device.server.service.engineering;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.engine.client.model.dto.RuleEngineServerDataDTO;
import com.nti56.nlink.engine.client.model.service.EngineeringRuleEngineServiceClient;
import com.nti56.nlink.product.device.server.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/7 15:26<br/>
 * @since JDK 1.8
 */
//@Service
//@Slf4j
//public class RuleEngineHandle {
//
//    @Autowired
//    private EngineeringRuleEngineServiceClient engineeringRuleEngineServiceClient;
//
//    public void handle(Long tenantId, RuleEngineServerDataDTO ruleEngineServerData, boolean tenantIdEqual,
//                       Map<Long,Long> deviceMap,Map<Long,Long> serviceMap,   Map<Long,Long> tagMap, Map<Long,Long> redirectMap, Map<Long,Long> templateMap) {
//        if(ruleEngineServerData == null){
//            return;
//        }
//
//        HashMap<Long, Long> ruleEngineMap = new HashMap<>(ruleEngineServerData.getRuleEngineDTOList().size(),1);
//        HashMap<Long, Long> instanceMap = new HashMap<>(ruleEngineServerData.getInstanceDTOList().size(),1);
//        HashMap<String, String> wires = new HashMap<>(1024);
//
//        if (CollectionUtils.isNotEmpty(ruleEngineServerData.getRuleEngineDTOList())){
//            ruleEngineServerData.getRuleEngineDTOList().forEach(c-> {
//                long id = IdGenerator.generateId();
//                String oldNodeRedFlowId = c.getNodeRedFlowId();
//                String newNodeRedFlowId = IdUtil.objectId();
//                c.setNodeRedFlowId(newNodeRedFlowId);
//                String jsonData = c.getJsonData();
//                if(StringUtils.isNotBlank(jsonData)){
//                    JSONArray jsonDataArr = JSON.parseArray(jsonData);
//                    for (int i = 0; i < jsonDataArr.size(); i++) {
//                        JSONObject jsonObject = jsonDataArr.getJSONObject(i);
//                        if (oldNodeRedFlowId.equals(jsonObject.getString("id"))){
//                            jsonObject.put("id",newNodeRedFlowId);
//                        }else {
//                            String nodeId = IdUtil.objectId();
//                            wires.put(jsonObject.getString("id"),nodeId);
//                            jsonObject.put("id",nodeId);
//                        }
//                        if (oldNodeRedFlowId.equals(jsonObject.getString("z"))){
//                            jsonObject.put("z",newNodeRedFlowId);
//                        }
//                        if (jsonObject.getLong("templateId") != null){
//                            jsonObject.put("templateId",templateMap.get(jsonObject.getLong("templateId")));
//                        }
//
//                        if (jsonObject.getLong("device") != null){
//                            jsonObject.put("device",deviceMap.get(jsonObject.getLong("device")));
//                        }
//                        if (StringUtils.isNotBlank(jsonObject.getString("service"))){
//                            jsonObject.put("service",serviceMap.get(Long.parseLong(jsonObject.getString("service"))));
//                        }
//                    }
//                    for (int i = 0; i < jsonDataArr.size(); i++) {
//                        JSONObject jsonObject = jsonDataArr.getJSONObject(i);
//                        JSONArray wireArr = jsonObject.getJSONArray("wires");
//                        JSONArray newWireArr = new JSONArray();
//                        if (wireArr != null && wireArr.size()>0){
//                            for (int i1 = 0; i1 < wireArr.size(); i1++) {
//                                JSONArray jsonArray = wireArr.getJSONArray(i1);
//                                JSONArray idArr = new JSONArray();
//                                if (jsonArray.size()>0){
//                                    for (int i2 = 0; i2 < jsonArray.size(); i2++) {
//                                        idArr.add(wires.get(jsonArray.getString(i2)));
//                                    }
//                                }
//                                newWireArr.add(idArr);
//                            }
//                        }
//                        jsonObject.put("wires",newWireArr);
//                    }
//                    c.setJsonData(jsonDataArr.toJSONString());
//                }
//
//                ruleEngineMap.put(c.getId(),id);
//                c.setId(id);
//                c.setStatus(2);
//                if (!tenantIdEqual){
//                    c.setTenantId(tenantId);
//                }
//            });
//        }
//
//        if (CollectionUtils.isNotEmpty(ruleEngineServerData.getInstanceDTOList())){
//            ruleEngineServerData.getInstanceDTOList().forEach(c-> {
//                long id = IdGenerator.generateId();
//                instanceMap.put(c.getId(),id);
//                c.setId(id);
//                c.setStatus(0);
//                if (!tenantIdEqual){
//                    c.setTenantId(tenantId);
//                }
//            });
//        }
//
//
//        if (CollectionUtils.isNotEmpty(ruleEngineServerData.getInstanceExecDTOList())){
//            ruleEngineServerData.getInstanceExecDTOList().forEach(c-> {
//                c.setId(IdGenerator.generateId());
//                c.setNotifyTm(templateMap.getOrDefault(c.getNotifyTm(),c.getNotifyTm()));
//                c.setRedirectId(redirectMap.getOrDefault(c.getRedirectId(),c.getRedirectId()));
//                c.setScenesId(ruleEngineMap.getOrDefault(c.getScenesId(),c.getScenesId()));
//                c.setInstanceId(instanceMap.getOrDefault(c.getInstanceId(),c.getInstanceId()));
//                if (!tenantIdEqual){
//                    c.setTenantId(tenantId);
//                }
//            });
//        }
//
//
//        if (CollectionUtils.isNotEmpty(ruleEngineServerData.getInstanceTriggerDTOList())){
//            ruleEngineServerData.getInstanceTriggerDTOList().forEach(c-> {
//                c.setId(IdGenerator.generateId());
//                c.setInstanceId(instanceMap.getOrDefault(c.getInstanceId(),c.getInstanceId()));
//                if (!tenantIdEqual){
//                    c.setTenantId(tenantId);
//                }
//            });
//
//        }
//
//        if (CollectionUtils.isNotEmpty(ruleEngineServerData.getTagRelationDTOList())){
//            ruleEngineServerData.getTagRelationDTOList().forEach(c-> {
//                c.setId(IdGenerator.generateId());
//                c.setTagId(tagMap.getOrDefault(c.getTagId(),c.getTagId()));
//                if (Integer.valueOf(1).equals(c.getRelationType())){
//                    c.setRelationId(instanceMap.getOrDefault(c.getRelationId(),c.getRelationId()));
//                }else if (Integer.valueOf(2).equals(c.getRelationType())){
//                    c.setRelationId(ruleEngineMap.getOrDefault(c.getRelationId(),c.getRelationId()));
//                }
//                if (!tenantIdEqual){
//                    c.setTenantId(tenantId);
//                }
//            });
//        }
//
//        R r = engineeringRuleEngineServiceClient.deleteRuleEngineDataByTenantId(tenantId);
//        if (!(boolean) r.get("ok")) {
//            throw new BizException("服务器异常，请稍后重试7");
//        }
//        R r1 = engineeringRuleEngineServiceClient.createRuleEngineData(ruleEngineServerData);
//
//        if (!(boolean) r1.get("ok")) {
//            throw new BizException("服务器异常，请稍后重试8");
//        }
//
//        ruleEngineMap.clear();
//        instanceMap.clear();
//        log.info("rule-engine服务导入成功");
//    }
//}
