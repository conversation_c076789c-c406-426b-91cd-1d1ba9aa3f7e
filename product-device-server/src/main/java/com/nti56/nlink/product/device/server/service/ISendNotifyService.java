package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.send.NotifyDTO;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/8 22:56<br/>
 * @since JDK 1.8
 */
public interface ISendNotifyService {


    Result batchSend(List<NotifyDTO> dto, TenantIsolation tenantIsolation, String requestParams);

    Result send(NotifyDTO dto, TenantIsolation tenantIsolation, String requestParams);

    Result<Object> sendVerificationCode(int type, String phone, TenantIsolation tenantIsolation);
}
