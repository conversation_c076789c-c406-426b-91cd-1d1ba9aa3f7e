package com.nti56.nlink.product.device.server.model.label.vo;

import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.server.entity.LabelBindRelationEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/9/1 10:45<br/>
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PropertiesBindLabelVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer bindNum;

    private ModelField modelField;

    private List<LabelBindRelationEntity> labelBindRelationEntities;


}
