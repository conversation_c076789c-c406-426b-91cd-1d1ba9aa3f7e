package com.nti56.nlink.product.device.server.util;


import com.nti56.nlink.product.device.server.constant.NotifyConstant;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;


/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/7/5 14:52<br/>
 * @since JDK 1.8
 */
public class RSAUtils {
    // 加密算法
    private final static String ALGORITHM_RSA = "RSA";

    /**
     * 生成公钥、私钥的字符串
     * 方便传输
     *
     * @param modulus 模长
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static List<String> getRSAKeyString(int modulus) throws NoSuchAlgorithmException{

        List<String> keyList = new ArrayList<>(2);
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(ALGORITHM_RSA);
        keyPairGen.initialize(modulus);
        KeyPair keyPair = keyPairGen.generateKeyPair();
        String publicKey = Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded());
        String privateKey = Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded());
        keyList.add(publicKey);
        keyList.add(privateKey);
        return keyList;
    }


    /**
     * 公钥加密
     *
     * @param data
     * @param publicKey
     * @return
     * @throws Exception
     */
    public static String encryptByPublicKey(String data, String publicKey)
            throws Exception {

        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM_RSA);
        byte[] keyBytes = Base64.getDecoder().decode(publicKey);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        RSAPublicKey rsaPublicKey = (RSAPublicKey) keyFactory.generatePublic(spec);
        Cipher cipher = Cipher.getInstance(ALGORITHM_RSA);
        cipher.init(Cipher.ENCRYPT_MODE, rsaPublicKey);
        // 模长n转换成字节数
        int modulusSize = rsaPublicKey.getModulus().bitLength() / 8;
        // PKCS Padding长度为11字节，所以实际要加密的数据不能要 - 11byte
        int maxSingleSize = modulusSize - 11;
        // 切分字节数组，每段不大于maxSingleSize
        byte[][] dataArray = splitArray(data.getBytes(), maxSingleSize);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        // 分组加密，并将加密后的内容写入输出字节流
        for (byte[] s : dataArray) {
            out.write(cipher.doFinal(s));
        }
        // 使用Base64将字节数组转换String类型
        return Base64.getEncoder().encodeToString(out.toByteArray());
    }

    /**
     * 私钥解密
     *
     * @param data
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static String decryptByPrivateKey(String data, String privateKey)
            throws Exception {

        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM_RSA);
        byte[] keyBytes = Base64.getDecoder().decode(privateKey);
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
        RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyFactory.generatePrivate(spec);
        Cipher cipher = Cipher.getInstance(ALGORITHM_RSA);
        cipher.init(Cipher.DECRYPT_MODE, rsaPrivateKey);
        // RSA加密算法的模长 n
        int modulusSize = rsaPrivateKey.getModulus().bitLength() / 8;
        byte[] dataBytes = data.getBytes();
        // 之前加密的时候做了转码，此处需要使用Base64进行解码
        byte[] decodeData = Base64.getDecoder().decode(dataBytes);
        // 切分字节数组，每段不大于modulusSize
        byte[][] splitArrays = splitArray(decodeData, modulusSize);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        for(byte[] arr : splitArrays){
            out.write(cipher.doFinal(arr));
        }
        return new String(out.toByteArray());
    }

    /**
     * 按指定长度切分数组
     *
     * @param data
     * @param len 单个字节数组长度
     * @return
     */
    private static byte[][] splitArray(byte[] data,int len){

        int dataLen = data.length;
        if (dataLen <= len) {
            return new byte[][]{data};
        }
        byte[][] result = new byte[(dataLen-1)/len + 1][];
        int resultLen = result.length;
        for (int i = 0; i < resultLen; i++) {
            if (i == resultLen - 1) {
                int slen = dataLen - len * i;
                byte[] single = new byte[slen];
                System.arraycopy(data, len * i, single, 0, slen);
                result[i] = single;
                break;
            }
            byte[] single = new byte[len];
            System.arraycopy(data, len * i, single, 0, len);
            result[i] = single;
        }
        return result;
    }

    public static void main(String[] args) {
        try {
            String paxbwvjgmmmrbcbh = RSAUtils.encryptByPublicKey("paxbwvjgmmmrbcbh", "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIZq+RkX6D781rI8CtHooaIgPjqi5V93v1c30xEhKOmURs25D2dAoo2U8eLPIHtqJSbhlvS4SZPqqYG6yY/hxO8CAwEAAQ==");
            System.out.println(paxbwvjgmmmrbcbh);

            String s = RSAUtils.decryptByPrivateKey("B2zKmIQDm9lHKzWZBhgc+QCcrZ42ItEhnSoTDQQ1iY0mVeYC1QYt2I6PwL9mSRhTABBV8+0C4bsjOGf0PvzpPcdn1jXFO1klhhJOH/KB1rFcY/WQt+2rZy47bbR0//ByDurqvyOXgB84eY6eNWc9o+x00Ev8ZNw7HTdLu1mteUyeh9a6Tz4k8vobhe23vKLGHRHJROYJzGyAq9xj47hO1ZnuqCAlUTwuqofgNx86CO8SNvhk7yCqs2JvsZjEYLtLtLfNBQqZ5M3h2L+X5/QnII0Cl+YTevURodXCi8gC1+l6RjHtVFM5lc+zphNhzruD9en3Va55Ap6Nuv+OLvFQxSI/QcVyUTjCacdTpRpf36F5hMS47JPQurrrpAZPSBKcROkuahnjM46MOvK9T8nkvkp49NXfUTuiEQjBNLN2EPs2R+xMhin/clxrxek+SK697eMMaEN1HerNDQvaFcgs8/LfJkWK7pjtI4vH2AARHQVKcptPwHWRWOMgzfaTcJo1abRJ5GbQzocW4aTaxlsmalOiTDkXxLEhyqdODZdCq15V2nO1HIv4rAJPnE5pyXzm87QKqJ5kiayI5m55vRN52wiygutzzo+YxRV2xD+IpsJO9AEyxd1sLuaOfjEKyP6iNTw+UEpRMtNpv0gwyT+YCJEQ/5FT4FaMCPlefgYE76o=",
                    NotifyConstant.PRIVATE_KEY);
            System.out.println(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
