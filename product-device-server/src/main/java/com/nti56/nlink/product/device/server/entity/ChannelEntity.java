package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.influxdb.annotations.Column;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 类说明: 通道表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-18 17:40:01
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("channel")
@Schema(description = "通道对象")
public class ChannelEntity {

    /**
     * id
     */
    private Long id;
    /**
     * 通道名称
     */
    @Schema(description = "通道名字")
    private String name;
    /**
     * 所属驱动类型
     */
    @Schema(description = "所属驱动类型")
    private Integer driver;

    @Schema(description = "自定义协议名称")
    private String customDriverName;

    @Schema(description = "是否是服务端通道")
    private Boolean isServer;

    @Schema(description = "描述")
    private String descript;
    /**
     * 边缘网关ID
     */
    @Schema(description = "边缘网关")
    private Long edgeGatewayId;

    @Schema(description = "时间间隔，单位毫秒")
    private Integer intervalMs;

    /**
     * 通道状态：0-停用，1-启用
     */
    @Schema(description = "通道状态：0-停用，1-启用")
    private Integer status;

    /**
     * 通道运行时信息
     */
    @Schema(description = "通道运行时信息")
    private ChannelRuntimeInfoField runtimeInfo;

    @Schema(description = "md5校对")
    @Column(name = "md5_proofread")
    private String md5Proofread;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

    /**
     * 逻辑删除，1-删除
     */
    @Schema(description = "逻辑删除，1-删除")
    @TableLogic
    private Boolean deleted;

    @Schema(description = "租户ID")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @Schema(description = "工程ID")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @Schema(description = "模块ID")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @Schema(description = "空间ID")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


}
