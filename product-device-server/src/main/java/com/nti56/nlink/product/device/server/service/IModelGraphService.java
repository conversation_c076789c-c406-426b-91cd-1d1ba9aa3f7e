//package com.nti56.nlink.product.device.server.service;
//
//import com.nti56.nlink.common.util.Result;
//import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
//import com.nti56.nlink.product.device.server.model.DeviceDto;
//import com.nti56.nlink.product.device.server.model.neo4j.Neo4jDeviceDTO;
//import com.nti56.nlink.product.device.server.model.thingModel.dto.DeviceModelPreviewDTO;
//
//import java.util.List;
//
//public interface IModelGraphService {
//
//    /**
//     * 模型关系图保存
//     * @param modelEntity
//     * @param inheritIds
//     * @return
//     */
//    Result<Void> saveModelNodeDeprecated(ThingModelEntity modelEntity, List<Long> inheritIds);
//
//    /**
//     * 更新模型关系图
//     * @param modelEntity
//     * @param inheritIds
//     * @return
//     */
//    Result<Void> updateModelNodeDeprecated(ThingModelEntity modelEntity, List<Long> inheritIds);
//
//    /**
//     * 删除模型节点
//     * @param modelId
//     * @return
//     */
//    Result<Void> deleteModelNodeDeprecated(Long modelId);
//
//    /**
//     * 查询模型被谁继承了 包含设备和模型
//     * @param modelId
//     * @return
//     */
//    Result<Object> queryWhichInheritMeByModelIdDeprecated(Long modelId);
//
//    /**
//     * 查询继承的模型
//     * @param modelId
//     * @return
//     */
//    Result<Object> queryInheritByModelIdDeprecated(Long modelId);
//
//    /**
//     * 查询继承我的模型和设备 根据模型名称
//     * @param name
//     * @param tenantId
//     * @return
//     */
//    Result<Object> queryWhichInheritMeByNameDeprecated(String name, Long tenantId);
//
//    /**
//     * 查询我继承的模型 根据模型名称
//     * @param name
//     * @param tenantId
//     * @return
//     */
//    Result<Object> queryInheritByNameDeprecated(String name, Long tenantId);
//
//    /**
//     * 保存设备节点
//     * @param deviceDto
//     * @param inheritIds
//     * @return
//     */
//    Result<Void> saveDeviceNodeDeprecated(DeviceDto deviceDto, List<Long> inheritIds);
//
//    /**
//     * 更新设备节点
//     * @param deviceDto
//     * @param inheritIds
//     * @return
//     */
//    Result<Void> updateDeviceNodeDeprecated(DeviceDto deviceDto, List<Long> inheritIds);
//
//    /**
//     * 删除设备节点
//     * @param deviceId
//     * @return
//     */
//    Result<Void> deleteDeviceNodeDeprecated(Long deviceId);
//
//    /**
//     * 根据设备id 查询继承的模型
//     * @param deviceId
//     * @return
//     */
//    Result<Object> queryInheritByDeviceIdDeprecated(Long deviceId);
//
//    /**
//     * 根据设备名称 查询设备继承的模型
//     * @param deviceName
//     * @param tenantId
//     * @return
//     */
//    Result<Object> queryInheritByDeviceNameDeprecated(String deviceName, Long tenantId);
//
//    /**
//     * 模型预览
//     * @param id
//     * @return
//     */
//    Result<DeviceModelPreviewDTO> getModelPreviewDeprecated(Long id);
//
//
//    /**
//     * 获取设备预览
//     * @param id
//     * @return
//     */
//    Result<DeviceModelPreviewDTO> getDevicePreviewDeprecated(Long id);
//
//
//    Result<Object> queryWhichInheritMeByDeviceIdDeprecated(Long id);
//
//    Result<Void> deleteModelAndDeviceGraphDeprecated(Long tenantId);
//
//    Result<Void> addModelRelationDeprecated(Long id, Long thingModelId);
//
//    Result<Void> batchDeleteDeviceNodeDeprecated(List<Neo4jDeviceDTO> nodes);
//}
