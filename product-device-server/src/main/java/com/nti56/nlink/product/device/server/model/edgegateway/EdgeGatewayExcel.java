package com.nti56.nlink.product.device.server.model.edgegateway;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
@Schema(description = "网关excel")
public class EdgeGatewayExcel {

    /**
     * id
     */ 
    private Long id;
    /**
     * 边缘网关名称
     */
    @ExcelProperty(value = "name",index = 0)
    private String name;
    /**
     * 描述
     */
    @Schema(description = "描述")
    @ExcelProperty(value = "descript",index = 1)
    private String descript;

    /**
     * host
     */
    @Schema(description = "主机")
    @ExcelProperty(value = "host",index = 2)
    private String host;
    /**
     * port
     */
    @Schema(description = "端口")
    @ExcelProperty(value = "port",index = 3)
    private Integer port;

    /**
     * IMEI
     */
    @Schema(description = "IMEI")
    @ExcelProperty(value = "imei",index = 4)
    private String imei;

    /**
     * 流量卡号
     */
    @Schema(description = "流量卡号")
    @ExcelProperty(value = "trafficCard",index = 5)
    private String trafficCard;

    @Schema(description = "运营商 1-移动，2-电信，3-联通")
    @ExcelProperty(value = "operators",index = 6)
    private Integer operators;

    @Schema(description = "是否访问公共MQTT接口")
    @ExcelProperty(value = "visitPublicMqtt",index = 7)
    private Boolean visitPublicMqtt;

    @Schema(description = "外部mqtt主机")
    @ExcelProperty(value = "publicMqttIp",index = 8)
    private String publicMqttIp;

    @Schema(description = "外部mqtt端口")
    @ExcelProperty(value = "publicMqttPort",index = 9)
    private Integer publicMqttPort;

    @Schema(description = "网关类型 虚拟网关/网关设备 ")
    @ExcelProperty(value = "type",index = 10)
    private Integer type;

    /**
     * 内存监控百分比
     */
    @Schema(description = "网关内存监控百分比")
    @ExcelProperty(value = "memoryMonitor",index = 11)
    private Integer memoryMonitor;

    /**
     * 硬件空间监控百分比
     */
    @Schema(description = "网关空间监控百分比")
    @ExcelProperty(value = "spaceMonitor",index = 12)
    private Integer spaceMonitor;

}
