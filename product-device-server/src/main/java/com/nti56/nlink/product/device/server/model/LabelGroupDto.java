package com.nti56.nlink.product.device.server.model;


import com.nti56.nlink.product.device.server.entity.LabelGroupEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 通道dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:58
 * @since JDK 1.8
 */
@Data
@Schema
public class LabelGroupDto extends LabelGroupEntity {

    /**
     * 标签列表
     */
    @Schema(description = "标签列表")
    private List<LabelDto> labelDtoList;

    /**
     * 所属通道名称
     */
    private String channelName;


    /**
     * 所属网关名称
     */
    private String edgeGatewayName;
    
}
