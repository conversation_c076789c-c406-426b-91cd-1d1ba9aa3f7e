package com.nti56.nlink.product.device.server.model.thingModel.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 类说明：
 *
 * @ClassName DeviceModelPreviewDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/11/23 10:22
 * @Version 1.0
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeviceModelPreviewDTO {

    private String name;
    private String type;
    private String descript;

    private Set<PropertyPreviewDTO> props;
    private Set<EventPreviewDTO> events;
    private Set<ServicePreviewDTO> services;
    private Set<SubscribePreviewDTO> subscribes;

}
