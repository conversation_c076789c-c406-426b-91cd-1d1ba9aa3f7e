package com.nti56.nlink.product.device.server.model.channel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/5/11 9:39<br/>
 * @since JDK 1.8
 */
@Data
public class ChannelVO {

    private Long id;

    @Schema(description = "通道名字")
    private String name;

    @Schema(description = "通道状态：0-停用，1-启用")
    private Integer status;

    @Schema(description = "所属驱动类型，1-Snap7，2-Modbus，3-OPC UA，4-ZigBee，5-B<PERSON>")
    private Integer driver;

    @Schema(description = "描述")
    private String descript;

    @Schema(description = "边缘网关")
    private Long edgeGatewayId;

    @Schema(description = "驱动类型名称")
    private String driverName;
    
    @Schema(description = "租户id")
    private Long tenantId;
}
