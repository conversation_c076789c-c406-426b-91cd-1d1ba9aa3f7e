package com.nti56.nlink.product.device.server.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/13 16:56<br/>
 * @since JDK 1.8
 */
public enum AuditEnum {

    AUDITING(0, "审核中"),
    AUDIT_SUCCESS(1,"审核通过"),
    AUDIT_FAIL(2, "审核失败")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    AuditEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static AuditEnum typeOfValue(Integer value){
        AuditEnum[] values = AuditEnum.values();
        for (AuditEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static AuditEnum typeOfName(String name){
        AuditEnum[] values = AuditEnum.values();
        for (AuditEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static String getNameByValue(Integer value){
        AuditEnum[] values = AuditEnum.values();
        for (AuditEnum v : values) {
            if (v.value.equals(value)) {
                return v.name;
            }
        }
        return null;

    }


    public static List toList(){
        List<Map> result = new ArrayList<>();
        AuditEnum[] values = AuditEnum.values();
        Map<String,Object> map ;
        for (AuditEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            result.add(map);
        }
        return result;
    }
}
