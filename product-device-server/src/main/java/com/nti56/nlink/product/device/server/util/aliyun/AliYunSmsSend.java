package com.nti56.nlink.product.device.server.util.aliyun;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.*;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/13 11:38<br/>
 * @since JDK 1.8
 */
public class AliYunSmsSend {
    /**
     * 单条短信发送
     * @param client 调用AliyunSmsBase.createClient()创建
     * @param phoneNumbers 接收短信的手机号码，多个用英文逗号隔开
     * @param signName 短信签名名称，eg: "阿里云"
     * @param templateCode 短信模板CODE
     * @param templateParam 短信模板变量对应的实际值，eg：{"code":"1234"}
     * @return sendResp.body.bizId可用于查询短信发送结果
     * @throws Exception
     */
    public static SendSmsResponse send(com.aliyun.dysmsapi20170525.Client client, String phoneNumbers, String signName, String templateCode, String templateParam) throws Exception {
        SendSmsRequest sendReq = new SendSmsRequest()
                .setPhoneNumbers(phoneNumbers)
                .setSignName(signName)
                .setTemplateCode(templateCode)
                .setTemplateParam(templateParam);

        return client.sendSms(sendReq);
    }

    /**
     * 批量短信发送
     * @param client 调用AliyunSmsBase.createClient()创建
     * @param phoneNumberJson  接收短信的手机号码，JSON 数组格式，eg: ["1590***0000","13500***000"]
     * @param signNameJson 短信签名名称，JSON 数组格式，eg: ["阿里云","阿里巴巴"]
     * @param templateCode 短信模板CODE
     * @param templateParamJson 短信模板变量对应的实际值，JSON 数组格式，eg：[{"code":"1234"},{"code":"2345"}]
     * @return sendResp.body.bizId可用于查询短信发送结果
     * @throws Exception
     */
    public static SendBatchSmsResponse batchSend(com.aliyun.dysmsapi20170525.Client client, String phoneNumberJson, String signNameJson, String templateCode, String templateParamJson) throws Exception {
        SendBatchSmsRequest sendReq = new SendBatchSmsRequest()
                .setPhoneNumberJson(phoneNumberJson)
                .setSignNameJson(signNameJson)
                .setTemplateCode(templateCode)
                .setTemplateParamJson(templateParamJson);
        return client.sendBatchSms(sendReq);
    }

    /**
     * 单条短信发送 多个手机号发送结果查询
     * @param client 调用AliyunSmsBase.createClient()创建
     * @param phoneNumbers 接收短信的手机号码
     * @param bizId 发送回执ID，即发送流水号。调用发送接口SendSms或SendBatchSms发送短信时，返回值中的BizId字段。
     * @param pageSize 分页查看发送记录，指定每页显示的短信记录数量 取值范围为1~50
     * @param currentPage 分页查看发送记录，指定发送记录的当前页码。
     * @return Map集合 key:手机号value返回结果
     * 返回示例
    QuerySendDetails：

    {
    "TotalCount":1,
    "Message":"OK",
    "RequestId":"819BE656-D2E0-4858-8B21-B2E477085AAF",
    "SmsSendDetailDTOs":{
    "SmsSendDetailDTO":{
    "SendDate":"2019-01-08 16:44:10",
    "OutId":123,
    "SendStatus":3,
    "ReceiveDate":"2019-01-08 16:44:13",
    "ErrCode":"DELIVERED",
    "TemplateCode":"SMS_122310183",
    "Content":"【阿里云】验证码为：123，您正在登录，若非本人操作，请勿泄露",
    "PhoneNum":15298356881
    }
    },
    "Code":"OK"
    }
     * @throws Exception
     */
    public static Map<String, QuerySendDetailsResponse> sendResultMany(com.aliyun.dysmsapi20170525.Client client, String phoneNumbers, String bizId, Long pageSize, Long currentPage) throws Exception {
        Map<String, QuerySendDetailsResponse> map = new HashMap<String, QuerySendDetailsResponse>();
        java.util.List<String> phoneNums = Arrays.asList(phoneNumbers.split(","));
        for (String phoneNum : phoneNums) {
            SimpleDateFormat ft = new SimpleDateFormat("yyyyMMdd");
            QuerySendDetailsRequest queryReq = new QuerySendDetailsRequest()
                    .setPhoneNumber(com.aliyun.teautil.Common.assertAsString(phoneNum))
                    .setBizId(bizId)
                    .setSendDate(ft.format(new Date()))
                    .setPageSize(pageSize)
                    .setCurrentPage(currentPage);
            QuerySendDetailsResponse queryResp = client.querySendDetails(queryReq);
            java.util.List<QuerySendDetailsResponseBody.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO> dtos = queryResp.body.smsSendDetailDTOs.smsSendDetailDTO;
            // 打印结果
            for (QuerySendDetailsResponseBody.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO dto : dtos) {
                if (com.aliyun.teautil.Common.equalString("" + dto.sendStatus + "", "3")) {
                    System.out.println("" + dto.phoneNum + " 发送成功，接收时间: " + dto.receiveDate + "");
                } else if (com.aliyun.teautil.Common.equalString("" + dto.sendStatus + "", "2")) {
                    System.out.println("" + dto.phoneNum + " 发送失败");
                } else {
                    System.out.println("" + dto.phoneNum + " 正在发送中...");
                }
            }
            map.put(phoneNum, queryResp);
        }
        return map;
    }

    /**
     * 单条短信发送 单个手机号发送结果查询
     * @param client 调用AliyunSmsBase.createClient()创建
     * @param phone 接收短信的手机号码
     * @param bizId 发送回执ID，即发送流水号。调用发送接口SendSms或SendBatchSms发送短信时，返回值中的BizId字段。
     * @param pageSize 分页查看发送记录，指定每页显示的短信记录数量 取值范围为1~50
     * @param currentPage 分页查看发送记录，指定发送记录的当前页码。
     * @return 返回结果
    返回示例
    QuerySendDetails：

    {
    "TotalCount":1,
    "Message":"OK",
    "RequestId":"819BE656-D2E0-4858-8B21-B2E477085AAF",
    "SmsSendDetailDTOs":{
    "SmsSendDetailDTO":{
    "SendDate":"2019-01-08 16:44:10",
    "OutId":123,
    "SendStatus":3,
    "ReceiveDate":"2019-01-08 16:44:13",
    "ErrCode":"DELIVERED",
    "TemplateCode":"SMS_122310183",
    "Content":"【阿里云】验证码为：123，您正在登录，若非本人操作，请勿泄露",
    "PhoneNum":15298356881
    }
    },
    "Code":"OK"
    }
     * @throws Exception
     */
    public static QuerySendDetailsResponse sendResultOne(com.aliyun.dysmsapi20170525.Client client, String phone, String bizId, Long pageSize, Long currentPage) throws Exception {
        SimpleDateFormat ft = new SimpleDateFormat("yyyyMMdd");
        QuerySendDetailsRequest queryReq = new QuerySendDetailsRequest()
                .setPhoneNumber(com.aliyun.teautil.Common.assertAsString(phone))
                .setBizId(bizId)
                .setSendDate(ft.format(new Date()))
                .setPageSize(10L)
                .setCurrentPage(1L);
        QuerySendDetailsResponse queryResp = client.querySendDetails(queryReq);
        java.util.List<QuerySendDetailsResponseBody.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO> dtos = queryResp.body.smsSendDetailDTOs.smsSendDetailDTO;
        // 打印结果
        for (QuerySendDetailsResponseBody.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO dto : dtos) {
            if (com.aliyun.teautil.Common.equalString("" + dto.sendStatus + "", "3")) {
                System.out.println("" + dto.phoneNum + " 发送成功，接收时间: " + dto.receiveDate + "");
            } else if (com.aliyun.teautil.Common.equalString("" + dto.sendStatus + "", "2")) {
                System.out.println("" + dto.phoneNum + " 发送失败");
            } else {
                System.out.println("" + dto.phoneNum + " 正在发送中...");
            }
        }
        return queryResp;
    }

    /**
     * 批量短信发送 多个手机号发送结果查询
     * @param client 调用AliyunSmsBase.createClient()创建
     * @param phoneNumberJson 接收短信的手机号码
     * @param bizId 发送回执ID，即发送流水号。调用发送接口SendSms或SendBatchSms发送短信时，返回值中的BizId字段。
     * @param pageSize 分页查看发送记录，指定每页显示的短信记录数量 取值范围为1~50
     * @param currentPage 分页查看发送记录，指定发送记录的当前页码。
     * @return Map集合 key:手机号value返回结果
     * 返回示例
    QuerySendDetails：

    {
    "TotalCount":1,
    "Message":"OK",
    "RequestId":"819BE656-D2E0-4858-8B21-B2E477085AAF",
    "SmsSendDetailDTOs":{
    "SmsSendDetailDTO":{
    "SendDate":"2019-01-08 16:44:10",
    "OutId":123,
    "SendStatus":3,
    "ReceiveDate":"2019-01-08 16:44:13",
    "ErrCode":"DELIVERED",
    "TemplateCode":"SMS_122310183",
    "Content":"【阿里云】验证码为：123，您正在登录，若非本人操作，请勿泄露",
    "PhoneNum":15298356881
    }
    },
    "Code":"OK"
    }
     * @throws Exception
     */
    public static Map<String, QuerySendDetailsResponse> batchSendResultMany(com.aliyun.dysmsapi20170525.Client client, String phoneNumberJson, String bizId, Long pageSize, Long currentPage) throws Exception {
        java.util.List<Object> phoneNums = com.aliyun.teautil.Common.assertAsArray(com.aliyun.teautil.Common.parseJSON(phoneNumberJson));
        String phoneNumbers = "";
        for (Object phoneNum : phoneNums) {
            phoneNumbers += com.aliyun.teautil.Common.assertAsString(phoneNum) + ",";
        }
        phoneNumbers = phoneNumbers.substring(0, phoneNumbers.length()-1);
        return sendResultMany(client, phoneNumbers, bizId, pageSize, currentPage);
    }

    /**
     * 批量短信发送 单个手机号发送结果查询
     * @param client 调用AliyunSmsBase.createClient()创建
     * @param phone 接收短信的手机号码
     * @param bizId 发送回执ID，即发送流水号。调用发送接口SendSms或SendBatchSms发送短信时，返回值中的BizId字段。
     * @param pageSize 分页查看发送记录，指定每页显示的短信记录数量 取值范围为1~50
     * @param currentPage 分页查看发送记录，指定发送记录的当前页码。
     * @return 返回结果
     * 返回示例
    QuerySendDetails：

    {
    "TotalCount":1,
    "Message":"OK",
    "RequestId":"819BE656-D2E0-4858-8B21-B2E477085AAF",
    "SmsSendDetailDTOs":{
    "SmsSendDetailDTO":{
    "SendDate":"2019-01-08 16:44:10",
    "OutId":123,
    "SendStatus":3,
    "ReceiveDate":"2019-01-08 16:44:13",
    "ErrCode":"DELIVERED",
    "TemplateCode":"SMS_122310183",
    "Content":"【阿里云】验证码为：123，您正在登录，若非本人操作，请勿泄露",
    "PhoneNum":15298356881
    }
    },
    "Code":"OK"
    }
     * @throws Exception
     */
    public static QuerySendDetailsResponse batchSendResultOne(com.aliyun.dysmsapi20170525.Client client, String phone, String bizId, Long pageSize, Long currentPage) throws Exception {
        return sendResultOne(client, phone, bizId, pageSize, currentPage);
    }


    public static void main(String[] args_) throws Exception {

        Client client = AliYunSmsBase.createClient("LTAI4G528qGjxsg7e8tm37BU", "******************************");
        QuerySendDetailsRequest querySendDetailsRequest = new QuerySendDetailsRequest()
                .setPhoneNumber("13110788020")
                .setSendDate("20220415")
                .setPageSize(10L)
                .setCurrentPage(1L);
        // 复制代码运行请自行打印 API 的返回值
        QuerySendDetailsResponse querySendDetailsResponse = client.querySendDetails(querySendDetailsRequest);
        QuerySendDetailsResponseBody body = querySendDetailsResponse.getBody();
    }

}
