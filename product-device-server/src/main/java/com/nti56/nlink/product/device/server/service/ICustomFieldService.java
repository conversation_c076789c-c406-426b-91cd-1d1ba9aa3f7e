package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.product.device.server.entity.CustomFieldEntity;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.util.Result;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.nlink.common.mybatis.IBaseService;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <p>
 * 自定义字段表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-11-18 11:43:28
 * @since JDK 1.8
 */
public interface ICustomFieldService extends IBaseService<CustomFieldEntity> {

    Result<CustomFieldEntity> save(TenantIsolation tenant, CustomFieldEntity entity);

    Result<Page<CustomFieldEntity>> getPage(TenantIsolation tenant, @Nullable CustomFieldEntity entity, Page<CustomFieldEntity> page);

    Result<List<CustomFieldEntity>> list(TenantIsolation tenant, CustomFieldEntity entity);

    Result<Void> update(TenantIsolation tenant, CustomFieldEntity entity);

    Result<Void> deleteById(TenantIsolation tenant, Long id);

    Result<CustomFieldEntity> getById(TenantIsolation tenant, Long id);

}
