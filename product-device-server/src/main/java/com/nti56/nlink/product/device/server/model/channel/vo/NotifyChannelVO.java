package com.nti56.nlink.product.device.server.model.channel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/22 21:00<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "渠道dto")
public class NotifyChannelVO {

    private Long id;

    @Schema(description = "渠道名称")
    private String name;

    @Schema(description = "渠道描述")
    private String description;

    @Schema(description = "通知类型 0邮件 1短信 2钉钉机器人")
    private Integer notifyType;

    @Schema(description = "渠道参数")
    private String params;

    @Schema(description = "templateFiled")
    private String templateFiled;

}
