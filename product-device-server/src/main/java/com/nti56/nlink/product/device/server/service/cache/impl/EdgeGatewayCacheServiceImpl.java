package com.nti56.nlink.product.device.server.service.cache.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.nti56.nlink.product.device.server.mapper.EdgeGatewayMapper;
import com.nti56.nlink.product.device.server.service.cache.IEdgeGatewayCacheService;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-08-05 14:43:37
 * @since JDK 1.8
 */
@Service
public class EdgeGatewayCacheServiceImpl implements IEdgeGatewayCacheService{

    @Autowired
    EdgeGatewayMapper edgeGatewayMapper;

    @Override
    @Cacheable(cacheNames = "edgeGatewayHeartbeatUuid", key="#tenantId + '_' + #edgeGatewayId",unless = "#result == null")
    public String getHeartbeatUuid(Long tenantId, Long edgeGatewayId) {
        String heartbeatUuid = edgeGatewayMapper.getHeartbeatUuidById(tenantId, edgeGatewayId);
        return heartbeatUuid;
    }

    @Override
    @CacheEvict(cacheNames = "edgeGatewayHeartbeatUuid", key="#tenantId + '_' + #edgeGatewayId")
    public void evictHeartbeatUuid(Long tenantId, Long edgeGatewayId, String heartbeatUuid) {
        
    }
}
