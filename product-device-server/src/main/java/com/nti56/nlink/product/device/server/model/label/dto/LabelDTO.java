package com.nti56.nlink.product.device.server.model.label.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.nti56.nlink.product.device.server.excel.converter.ReadOnlyConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 11:27<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "标签dto")
@ExcelIgnoreUnannotated
public class LabelDTO {

    private Long id;

    private Long sourceId;

    private Long labelGroupId;

    private Long channelId;

    private String labelGroupName;

    @Schema(description = "名称")
    @NotBlank(message = "标签名称不能为空")
    @Length(max = 128,message = "标签名称不能超过128个字符")
    @ExcelProperty(value = "Tag Name",index = 0)
    private String name;


    @Schema(description = "标签描述")
    @Length(max = 256,message = "标签描述不能超过256个字符")
    @ExcelProperty(value = "Description",index = 9)
    private String descript;

    @Schema(description = "别名")
    @ExcelProperty(value = "Alias",index = 1)
    @Length(max = 256,message = "标签描述不能超过256个字符")
    private String alias;

    @Schema(description = "地址，如DB50.DBB1")
    @ExcelProperty(value = "Address",index = 2)
    private String address;

    @Schema(description = "长度")
    @ExcelProperty(value = "length",index = 6)
    private Integer length;

    @Schema(description = "数据类型，bool/byte/short/int/float/string/ushort/double")
    @ExcelProperty(value = "Data Type",index = 3)
    private String dataType;

    @Schema(description = "是否数组")
    @ExcelProperty(value = "isArray",index = 5)
    private Boolean isArray;

    @Schema(description = "type是string类型时，表示string元素的byte长度，其他type类型放空")
    @ExcelProperty(value = "stringBytes",index = 4)
    private Integer stringBytes;

    @Schema(description = "是否只读")
    @ExcelProperty(value = "Client Access", index = 7, converter = ReadOnlyConverter.class)
    private Boolean readOnly;

    @Schema(description = "时间间隔，单位毫秒")
    @ExcelProperty(value = "Scan Rate",index = 8)
    private Integer intervalMs;

}
