package com.nti56.nlink.product.device.server.model.edgegateway.vo;

import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.entity.ConnectorEntity;
import com.nti56.nlink.product.device.server.model.edgegateway.EdgeGatewayChangeState;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/25 15:11<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "网关vo")
public class EdgeGatewayVO {


    private Long id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "描述")
    private String descript;

    @Schema(description = "是否访问公共MQTT接口")
    private Boolean visitPublicMqtt;

    @Schema(description = "外部mqtt主机")
    private String publicMqttIp;

    @Schema(description = "外部mqtt端口")
    private Integer publicMqttPort;

    @Schema(description = "主机")
    private String host;

    @Schema(description = "端口")
    private Integer port;

    @Schema(description = "IMEI")
    private String imei;

    @Schema(description = "流量卡号")
    private String trafficCard;

    @Schema(description = "运营商 1-移动，2-电信，3-联通")
    private Integer operators;

    @Schema(description = "标记实体")
    private List<TagRsp> tags;

    @Schema(description = "网关类型 虚拟网关/网关设备 ")
    private Integer type;

    @Schema(description = "状态 1启用 0停用 2未激活")
    private Integer status;

    @Schema(description = "同步时间")
    private LocalDateTime syncTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "设备同步状态，1-已最新，0-有更新")
    private Integer syncStatus;

    @Schema(description = "mqtt主机")
    private String mqttHost;

    @Schema(description = "mqtt端口")
    private Integer mqttPort;

    @Schema(description = "mqtt账号")
    private String mqttUsername;

    @Schema(description = "mqtt密码")
    private String mqttPassword;

    @Schema(description = "ot主机")
    private String otHost;

    @Schema(description = "ot端口")
    private Integer otPort;

    @Schema(description = "心跳uuid")
    private String heartbeatUuid;

    @Schema(description = "租户id")
    private Long tenantId;
    
    @Schema(description = "目标版本号")
    private String targetVersion;
    
    @Schema(description = "1在线 0离线")
    private Integer online;

    @Schema(description = "连接器集")
    private List<ConnectorEntity> connectorEntityList;

    @Schema(description = "连接器状态 0-停用;1-启用")
    private Integer connectorStatus;

    /**
     * 内存监控百分比
     */
    @Schema(description = "网关内存监控百分比")
    private Integer memoryMonitor;

    /**
     * 硬件空间监控百分比
     */
    @Schema(description = "网关空间监控百分比")
    private Integer spaceMonitor;


}
