package com.nti56.nlink.product.device.server.exception;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.text.MessageFormat;

/**
 * 类说明: 自定义运行时业务异常
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-27 09:41:28
 * @since JDK 1.8
 */
@Slf4j
public class BizException extends RuntimeException {

    @Getter
    private int code;
    
    public BizException() {
        super();
        this.code = ServiceCodeEnum.CODE_UNKNOWN_ERROR.getCode();
        log.error("未知异常！");
    }

    public BizException(String message) {
        super(message);
        this.code = ServiceCodeEnum.CODE_UNKNOWN_ERROR.getCode();
        log.error("业务异常：错误码：{}，错误信息：{}",code,message);
    }

    public BizException(int code,String message) {
        super(message);
        this.code = code;
        log.error("业务异常：错误码：{}，错误信息：{}",code,message);
    }

    public BizException(Result result){
        super(result.getMessage());
        this.code = result.getServiceCode();
        log.error("业务异常：错误码：{}，错误信息：{}",code,result.getMessage());
    }

    public BizException(int code,String message,Object... arguments) {
        super(MessageFormat.format(message, arguments));
        this.code = code;
        log.error("业务异常：错误码：{}，错误信息：{}",code,MessageFormat.format(message, arguments));
    }

    public BizException(ServiceCodeEnum serviceCode) {
        super(serviceCode.getMessage());
        this.code = serviceCode.getCode();
        log.error("业务异常：错误码：{}，错误信息：{}",code,serviceCode.getMessage());
    }

    @Override
    public Throwable fillInStackTrace() {
        return this;
    }
}
