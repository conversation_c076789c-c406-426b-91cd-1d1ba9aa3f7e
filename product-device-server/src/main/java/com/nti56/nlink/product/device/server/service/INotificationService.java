package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.NotificationEntity;
import com.nti56.nlink.product.device.server.model.notification.dto.NotificationDTO;

/**
 * <AUTHOR>
 * @description 针对表【notification(系统通知表)】的数据库操作Service
 * @createDate 2023-06-07 10:13:21
 */
public interface INotificationService extends IService<NotificationEntity> {

    Result<Page<NotificationEntity>> pageList(PageParam pageParam, NotificationDTO queryDTO);

    Result<Void> createNew(NotificationDTO notificationDTO);

    Result<Boolean> read(TenantIsolation tenantIsolation, Long id);

    Result listLatest4(TenantIsolation tenantIsolation);

    Result<Boolean> deleteById(Long id);

    Result<Boolean> updateNotification(TenantIsolation tenantIsolation, NotificationDTO notificationDTO, Long id);
}
