package com.nti56.nlink.product.device.server.scriptApi;

import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName EngineeringApi
 * @date 2022/4/15 15:33
 * @Version 1.0
 */
public interface EngineeringSpi {

    Thing getByName(String deviceName);

    Thing getByResourceId(String resourceId);

    Thing getByDeviceId(Long deviceId);

    List<Thing> getByTag(String key, String value);

    List<Thing> getByTagKey(String key);

    List<Thing> getByTagId(String tagId);
}
