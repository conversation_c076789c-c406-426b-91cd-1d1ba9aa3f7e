package com.nti56.nlink.product.device.server.model.edgegateway.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class HardwareInfo {
    
    @Schema(description = "当前时间")
    private LocalDateTime currentTime;
    
    @Schema(description = "系统硬件空间总大小")
    private String totalSpace;
    
    @Schema(description = "系统硬件已使用空间")
    private String usedSpace;

    @Schema(description = "系统硬件可使用空间")
    private String usableSpace;
    
    @Schema(description = "系统硬件已使用百分比")
    private String usedSpacePercent;
    
    @Schema(description = "运行总内存")
    private String totalMemory;

    @Schema(description = "已使用内存")
    private String usedMemory;
    
    @Schema(description = "可使用内存")
    private String usableMemory;
    
    @Schema(description = "已使用内存百分比")
    private String usedMemoryPercent;
    
}
