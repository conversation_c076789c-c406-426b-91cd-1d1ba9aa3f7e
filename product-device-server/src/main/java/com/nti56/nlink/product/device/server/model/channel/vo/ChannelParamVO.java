package com.nti56.nlink.product.device.server.model.channel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 13:06<br/>
 * @since JDK 1.8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "通道参数dto")
public class ChannelParamVO {


    private Long id;


    @Schema(description = "参数名称")
    private String name;


    @Schema(description = "描述")
    private String descript;


    @Schema(description = "通道参数值")
    private String value;

    @Schema(description = "是否必须")
    private Boolean necessary;

}
