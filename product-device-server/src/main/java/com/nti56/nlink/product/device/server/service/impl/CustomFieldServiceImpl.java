package com.nti56.nlink.product.device.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.domain.thing.custom.CustomField;
import com.nti56.nlink.product.device.server.entity.CustomFieldEntity;
import com.nti56.nlink.product.device.server.mapper.CustomFieldMapper;
import com.nti56.nlink.product.device.server.service.ICustomFieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;

import javax.annotation.Nullable;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 自定义字段表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-11-18 11:43:28
 * @since JDK 1.8
 */
@Service
public class CustomFieldServiceImpl  extends BaseServiceImpl<CustomFieldMapper,CustomFieldEntity>  implements ICustomFieldService {

    @Autowired
    CustomFieldMapper mapper;

    @Autowired
    CommonFetcherFactory commonFetcherFactory;

    @Override
    public Result<CustomFieldEntity> save(TenantIsolation tenant, @NotNull CustomFieldEntity entity) {
        if (mapper.insert(entity) == 1) {
            return Result.ok(entity);
        }
        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }

    @Override
    public Result<Page<CustomFieldEntity>> getPage(TenantIsolation tenant, @Nullable CustomFieldEntity entity, Page<CustomFieldEntity> page) {
        Page<CustomFieldEntity> list = mapper.selectPage(page, new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    @Override
    public Result<List<CustomFieldEntity>> list(TenantIsolation tenant, CustomFieldEntity entity) {
        List<CustomFieldEntity> list = mapper.selectList(new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    @Override
    public Result<Void> update(TenantIsolation tenant, @NotNull CustomFieldEntity entity) {
        if (mapper.updateById(entity) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    @Override
    @Transactional
    public Result<Void> deleteById(TenantIsolation tenant, @NotNull Long entityId) {
        if (mapper.deleteById(entityId) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    @Override
    public Result<CustomFieldEntity> getById(TenantIsolation tenant, @NotNull Long entityId) {
        CustomFieldEntity entity = mapper.selectById(entityId);
        return Result.ok(entity);
    }


}
