package com.nti56.nlink.product.device.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.entity.DataModelEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceEntity;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.DataModelMapper;
import com.nti56.nlink.product.device.server.mapper.DeviceServiceMapper;
import com.nti56.nlink.product.device.server.mapper.ThingServiceMapper;
import com.nti56.nlink.product.device.server.service.IDataModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据模型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 13:31:16
 * @since JDK 1.8
 */
@Service
public class DataModelServiceImpl extends BaseServiceImpl<DataModelMapper, DataModelEntity> implements IDataModelService {

    @Autowired
    DataModelMapper mapper;

    @Autowired
    ThingServiceMapper thingServiceMapper;

    @Autowired
    DeviceServiceMapper deviceServiceMapper;

    @Override
    public Result<DataModelEntity> save(TenantIsolation tenantIsolation, DataModelEntity entity) {

        Result<Void> uniqueNameResult = this.uniqueName(entity.getName(), tenantIsolation);
        if (!uniqueNameResult.getSignal()) {
            throw new BizException(uniqueNameResult.getMessage());
        }
        entity.setTenantId(tenantIsolation.getTenantId());
        if (mapper.insert(entity) == 1) {
            return Result.ok(entity);
        }
        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }

    @Override
    public Result<Page<DataModelEntity>> getPage(DataModelEntity entity, Page<DataModelEntity> page) {
        Page<DataModelEntity> list = mapper.selectPage(page, new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    @Override
    public Result<List<DataModelEntity>> list(TenantIsolation tenantIsolation) {
        List<DataModelEntity> list = mapper.listDataModel(tenantIsolation.getTenantId());
        return Result.ok(list);
    }

    @Override
    public Result<Integer> update(TenantIsolation tenantIsolation, DataModelEntity entity) {
        DataModelEntity dataModelEntity = mapper.getById(tenantIsolation.getTenantId(), entity.getId());
        if(dataModelEntity == null){
            return Result.error("找不到该数据模型");
        }

        Result<Void> uniqueNameResult = this.uniqueName(entity.getId(),entity.getName(), tenantIsolation);
        if (!uniqueNameResult.getSignal()) {
            throw new BizException(uniqueNameResult.getMessage());
        }
        entity.setTenantId(tenantIsolation.getTenantId());
        entity.setUpdateTime(LocalDateTime.now());
        int updateById = mapper.updateById(entity);
        if (updateById == 1) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    @Override
    public Result<Integer> deleteById(TenantIsolation tenantIsolation, Long entityId) {
        DataModelEntity dataModelEntity = mapper.getById(tenantIsolation.getTenantId(), entityId);
        if(dataModelEntity == null){
            return Result.error("找不到该数据模型");
        }
        List<ThingServiceEntity> thingServiceList = thingServiceMapper.listByDataModelId(tenantIsolation.getTenantId(), entityId);
        if(thingServiceList != null && thingServiceList.size() > 0){
            List<String> thingModelIds = thingServiceList.stream()
                .map(t -> t.getThingModelId().toString())
                .collect(Collectors.toList());
            return Result.error("被物模型的服务依赖，物模型id:" + String.join(",", thingModelIds));
        }
        List<DeviceServiceEntity> deviceServiceEntityList = deviceServiceMapper.listByDataModelId(tenantIsolation.getTenantId(), entityId);
        if(deviceServiceEntityList != null && deviceServiceEntityList.size() > 0){
            List<String> deviceIds = deviceServiceEntityList.stream()
                .map(t -> t.getDeviceId().toString())
                .collect(Collectors.toList());
            return Result.error("被设备模型的服务依赖，设备id:" + String.join(",", deviceIds));
        }
        if (mapper.deleteById(entityId) == 1) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    @Override
    public Result<DataModelEntity> getById(TenantIsolation tenantIsolation, Long entityId) {
        DataModelEntity entity = mapper.getById(tenantIsolation.getTenantId(), entityId);
        return Result.ok(entity);
    }

    @Override
    public Result<Page<DataModelEntity>> page(TenantIsolation tenantIsolation, String searchStr, Page<DataModelEntity> page) {

        Page<DataModelEntity> list = mapper.pageDataModel(tenantIsolation.getTenantId(), page, searchStr);
        return Result.ok(list);
    }

    private Result<Void> uniqueName(String name, TenantIsolation tenantIsolation) {
        return this.uniqueName(null, name, tenantIsolation);
    }

    /**
     * 判断名称是否唯一
     *
     * @param id
     * @param name
     * @param tenantIsolation
     * @return
     */
    private Result<Void> uniqueName(Long id, String name, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<DataModelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, DataModelEntity::getId, id)
                .eq(DataModelEntity::getName, name)
                .eq(DataModelEntity::getTenantId, tenantIsolation.getTenantId());

        if (mapper.selectCount(lqw) > 0) {
            return Result.error("已经存在该名称的数据模型,名称：" + name);
        }

        return Result.ok();
    }

}
