package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.nti56.nlink.product.device.client.model.dto.json.DeviceRuntimeMetadataField;
import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 * 类说明: 设备表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:08:00
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("device")
@Schema(description = "设备对象")
public class DeviceEntity  {

    /**
     * id
     */ 
    private Long id;
    /**
     * 设备名称
     */ 
    private String name;
    /**
     * 设备模型定义
     */
    @Schema(description = "设备模型定义")
    private ModelField model;
    /**
     * 描述
     */ 
    private String descript;
    /**
     * 运行时元数据
     */
    // private DeviceRuntimeMetadataField runtimeMetadata;
    /**
     * 网关设备id，当节点类型是网关子设备的时候，其连接的网关设备id，其他节点类型为null
     */ 
    @Schema(description = "所属网关id")
    private Long edgeGatewayId;

    /**
     * 设备状态，2-已离线， 1-已上线，0-未激活
     */
    @Schema(description = "设备状态，2-已上线， 1-已离线，0-未激活")
    private Integer status;


    @Schema(description = "设备同步状态，1-已最新，0-有更新")
    private Integer syncStatus;

    @Schema(description = "最后同步时间")
    private LocalDateTime lastSyncTime;

    @Schema(description = "激活时间")
    private LocalDateTime activationTime;

    @Schema(description = "模型最后变动时间")
    private LocalDateTime modelUpdateTime;

    @Schema(description = "设备绑定的分组")
    private String source;

    @Schema(description = "设备绑定的通道")
    private String channel;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

    /**
     * 逻辑删除，1-删除
     */
    @Schema(description = "逻辑删除，1-删除")
    @TableLogic
    private Boolean deleted;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 工程ID
     */
    @Schema(description = "工程ID")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @Schema(description = "模块ID")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @Schema(description = "空间ID")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    @Schema(description = "设备标识")
    private String resourceId;

}
