package com.nti56.nlink.product.device.server.model.custom;

import java.util.Map;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;

@Data
public class CustomParam {

    @Schema(description = "网关id")
    @NotNull(message = "网关id不能为空")
    private Long edgeGatewayId;
    
    @Schema(description = "自定义协议名")
    private String customDriverName;
    
    @Schema(description = "协议包名")
    private String messageName;

    @Schema(description = "参数")
    private Map<String, Object> var;

    @Schema(description = "十六进制序字符串，0x开头")
    private String str;

}
