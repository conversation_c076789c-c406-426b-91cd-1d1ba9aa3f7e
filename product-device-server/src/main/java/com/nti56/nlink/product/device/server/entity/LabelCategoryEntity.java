package com.nti56.nlink.product.device.server.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 类说明: 标签目录表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:08:15
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("label_category")
public class LabelCategoryEntity {

    /**
     * id
     */ 
    private Long id;
    /**
     * 目录名称
     */ 
    private String name;
    /**
     * 所属目录id，null表示是顶级目录
     */ 
    private Long parentId;
    /**
     * 创建时间
     */ 
    private LocalDateTime createTime;

}
