package com.nti56.nlink.product.device.server.model.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/22 10:05<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "修改产品dto")
public class EditProductDTO {

    @NotNull(message = "唯一标识不能为空")
    private Long id;

    @Schema(description = "产品名称")
    @NotBlank(message = "产品名称不能为空")
    @Length(max = 32,message = "产品名称不能超过32个字符")
    private String name;
    /**
     * 产品描述
     */
    @Schema(description = "产品描述")
    @Length(max = 256,message = "产品描述不能超过256个字符")
    private String descript;

    @Schema(description = "标记id列表")
    private List<Long> tagIds;

    @Schema(description = "新增物模型id")
    private List<Long> thingModelIds;

    @Schema(description = "删除物模型id")
    private List<Long> deleteThingModelIds;

    @NotNull(message = "桶id不能为空")
    private Long bucketId;
}
