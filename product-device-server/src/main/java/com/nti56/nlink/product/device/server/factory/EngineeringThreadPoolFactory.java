package com.nti56.nlink.product.device.server.factory;


import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName EngineeringThreadPoolFactory
 * @date 2022/4/20 9:59
 * @Version 1.0
 */
public class EngineeringThreadPoolFactory implements ThreadFactory {

    private AtomicInteger threadIdx = new AtomicInteger(0);

    private String threadNamePrefix;

    public EngineeringThreadPoolFactory(String Prefix) {
        threadNamePrefix = Prefix;
    }

    @Override
    public Thread newThread(Runnable r) {
        Thread thread = new Thread(r);
        thread.setName(threadNamePrefix + "-ThingServiceJob-" + threadIdx.getAndIncrement());
        return thread;
    }
}
