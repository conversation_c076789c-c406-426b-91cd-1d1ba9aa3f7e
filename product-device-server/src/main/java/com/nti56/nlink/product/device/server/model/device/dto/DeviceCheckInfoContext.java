package com.nti56.nlink.product.device.server.model.device.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceModelInheritEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceEntity;
import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import com.nti56.nlink.product.device.server.entity.LabelBindRelationEntity;
import com.nti56.nlink.product.device.server.entity.LabelEntity;
import com.nti56.nlink.product.device.server.entity.LabelGroupEntity;
import com.nti56.nlink.product.device.server.entity.ResourceRelationEntity;
import com.nti56.nlink.product.device.server.entity.SubscriptionEntity;
import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
import com.nti56.nlink.product.device.server.entity.ThingModelInheritEntity;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import com.nti56.nlink.product.device.server.mapper.ChannelMapper;
import com.nti56.nlink.product.device.server.mapper.DeviceModelInheritMapper;
import com.nti56.nlink.product.device.server.mapper.DeviceServiceMapper;
import com.nti56.nlink.product.device.server.mapper.EdgeGatewayMapper;
import com.nti56.nlink.product.device.server.mapper.LabelBindRelationMapper;
import com.nti56.nlink.product.device.server.mapper.LabelGroupMapper;
import com.nti56.nlink.product.device.server.mapper.LabelMapper;
import com.nti56.nlink.product.device.server.mapper.ResourceRelationMapper;
import com.nti56.nlink.product.device.server.mapper.SubscriptionMapper;
import com.nti56.nlink.product.device.server.mapper.ThingModelInheritMapper;
import com.nti56.nlink.product.device.server.mapper.ThingModelMapper;
import com.nti56.nlink.product.device.server.mapper.ThingServiceMapper;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class DeviceCheckInfoContext {

    private EdgeGatewayMapper edgeGatewayMapper;

    private ResourceRelationMapper resourceRelationMapper;

    private LabelBindRelationMapper labelBindRelationMapper;

    private LabelMapper labelMapper;

    private LabelGroupMapper labelGroupMapper;

    private ChannelMapper channelMapper;

    private ThingModelMapper thingModelMapper;

    private ThingModelInheritMapper thingModelInheritMapper;

    public ThingServiceMapper thingServiceMapper;

    public SubscriptionMapper subscriptionMapper;
    
    private DeviceModelInheritMapper deviceModelInheritMapper;

    private DeviceServiceMapper deviceServiceMapper;

    //key:edgeGateWayId,value:self
    private Map<Long,EdgeGatewayEntity> edgeGatewayMap=Maps.newHashMap();
    //key:deviceId
    private Map<Long,List<ResourceRelationEntity>>  resourceRelationMap=Maps.newHashMap();
    //key:deviceId
    private Map<Long,List<LabelBindRelationEntity>> labelBindRelationMap=Maps.newHashMap();
    //key:labelId
    private Map<Long,LabelEntity> labelMap=Maps.newHashMap();
    //row:labelGroupId,column:labelName,,value:self
    private Table<Long,String,LabelEntity> labelGroupTable=HashBasedTable.create();
    //key:labelGroupId
    private Map<Long,LabelGroupEntity> labelGroupMap=Maps.newHashMap();
    //row:channelId,column:labelGroupName,value:self
    private Table<Long,String,LabelGroupEntity> channelLabelGroupTable=HashBasedTable.create();
    //row:edgeGateWayId,column:channelName,value:self
    private Table<Long,String,ChannelEntity> edgeGatewayChannelTable=HashBasedTable.create();
    //key:channelId,value:self
    private Map<Long,ChannelEntity> channelMap=Maps.newHashMap();
    //key:deviceId
    private Map<Long,List<ThingModelEntity>> deviceInheritThingModelMap=Maps.newHashMap();
    //key:thingModelId
    private Map<Long,List<ThingModelEntity>> thingModelInheritThingModelMap=Maps.newHashMap();
    //key:thingModelId
    private Map<Long,List<ThingServiceEntity>> modelThingServiceEntityMap=Maps.newHashMap();
    //key:thingModelId
    private Map<Long,List<SubscriptionEntity>> modelSubscriptionEntityMap=Maps.newHashMap();
    //key:deivceId
    private Map<Long,List<DeviceServiceEntity>> deviceServiceEntityMap=Maps.newHashMap();
    //key:deivceId
    private Map<Long,List<SubscriptionEntity>> deviceSubscriptionEntityMap=Maps.newHashMap();



    public DeviceCheckInfoContext(List<DeviceEntity> deviceList){
         if(CollectionUtil.isEmpty(deviceList)){
            return ;
        }
        edgeGatewayMapper=SpringUtil.getBean(EdgeGatewayMapper.class);
        resourceRelationMapper=SpringUtil.getBean(ResourceRelationMapper.class);
        labelBindRelationMapper=SpringUtil.getBean(LabelBindRelationMapper.class);
        labelMapper=SpringUtil.getBean(LabelMapper.class);
        labelGroupMapper=SpringUtil.getBean(LabelGroupMapper.class);
        channelMapper=SpringUtil.getBean(ChannelMapper.class);
        thingModelMapper=SpringUtil.getBean(ThingModelMapper.class);
        deviceModelInheritMapper=SpringUtil.getBean(DeviceModelInheritMapper.class);
        thingModelInheritMapper=SpringUtil.getBean(ThingModelInheritMapper.class);
        thingServiceMapper=SpringUtil.getBean(ThingServiceMapper.class);
        subscriptionMapper=SpringUtil.getBean(SubscriptionMapper.class);
        deviceServiceMapper=SpringUtil.getBean(DeviceServiceMapper.class);

        Set<Long> edgeGatewayIds=deviceList.stream().map(DeviceEntity::getEdgeGatewayId).collect(Collectors.toSet());
        initEdgeGatewayMap(edgeGatewayIds);
        
        Set<Long> deviceIds=deviceList.stream().map(DeviceEntity::getId).collect(Collectors.toSet());
        initDeviceRelationMap(deviceIds);

        initDeviceInheritThingModelMap(deviceIds);
        initLabelBindRelationMap(deviceIds);
    }

   

    private void initEdgeGatewayMap(Set<Long> edgeGatewayIds){
        if(CollectionUtil.isEmpty(edgeGatewayIds)){
            return ;
        }
        List<EdgeGatewayEntity> result=new LambdaQueryChainWrapper<EdgeGatewayEntity>(edgeGatewayMapper)
        .select(EdgeGatewayEntity::getId,EdgeGatewayEntity::getName)
        .in(EdgeGatewayEntity::getId, edgeGatewayIds)
        .list();
        result.forEach(gw->edgeGatewayMap.put(gw.getId(), gw));
    }

    private void initDeviceRelationMap(Set<Long> deviceIds){
         //根据设备id批量查询数据并缓存到deviceRelationMap
        if(CollectionUtil.isEmpty(deviceIds)){
            return ;
        }
        List<ResourceRelationEntity> result=new LambdaQueryChainWrapper<ResourceRelationEntity>(resourceRelationMapper)
        .select(ResourceRelationEntity::getDeviceId,ResourceRelationEntity::getId)
        .in(ResourceRelationEntity::getDeviceId, deviceIds)
        .list();
        resourceRelationMap = result.stream().collect(Collectors.groupingBy(ResourceRelationEntity::getDeviceId));
    }
    private void  initDeviceInheritThingModelMap(Set<Long> deviceIds){
        if(CollectionUtil.isEmpty(deviceIds)){
            return ;
        }
        List<DeviceModelInheritEntity> result=new LambdaQueryChainWrapper<DeviceModelInheritEntity>(deviceModelInheritMapper)
        .select(DeviceModelInheritEntity::getInheritThingModelId,DeviceModelInheritEntity::getDeviceId)
        .in(DeviceModelInheritEntity::getDeviceId, deviceIds)
        .list();
        Map<Long,List<DeviceModelInheritEntity>> deviceInheritThingIdMap=result.stream().collect(Collectors.groupingBy(DeviceModelInheritEntity::getDeviceId));
        Set<Long> thingModelIds=result.stream()
            .map(DeviceModelInheritEntity::getInheritThingModelId)
            .collect(Collectors.toSet());
        if(thingModelIds.isEmpty()) {
            return;
        }
        List<ThingModelEntity> thingModelList=new LambdaQueryChainWrapper<ThingModelEntity>(thingModelMapper)
        .in(ThingModelEntity::getId, thingModelIds)
        .list();
        Map<Long,ThingModelEntity> thingModelEntityMap = thingModelList.stream().collect(Collectors.toMap(ThingModelEntity::getId, thingModelEntity -> thingModelEntity));

        deviceInheritThingIdMap.keySet().forEach( devcieId->{ 
           List<DeviceModelInheritEntity> inheritList= deviceInheritThingIdMap.getOrDefault(devcieId,new ArrayList());
           inheritList.forEach(inherit->{
              ThingModelEntity thingModelEntity= thingModelEntityMap.get(inherit.getInheritThingModelId());
              if(thingModelEntity!=null){
                List<ThingModelEntity> inheritThingList=deviceInheritThingModelMap.get(devcieId);
                if(inheritThingList==null){ 
                    inheritThingList=new ArrayList<>();
                    deviceInheritThingModelMap.put(devcieId, inheritThingList);
                }
                inheritThingList.add(thingModelEntity);
              }
           });
        });
        //最多递归10次
        initThingInheritThingModelMap(thingModelIds,9);
        initThingServiceEntityMap(thingModelInheritThingModelMap.keySet());
        initsubscriptionEntityMap(deviceInheritThingModelMap.keySet(),thingModelInheritThingModelMap.keySet());
        initDeviceServiceEntityMap(deviceInheritThingModelMap.keySet());
    }

    private void initDeviceServiceEntityMap(Set<Long> deviceIds){
        if(CollectionUtil.isEmpty(deviceIds)){
            return ;
        }
        List<DeviceServiceEntity> deviceServcieList=new LambdaQueryChainWrapper<DeviceServiceEntity>(deviceServiceMapper)
        .in(DeviceServiceEntity::getDeviceId, deviceIds)
        .list();
        deviceServiceEntityMap=deviceServcieList.stream().collect(Collectors.groupingBy(DeviceServiceEntity::getDeviceId));
    }

    private void initThingServiceEntityMap(Set<Long> thingModelIds) { 
         if(CollectionUtil.isEmpty(thingModelIds)){
            return ;
        }
        List<ThingServiceEntity> thingServcieList=new LambdaQueryChainWrapper<ThingServiceEntity>(thingServiceMapper)
        .in(ThingServiceEntity::getThingModelId, thingModelIds)
        .list();
        modelThingServiceEntityMap=thingServcieList.stream().collect(Collectors.groupingBy(ThingServiceEntity::getThingModelId));
    }


    private void initsubscriptionEntityMap(Set<Long> devcieIds,Set<Long> thingModelIds) { 
         if(CollectionUtil.isEmpty(thingModelIds)){
            return ;
        }
        List<SubscriptionEntity> subscriptList=new LambdaQueryChainWrapper<SubscriptionEntity>(subscriptionMapper)
        .in(SubscriptionEntity::getDirectlyModelId, thingModelIds)
        // .eq(SubscriptionEntity::getModelType, ModelTypeEnum.THING_MODEL.getValue())
        .list();

        modelSubscriptionEntityMap=subscriptList.stream().filter(x->ModelTypeEnum.THING_MODEL.getValue().equals(x.getModelType())).collect(Collectors.groupingBy(SubscriptionEntity::getDirectlyModelId));
        deviceSubscriptionEntityMap=subscriptList.stream().filter(x->ModelTypeEnum.DEVICE_MODEL.getValue().equals(x.getModelType())).collect(Collectors.groupingBy(SubscriptionEntity::getDirectlyModelId));
    }


    //递归把所有集成的物模型全部查出来
    private void initThingInheritThingModelMap(Set<Long> thingModelIds,int level){
        if(level<=0){
            log.warn("initThingInheritThingModelMap level<=0");
            return;
        }
        if(CollectionUtil.isEmpty(thingModelIds)){
            return ;
        }
        //根据设备id批量查询数据并缓存到deviceInheritThingModelMap
        List<ThingModelInheritEntity> thingInheritThingModelEntities=new LambdaQueryChainWrapper<ThingModelInheritEntity>(thingModelInheritMapper)
        .select(ThingModelInheritEntity::getThingModelId,ThingModelInheritEntity::getInheritThingModelId)
        .in(ThingModelInheritEntity::getThingModelId, thingModelIds)
        .list();
        Map<Long,List<ThingModelInheritEntity>> thingInheritThingIdMap=thingInheritThingModelEntities.stream().collect(Collectors.groupingBy(ThingModelInheritEntity::getThingModelId));
        Set<Long> modelIds=thingInheritThingModelEntities.stream()
            .map(ThingModelInheritEntity::getInheritThingModelId)
            .collect(Collectors.toSet());
        if(modelIds.isEmpty()) {
            return;
        }
        List<ThingModelEntity> thingModelList=new LambdaQueryChainWrapper<ThingModelEntity>(thingModelMapper)
        .in(ThingModelEntity::getId, modelIds)
        .list();
        Map<Long,ThingModelEntity> thingModelEntityMap = thingModelList.stream().collect(Collectors.toMap(ThingModelEntity::getId, thingModelEntity -> thingModelEntity));
        
        thingInheritThingIdMap.keySet().forEach( thingModelId->{ 
           List<ThingModelInheritEntity> inheritList= thingInheritThingIdMap.getOrDefault(thingModelId,new ArrayList());
           inheritList.forEach(inherit->{
              ThingModelEntity thingModelEntity= thingModelEntityMap.get(inherit.getInheritThingModelId());
              if(thingModelEntity!=null){
                List<ThingModelEntity> inheritThingList=thingModelInheritThingModelMap.get(thingModelId);
                if(inheritThingList==null){ 
                    inheritThingList=new ArrayList<>();
                    thingModelInheritThingModelMap.put(thingModelId, inheritThingList);
                }
                inheritThingList.add(thingModelEntity);
              }
           });
        });
        //已经加载过的物模型无需再加载
        modelIds.removeAll(thingModelIds);
        initThingInheritThingModelMap(modelIds,level--);
    }

    private void initLabelBindRelationMap(Set<Long> deviceIds){
        //根据设备id批量查询数据并缓存到labelBindRelationMap
        if(CollectionUtil.isEmpty(deviceIds)){
            return ;
        }
        List<LabelBindRelationEntity> result =new LambdaQueryChainWrapper<LabelBindRelationEntity>(labelBindRelationMapper)
                .select(LabelBindRelationEntity::getDeviceId,LabelBindRelationEntity::getId,LabelBindRelationEntity::getDirectlyModelId,LabelBindRelationEntity::getPropertyName,LabelBindRelationEntity::getChannelName,LabelBindRelationEntity::getLabelGroupName,LabelBindRelationEntity::getEdgeGatewayId,LabelBindRelationEntity::getLabelName,LabelBindRelationEntity::getLabelId)
                .in(LabelBindRelationEntity::getDeviceId, deviceIds).list();

        labelBindRelationMap=result.stream().collect(Collectors.groupingBy(LabelBindRelationEntity::getDeviceId));

        Set<Long> labelIds=Sets.newHashSet();
        
        Set<Long> edgeGatewayIds=Sets.newHashSet();
        Set<String> channelNames=Sets.newHashSet();
        Set<String> labelGroupNames=Sets.newHashSet();

        result.forEach(labelBindRelationEntity->{
            labelIds.add(labelBindRelationEntity.getLabelId());
            edgeGatewayIds.add(labelBindRelationEntity.getEdgeGatewayId());
            channelNames.add(labelBindRelationEntity.getChannelName());
            labelGroupNames.add(labelBindRelationEntity.getLabelGroupName());
        });

        initLabelMap(labelIds);
        Set<Long> channelIds= initEdgeGatewayChannelTable(edgeGatewayIds,channelNames);
        initChannelLabelGroupTable(channelIds,labelGroupNames);
    }

    private void initLabelMap(Set<Long> labelIds){
        if(CollectionUtil.isEmpty(labelIds)){
            return ;
        }
        //todo 根据标签id批量查询数据并缓存到labelMap,按labelGroupId和标签名称进行分组到labelGroupTable
        List<LabelEntity> result =new LambdaQueryChainWrapper<LabelEntity>(labelMapper)
                .select(LabelEntity::getLabelGroupId,LabelEntity::getId,LabelEntity::getName)
                .in(LabelEntity::getId, labelIds).list();
        labelMap=result.stream().collect(Collectors.toMap(LabelEntity::getId, Function.identity()));
        result.forEach(label->{
            labelGroupTable.put(label.getLabelGroupId(), label.getName(), label);
        });
    }

    private Set<Long> initEdgeGatewayChannelTable(Set<Long> edgeGatewayIds,Set<String> channelNames){
        Set<Long> channelIds = Sets.newHashSet();
        LambdaQueryChainWrapper<ChannelEntity> lqcw = new LambdaQueryChainWrapper<ChannelEntity>(channelMapper)
                .select(ChannelEntity::getId,ChannelEntity::getName,ChannelEntity::getEdgeGatewayId);
        if(CollectionUtil.isEmpty(edgeGatewayIds)){
            return channelIds;
        }else if(CollectionUtil.isEmpty(channelNames)){
            lqcw.in(ChannelEntity::getEdgeGatewayId, edgeGatewayIds);
        }
        if(!CollectionUtil.isEmpty(channelNames)){
            lqcw.in(ChannelEntity::getName, channelNames);
        }
        List<ChannelEntity> result = lqcw.list();
        result.forEach(channel->{
            channelIds.add(channel.getId());
            edgeGatewayChannelTable.put(channel.getEdgeGatewayId(), channel.getName(), channel);
            channelMap.put(channel.getId(), channel);
        });
        return channelIds;
    }

    private void initChannelLabelGroupTable(Set<Long> channelIds, Set<String> labelGroupNames) {


        LambdaQueryChainWrapper<LabelGroupEntity> lqcw = new LambdaQueryChainWrapper<LabelGroupEntity>(labelGroupMapper)
            .select(LabelGroupEntity::getId, LabelGroupEntity::getName, LabelGroupEntity::getChannelId);


        if(CollectionUtil.isEmpty(channelIds)){
           return;
        }else{
            lqcw.in(LabelGroupEntity::getChannelId, channelIds);    
        }
        if(!CollectionUtil.isEmpty(labelGroupNames)){
            lqcw.in(LabelGroupEntity::getName, labelGroupNames);
        }

        List<LabelGroupEntity> result =lqcw.list();
        result.forEach(labelGroup->{
            channelLabelGroupTable.put(labelGroup.getChannelId(),labelGroup.getName(),labelGroup);
            labelGroupMap.put(labelGroup.getId(), labelGroup);
        });       
    }

    public EdgeGatewayEntity getEedgeGatewayMapById(Long edgeGatewayId) {
        return edgeGatewayMap.get(edgeGatewayId);
    }

    public List<ResourceRelationEntity> getResourceRelationMapByDeviceId(Long deviceId) {
        return resourceRelationMap.get(deviceId);
    }

    public List<LabelBindRelationEntity> getLabelBindRelationEntitiesByDeviceId(Long deviceId) {
        return labelBindRelationMap.get(deviceId);
    }

    public LabelEntity getLabelEntityByLabelId(Long labelId) {
        return labelMap.get(labelId);
    }

    public ChannelEntity getChannelEntityByGwId(Long edgeGatewayId,String channelName) {
        return edgeGatewayChannelTable.get(edgeGatewayId,channelName);
    }

    public LabelGroupEntity getLabelGroupEntityByChannelId(Long channelId,String labelGroupName) {
        return channelLabelGroupTable.get(channelId,labelGroupName);
    }
    public LabelGroupEntity getLabelGroupEntityById(Long labelGroupId) {
        return labelGroupMap.get(labelGroupId);
    }

    public LabelEntity getLabelEntityByLabelGroupId(Long labelGroupId,String labelName) {
        return labelGroupTable.get(labelGroupId,labelName);
    }
    public LabelEntity getLabelEntityById(Long labelId) {
        return labelMap.get(labelId);
    }

    public List<ThingModelEntity> getInheritThingModelByDeviceId(Long deviceId){
        return deviceInheritThingModelMap.getOrDefault(deviceId,new ArrayList<>());
    }
    
    public List<ThingModelEntity> getInheritThingModelByThingModelId(Long modelId){
        return thingModelInheritThingModelMap.getOrDefault(modelId,new ArrayList<>());
    }

    public ChannelEntity getChannelEntityById(Long channelId){
        return channelMap.get(channelId);
    }

    public List<LabelEntity> getLabelByLabelGroupId(Long groupId) {
        return new ArrayList<LabelEntity>(labelGroupTable.row(groupId).values());
    }

    public List<ThingServiceEntity> getThingServiceByThingId(Long thingId){
        return modelThingServiceEntityMap.getOrDefault(thingId,new ArrayList<>());
    }

    public List<SubscriptionEntity> getSubscriptionByThingId(Long thingId){
        return modelSubscriptionEntityMap.getOrDefault(thingId,new ArrayList<>());
    }

     public List<DeviceServiceEntity> getDeviceServiceByThingId(Long deviceId){
        return deviceServiceEntityMap.getOrDefault(deviceId,new ArrayList<>());
    }

    public List<SubscriptionEntity> getSubscriptionByDeviceId(Long deviceId){
        return deviceSubscriptionEntityMap.getOrDefault(deviceId,new ArrayList<>());
    }

}
