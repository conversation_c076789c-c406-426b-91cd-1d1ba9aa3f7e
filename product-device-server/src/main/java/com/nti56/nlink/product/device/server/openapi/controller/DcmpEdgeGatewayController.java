package com.nti56.nlink.product.device.server.openapi.controller;

import cn.hutool.core.util.ObjectUtil;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.it.ITResult;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.model.channel.vo.ChannelStatusVO;
import com.nti56.nlink.product.device.server.model.device.vo.DcmpEdgeGatewayVO;
import com.nti56.nlink.product.device.server.model.edgegateway.dto.EditEdgeGatewayDTO;
import com.nti56.nlink.product.device.server.model.edgegateway.Traffic5gInfo;
import com.nti56.nlink.product.device.server.openapi.convertor.ITResultConvertor;
import com.nti56.nlink.product.device.server.openapi.domain.request.GetEdgeGatewayRequest;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListDcmpEdgeGatewayRequest;
import com.nti56.nlink.product.device.server.service.IChannelService;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 类说明: 设备controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:04
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/it/dcmp/edge-gateway")
@Tag(name = "云管网关模块")
public class DcmpEdgeGatewayController {

    @Autowired
    IEdgeGatewayService edgeGatewayService;

    @Autowired
    private IChannelService channelService;


    @PostMapping(path = "get/one")
    @Operation(summary = "通过id获取网关信息",  parameters = {
            @Parameter(name = "edgeGatewayId", description = "网关ID"),
            @Parameter(name = "imei", description = "IMEI")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DcmpEdgeGatewayVO.class)
                    )})
    })
    public ITResult<DcmpEdgeGatewayVO> getEdgeGatewayForDcmp(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
            @RequestBody GetEdgeGatewayRequest request) {
        if (ObjectUtil.isEmpty(request.getEdgeGatewayId()) && StringUtils.isEmpty(request.getImei())){
            throw new BizException("网关ID、IMEI不能都为空");
        }
        return ITResultConvertor.convert(R.result(edgeGatewayService.getEdgeGatewayForDcmp(tenantIsolation,request)));
    }


    @PostMapping(path = "list")
    @Operation(summary = "获取网关列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DcmpEdgeGatewayVO.class)
                    )})
    })
    public ITResult<List<DcmpEdgeGatewayVO>> listEdgeGatewayForDcmp(@RequestHeader("ot_headers") TenantIsolation tenantIsolation
            , @RequestBody ListDcmpEdgeGatewayRequest request) {

        return ITResultConvertor.convert(R.result(edgeGatewayService.listEdgeGatewayForDcmp(tenantIsolation,request)));
    }

    @PutMapping("{id}/sync")
    @Operation(summary = "根据id进行网关同步", description = "根据id进行网关同步")
    public ITResult<Void> edgeGatewaySync(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        if (edgeGatewayService.edgeGatewayOnline(tenantIsolation.getTenantId(), id).getResult()) {
            Result<Void> result = edgeGatewayService.edgeGatewaySyncById(id,tenantIsolation);
            return ITResultConvertor.convert(R.result(result));
        }else {
            return ITResultConvertor.convert(R.error("网关离线，无法同步！"));
        }
    }

    @PostMapping(path = "status/{edgeGatewayId}")
    @Operation(summary = "获取网关状态")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = Boolean.class)
                    )})
    })
    public ITResult<Boolean> edgeGatewayOnline(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @Parameter(description = "网关ID") @PathVariable Long edgeGatewayId) {
        return ITResultConvertor.convert(R.result(edgeGatewayService.edgeGatewayOnline(tenantIsolation.getTenantId(),edgeGatewayId)));
    }

    @PostMapping(path = "channel/status/{edgeGatewayId}")
    @Operation(summary = "获取通道在线状态")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = List.class)
                    )})
    })
    public ITResult<List<ChannelStatusVO>> channelStatus(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @Parameter(description = "网关ID") @PathVariable Long edgeGatewayId) {
        return ITResultConvertor.convert(R.result(channelService.channelStatus(edgeGatewayId,tenantIsolation)));
    }

    @PostMapping("{edgeGatewayId}")
    @Operation(summary = "更新网关", description = "更新网关")
    public ITResult<Void> updateEdgeGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody EditEdgeGatewayDTO dto){
        return ITResultConvertor.convert(R.result( edgeGatewayService.updateEdgeGatewayInfo(dto,tenantIsolation)));
    }

    @GetMapping(path = "get/traffic5g/{edgeGatewayId}")
    @Operation(summary = "通过网关id查看5G流量",  parameters = {
            @Parameter(name = "edgeGatewayId", description = "网关ID")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = Traffic5gInfo.class)
                    )})
    })
    public ITResult<Traffic5gInfo> getTraffic5g(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @Parameter(description = "网关ID") @PathVariable Long edgeGatewayId) {
        Result<Traffic5gInfo> result = edgeGatewayService.getTraffic5g(tenantIsolation, edgeGatewayId);
        return ITResultConvertor.convert(R.result(result));
    }
}
