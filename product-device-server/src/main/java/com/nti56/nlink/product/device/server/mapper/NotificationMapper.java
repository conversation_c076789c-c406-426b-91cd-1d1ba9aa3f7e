package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nti56.nlink.product.device.server.entity.NotificationEntity;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【notification(系统通知表)】的数据库操作Mapper
* @createDate 2023-06-07 10:13:21
* @Entity generator.domain.Notification
*/
public interface NotificationMapper extends BaseMapper<NotificationEntity> {


    void updateSelf(@Param("byId") NotificationEntity byId);
}
