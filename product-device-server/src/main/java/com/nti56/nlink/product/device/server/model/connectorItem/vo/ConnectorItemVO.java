package com.nti56.nlink.product.device.server.model.connectorItem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 类说明: <br/>
 * <AUTHOR> <br/>
 * @version 1.0
 * @since JDK 1.8
 */
@Data
@Schema(description = "连接器VO")
public class ConnectorItemVO {
    
    /**
     * id
     */
    private Long id;
    
    /**
     * 连接器名称
     */
    @Schema(description = "连接器名称")
    private String name;
    
    /**
     * topic
     */
    @Schema(description = "topic")
    private String topic;
    
    /**
     * 连接器描述
     */
    @Schema(description = "连接器描述")
    private String descript;
    
    /**
     * 处理代码
     */
    @Schema(description = "处理代码")
    private String processCode;
    
    /**
     * 连接器id
     */
    @Schema(description = "连接器id")
    private Long connectorId;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
