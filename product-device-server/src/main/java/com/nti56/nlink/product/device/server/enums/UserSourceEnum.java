package com.nti56.nlink.product.device.server.enums;

import lombok.Getter;

/**
 * 类说明: 状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:33:25
 * @since JDK 1.8
 */
public enum UserSourceEnum {
    IT(0, "IT"),
    OT(1, "OT"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String desc;

    UserSourceEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }


}
