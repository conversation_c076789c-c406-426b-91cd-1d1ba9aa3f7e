package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.DeviceModelInheritEntity;
import com.nti56.nlink.product.device.server.model.thingModel.vo.ThingModelVO;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * 类说明: 设备继承关系mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:23
 * @since JDK 1.8
 */
public interface DeviceModelInheritMapper extends CommonMapper<DeviceModelInheritEntity> {

    @Select({
            "SELECT i.inherit_thing_model_id id,t.name name",
            "FROM device_model_inherit i LEFT JOIN thing_model t ON i.inherit_thing_model_id = t.id",
            "WHERE i.device_id = #{deviceId} AND i.DELETED = 0"
    })
    List<ThingModelVO> getInheritModelsByDeviceId(Long deviceId);

    List<DeviceModelInheritEntity> listInheritThingModelsByIds(@Param("tenantId") Long tenantId, @Param("deviceIds") List<Long> deviceIds);
}
