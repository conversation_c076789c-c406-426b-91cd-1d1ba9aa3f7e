//package com.nti56.nlink.product.device.server.mapper;
//
//import com.nti56.nlink.product.device.server.model.neo4j.MovieDTO;
//import org.springframework.data.neo4j.repository.Neo4jRepository;
//
///**
// * 类说明：
// *
// * @ClassName MovieRepository
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/11/16 17:49
// * @Version 1.0
// */
//
//public interface MovieRepository extends Neo4jRepository<MovieDTO,String> {
//
//}
