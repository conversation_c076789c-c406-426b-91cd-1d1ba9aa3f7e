package com.nti56.nlink.product.device.server.model.label.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
public class ImportLabelDTO {

    @Schema(description = "选择导入文件的标签列表")
    private List<LabelDTO> labelList;

    @Schema(description = "选择导入的标签源文件")
    private MultipartFile file;

}
