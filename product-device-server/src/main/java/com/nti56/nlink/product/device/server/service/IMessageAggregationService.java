package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.MessageAggregationEntity;

/**
* <AUTHOR>
* @description 针对表【message_aggregation】的数据库操作Service
* @createDate 2023-08-11 09:34:16
*/
public interface IMessageAggregationService extends IService<MessageAggregationEntity> {

    Result pageMessage(PageParam pageParam, TenantIsolation tenantIsolation, Long id);
}
