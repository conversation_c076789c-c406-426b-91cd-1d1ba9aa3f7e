//package com.nti56.nlink.product.device.server.model.neo4j;
//
//import lombok.Data;
//import org.neo4j.ogm.annotation.*;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 类说明：
// *
// * @ClassName MovieDTO
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/11/17 9:03
// * @Version 1.0
// */
//@NodeEntity("Movie")
//@Data
//public class MovieDTO {
//
//
//    @Id
//    private String title;
//
//    @Property
//    private String tagline;
//
//    @Property
//    private Integer released;
//
//
//   @Relationship(type = "DIRECTED", direction = Relationship.INCOMING)
//   private List<PersonDTO> directors = new ArrayList<>();
//
//   @Relationship(type = "ACTED_IN", direction = Relationship.INCOMING)
//   private List<PersonDTO> actIn = new ArrayList<>();
//}
