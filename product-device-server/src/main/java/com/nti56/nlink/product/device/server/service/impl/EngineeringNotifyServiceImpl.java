package com.nti56.nlink.product.device.server.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.NotifyChannelDTO;
import com.nti56.nlink.product.device.client.model.dto.TemplateDTO;
import com.nti56.nlink.product.device.client.model.dto.engineering.NotifyServiceDataDTO;
import com.nti56.nlink.product.device.server.entity.NotifyChannelEntity;
import com.nti56.nlink.product.device.server.entity.TemplateEntity;
import com.nti56.nlink.product.device.server.mapper.TemplateMapper;
import com.nti56.nlink.product.device.server.service.IEngineeringNotifyService;
import com.nti56.nlink.product.device.server.service.INotifyChannelService;
import com.nti56.nlink.product.device.server.service.ITemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/4 13:43<br/>
 * @since JDK 1.8
 */
@Service
public class EngineeringNotifyServiceImpl implements IEngineeringNotifyService {

    @Autowired
    private INotifyChannelService channelService;

    @Autowired
    private ITemplateService templateService;

    @Autowired
    private TemplateMapper templateMapper;

    @Override
    public Result<Void> deleteNotifyDataByTenantId(Long tenantId) {
        templateMapper.deleteAllByTenantId(tenantId);
        return Result.ok();
    }

    @Override
    public Result<NotifyServiceDataDTO> getNotifyDataByTenantId(Long tenantId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("tenant_id", tenantId);
        map.put("deleted", 0);
        return Result.ok(NotifyServiceDataDTO.builder()
                .channelList(BeanUtilsIntensifier.copyBeanList(channelService.listByMap(map), NotifyChannelDTO.class))
                .templateList(BeanUtilsIntensifier.copyBeanList(templateService.listByMap(map), TemplateDTO.class))
                .build());
    }

    @Override
    public Result<Void> createNotifyData(NotifyServiceDataDTO dto) {
        if (CollectionUtils.isNotEmpty(dto.getChannelList())) {
            channelService.saveBatch(BeanUtilsIntensifier.copyBeanList(dto.getChannelList(), NotifyChannelEntity.class));
        }
        if (CollectionUtils.isNotEmpty(dto.getTemplateList())) {
            templateService.saveBatch(BeanUtilsIntensifier.copyBeanList(dto.getTemplateList(), TemplateEntity.class));
        }
        return Result.ok();
    }
}
