package com.nti56.nlink.product.device.server.domain.thing.topic;

import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;
import lombok.Builder;
import lombok.Data;

/**
 * 类说明: 网关硬件监控topic
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-10-12 19:33:52
 * @since JDK 1.8
 */
public class GwHardwareMonitorTopic {
    
    @Data
    @Builder
    public static class TopicInfo{
        private Long tenantId;
        private Long edgeGatewayId;
    }

    public static TopicInfo parseTopic(String topic){
        String[] split = topic.split("/");
        String tenantId = split[3];
        String edgeGatewayId = split[4];

        return TopicInfo.builder()
            .tenantId(Long.parseLong(tenantId))
            .edgeGatewayId(Long.parseLong(edgeGatewayId))
            .build();
    }

    public static String createSubscribeTopic(String group){
        return "$share/" + group + "/" + MqttTopicEnum.GW_HARDWARE_MONITOR.getPrefix()
                + "+/+";
    }

    public static String createHardwareMonitorTopic(Long tenantId, Long edgeGatewayId) {
        return MqttTopicEnum.GW_HARDWARE_MONITOR.getPrefix()
                + tenantId + "/" + edgeGatewayId;
    }


}
