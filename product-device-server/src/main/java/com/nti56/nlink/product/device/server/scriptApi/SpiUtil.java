package com.nti56.nlink.product.device.server.scriptApi;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.PropertyMetadataItem;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.scriptApi.Service;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceDataResource;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewaySpiProxy;
import com.nti56.nlink.product.device.server.model.ConnectResult;
import com.nti56.nlink.product.device.server.serviceEngine.BaseService;
import com.nti56.nlink.product.device.server.serviceEngine.ServiceFactory;
import com.nti56.nlink.product.device.server.util.ApplicationContextUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.vertx.core.Context;
import jdk.nashorn.api.scripting.ScriptObjectMirror;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName SpiUtil
 * @date 2022/4/21 15:06
 * @Version 1.0
 */
@Slf4j
public class SpiUtil {

    public static void dealLog(DeviceDataResource deviceDataResource, DeviceServiceLogEntity logEntity, String msg, boolean isSuccess){
        logEntity.setOutputData(msg);
        logEntity.setCreateTime(LocalDateTime.now());
        logEntity.setCallSuccess(isSuccess);
        deviceDataResource.addDeviceServiceCallLog(logEntity);
    }

    public static List<Thing> devices2Things(List<Device> devices) {
        List<Thing> things = new ArrayList<>();
        Optional.ofNullable(devices).orElse(new ArrayList<>()).forEach(device -> {
            things.add(device2Thing(device, null));
        });
        return things;
    }

    public static Thing device2Thing(Device device, String serviceName) {
        Thing thing = new Thing(device, serviceName);
        return thing;
    }

    public static Map<String, BaseService> services2BaseServices(Device device) {
        Map<String, Service> services = device.getServices();
        Map<String, BaseService> serviceList = new HashMap<>();
        Optional.ofNullable(services).orElse(new HashMap<>()).forEach((k, v) -> {
            Result<BaseService> result = service2BaseService(v, device.getId());
            if (result.getSignal()) {
                serviceList.put(k, result.getResult());
            }
        });
        return serviceList;
    }

    public static Result<BaseService> service2BaseService(Service service, Long deviceId) {
        return service2BaseService(service,deviceId, DeviceServiceLogEntity.builder().build());
    }

    public static Result<BaseService> service2BaseService(Service service, Long deviceId, DeviceServiceLogEntity logEntity) {
        BaseService baseService = ApplicationContextUtil.getBean("serviceFactory", ServiceFactory.class).getService(service, logEntity);
        if (!Optional.ofNullable(baseService).isPresent()) {
            log.warn("创建服务执行器失败！");
            return Result.error(ServiceCodeEnum.SERVICE_CREATE_FAIL);
        }
        baseService.setDeviceId(deviceId);
        return Result.ok(baseService);

    }

    public static Map<String, Object> getTrueProperties(Device device) {
        Map<String, Object> realRuntimeData = new HashMap<>();
        Map<String, Object> runTimeData = device.getDeviceTwin().getActual();
        Map<String, PropertyMetadataItem> propertyMetadata = device.getPropertyMetadata();
        List<String> list = Arrays.asList("id", "name", "status", "description");
        runTimeData.forEach((k, v) -> {
            if (list.contains(k)) {
                realRuntimeData.put(k,v);
                return;
            }
            Result<Object> result = checkAndGetPropertyValue(propertyMetadata.get(k), v);
            if (result.getSignal()) {
                realRuntimeData.put(k,result.getResult());
            }
        });
        return realRuntimeData;
    }

    public static Result<Object> readProperty(
        IEdgeGatewaySpiProxy edgeGatewaySpiProxy,
        PropertyMetadataItem propertyMetadataItem, 
        ChannelRuntimeInfoField channel,
        Long edgeGatewayId, 
        Long tenantId
    ) {
        List<AccessElm> labelList = new ArrayList<>();
        AccessElm access = propertyMetadataItem.toAccess(channel);
        labelList.add(access);
        Result<List<ConnectResult>> result = edgeGatewaySpiProxy.connectLabel(edgeGatewayId, tenantId, labelList, null);
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }
        List<ConnectResult> list = result.getResult();
        if(list == null || list.size() <= 0){
            return Result.error("读取失败，值为空");            
        }
        ConnectResult connectResult = list.get(0);
        return Result.ok(connectResult.getValue());
    }

    public static Boolean writeProperty(IEdgeGatewaySpiProxy edgeGatewaySpiProxy, PropertyMetadataItem propertyMetadataItem, ChannelRuntimeInfoField channel, Object value, Long edgeGatewayId, Long tenantId) {
        log.info("调用网关代理下放设备属性值，网关：{}，通道：{}，属性：{},点位：{},值：{}",edgeGatewayId,channel,propertyMetadataItem.getProperty(),propertyMetadataItem.getAddress(),value);
        boolean isArray = propertyMetadataItem.getIsArray();
        Result<Void> result ;
        switch (ThingDataTypeEnum.typeOfName(propertyMetadataItem.getDataType().getType())) {
            case BOOLEAN:
                if (isArray) {
                    result = edgeGatewaySpiProxy.writeBoolArray(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (Boolean[]) value);
                } else {
                    result = edgeGatewaySpiProxy.writeBool(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (Boolean) value);
                }
                break;
            case CHAR:
                if (isArray) {
                    result = edgeGatewaySpiProxy.writeByteArray(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (byte[]) value);
                } else {
                    result = edgeGatewaySpiProxy.writeByte(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (Byte) value);
                }
                break;
            case BYTE:
            case SHORT:
                if (isArray) {
                    result = edgeGatewaySpiProxy.writeShortArray(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (Short[]) value);
                } else {
                    result = edgeGatewaySpiProxy.writeShort(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (Short) value);
                }
                break;
            case WORD:
            case LONG:
                if (isArray) {
                    result = edgeGatewaySpiProxy.writeIntArray(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (Integer[]) value);
                } else {
                    result = edgeGatewaySpiProxy.writeInt(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (Integer) value);
                }
                break;
            case DWORD: {
                if (isArray) {
                    result = edgeGatewaySpiProxy.writeLongArray(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (Long[]) value);
                } else {
                    result = edgeGatewaySpiProxy.writeLong(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (Long) value);
                }
                break;
            }
            case FLOAT:
                if (isArray) {
                    result = edgeGatewaySpiProxy.writeFloatArray(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (Float[]) value);
                } else {
                    result = edgeGatewaySpiProxy.writeFloat(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (Float) value);
                }
                break;
            case DOUBLE:
                if (isArray) {
                    result = edgeGatewaySpiProxy.writeDoubleArray(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (Double[]) value);
                } else {
                    result = edgeGatewaySpiProxy.writeDouble(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (Double) value);
                }
                break;
            case STRING:
                if (isArray) {
                    result = edgeGatewaySpiProxy.writeStringArray(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (String[]) value);
                } else {
                    result = edgeGatewaySpiProxy.writeString(edgeGatewayId, tenantId, propertyMetadataItem.toAccess(channel), (String) value);
                }
                break;
                //暂不支持模型数据上报
            case DATA_MODEL:
            default:
                log.warn("不支持的类型：{}",propertyMetadataItem.getDataType().getType());
                return false;

        }
        if (Optional.ofNullable(result).isPresent() && result.getSignal()) {
            log.info(result.getMessage());
        }else {
            log.warn(result.getMessage());
        }
        return Optional.ofNullable(result).isPresent() ? result.getSignal() : false;
    }

    private static <T> T[] object2Array(Class<T> c, Object o) {
        if (o.getClass().isArray()) {
            return (T[]) o;
        }
        return null;
    }

    private static <T> T[] list2Array(T[] ts, List list) {
        if (Optional.ofNullable(list).isPresent() && list.size() > 0) {
            ArrayList<T> arrayList = new ArrayList<>();
            list.forEach(l -> {
                arrayList.add((T) l);
            });
            return arrayList.toArray(ts);
        }
        return null;
    }


    public static Result<Object> checkAndGetPropertyValue(PropertyMetadataItem propertyMetadataItem, Object value) {
        if (!Optional.ofNullable(propertyMetadataItem).isPresent() || !Optional.ofNullable(value).isPresent()) {
            return Result.error("属性或值为空！");
        }
        if (value instanceof ScriptObjectMirror && propertyMetadataItem.getIsArray()) {
            value = ((ScriptObjectMirror) value).values();
        }
        if (propertyMetadataItem.getIsArray() == value.getClass().isArray() || value instanceof List == propertyMetadataItem.getIsArray()) {
            Object o = value;
            List list = new ArrayList();
            boolean isArray = propertyMetadataItem.getIsArray();
            if (value.getClass().isArray()) {
                list = Arrays.asList(value);
                o = JSON.toJSON(value);
            }
            boolean isList = o instanceof List;
            if (isList) {
                list = (List) o;
                if (list.size() <= 0) {
                    return Result.error("值列表为空！");
                }
                o = list.get(0);
            }
            ThingDataTypeEnum dataType = ThingDataTypeEnum.typeOfName(propertyMetadataItem.getDataType().getType());
            if (dataType == null) {
                return Result.error();
            }
            if (isArray && !checkArrayParam(dataType,list)) {
                return Result.error(ServiceCodeEnum.THING_SERVICE_ARRAY_PARAM_ERROR);
            }
            String className = o.getClass().getName();
            BigDecimal decimal = toBigDecimal(o);

            switch (dataType) {
                case BOOLEAN:
                    if (!"boolean".equals(className) && !"java.lang.Boolean".equals(className)) {
                        log.warn("值类型不一致！属性类型：bool，值类型：{}" , className);
                        return Result.error();
                    }
                    if (isArray) {
                        Boolean[] booleans = new Boolean[list.size()];
                        return Result.ok(list2Array(booleans, list));
                    } else {
                        return Result.ok((Boolean) value);
                    }
                case CHAR:
                    if (!Optional.ofNullable(decimal).isPresent()){
                        log.warn("值类型不一致！属性类型：CHAR，值类型：{}" , className);
                        return Result.error(ServiceCodeEnum.THING_SERVICE_PARAM_TYPE_ERROR);
                    }
                    if (isArray) {
                        ByteBuf bb = Unpooled.buffer(32);
                        list.forEach(o1 -> bb.writeByte((Integer) o1));
                        return Result.ok(bb.array());
                    } else {
                        return Result.ok(decimal.byteValue());
                    }
                case BYTE:
                case SHORT:
                    if (!Optional.ofNullable(decimal).isPresent()){
                        log.warn("值类型不一致！属性类型：SHORT，值类型：{}" , className);
                        return Result.error(ServiceCodeEnum.THING_SERVICE_PARAM_TYPE_ERROR);
                    }
                    if (isArray) {
                        Short[] shorts = new Short[list.size()];
                        for (int i = 0; i < list.size(); i++) {
                            shorts[i] =  toBigDecimal(list.get(i)).shortValue();
                        }
                        return Result.ok(shorts);
                    } else {
                        return Result.ok(decimal.shortValue());
                    }
                case WORD:
                case LONG:
                    if (!Optional.ofNullable(decimal).isPresent()){
                        log.warn("值类型不一致！属性类型：INT或USHORT，值类型：{}" , className);
                        return Result.error(ServiceCodeEnum.THING_SERVICE_PARAM_TYPE_ERROR);
                    }
                    if (isArray) {
                        Integer[] integers = new Integer[list.size()];
                        for (int i = 0; i < list.size(); i++) {
                            integers[i] =  toBigDecimal(list.get(i)).intValue();
                        }
                        return Result.ok(integers);
                    } else {
                        return Result.ok(decimal.intValue());
                    }
                case DWORD: {
                    if (!Optional.ofNullable(decimal).isPresent()){
                        log.warn("值类型不一致！属性类型：UINT，值类型：{}" , className);
                        return Result.error(ServiceCodeEnum.THING_SERVICE_PARAM_TYPE_ERROR);
                    }
                    if (isArray) {
                        Long[] longs = new Long[list.size()];
                        for (int i = 0; i < list.size(); i++) {
                            longs[i] =  toBigDecimal(list.get(i)).longValue();
                        }
                        return Result.ok(longs);
                    } else {
                        return Result.ok(decimal.longValue());
                    }
                }
                case FLOAT:
                    if (!Optional.ofNullable(decimal).isPresent()){
                        log.warn("值类型不一致！属性类型：FLOAT，值类型：{}" , className);
                        return Result.error(ServiceCodeEnum.THING_SERVICE_PARAM_TYPE_ERROR);
                    }
                    if (isArray) {
                        Float[] floats = new Float[list.size()];
                        for (int i = 0; i < list.size(); i++) {
                            floats[i] = toBigDecimal(list.get(i)).floatValue();
                        }
                        return Result.ok(floats);
                    } else {
                        return Result.ok(decimal.floatValue());
                    }
                case DOUBLE:
                    if (!Optional.ofNullable(decimal).isPresent()){
                        log.warn("值类型不一致！属性类型：DOUBLE，值类型：{}" , className);
                        return Result.error(ServiceCodeEnum.THING_SERVICE_PARAM_TYPE_ERROR);
                    }
                    if (isArray) {
                        Double[] doubles = new Double[list.size()];
                        for (int i = 0; i < list.size(); i++) {
                            doubles[i] = toBigDecimal(list.get(i)).doubleValue();
                        }
                        return Result.ok(doubles);
                    } else {
                        return Result.ok(decimal.doubleValue());
                    }
                case STRING:
                    if (!"java.lang.String".equals(className)) {
                        log.warn("值类型不一致！属性类型：STRING，值类型：{}" , className);
                        return Result.error();
                    }
                    if (isArray) {
                        String[] strings = new String[list.size()];
                        value = list2Array(strings, list);
                        return Result.ok(value);
                    } else {
                        return Result.ok((String) value);
                    }
                    //暂不支持模型数据上报
                case DATA_MODEL:
                default:
                    log.warn("未知数据类型！");
                    return Result.error();
            }
        }
        log.warn("属性和值是否数组不一致！");
        return Result.error();
    }

    public static BigDecimal toBigDecimal(Object o){
        BigDecimal bigDecimal = null;
        if (o instanceof Double || "double".equals(o.getClass().getName())) {
            bigDecimal = BigDecimal.valueOf((Double) o);
        }else if (o instanceof Integer || "int".equals(o.getClass().getName())){
            bigDecimal = BigDecimal.valueOf((Integer)o);
        }else if (o instanceof Float || "float".equals(o.getClass().getName())){
            bigDecimal = BigDecimal.valueOf((Float)o);
        }else if (o instanceof Short || "short".equals(o.getClass().getName())) {
            bigDecimal = BigDecimal.valueOf((Short) o);
        }else if (o instanceof Long || "long".equals(o.getClass().getName())) {
            bigDecimal = BigDecimal.valueOf((Long) o);
        }else if (o instanceof Byte || "byte".equals(o.getClass().getName())){
            bigDecimal = BigDecimal.valueOf((Byte)o);
        }else if (o instanceof String) {
            try {
                bigDecimal = new BigDecimal(o.toString());
            } catch (Exception e) {
            }
        }
        return bigDecimal;
    }

    public static boolean checkArrayParam(ThingDataTypeEnum dataType, List list) {
        List<Object> collect = (List<Object>) list.stream().filter(o1 -> o1 == null).collect(Collectors.toList());
        if (collect.size() > 0) {
            return false;
        }
        List<BigDecimal> o2 = (List<BigDecimal>) list.stream().map(o1 -> SpiUtil.toBigDecimal(o1)).filter(Objects::nonNull).collect(Collectors.toList());
        switch (dataType) {
            case BOOLEAN : case STRING: case DATA_MODEL: case DATE: case VOID:
            {
                Set<Class> classSet = (Set<Class>) list.stream().map(o1 -> o1.getClass()).collect(Collectors.toSet());
                if (classSet.size() != 1){
                    return false;
                }
                break;
            }
            case DWORD: case WORD: case BYTE:
            {
                List<Integer> integers = o2.stream().map(oo2 -> oo2.compareTo(new BigDecimal(0))).collect(Collectors.toList());
                if (integers.contains(-1)) {
                    return false;
                }
            }
            default:
            {
                if (o2.size() != list.size()) {
                    return false;
                }
            }
        }
        return true;
    }
    
    public static Result<Object> convertValue(PropertyMetadataItem propertyMetadataItem,String writeValue){
        try {
            ThingDataTypeEnum dataType = ThingDataTypeEnum.typeOfName(propertyMetadataItem.getDataType().getType());
            if(propertyMetadataItem.getIsArray() && ObjectUtil.isNull(propertyMetadataItem.getLength())){ //没有绑定标签
                propertyMetadataItem.setLength(writeValue.split(",").length);
            }
            switch (dataType) {
                case BOOLEAN:
                    if (propertyMetadataItem.getIsArray()) {
                        String[] writeArr = writeValue.split(",");
                        if(propertyMetadataItem.getLength() != writeArr.length){
                            return Result.error(propertyMetadataItem.getDataType().getType() +  "数组写入长度不一致");
                        }
                        Boolean[] booleans = new Boolean[propertyMetadataItem.getLength()];
                        for (int i = 0; i < writeArr.length; i++) {
                            if("0".equals(writeArr[i]) || "false".equals(writeArr[i].toLowerCase())){
                                booleans[i] = false;
                            }else if("1".equals(writeArr[i]) || "true".equals(writeArr[i].toLowerCase())){
                                booleans[i] = true;
                            }else {
                                return Result.error(propertyMetadataItem.getDataType().getType() +  "数组写入格式应为1,0 | true,false");
                            }
                        }
                        return Result.ok(booleans);
                    } else {
                        if("0".equals(writeValue) || "false".equals(writeValue.toLowerCase())){
                            return Result.ok(false);
                        }else if("1".equals(writeValue) || "true".equals(writeValue.toLowerCase())){
                            return Result.ok(true);
                        }else {
                            return Result.error(propertyMetadataItem.getDataType().getType() +  "数组写入格式应为1、0 | true、false");
                        }
                    }
                case CHAR:
                    if (propertyMetadataItem.getIsArray()) {
                        String[] writeArr = writeValue.split(",");
                        if(propertyMetadataItem.getLength() != writeArr.length){
                            return Result.error(propertyMetadataItem.getDataType().getType() +  "数组写入长度不一致");
                        }
                        byte[] bytes = new byte[propertyMetadataItem.getLength()];
                        for (int i = 0; i < writeArr.length; i++) {
                            bytes[i] = Byte.parseByte(writeArr[i]);
                        }
                        return Result.ok(bytes);
                    } else {
                        return Result.ok(Byte.parseByte(writeValue));
                    }
                case BYTE:
                case SHORT:
                    if (propertyMetadataItem.getIsArray()) {
                        String[] writeArr = writeValue.split(",");
                        if(propertyMetadataItem.getLength() != writeArr.length){
                            return Result.error(propertyMetadataItem.getDataType().getType() + "数组写入长度不一致");
                        }
                        Short[] shorts = new Short[propertyMetadataItem.getLength()];
                        for (int i = 0; i < writeArr.length; i++) {
                            shorts[i] = Short.parseShort(writeArr[i]);
                        }
                        return Result.ok(shorts);
                    } else {
                        return Result.ok(Short.parseShort(writeValue));
                    }
                case WORD:
                case LONG:
                    if (propertyMetadataItem.getIsArray()) {
                        String[] writeArr = writeValue.split(",");
                        if(propertyMetadataItem.getLength() != writeArr.length){
                            return Result.error(propertyMetadataItem.getDataType().getType() + "数组写入长度不一致");
                        }
                        Integer[] ints = new Integer[propertyMetadataItem.getLength()];
                        for (int i = 0; i < writeArr.length; i++) {
                            ints[i] = Integer.parseInt(writeArr[i]);
                        }
                        return Result.ok(ints);
                    } else {
                        return Result.ok(Integer.parseInt(writeValue));
                    }
                case DWORD: {
                    if (propertyMetadataItem.getIsArray()) {
                        String[] writeArr = writeValue.split(",");
                        if(propertyMetadataItem.getLength() != writeArr.length){
                            return Result.error(propertyMetadataItem.getDataType().getType() + "数组写入长度不一致");
                        }
                        Long[] longs = new Long[propertyMetadataItem.getLength()];
                        for (int i = 0; i < writeArr.length; i++) {
                            longs[i] = Long.parseLong(writeArr[i]);
                        }
                        return Result.ok(longs);
                    } else {
                        return Result.ok(Long.parseLong(writeValue));
                    }
                }
                case FLOAT:
                    if (propertyMetadataItem.getIsArray()) {
                        String[] writeArr = writeValue.split(",");
                        if(propertyMetadataItem.getLength() != writeArr.length){
                            return Result.error(propertyMetadataItem.getDataType().getType() + "数组写入长度不一致");
                        }
                        Float[] floats = new Float[propertyMetadataItem.getLength()];
                        for (int i = 0; i < writeArr.length; i++) {
                            floats[i] = Float.parseFloat(writeArr[i]);
                        }
                        return Result.ok(floats);
                    } else {
                        return Result.ok(Float.parseFloat(writeValue));
                    }
                case DOUBLE:
                    if (propertyMetadataItem.getIsArray()) {
                        String[] writeArr = writeValue.split(",");
                        if(propertyMetadataItem.getLength() != writeArr.length){
                            return Result.error(propertyMetadataItem.getDataType().getType() + "数组写入长度不一致");
                        }
                        Double[] doubles = new Double[propertyMetadataItem.getLength()];
                        for (int i = 0; i < writeArr.length; i++) {
                            doubles[i] = Double.parseDouble(writeArr[i]);
                        }
                        return Result.ok(doubles);
                    } else {
                        return Result.ok(Double.parseDouble(writeValue));
                    }
                case STRING:
                    if (propertyMetadataItem.getIsArray()) {
                        String[] strings = writeValue.split(",");
                        if(propertyMetadataItem.getLength() != strings.length){
                            return Result.error(propertyMetadataItem.getDataType().getType() + "数组写入长度不一致");
                        }
                        return Result.ok(strings);
                    } else {
                        return Result.ok(writeValue);
                    }
                    //暂不支持模型数据上报
                case DATA_MODEL:
                default:
                    log.warn("未知数据类型！");
                    return Result.error("未知数据类型！");
            }
        }catch (Exception e){
            log.error("device property write value error and exception is {} ",e.getMessage());
            return Result.error(propertyMetadataItem.getDataType().getType() + "数据类型转换错误");
        }
    }

}
