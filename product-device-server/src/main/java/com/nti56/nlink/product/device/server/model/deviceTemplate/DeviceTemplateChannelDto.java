package com.nti56.nlink.product.device.server.model.deviceTemplate;

import com.nti56.nlink.product.device.server.model.channel.dto.ChannelParamDTO;
import lombok.Data;

import java.util.List;

@Data
public class DeviceTemplateChannelDto {

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 所属驱动类型，1-Snap7，2-Modbus，3-OPC UA，4-ZigBee，5-BLE
     */
    private Integer driver;

    /**
     * 自定义协议名
     */
    private String customDriverName;
    /**
     * 通道参数列表
     */
    private List<ChannelParamDTO> channelParamList;

    /**
     * 初始化协议参数信息
     */
    private String[] initChannelParamInfo;
}
