package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.ConnectorEntity;
import com.nti56.nlink.product.device.server.model.connector.dto.QueryConnectorDTO;
import org.apache.ibatis.annotations.Param;

/**
 * 类说明:
 * <AUTHOR>
 * @version 1.0
 * @since JDK 1.8
 */
public interface ConnectorMapper extends CommonMapper<ConnectorEntity> {
    
    Page<ConnectorEntity> pageConnector(IPage<ConnectorEntity> connectorPage, @Param("queryConnectorDTO") QueryConnectorDTO queryConnectorDTO, @Param("tenantId") Long tenantId);
}
