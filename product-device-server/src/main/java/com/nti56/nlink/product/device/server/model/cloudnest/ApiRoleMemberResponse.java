package com.nti56.nlink.product.device.server.model.cloudnest;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ApiRoleMemberResponse extends BaseResponse {

    /**
     * 用户-角色关联id
     */
    @ApiModelProperty(value = "用户-角色关联id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 角色id
     */
    @ApiModelProperty(value = "角色id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long roleId;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String empName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String empContact;

    /**
     * 所属组织
     */
    @ApiModelProperty(value = "所属组织")
    private List<String> orgNames;

}
