package com.nti56.nlink.product.device.server.model.channel.vo;

import com.nti56.nlink.product.device.server.domain.thing.label.LabelGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/10/12 10:41<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "根据网关id查询channel列表")
public class ListChannelWithLabelGroupTreeAndLabelVO {
    private Long id;
    private String name;
    private String descript;
    private Integer driver;
    private Integer status;
    private List<LabelGroup> labelGroups;
}
