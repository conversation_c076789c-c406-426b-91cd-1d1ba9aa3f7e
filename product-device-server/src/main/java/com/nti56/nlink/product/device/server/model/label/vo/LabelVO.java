package com.nti56.nlink.product.device.server.model.label.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 11:27<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "标签dto")
public class LabelVO {

    private Long id;

    @Schema(description = "名称")
    @NotBlank(message = "标签名称不能为空")
    private String name;

    @Schema(description = "别名")
    private String alias;

    @Schema(description = "标签描述")
    private String descript;

    @Schema(description = "地址，如DB50.DBB1")
    private String address;

    @Schema(description = "长度")
    private Integer length;

    @Schema(description = "数据类型，bool/byte/short/int/float/string")
    private String dataType;

    @Schema(description = "是否数组")
    private Boolean isArray;

    @Schema(description = "type是string类型时，表示string元素的byte长度，其他type类型放空")
    private Integer stringBytes;

    @Schema(description = "设备id")
    private Long deviceId;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "属性名称")
    private String propertyName;
    
    @Schema(description = "当前值")
    private String currentValue;
    
    @Schema(description = "最后一次采集时间")
    private String lastGatherTime;

    @Schema(description = "是否只读")
    private Boolean readOnly;

}
