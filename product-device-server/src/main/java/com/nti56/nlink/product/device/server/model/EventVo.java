package com.nti56.nlink.product.device.server.model;

import com.nti56.nlink.product.device.server.domain.thing.modelbase.Event;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName EventVo
 * @date 2022/5/12 15:04
 * @Version 1.0
 */
@Data
public class EventVo {

    private Long deviceId;

    private String eventName;

    private List<String> properties;
}
