package com.nti56.nlink.product.device.server.model.inherit;

import com.nti56.nlink.product.device.client.model.dto.json.InputDataField;
import com.nti56.nlink.product.device.client.model.dto.json.OutputDataField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ServiceOfInherit {

    private Long id;

    /**
     * 所属物模型ID
     */
    @Schema(description = "所属物模型ID")
    private Long thingModelId;

    /**
     * 服务名称
     */
    @Schema(description = "服务名称")
    private String serviceName;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String descript;

    /**
     * 是否允许覆盖
     */
    @Schema(description = "是否允许覆盖")
    private Boolean override;

    /**
     * 调用方式:0-sync（同步调用） 1-async（异步调用）
     */
    @Schema(description = "调用方式:0-sync（同步调用） 1-async（异步调用）")
    private Boolean async;

    /**
     * 输入参数 JSON对象
     */
    @Schema(description = "输入参数 JSON对象")
    private InputDataField[] inputData;

    /**
     * 结果 JSON对象
     */
    @Schema(description = "结果 JSON对象")
    private OutputDataField outputData;

    /**
     * 结果描述
     */
    @Schema(description = "结果描述")
    private String outputDataDescript;

    /**
     * 代码文本 需要限制代码长度
     */
    @Schema(description = "代码文本 需要限制代码长度")
    private String serviceCode;

    @Schema(description = "直属物模型id")
    private Long baseThingModelId;

    @Schema(description = "直属物模型名称")
    private String baseThingModelName;


}
