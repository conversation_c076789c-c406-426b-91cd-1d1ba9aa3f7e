package com.nti56.nlink.product.device.server.model.inherit;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 类说明: 带继承信息的物模型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-13 17:30:39
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class ThingModelOfInherit {
    
    @Schema(description = "属性")
    private List<PropertyOfInherit> properties;

    @Schema(description = "事件")
    private List<EventOfInherit> events;

    @Schema(description = "服务Id列表")
    private List<ServiceOfInherit> services;

    @Schema(description = "订阅")
    private List<SubscriptionOfInherit> subscriptions;
    
}
