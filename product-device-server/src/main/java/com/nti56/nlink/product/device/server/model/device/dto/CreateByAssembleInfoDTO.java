package com.nti56.nlink.product.device.server.model.device.dto;

import com.nti56.nlink.product.device.server.model.device.vo.AssembleByLabelIdGroupIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/9/1 11:08<br/>
 * @since JDK 1.8
 */
@Data
public class CreateByAssembleInfoDTO {

    @Schema(description = "uuid")
    @NotNull(message = "唯一标识不能为空")
    private Long uuid;

    @Schema(description = "是否取消创建")
    @NotNull(message = "是否取消创建不能为空")
    private Boolean cancel;

    @Schema(description = "标记id")
    private List<Long> tagIds;


    @Schema(description = "所属网关id")
    @NotNull(message = "网关标识不能为空")
    private Long edgeGatewayId;

    @Schema(description = "继承物模型id列表")
    @NotNull(message = "至少选择一个模型继承")
    @Size(min = 1,message = "至少选择一个模型继承")
    private List<Long> inheritThingModelIds;

    @Valid
    @NotNull(message = "至少创建一个设备")
    @Size(min = 1,message = "至少创建一个设备")
    private List<AssembleByLabelIdGroupIdVO> assembleByLabelIdGroupIdList;
}
