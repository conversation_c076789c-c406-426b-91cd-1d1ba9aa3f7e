package com.nti56.nlink.product.device.server.model;

import com.nti56.nlink.product.device.server.entity.ThingModelInheritEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ThingModelInheritBo extends ThingModelInheritEntity{
    
    @Schema(description = "继承的物模型名称")
    private String inheritThingModelName;

    @Schema(description = "被继承的物模型名称")
    private String beInheritThingModelName;
}
