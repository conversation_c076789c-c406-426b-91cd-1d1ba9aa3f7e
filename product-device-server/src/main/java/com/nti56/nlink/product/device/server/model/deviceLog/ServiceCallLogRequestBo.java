package com.nti56.nlink.product.device.server.model.deviceLog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Map;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ServiceCallLogBo
 * @date 2022/8/8 17:26
 * @Version 1.0
 */
@Data
@Schema(name = "服务调用日志")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ServiceCallLogRequestBo {

    private Long deviceId;

    private String deviceName;

    private Long serviceId;

    private String serviceName;

    private Long start;

    private Long stop;

}
