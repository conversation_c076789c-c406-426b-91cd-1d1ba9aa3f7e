package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.nti56.nlink.product.device.client.model.dto.json.InputDataField;
import com.nti56.nlink.product.device.client.model.dto.json.OutputDataField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 物服务表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 10:28:52
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("device_service")
@Schema(description = "DeviceServiceEntity对象")
public class DeviceServiceEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 所属设备ID
     */
    @Schema(description = "所属设备ID")
    private Long deviceId;

    /**
     * 服务名称
     */
    @Schema(description = "服务名称")
    private String serviceName;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String descript;

    /**
     * 是否允许覆盖
     */
    @Schema(description = "是否允许覆盖")
    private Boolean override;

    /**
     * 调用方式:0-sync（同步调用） 1-async（异步调用）
     */
    @Schema(description = "调用方式:0-sync（同步调用） 1-async（异步调用）")
    private Boolean async;

    /**
     * 输入参数 JSON对象
     */
    @Schema(description = "输入参数 JSON对象")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private InputDataField[] inputData;

    /**
     * 结果 JSON对象
     */
    @Schema(description = "结果 JSON对象")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private OutputDataField outputData;

    /**
     * 代码文本 需要限制代码长度
     */
    @Schema(description = "代码文本 需要限制代码长度")
    private String serviceCode;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer version;

    /**
     * 删除
     */
    @Schema(description = "删除")
    @TableLogic
    private Integer deleted;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "租户ID")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @Schema(description = "工程ID")
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @Schema(description = "模块ID")
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @Schema(description = "空间ID")
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型 0-普通服务 1-故障服务")
    private Integer serviceType;

}
