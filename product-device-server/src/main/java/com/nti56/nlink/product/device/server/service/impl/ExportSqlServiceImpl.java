package com.nti56.nlink.product.device.server.service.impl;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.nti56.nlink.common.dto.ExportFile;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.FreemarkerUtils;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.config.ExportConfig;
import com.nti56.nlink.product.device.server.service.IExportSqlService;
import com.nti56.nlink.product.device.server.service.export.handler.*;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/5/25 20:53<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class ExportSqlServiceImpl implements IExportSqlService {
    @Autowired
    private ExportConfig exportConfig;

    @Value("${spring.application.name}")
    private String appName;

    @Override
    public R export(TenantIsolation tenantIsolation) {
        ExportFile export = new ExportFile();
        List<String> sqlList = exportSql(tenantIsolation.getTenantId());
        Map<String, Object> map = Maps.newHashMap();
        String path = String.format("%s/%s/%s", exportConfig.getSqlExport(), tenantIsolation.getTenantId(), exportConfig.getName());
        try {
            Files.deleteIfExists(Paths.get(path));
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        if (CollectionUtils.isEmpty(sqlList)) {
            sqlList.add(exportConfig.getSql());
        }
        if (CollectionUtils.isNotEmpty(sqlList)) {
            map.put("hasSql", "1==1");
            map.put("sqlSize", sqlList.size() - 1);
            map.put("sqlList", sqlList);
            FreemarkerUtils.geneFileStr("databaseChangeLog.ftl", this.getClass(), "/export/sql/ftl", path, map);
        }
        export.setApplicationPath(exportConfig.getApplicationPath());
        export.setBootstrapPath(exportConfig.getBootstrapPath());
        export.setJarPath(exportConfig.getJarPath());
        export.setSqlPath(path);
        export.setBat(exportConfig.getBat());
        export.setShell(exportConfig.getShell());
        export.setAppName(appName);
        return R.ok(export);
    }


    private List<String> exportSql(Long tenantId) {
        List<String> sqlList = Lists.newLinkedList();
        ThingExportSqlHandler thing = new ThingExportSqlHandler();
        thing.setNext(new EdgeGatewayExportSqlHandler())
              .setNext(new DeviceModelExportSqlHandler())
                .setNext(new ChannelExportSqlHandler())
                .setNext(new DeviceExportSqlHandler())
                .setNext(new LabelExportSqlHandler())
                .setNext(new ResourceExportSqlHandler())
                .setNext(new ComputeTaskExportSqlHandler())
                .setNext(new TagExportSqlHandler());
        thing.exportSqlDml(tenantId, sqlList);
        return sqlList;
    }
}
