package com.nti56.nlink.product.device.server.model;


import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类说明: 设备dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:58
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "批量操作响应对象")
public class DeviceRespondBo {

    @Schema(description = "设备Id")
    private Long id;

    @Schema(description = "设备名称")
    private String name;

    @Schema(description = "网关ID")
    private Long edgeGatewayId;

    @Schema(description = "网关名")
    private String edgeGatewayName;

    @Schema(description = "关联的分组名称")
    private String source;

    @Schema(description = "结果")
    private Result<Void> result;

    @Schema(description = "最后同步时间")
    private LocalDateTime lastSyncTime;

    @Schema(description = "设备状态，2-已上线， 1-已离线，0-未激活")
    private Integer status;
}
