package com.nti56.nlink.product.device.server.model.changeNotice.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/2/17 12:41<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "变动通知查询")
public class QueryChangeNoticeDTO {

    @Schema(description = "变动通知名称")
    private String name;

    @Schema(description = "变动项目(1网关,2设备)")
    private Integer changeSubject;

}
