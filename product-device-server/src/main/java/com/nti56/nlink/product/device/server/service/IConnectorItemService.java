package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.entity.ConnectorItemEntity;
import com.nti56.nlink.product.device.server.model.connectorItem.dto.CreateConnectorItemDTO;
import com.nti56.nlink.product.device.server.model.connectorItem.dto.EditConnectorItemDTO;
import com.nti56.nlink.product.device.server.model.connectorItem.dto.QueryConnectorItemDTO;
import com.nti56.nlink.product.device.server.model.connectorItem.vo.ConnectorItemVO;

/**
 * 类说明:
 * <AUTHOR>
 * @version 1.0
 * @since JDK 1.8
 */
public interface IConnectorItemService extends IBaseService<ConnectorItemEntity> {
    
    Result<Page<ConnectorItemVO>> pageConnectorItem(PageParam pageParam, QueryConnectorItemDTO queryConnectorItemDTO, TenantIsolation tenantIsolation);
    
    Result<ConnectorItemEntity> createConnectorItem(CreateConnectorItemDTO createConnectorItemDTO, TenantIsolation tenantIsolation);
    
    Result<Void> editConnectorItem(EditConnectorItemDTO editConnectorItemDTO, TenantIsolation tenantIsolation);
    
    Result<Void> deleteConnectorItem(Long id, TenantIsolation tenantIsolation);
    
    Result<Boolean> existConnectorItem(Long connectorId);
    
    Result<ConnectorItemVO> getConnectorItemInfo(Long id);
}
