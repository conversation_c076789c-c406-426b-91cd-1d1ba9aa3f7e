package com.nti56.nlink.product.device.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.send.NotifyDTO;
import com.nti56.nlink.product.device.server.constant.NotifyConstant;
import com.nti56.nlink.product.device.server.entity.NotifyChannelEntity;
import com.nti56.nlink.product.device.server.entity.NotifyServerLogEntity;
import com.nti56.nlink.product.device.server.entity.TemplateEntity;
import com.nti56.nlink.product.device.server.enums.NotifyEnum;
import com.nti56.nlink.product.device.server.enums.NotifyServerLogEnum;
import com.nti56.nlink.product.device.server.enums.NotifyStrategyEnum;
import com.nti56.nlink.product.device.server.mapper.NotifyChannelMapper;
import com.nti56.nlink.product.device.server.model.send.dto.NotifyTypeAndChannelParamsDTO;
import com.nti56.nlink.product.device.server.model.template.dto.CreateTemplateDTO;
import com.nti56.nlink.product.device.server.model.template.dto.EditTemplateDTO;
import com.nti56.nlink.product.device.server.service.INotifyServerLogService;
import com.nti56.nlink.product.device.server.service.NotifyStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class NotifyStrategyContext {
 
    @Autowired
    private final Map<String, NotifyStrategy> strategyMap = new ConcurrentHashMap<>();

    @Autowired
    private NotifyChannelMapper notifyChannelMapper;

    @Autowired
    private INotifyServerLogService notifyServerLogService;


    public Result addTemplate(CreateTemplateDTO dto, TenantIsolation tenantIsolation) {
        NotifyTypeAndChannelParamsDTO notifyTypeAndChannelParams = this.getNotifyTypeAndChannelParams(dto.getNotifyChannelId(), tenantIsolation);
        if (notifyTypeAndChannelParams == null){
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }

        return strategyMap.get(NotifyStrategyEnum.getNameByValue(notifyTypeAndChannelParams.getType()))
                .addTemplate(notifyTypeAndChannelParams.getChannelParams(),dto,tenantIsolation);
    }

    public Result deleteTemplate(TemplateEntity template, TenantIsolation tenantIsolation) {
        NotifyTypeAndChannelParamsDTO notifyTypeAndChannelParams = this.getNotifyTypeAndChannelParams(template.getNotifyChannelId(),tenantIsolation);
        if (notifyTypeAndChannelParams == null){
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        return strategyMap.get(NotifyStrategyEnum.getNameByValue(notifyTypeAndChannelParams.getType()))
                .deleteTemplate(notifyTypeAndChannelParams.getChannelParams(),template);
    }

    public Result editTemplate(EditTemplateDTO dto, TemplateEntity templateEntity, TenantIsolation tenantIsolation) {
        NotifyTypeAndChannelParamsDTO notifyTypeAndChannelParams = this.getNotifyTypeAndChannelParams(dto.getNotifyChannelId(),tenantIsolation);
        if (notifyTypeAndChannelParams == null){
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        return strategyMap.get(NotifyStrategyEnum.getNameByValue(notifyTypeAndChannelParams.getType()))
                .editTemplate(notifyTypeAndChannelParams.getChannelParams(),dto,templateEntity,tenantIsolation);
    }

    public Result sendNotify(NotifyDTO dto, TemplateEntity templateEntity , String content , TenantIsolation tenantIsolation) {
        NotifyTypeAndChannelParamsDTO notifyTypeAndChannelParams = this.getNotifyTypeAndChannelParams(templateEntity.getNotifyChannelId(),tenantIsolation);
        if (notifyTypeAndChannelParams == null){
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        return strategyMap.get(NotifyStrategyEnum.getNameByValue(notifyTypeAndChannelParams.getType()))
                .sendNotify(notifyTypeAndChannelParams.getChannelParams(),dto,templateEntity,content,tenantIsolation);
    }

    public void setAuditStatus(TemplateEntity t, TenantIsolation tenantIsolation) {
        NotifyTypeAndChannelParamsDTO notifyTypeAndChannelParams = this.getNotifyTypeAndChannelParams(t.getNotifyChannelId(),tenantIsolation);
        if (notifyTypeAndChannelParams == null){
            return ;
        }
        strategyMap.get(NotifyStrategyEnum.getNameByValue(notifyTypeAndChannelParams.getType()))
                .setAuditStatus(notifyTypeAndChannelParams.getChannelParams(),t);
    }

    public Result validationChannel(NotifyChannelEntity notifyChannelEntity) {
        NotifyTypeAndChannelParamsDTO notifyTypeAndChannelParams = this.getNotifyTypeAndChannelParams(notifyChannelEntity);
        if (notifyTypeAndChannelParams == null){
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        return strategyMap.get(NotifyStrategyEnum.getNameByValue(notifyTypeAndChannelParams.getType()))
                .validationChannel(notifyChannelEntity);
    }

    private NotifyTypeAndChannelParamsDTO getNotifyTypeAndChannelParams(Long channelId, TenantIsolation tenantIsolation){
        LambdaQueryWrapper<NotifyChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(NotifyChannelEntity::getId,channelId)
                .eq(NotifyChannelEntity::getTenantId,tenantIsolation.getTenantId());
        NotifyChannelEntity notifyChannelEntity = notifyChannelMapper.selectOne(lqw);
        if (notifyChannelEntity == null) {
            return null;
        }
        return this.getNotifyTypeAndChannelParams(notifyChannelEntity);
    }


    private NotifyTypeAndChannelParamsDTO getNotifyTypeAndChannelParams(NotifyChannelEntity notifyChannelEntity){
        JSONObject channelParamsJson = JSON.parseObject(notifyChannelEntity.getParams());
        Integer notifyType = notifyChannelEntity.getNotifyType();
        if (NotifyEnum.SMS.getValue().equals(notifyType)){
            notifyType = channelParamsJson.getIntValue(NotifyConstant.PROVIDER);
        }
        return NotifyTypeAndChannelParamsDTO.builder().type(notifyType)
                .channelParams(channelParamsJson)
                .build();
    }

    public void retryDeleteTemplate(NotifyServerLogEntity notifyServerLogEntity, TenantIsolation tenantIsolation) {
        JSONObject requestJson = JSONObject.parseObject(notifyServerLogEntity.getRequestParams());

        NotifyTypeAndChannelParamsDTO notifyTypeAndChannelParams = this.getNotifyTypeAndChannelParams(requestJson.getLong("channelId"),tenantIsolation);
        if (notifyTypeAndChannelParams == null){
            notifyServerLogService.createRequestFailLog(notifyServerLogEntity.getSourceId(),
                    NotifyServerLogEnum.DELETE_TEMPLATE_FAIL,notifyServerLogEntity.getRequestParams(),
                    "渠道不存在",tenantIsolation);
            return ;
        }
        strategyMap.get(NotifyStrategyEnum.getNameByValue(notifyTypeAndChannelParams.getType()))
                .retryDeleteTemplate(notifyTypeAndChannelParams.getChannelParams(),notifyServerLogEntity,requestJson,tenantIsolation);
    }


    public void retrySendFailNotify(NotifyServerLogEntity notifyServerLogEntity, TenantIsolation tenantIsolation) {
        JSONObject requestJson = JSON.parseObject(notifyServerLogEntity.getRequestParams());
        TemplateEntity templateEntity = requestJson.getObject("templateEntity", TemplateEntity.class);

        NotifyTypeAndChannelParamsDTO notifyTypeAndChannelParams = this.getNotifyTypeAndChannelParams(templateEntity.getNotifyChannelId(),tenantIsolation);
        if (notifyTypeAndChannelParams == null){
            notifyServerLogService.createRequestFailLog(notifyServerLogEntity.getSourceId(),
                    NotifyServerLogEnum.SEND_NOTIFY_FAIL,notifyServerLogEntity.getRequestParams(),
                    "渠道不存在",tenantIsolation);
            return ;
        }
        strategyMap.get(NotifyStrategyEnum.getNameByValue(notifyTypeAndChannelParams.getType()))
                .retrySendFailNotify(notifyTypeAndChannelParams.getChannelParams(),notifyServerLogEntity,templateEntity,requestJson,tenantIsolation);
    }
}