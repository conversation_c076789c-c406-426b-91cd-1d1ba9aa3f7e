package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.NotAssignGatewayEntity;
import com.nti56.nlink.product.device.server.model.NotAssignHeartbeatInfo;
import com.nti56.nlink.product.device.server.model.notAssign.dto.AssignGatewayDto;
import com.nti56.nlink.product.device.server.model.notAssign.dto.CreateGatewayDto;
import com.nti56.nlink.product.device.server.model.notAssign.vo.NotAssignGatewayVo;
import java.util.List;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-4-21 15:06:56
 * @since JDK 1.8
 */
public interface INotAssignGatewayService extends IBaseService<NotAssignGatewayEntity> {

    Result<Void> dealWithNotAssignHeartbeat(NotAssignHeartbeatInfo info);

    Result<Page<NotAssignGatewayVo>> pageNotAssignGateway(PageParam pageParam);

    Result<Void> clearNotAssignGateway();

    Result<Void> assignGateway(AssignGatewayDto assignGatewayDto);

    Result<Void> batchCreateGateway(List<CreateGatewayDto> createGatewayDtoList, TenantIsolation tenantIsolation);
    
    Result<Void> connectEdgeGateway(Long edgeGatewayId,Long tenantId);
    
    Result<Void> disConnectEdgeGateway(Long edgeGatewayId,Long tenantId);

}
