package com.nti56.nlink.product.device.server.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.EngineeringDocEntity;
import com.nti56.nlink.product.device.server.model.engineering.dto.CreateEngineeringDocDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


public interface IEngineeringDocService extends IService<EngineeringDocEntity> {


    Result<EngineeringDocEntity> getTheLatestAutoData(TenantIsolation tenantIsolation);

    Result<List<EngineeringDocEntity>> listEngineeringDoc(TenantIsolation tenantIsolation);

    Result<Void>  createEngineeringDoc(HttpServletResponse response , TenantIsolation tenantIsolation, CreateEngineeringDocDTO dto);

    Result<Void> importEngineeringDoc(TenantIsolation tenantIsolation, MultipartFile file,String password, String desc);

    void downloadEngineeringDoc(HttpServletResponse response ,TenantIsolation tenantIsolation, Long id);

    Result<Page<EngineeringDocEntity>> pageEngineeringDoc(TenantIsolation tenantIsolation, PageParam pageParam);
}
