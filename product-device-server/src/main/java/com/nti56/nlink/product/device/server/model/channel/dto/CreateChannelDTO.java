package com.nti56.nlink.product.device.server.model.channel.dto;

import com.nti56.nlink.product.device.server.util.RegexUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 9:45<br/>
 * @since JDK 1.8
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "新增通道配置dto")
public class CreateChannelDTO {

    @Schema(description = "通道名字")
    @NotBlank(message = "通道名称不能为空")
    @Length(max = 128,message = "通道名称不能超过32个字符")
    @Pattern(regexp = RegexUtil.channelNamePatternString,message = "通道名称只能包含中文、字母、数字、'_'、'-'这些字符!")
    private String name;

    @Schema(description = "所属驱动类型，1-Snap7，2-Modbus，3-OPC UA，4-ZigBee，5-BLE")
    @NotNull(message = "驱动类型不能为空")
    private Integer driver;

    @Schema(description = "自定义协议名")
    private String customDriverName;

    @Schema(description = "是否是服务端通道")
    private Boolean isServer;

    @Schema(description = "驱动型号spec")
    private String spec;

    @Schema(description = "描述")
    @Length(max = 256,message = "通道描述不能超过256个字符")
    private String descript;

    @Schema(description = "通道参数列表")
    @Valid
    private List<ChannelParamDTO> channelParamList;

    @Schema(description = "边缘网关")
    private Long edgeGatewayId;

    @Schema(description = "时间间隔，单位毫秒")
    @Min(value = 10,message = "轮询间隔最小不小于10ms")
    private Integer intervalMs;
}
