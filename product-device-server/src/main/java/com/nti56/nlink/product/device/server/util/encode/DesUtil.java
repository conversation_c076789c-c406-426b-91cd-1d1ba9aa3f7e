package com.nti56.nlink.product.device.server.util.encode;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Base64;

/**
 * DES加密 解密算法
 * <AUTHOR>
 * @date  2019/08/31
 * @version 1.0.0
 * @since JDK1.8
 */
@Slf4j
public class DesUtil {

    private static final byte[] DES_IV = { (byte) 0x12, (byte) 0x34, (byte) 0x56,
            (byte) 0x78, (byte) 0x90, (byte) 0xAB, (byte) 0xCD, (byte) 0xEF };

    private static final String DEFAULT_KEY = "ntitotci";
    private static final String TRANSFORMATION = "DES/CBC/PKCS5Padding";
    private static final String ALGORITHM = "DES";
    private static final String CHARSET = "utf-8";
    private static final int LENGTH_8 =8;

    /**
     *
     * 方法说明:加密. <br/>
     *
     * @param data 加密
     *
     * @param key 密钥
     *
     * @return 返回值
     *
     */
    public static String encode(String data, String key) {

        try {
            if(key.length()>= LENGTH_8){
                key=key.substring(0, LENGTH_8);
            }else{
                key=DEFAULT_KEY;
            }

            // 得到加密对象Cipher
            Cipher enCipher = Cipher.getInstance(TRANSFORMATION);
            // 设置工作模式为加密模式，给出密钥和向量
            enCipher.init(Cipher.ENCRYPT_MODE, generateSecret(key), new IvParameterSpec(DES_IV));
            byte[] pasByte = enCipher.doFinal(data.getBytes(CHARSET));
            return new String(Base64.getEncoder().encode(pasByte),CHARSET);
        } catch (Exception e) {
            log.error("encode error", e);
        }
        return data;
    }

    /**
     *
     * 方法说明:解密. <br/>
     *
     * @param data 解密数据
     *
     * @param key 密钥
     *
     * @return 返回值
     *
     */
    public static String decode(String data, String key) {

        try {
            if(key.length()>= LENGTH_8){
                key=key.substring(0, LENGTH_8);
            }else{
                key=DEFAULT_KEY;
            }
            Cipher deCipher = Cipher.getInstance(TRANSFORMATION);
            deCipher.init(Cipher.DECRYPT_MODE, generateSecret(key), new IvParameterSpec(DES_IV));
            byte[] decode=Base64.getDecoder().decode(data);
            byte[] pasByte = deCipher.doFinal(decode);
            return new String(pasByte, CHARSET);
        } catch (Exception e) {
            log.error("decode error", e);
        }
        return data;

    }

    /**
     *
     * 方法说明:默认加密. <br/>
     *
     * @param data 内容
     *
     * @return 返回值
     *
     */
    public static String encode(String data) {

        try {
            // 得到加密对象Cipher
            Cipher enCipher = Cipher.getInstance(TRANSFORMATION);
            // 设置工作模式为加密模式，给出密钥和向量
            enCipher.init(Cipher.ENCRYPT_MODE, generateSecret(DEFAULT_KEY), new IvParameterSpec(DES_IV));
            byte[] pasByte = enCipher.doFinal(data.getBytes(CHARSET));
            return new String(Base64.getEncoder().encode(pasByte),CHARSET);

        } catch (Exception e) {
            log.error("encode error", e);
        }
        return data;
    }

    /**
     *
     * 方法说明:默认解密. <br/>
     *
     * @param data 内容
     *
     * @return 返回值
     *
     */
    public static String decode(String data) {

        try {

            Cipher deCipher = Cipher.getInstance(TRANSFORMATION);
            deCipher.init(Cipher.DECRYPT_MODE, generateSecret(DEFAULT_KEY), new IvParameterSpec(DES_IV));
            byte[] pasByte = deCipher.doFinal(Base64.getDecoder().decode(data));
            return new String(pasByte, CHARSET);
        } catch (Exception e) {
            log.error("decode error", e);
        }
        return data;
    }

    /**
     *
     * @param key
     * @return
     * @throws InvalidKeySpecException
     * @throws UnsupportedEncodingException
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    private static SecretKey generateSecret(String key) throws InvalidKeySpecException,
            UnsupportedEncodingException, NoSuchAlgorithmException, InvalidKeyException {
        DESKeySpec keySpec = new DESKeySpec(key.getBytes(CHARSET));
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        return keyFactory.generateSecret(keySpec);

    }
    public static void main(String[] args) throws Exception {

        String ba="1234567890";
        System.out.println(ba.substring(0,8));
        System.out.println("加密:" + DesUtil.encode("innersect://navigation?target=product&id=2935&source=wxapp", "tmmt2017"));
        System.out.println("加密:" + DesUtil.encode("innersect://navigation?target=reserve&id=15", "tmmt2017"));
        System.out.println("加密:" + DesUtil.encode("innersect://navigation?target=reserve&id=17", "tmmt2017"));
        System.out.println("解密:" + DesUtil.decode("zetSTOPSYAv/ceHEX72fUQ==", "ntitotci"));
        System.out.println(
                "解密:" + DesUtil.decode("wXgrbJBCU2vZqzTJRd6VP/evr5rQSC/VM7ckcFYXW8DeGOt/rRFIXgg8CI/CQ78h", "tmmt2017"));
        System.out.println(DesUtil.decode(DesUtil.encode("Nti56@com")));

    }

}
