package com.nti56.nlink.product.device.server.model.thingModel.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 类说明：
 *
 * @ClassName PropertyPreviewDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/11/23 9:36
 * @Version 1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServicePreviewDTO {

    private String name;

    private boolean override;

    private String descript;

    @Override
    public boolean equals(Object o) {
        //自反性
        if (this == o) {
            return true;
        }
        //任何对象不等于null，比较是否为同一类型
        if (!(o instanceof ServicePreviewDTO)) {
            return false;
        }
        //强制类型转换
        ServicePreviewDTO propertyPreviewDTO = (ServicePreviewDTO) o;
        //比较属性值
        return Objects.equals(getName(), propertyPreviewDTO.getName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getName());
    }



}
