package com.nti56.nlink.product.device.server.model.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/2/1 15:11<br/>
 * @since JDK 1.8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceOnlineStatusVO {

    private Long id;

    @Schema(description = "0 未激活,1 已停用,2 在线,3 离线")
    private Integer online;

}
