package com.nti56.nlink.product.device.server.verticle.post.processor.label;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Map;
import java.util.Set;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.config.websocket.WsSessionManager;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpPropertyTopic;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpPropertyTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.verticle.post.processor.PostProcessorHandler;

import lombok.extern.slf4j.Slf4j;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-09-01 11:32:00
 * @since JDK 1.8
 */
@Slf4j
@Component
public class PropertyChangeToWSHandler extends PostProcessorHandler<GwUpPropertyTopic.TopicInfo> {

    @Async("labelConsumerAsyncExecutor")
    @Override
    public void process(GwUpPropertyTopic.TopicInfo topicInfo, UpData upData){
        super.process(topicInfo,upData);
    }

    @Override
    public void doProcess(GwUpPropertyTopic.TopicInfo topicInfo, UpData upData) {
        String prefix = WsSessionManager.PROPERTY_SESSION_KEY_PREFIX + topicInfo.getEdgeGatewayId();
        if (WsSessionManager.containsPrefix(prefix)) {
            Set<Map.Entry<String, WebSocketSession>> list = WsSessionManager.list(prefix);
            list.forEach(entry -> {
                try {
                    R r = R.ok(upData.getProp());
                    r.put("timestamp",upData.getTimestamp());
                    entry.getValue().sendMessage(new TextMessage(JSON.toJSONString(r)));
                    log.debug("属性改变消息发送：sessionId:{}",entry.getKey());
                } catch (IOException e) {
                    log.error("属性改变触发消息发送失败！topicInfo:{},upData:{}",topicInfo,upData);
                }catch (IllegalStateException e){
                    log.error("TheWebSocketSession {} has been closed {}",entry.getKey(), e.fillInStackTrace());
                    WsSessionManager.removeAndClose(entry.getKey());
                }
            });
        }
    }
}
