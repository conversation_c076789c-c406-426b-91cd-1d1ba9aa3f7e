package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;

import java.util.List;

/**
 * <p>
 * 物服务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 10:28:52
 * @since JDK 1.8
 */
public interface IThingServiceService extends IBaseService<ThingServiceEntity> {

    Result<ThingServiceEntity> save(TenantIsolation tenantIsolation, ThingServiceEntity entity);

    Result<Page<ThingServiceEntity>> getPage(ThingServiceEntity entity, Page<ThingServiceEntity> page);

    Result<List<ThingServiceEntity>> list(ThingServiceEntity entity);

    Result update(ThingServiceEntity entity, TenantIsolation tenantIsolation);

    Result deleteById(Long id, TenantIsolation tenantIsolation);
    
    Result<ThingServiceEntity> getByIdAndTenantIsolation(Long entityId, TenantIsolation tenantIsolation);

    Result<Void> deleteBatchByIds(List<Long> ids, TenantIsolation tenantIsolation);

    void deleteByThingModelId(Long tenantId , Long thingModelId);
}
