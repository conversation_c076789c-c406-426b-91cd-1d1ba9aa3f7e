package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.EdgeGatewayTypeEnum;
import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import com.nti56.nlink.product.device.server.entity.NotAssignGatewayEntity;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayControlProxy;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayNotAssignControlProxy;
import com.nti56.nlink.product.device.server.mapper.NotAssignGatewayMapper;
import com.nti56.nlink.product.device.server.model.NotAssignHeartbeatInfo;
import com.nti56.nlink.product.device.server.model.edgegateway.dto.CreateEdgeGatewayDTO;
import com.nti56.nlink.product.device.server.model.notAssign.dto.*;
import com.nti56.nlink.product.device.server.model.notAssign.vo.NotAssignGatewayVo;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;
import com.nti56.nlink.product.device.server.service.INotAssignGatewayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-02-18 16:11:55
 * @since JDK 1.8
 */
@Service
@Slf4j
public class NotAssignGatewayServiceImpl extends BaseServiceImpl<NotAssignGatewayMapper, NotAssignGatewayEntity> implements INotAssignGatewayService {

    @Value("${mqtt.publicHost}")
    private String mqttPublicHost;

    @Value("${mqtt.publicPort}")
    private Integer mqttPublicPort;

    @Value("${mqtt.host}")
    private String mqttHost;

    @Value("${mqtt.port}")
    private Integer mqttPort;

    @Value("${ot.publicHost}")
    private String otHost;

    @Value("${ot.publicPort}")
    private Integer otPort;

    @Value("${mqtt.username}")
    private String mqttUsername;

    @Value("${mqtt.password}")
    private String mqttPassword;

    private static final UniqueConstraint hostPortUniqueConstraint = new UniqueConstraint("host", "port");

    @Autowired
    private NotAssignGatewayMapper notAssignGatewayMapper;

    @Autowired
    private IEdgeGatewayNotAssignControlProxy edgeGatewayNotAssignControlProxy;
    
    @Autowired
    private IEdgeGatewayService edgeGatewayService;
    
    @Autowired
    private IEdgeGatewayControlProxy edgeGatewayControlProxy;

    @Autowired
    private CommonFetcherFactory commonFetcherFactory;


    @Override
    @Async("notAssignHeartbeatAsyncExecutor")
    public Result<Void> dealWithNotAssignHeartbeat(NotAssignHeartbeatInfo info) {
        // imei + adminPort 保证唯一
        if(!StringUtils.isEmpty(info.getImei())){
            LambdaQueryWrapper<NotAssignGatewayEntity> lqw = new LambdaQueryWrapper<>();
            lqw.eq(NotAssignGatewayEntity::getImei, info.getImei());
            saveOrUpdateNotAssignGateway(info, lqw);
        }

        // ip + adminPort 保证唯一
        if(StringUtils.isEmpty(info.getHost())){
            return Result.error("imei and ip all empty");
        }
        LambdaQueryWrapper<NotAssignGatewayEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(NotAssignGatewayEntity::getHost, info.getHost());
        saveOrUpdateNotAssignGateway(info, lqw);
        return Result.ok();
    }

    @Override
    public Result<Page<NotAssignGatewayVo>> pageNotAssignGateway(PageParam pageParam) {
        Page<NotAssignGatewayVo> pageNotAssignGateway = notAssignGatewayMapper.pageNotAssignGateway(pageParam.toPage(NotAssignGatewayEntity.class));
        return Result.ok(pageNotAssignGateway);
    }

    @Override
    public Result<Void> clearNotAssignGateway() {
        notAssignGatewayMapper.clearNotAssignGateway();
        return Result.ok();
    }
    
    @Override
    public Result<Void> assignGateway(AssignGatewayDto assignGatewayDto) {
        return assignGateway(assignGatewayDto,true);
    }
    
    private Result<Void> assignGateway(AssignGatewayDto assignGatewayDto,Boolean hostPortUniqueCheck) {
        if(StringUtils.isEmpty(assignGatewayDto.getImei()) && StringUtils.isEmpty(assignGatewayDto.getHost())){
            return Result.error("imei and ip all empty!");
        }
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayService.getById(assignGatewayDto.getEdgeGatewayId());
        if(ObjectUtil.isNull(edgeGatewayEntity)){
            return Result.error("该网关不存在!");
        }
        if(hostPortUniqueCheck && !StringUtils.isEmpty(assignGatewayDto.getHost()) && assignGatewayDto.getAdminPort() != null){
            CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(assignGatewayDto.getTenantId());
            //host port 联合唯一性校验
            UniqueConstraint.Unique hostPortUnique = hostPortUniqueConstraint.buildUnique(new FieldValue(assignGatewayDto.getHost()), new FieldValue(assignGatewayDto.getAdminPort()));
            EdgeGatewayEntity sameHostPortEntity = commonFetcher.get(hostPortUnique, EdgeGatewayEntity.class);
            if(sameHostPortEntity != null){
                return Result.error("网关host port重复");
            }
        }
        return doAssignResult(edgeGatewayEntity, assignGatewayDto.getImei(), assignGatewayDto.getHost(), assignGatewayDto.getPort(), assignGatewayDto.getAdminPort());
    }
    
    
    @Override
    public Result<Void> connectEdgeGateway(Long edgeGatewayId,Long tenantId) {
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayService.getById(edgeGatewayId);
        if(ObjectUtil.isNull(edgeGatewayEntity)){
            return Result.error("该网关不存在!");
        }
        Result<Boolean> onlineResult = edgeGatewayService.edgeGatewayOnline(tenantId,edgeGatewayId);
        if(onlineResult.getResult()){
            return Result.ok();
        }
        NotAssignGatewayEntity notAssignGatewayEntity = null;
        if(!StringUtils.isEmpty(edgeGatewayEntity.getImei())){
            notAssignGatewayEntity = notAssignGatewayMapper.getAssignGateway(edgeGatewayEntity.getImei(),null,edgeGatewayEntity.getPort());
        }else {
            notAssignGatewayEntity = notAssignGatewayMapper.getAssignGateway(null,edgeGatewayEntity.getHost(),edgeGatewayEntity.getPort());
        }
        if(ObjectUtil.isNull(notAssignGatewayEntity)){
            return Result.error("待分配列表不存在该网关记录!");
        }
        return doAssignResult(edgeGatewayEntity, notAssignGatewayEntity.getImei(), notAssignGatewayEntity.getHost(), notAssignGatewayEntity.getPort(), notAssignGatewayEntity.getAdminPort());
    }
    
    private Result<Void> doAssignResult(EdgeGatewayEntity edgeGatewayEntity, String imei, String host, Integer port, Integer adminPort) {
        String imeiOrHost = !StringUtils.isEmpty(imei) ? imei : host;
        Boolean visitPublicMqtt = !StringUtils.isEmpty(imei) ? true : false;
        ConnectGatewayDto connectGatewayDto = new ConnectGatewayDto();
        connectGatewayDto.setHost(host);
        connectGatewayDto.setPort(port);
        connectGatewayDto.setAdminPort(adminPort);
        connectGatewayDto.setMqtt(buildConnectMqtt(visitPublicMqtt));
        connectGatewayDto.setOt(buildConnectOt());
        connectGatewayDto.setTenantId(String.valueOf(edgeGatewayEntity.getTenantId()));
        connectGatewayDto.setEdgeGatewayId(String.valueOf(edgeGatewayEntity.getId()));
        connectGatewayDto.setHeartbeatUuid(edgeGatewayEntity.getHeartbeatUuid());
        edgeGatewayNotAssignControlProxy.connectOt(imeiOrHost,String.valueOf(adminPort), JSONObject.toJSONString(connectGatewayDto));
        edgeGatewayEntity.setImei(imei);
        edgeGatewayEntity.setHost(host);
        edgeGatewayEntity.setPort(adminPort);
        edgeGatewayService.updateById(edgeGatewayEntity);
        if(!StringUtils.isEmpty(imei)){
           notAssignGatewayMapper.deleteNotAssignGateway(imei,null,adminPort);
        }else {
           notAssignGatewayMapper.deleteNotAssignGateway(null,host,adminPort);
        }
        return Result.ok();
    }
    
    @Override
    @Transactional
    public Result<Void> batchCreateGateway(List<CreateGatewayDto> createGatewayDtoList, TenantIsolation tenantIsolation) {
        List<EdgeGatewayEntity> edgeGatewayList = new ArrayList<>();
        Map<Long,Integer> portMap = new HashMap<>();
        for(CreateGatewayDto createGatewayDto : createGatewayDtoList){
            CreateEdgeGatewayDTO dto = new CreateEdgeGatewayDTO();
            dto.setName(createGatewayDto.getName());
            dto.setImei(createGatewayDto.getImei());
            dto.setHost(createGatewayDto.getHost());
            dto.setPort(createGatewayDto.getAdminPort());
            dto.setType(EdgeGatewayTypeEnum.GATEWAY.getValue());
            Boolean visitPublicMqtt = !StringUtils.isEmpty(createGatewayDto.getImei()) ? true : false;
            dto.setVisitPublicMqtt(visitPublicMqtt);
            Result<EdgeGatewayEntity> edgeGatewayResult = edgeGatewayService.buildEdgeGateway(dto,createGatewayDto.getTenantId());
            if(!edgeGatewayResult.getSignal()){
                return Result.error(edgeGatewayResult.getMessage());
            }
            EdgeGatewayEntity edgeGatewayEntity = edgeGatewayResult.getResult();
            Long id = IdGenerator.generateId();
            edgeGatewayEntity.setId(id);
            edgeGatewayEntity.setTenantId(createGatewayDto.getTenantId());
            portMap.put(id,createGatewayDto.getPort());
            edgeGatewayList.add(edgeGatewayEntity);
        }
        if(createGatewayDtoList.size() != edgeGatewayList.size()){
            return Result.error("校验格式错误，数量不一致");
        }
        boolean insertSuccess = false;
        if(CollectionUtil.isNotEmpty(edgeGatewayList)){
            insertSuccess = edgeGatewayService.saveBatch(edgeGatewayList);
        }
        if(!insertSuccess){
            return Result.error("批量新增失败");
        }

        //下发连接网关
        for(EdgeGatewayEntity edgeGatewayEntity : edgeGatewayList){
            AssignGatewayDto assignGatewayDto = new AssignGatewayDto();
            assignGatewayDto.setImei(edgeGatewayEntity.getImei());
            assignGatewayDto.setHost(edgeGatewayEntity.getHost());
            assignGatewayDto.setAdminPort(edgeGatewayEntity.getPort());
            Integer port = portMap.get(edgeGatewayEntity.getId()) != null ? portMap.get(edgeGatewayEntity.getId()) : 8088;
            assignGatewayDto.setPort(port);
            assignGatewayDto.setTenantId(edgeGatewayEntity.getTenantId());
            assignGatewayDto.setEdgeGatewayId(edgeGatewayEntity.getId());
            assignGateway(assignGatewayDto,false);
        }
        return Result.ok();
    }
    
    @Override
    public Result<Void> disConnectEdgeGateway(Long edgeGatewayId,Long tenantId) {
        Result<Boolean> onlineResult = edgeGatewayService.edgeGatewayOnline(tenantId,edgeGatewayId);
        if(!onlineResult.getResult()){
            return Result.error("网关不在线");
        }
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayService.getById(edgeGatewayId);
        edgeGatewayControlProxy.disconnectOt(edgeGatewayEntity.getId(),edgeGatewayEntity.getTenantId(),edgeGatewayEntity.getHeartbeatUuid());
        return Result.ok();
    }


    private void saveOrUpdateNotAssignGateway(NotAssignHeartbeatInfo info, LambdaQueryWrapper<NotAssignGatewayEntity> lqw) {
        lqw.eq(NotAssignGatewayEntity::getAdminPort, info.getAdminPort());
        lqw.orderByAsc(NotAssignGatewayEntity::getCreateTime);
        List<NotAssignGatewayEntity> notAssignGatewayEntityList = notAssignGatewayMapper.selectList(lqw);
        if(CollectionUtil.isEmpty(notAssignGatewayEntityList)){
            //新增
            NotAssignGatewayEntity notAssignGatewayEntity = new NotAssignGatewayEntity();
            notAssignGatewayEntity.setImei(info.getImei());
            notAssignGatewayEntity.setAdminPort(info.getAdminPort());
            notAssignGatewayEntity.setHost(info.getHost());
            notAssignGatewayEntity.setPort(info.getPort());
            notAssignGatewayEntity.setTenantId(info.getTenantId());
            notAssignGatewayEntity.setEdgeGatewayId(info.getEdgeGatewayId());
            notAssignGatewayMapper.insert(notAssignGatewayEntity);
            return;
        }
        NotAssignGatewayEntity notAssignGatewayEntity = notAssignGatewayEntityList.get(0);
        notAssignGatewayEntity.setImei(info.getImei());
        notAssignGatewayEntity.setAdminPort(info.getAdminPort());
        notAssignGatewayEntity.setHost(info.getHost());
        notAssignGatewayEntity.setPort(info.getPort());
        notAssignGatewayEntity.setTenantId(info.getTenantId());
        notAssignGatewayEntity.setEdgeGatewayId(info.getEdgeGatewayId());
        notAssignGatewayMapper.updateById(notAssignGatewayEntity);
    }

    private ConnectMqtt buildConnectMqtt(Boolean visitPublicMqtt){
        ConnectMqtt connectMqtt = new ConnectMqtt();
        if(visitPublicMqtt){
            connectMqtt.setHost(mqttPublicHost);
            connectMqtt.setPort(mqttPublicPort);
        }else{
            connectMqtt.setHost(mqttHost);
            connectMqtt.setPort(mqttPort);
        }
        connectMqtt.setUsername(mqttUsername);
        connectMqtt.setPassword(mqttPassword);
        return connectMqtt;
    }

    private ConnectOt buildConnectOt(){
        ConnectOt connectOt = new ConnectOt();
        connectOt.setHost(otHost);
        connectOt.setPort(otPort);
        return connectOt;
    }

}
