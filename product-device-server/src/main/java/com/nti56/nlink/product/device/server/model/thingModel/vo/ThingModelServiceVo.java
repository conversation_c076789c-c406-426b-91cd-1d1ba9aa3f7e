package com.nti56.nlink.product.device.server.model.thingModel.vo;


import com.nti56.nlink.product.device.client.model.dto.json.InputDataField;
import com.nti56.nlink.product.device.client.model.dto.json.OutputDataField;
import lombok.Data;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 08:59:16
 * @since JDK 1.8
 */
@Data
public class ThingModelServiceVo {

    private Long id;
    
    private Long thingModelId;

    private String thingModelName;
    
    private String serviceName;

    private Boolean override;

    private Boolean async;

    private InputDataField[] inputData;

    private OutputDataField outputData;

    private String serviceCode;

    private Boolean covered;

    private String descript;

    private Integer serviceType;

    private Boolean repeat;

    private String belongModel;
}
