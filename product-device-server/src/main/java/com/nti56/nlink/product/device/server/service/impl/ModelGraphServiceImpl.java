//package com.nti56.nlink.product.device.server.service.impl;
//
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.core.map.MapUtil;
//import cn.hutool.json.JSONUtil;
//import com.google.common.collect.Sets;
//import com.nti56.nlink.common.util.Result;
//import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
//import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventElm;
//import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
//import com.nti56.nlink.product.device.server.domain.thing.enumerate.EventTypeEnum;
//import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
//import com.nti56.nlink.product.device.server.entity.*;
//import com.nti56.nlink.product.device.server.exception.BizException;
//import com.nti56.nlink.product.device.server.mapper.DeviceMapper;
//import com.nti56.nlink.product.device.server.mapper.Neo4jDeviceRepository;
//import com.nti56.nlink.product.device.server.mapper.Neo4jModelRepository;
//import com.nti56.nlink.product.device.server.mapper.ThingModelMapper;
//import com.nti56.nlink.product.device.server.model.DeviceDto;
//import com.nti56.nlink.product.device.server.model.neo4j.Neo4jDeviceDTO;
//import com.nti56.nlink.product.device.server.model.neo4j.Neo4jModelDTO;
//import com.nti56.nlink.product.device.server.model.thingModel.dto.*;
//import com.nti56.nlink.product.device.server.service.IDeviceServiceService;
//import com.nti56.nlink.product.device.server.service.IModelGraphService;
//import com.nti56.nlink.product.device.server.service.ISubscriptionService;
//import com.nti56.nlink.product.device.server.service.IThingServiceService;
//import lombok.extern.slf4j.Slf4j;
//import org.jetbrains.annotations.NotNull;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.*;
//
///**
// * 类说明：
// *
// * @ClassName ModelGraphServiceImpl
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/11/22 14:49
// * @Version 1.0
// */
//@Service
//@Slf4j
//public class ModelGraphServiceImpl implements IModelGraphService {
//
//    @Resource
//    ThingModelMapper thingModelMapper;
//    @Resource
//    DeviceMapper deviceMapper;
//    @Resource
//    Neo4jDeviceRepository neo4jDeviceRepository;
//    @Resource
//    Neo4jModelRepository neo4jModelRepository;
//
//    @Autowired
//    IThingServiceService thingServiceService;
//
//    @Autowired
//    ISubscriptionService subscriptionService;
//    @Autowired
//    IDeviceServiceService deviceServiceService;
//
//    @Override
//    public Result<Void> saveModelNodeDeprecated(ThingModelEntity modelEntity, List<Long> inheritIds) {
//        try{
//            Neo4jModelDTO modelNode = Neo4jModelDTO.builder()
//                    .description(modelEntity.getDescript())
//                    .modelId(modelEntity.getId())
//                    .updateTime(modelEntity.getUpdateTime().toString())
//                    .modelType(modelEntity.getModelType())
//                    .name(modelEntity.getName())
//                    .tenantId(modelEntity.getTenantId())
//                    .build();
//
//            /*if(CollectionUtil.isNotEmpty(inheritIds)){
//                List<ThingModelEntity> thingModelEntities = thingModelMapper.selectBatchIds(inheritIds);
//                Set<Neo4jModelDTO> inherits = new HashSet<>(thingModelEntities.size());
//                for (ThingModelEntity thingModelEntity : thingModelEntities) {
//                    Neo4jModelDTO inheritModel = Neo4jModelDTO.builder()
//                            .description(thingModelEntity.getDescript())
//                            .modelId(thingModelEntity.getId())
//                            .updateTime(thingModelEntity.getUpdateTime().toString())
//                            .modelType(thingModelEntity.getModelType())
//                            .name(thingModelEntity.getName())
//                            .tenantId(thingModelEntity.getTenantId())
//                            .build();
//                    inherits.add(inheritModel);
//                }
//                modelNode.setInheritModels(inherits);
//            }*/
//            neo4jModelRepository.save(modelNode);
//            if(CollectionUtil.isNotEmpty(inheritIds)){
//                for (Long inheritId : inheritIds) {
//                    neo4jModelRepository.addModelInheritRelation(modelEntity.getId(),inheritId);
//                }
//            }
//        }catch (Exception e){
//            log.error("save model graph node error.error msg:{}",e.getMessage());
//            e.printStackTrace();
//            return Result.error("插入模型图信息失败，失败原因："+e.getMessage());
//        }
//        return Result.ok();
//    }
//
//    @Override
//    public Result<Void> updateModelNodeDeprecated(ThingModelEntity modelEntity, List<Long> inheritIds) {
//        try{
////            neo4jModelRepository.deleteById(modelEntity.getId());
//            org.neo4j.ogm.model.Result deleteResult = neo4jModelRepository.deleteInhertedInRelationById(modelEntity.getId());
//            this.saveModelNodeDeprecated(modelEntity,inheritIds);
//        }catch (Exception e){
//            log.error("update model graph node error.error msg:{}",e.getMessage());
//            e.printStackTrace();
//            return Result.error("更新模型图信息失败，失败原因："+e.getMessage());
//        }
//        return Result.ok();
//    }
//
//    @Override
//    public Result<Void> deleteModelNodeDeprecated(Long modelId) {
//        try{
//            neo4jModelRepository.deleteById(modelId);
//        }catch (Exception e){
//            log.error("delete model graph node error.error msg:{}",e.getMessage());
//            return Result.error();
//        }
//        return Result.ok();
//    }
//
//    @Override
//    public Result<Object> queryWhichInheritMeByModelIdDeprecated(Long modelId) {
//        org.neo4j.ogm.model.Result result = neo4jModelRepository.queryWhichInheritMe(modelId);
//        return getResult(result, "查不到模型被引用关系信息");
//    }
//
//    @Override
//    public Result<Object> queryInheritByModelIdDeprecated(Long modelId) {
//        org.neo4j.ogm.model.Result result = neo4jModelRepository.queryModelInherit(modelId);
//        return getResult(result, "查不到模型引用关系信息");
//    }
//
//    @NotNull
//    private Result<Object> getResult(org.neo4j.ogm.model.Result result, String msg) {
//        Iterator<Map<String, Object>> iterator = result.iterator();
//        Map<String, Object> next = iterator.next();
//        if(!Objects.isNull(next) && !next.isEmpty()){
//            return Result.ok(JSONUtil.parseObj(next.get("value")));
//        }
//        return Result.error(msg);
//    }
//
//    @Override
//    public Result<Object> queryWhichInheritMeByNameDeprecated(String name, Long tenantId) {
//        org.neo4j.ogm.model.Result result = neo4jModelRepository.queryWhichInheritMeByName(name,tenantId);
//        return getResult(result, "查不到模型被引用关系信息");
//    }
//
//    @Override
//    public Result<Object> queryInheritByNameDeprecated(String name, Long tenantId) {
//        org.neo4j.ogm.model.Result result = neo4jModelRepository.queryModelInheritByName(name, tenantId);
//        return getResult(result, "查不到模型引用关系信息");
//    }
//
//    @Override
//    public Result<Void> saveDeviceNodeDeprecated(DeviceDto deviceDto, List<Long> inheritIds) {
//
//        try{
//            Neo4jDeviceDTO deviceDTO = Neo4jDeviceDTO.builder()
//                    .description(deviceDto.getDescript())
//                    .deviceId(deviceDto.getId())
//                    .updateTime(deviceDto.getUpdateTime().toString())
//                    .name(deviceDto.getName())
//                    .tenantId(deviceDto.getTenantId())
//                    .gatewayId(deviceDto.getEdgeGatewayId())
//                    .build();
//            neo4jDeviceRepository.save(deviceDTO);
//            if(CollectionUtil.isNotEmpty(inheritIds)){
//                for (Long inheritId : inheritIds) {
//
//                    neo4jDeviceRepository.addDeviceInheritRelation(deviceDTO.getDeviceId(),inheritId);
//                }
//                /*List<ThingModelEntity> thingModelEntities = thingModelMapper.selectBatchIds(inheritIds);
//                Set<Neo4jModelDTO> inherits = new HashSet<>(thingModelEntities.size());
//                for (ThingModelEntity thingModelEntity : thingModelEntities) {
//                    Neo4jModelDTO inheritModel = Neo4jModelDTO.builder()
//                            .description(thingModelEntity.getDescript())
//                            .modelId(thingModelEntity.getId())
//                            .updateTime(thingModelEntity.getUpdateTime().toString())
//                            .modelType(thingModelEntity.getModelType())
//                            .name(thingModelEntity.getName())
//                            .tenantId(thingModelEntity.getTenantId())
//                            .build();
//                    inherits.add(inheritModel);
//                }
//                deviceDTO.setInheritModels(inherits);*/
//            }
//
//        }catch (Exception e){
//            log.error("save device graph node error.error msg:{}",e.getMessage());
//            e.printStackTrace();
//            return Result.error("插入设备图信息失败，失败原因："+e.getMessage());
//        }
//        return Result.ok();
//    }
//
//    @Override
//    public Result<Void> updateDeviceNodeDeprecated(DeviceDto deviceDto, List<Long> inheritIds) {
//        try{
//            org.neo4j.ogm.model.Result deleteResult = neo4jDeviceRepository.deleteInhertedInRelationById(deviceDto.getId());
//            this.saveDeviceNodeDeprecated(deviceDto,inheritIds);
//        }catch (Exception e){
//            log.error("update device graph node error.error msg:{}",e.getMessage());
//            e.printStackTrace();
//            return Result.error("更新设备图信息失败，失败原因："+e.getMessage());
//        }
//        return Result.ok();
//    }
//
//    @Override
//    public Result<Void> deleteDeviceNodeDeprecated(Long deviceId) {
//
//        try{
//            neo4jDeviceRepository.deleteById(deviceId);
//        }catch (Exception e){
//            log.error("delete device graph node error.error msg:{}",e.getMessage());
//            return Result.error();
//        }
//        return Result.ok();
//    }
//
//    @Override
//    public Result<Object> queryInheritByDeviceIdDeprecated(Long deviceId) {
//
//        org.neo4j.ogm.model.Result result = neo4jDeviceRepository.queryDeviceInherit(deviceId);
//        return getResult(result, "查不到设备引用关系信息");
//    }
//
//    @Override
//    public Result<Object> queryInheritByDeviceNameDeprecated(String deviceName, Long tenantId) {
//
//        org.neo4j.ogm.model.Result result = neo4jDeviceRepository.queryDeviceInheritByName(deviceName, tenantId);
//        return getResult(result, "查不到设备引用关系信息");
//    }
//
//    @Override
//    public Result<DeviceModelPreviewDTO> getModelPreviewDeprecated(Long id) {
//
//        //获取当前模型继承的所有模型id
//        org.neo4j.ogm.model.Result result = neo4jModelRepository.collectorInheritModelId(id);
//        Iterator<Map<String, Object>> iterator = result.iterator();
//        Map<String, Object> next = iterator.next();
//        Set<Long> modelIds = new LinkedHashSet<>();
////
//        if(MapUtil.isNotEmpty(next)){
//            next.forEach((k,v)->{
//                List<Long> ids = JSONUtil.toList(JSONUtil.toJsonStr(v), Long.class);
//                modelIds.addAll(ids);
//            });
//        }
////        modelIds.add(id);
//        Set<PropertyPreviewDTO> props = Sets.newHashSet();
//        Set<EventPreviewDTO> events = Sets.newHashSet();
//        Set<ServicePreviewDTO> services = Sets.newHashSet();
//        Set<SubscribePreviewDTO> subscribs = Sets.newHashSet();
//        getPreviewDetail(modelIds, props, events, services, subscribs);
//        DeviceModelPreviewDTO deviceModelPreviewDTO = DeviceModelPreviewDTO.builder().services(services)
//                .props(props)
//                .events(events)
//                .subscribes(subscribs)
//                .build();
//
//        // 设置自己的属性
//        ThingModelEntity thingModelEntity = thingModelMapper.selectById(id);
//        deviceModelPreviewDTO.setName(thingModelEntity.getName());
//        deviceModelPreviewDTO.setDescript(thingModelEntity.getDescript());
//        ModelTypeEnum modelTypeEnum = ModelTypeEnum.typeOfValue(thingModelEntity.getModelType());
//        deviceModelPreviewDTO.setType(modelTypeEnum != null ? modelTypeEnum.getName():" ");
//        getPreviewDetail(Sets.newHashSet(id),props, events, services, subscribs);
//        return Result.ok(deviceModelPreviewDTO);
//    }
//
//    private void getPreviewDetail(Set<Long> modelIds, Set<PropertyPreviewDTO> props, Set<EventPreviewDTO> events, Set<ServicePreviewDTO> services, Set<SubscribePreviewDTO> subscribs) {
//        if(CollectionUtil.isEmpty(modelIds)) return;
//        List<ThingModelEntity> thingModelEntities = thingModelMapper.selectBatchIds(modelIds);
//        if(CollectionUtil.isNotEmpty(thingModelEntities)){
//            for (ThingModelEntity thingModelEntity : thingModelEntities) {
//
//
//                //查找模型带有的订阅
//                SubscriptionEntity subscriptionEntity = new SubscriptionEntity();
//                subscriptionEntity.setDirectlyModelId(thingModelEntity.getId());
//                subscriptionEntity.setDeleted(false);
//                Result<List<SubscriptionEntity>> subscriptionResult = subscriptionService.list(subscriptionEntity);
//                if(subscriptionResult.getSignal()){
//                    List<SubscriptionEntity> subscriptions = subscriptionResult.getResult();
//                    if(CollectionUtil.isNotEmpty(subscriptions)){
//                        for (SubscriptionEntity subscription : subscriptions) {
//                            EventTypeEnum eventTypeEnum = EventTypeEnum.typeOfValue(subscription.getEventType());
//                            SubscribePreviewDTO tSubscription = SubscribePreviewDTO.builder().type(eventTypeEnum != null ? eventTypeEnum.getName():" ")
//                                    .name(subscription.getName())
//                                    .descript(subscription.getDescript())
//                                    .build();
//                            subscribs.add(tSubscription);
//                        }
//                    }
//                }
//
//                //查找模型带有的服务
//                ThingServiceEntity thingServiceEntity = new ThingServiceEntity();
//                thingServiceEntity.setThingModelId(thingModelEntity.getId());
//                thingServiceEntity.setDeleted(0);
//                Result<List<ThingServiceEntity>> serviceResult = thingServiceService.list(thingServiceEntity);
//                if(serviceResult.getSignal()){
//                    List<ThingServiceEntity> thingServices = serviceResult.getResult();
//                    if(CollectionUtil.isNotEmpty(thingServices)){
//                        for (ThingServiceEntity thingService : thingServices) {
//                            ServicePreviewDTO tService = ServicePreviewDTO.builder().override(thingService.getOverride())
//                                    .name(thingService.getServiceName())
//                                    .descript(thingService.getDescript())
//                                    .build();
//                            services.add(tService);
//                        }
//                    }
//                }
//                ModelField model = thingModelEntity.getModel();
//                if(Objects.isNull(model)) continue;
//                List<PropertyElm> properties = model.getProperties();
//                properties.forEach(prop->{
//                    PropertyPreviewDTO tProp = PropertyPreviewDTO.builder().name(prop.getName())
//                            .descript(prop.getDescript())
//                            .type(prop.getDataType().getType())
//                            .build();
//                    props.add(tProp);
//                });
//                List<EventElm> modelEvents = model.getEvents();
//                if(CollectionUtil.isNotEmpty(modelEvents)){
//                    modelEvents.forEach(eventElm -> {
//                        EventPreviewDTO tEvent = EventPreviewDTO.builder().name(eventElm.getName())
//                                .descript(eventElm.getDescript())
//                                .type(eventElm.getType())
//                                .build();
//                        events.add(tEvent);
//                    });
//                }
//
//            }
//        }
//    }
//
//    @Override
//    public Result<DeviceModelPreviewDTO> getDevicePreviewDeprecated(Long id) {
//
//        org.neo4j.ogm.model.Result result = neo4jDeviceRepository.collectorInheritModelId(id);
//        Iterator<Map<String, Object>> iterator = result.iterator();
//        Map<String, Object> next = iterator.next();
//        Set<Long> modelIds = new LinkedHashSet<>();
////
//        if(MapUtil.isNotEmpty(next)){
//            next.forEach((k,v)->{
//                List<Long> ids = JSONUtil.toList(JSONUtil.toJsonStr(v), Long.class);
//                modelIds.addAll(ids);
//            });
//        }
//        Set<PropertyPreviewDTO> props = Sets.newHashSet();
//        Set<EventPreviewDTO> events = Sets.newHashSet();
//        Set<ServicePreviewDTO> services = Sets.newHashSet();
//        Set<SubscribePreviewDTO> subscribs = Sets.newHashSet();
//        getPreviewDetail(modelIds, props, events, services, subscribs);
//        DeviceModelPreviewDTO deviceModelPreviewDTO = DeviceModelPreviewDTO.builder().services(services)
//                .props(props)
//                .events(events)
//                .subscribes(subscribs)
//                .build();
//        DeviceEntity deviceEntity = deviceMapper.selectById(id);
//        deviceModelPreviewDTO.setName(deviceEntity.getName());
//        deviceModelPreviewDTO.setDescript(deviceEntity.getDescript());
//        deviceModelPreviewDTO.setType(" ");
//        //设置自己属性
//        ModelField model = deviceEntity.getModel();
//        if(!Objects.isNull(model)){
//            List<EventElm> eventElms = model.getEvents();
//            if(CollectionUtil.isNotEmpty(eventElms)){
//                eventElms.forEach(eventElm -> {
//                    EventPreviewDTO previewDTO = EventPreviewDTO.builder()
//                            .name(eventElm.getName())
//                            .descript(eventElm.getDescript())
//                            .type(eventElm.getType())
//                            .build();
//                    events.add(previewDTO);
//                });
//            }
//            List<PropertyElm> properties = model.getProperties();
//            for (PropertyElm propertyElm : properties) {
//                PropertyPreviewDTO propertyPreviewDTO = PropertyPreviewDTO.builder()
//                        .type(propertyElm.getDataType() != null ? propertyElm.getDataType().getType():"")
//                        .name(propertyElm.getName())
//                        .descript(propertyElm.getDescript())
//                        .build();
//                props.add(propertyPreviewDTO);
//            }
//        }
//        //查找设备自身服务
//        DeviceServiceEntity queryEntity = new DeviceServiceEntity();
//        queryEntity.setDeviceId(id);
//        queryEntity.setDeleted(0);
//        Result<List<DeviceServiceEntity>> list = deviceServiceService.list(queryEntity);
//        if(list.getSignal()){
//            List<DeviceServiceEntity> deviceServiceEntities = list.getResult();
//            for (DeviceServiceEntity deviceServiceEntity : deviceServiceEntities) {
//                ServicePreviewDTO tService = ServicePreviewDTO.builder().override(deviceServiceEntity.getOverride())
//                        .name(deviceServiceEntity.getServiceName())
//                        .descript(deviceServiceEntity.getDescript())
//                        .build();
//                services.add(tService);
//            }
//        }
//        return Result.ok(deviceModelPreviewDTO);
//
//    }
//
//    @Override
//    public Result<Object> queryWhichInheritMeByDeviceIdDeprecated(Long id) {
//        org.neo4j.ogm.model.Result result = neo4jDeviceRepository.queryWhichInheritMeByDeviceId(id);
//        return getResult(result, "查不到设备引用关系信息");
//    }
//
//    @Override
//    public Result<Void> deleteModelAndDeviceGraphDeprecated(Long tenantId) {
//        neo4jDeviceRepository.deleteByTenantId(tenantId);
//        neo4jModelRepository.deleteByTenantId(tenantId);
//        return Result.ok();
//    }
//
//    @Override
//    public Result<Void> addModelRelationDeprecated(Long id, Long thingModelId) {
//        neo4jModelRepository.addModelInheritRelation(id,thingModelId);
//        return Result.ok();
//    }
//
//    @Override @Async
//    public Result<Void> batchDeleteDeviceNodeDeprecated(List<Neo4jDeviceDTO> nodes) {
//        try{
//            neo4jDeviceRepository.deleteAll(nodes);
//        }catch (Exception e){
//            log.error("delete device graph node error.error msg:{}",e.getMessage());
//            throw new BizException(Result.error(e.getMessage()));
//        }
//        return Result.ok();
//    }
//}
