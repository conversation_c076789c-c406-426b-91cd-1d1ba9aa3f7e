package com.nti56.nlink.product.device.server.model;

import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import lombok.Data;


/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-09 11:33:49
 * @since JDK 1.8
 */
@Data
public class DeviceBo extends DeviceEntity {

    private ModelField thingModelModel;

    private Long thingModelId;
    
}
