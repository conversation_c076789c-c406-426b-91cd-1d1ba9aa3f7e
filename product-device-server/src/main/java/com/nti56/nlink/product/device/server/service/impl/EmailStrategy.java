package com.nti56.nlink.product.device.server.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.send.NotifyDTO;
import com.nti56.nlink.product.device.server.constant.EmailConstant;
import com.nti56.nlink.product.device.server.constant.NotifyConstant;
import com.nti56.nlink.product.device.server.entity.NotifyChannelEntity;
import com.nti56.nlink.product.device.server.entity.NotifyServerLogEntity;
import com.nti56.nlink.product.device.server.entity.TemplateEntity;
import com.nti56.nlink.product.device.server.enums.NotifyServerLogEnum;
import com.nti56.nlink.product.device.server.enums.error.NotifyErrorEnum;
import com.nti56.nlink.product.device.server.service.INotifyServerLogService;
import com.nti56.nlink.product.device.server.service.NotifyStrategy;
import com.nti56.nlink.product.device.server.util.RSAUtils;
import com.sun.mail.util.MailSSLSocketFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.mail.Address;
import javax.mail.Message;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Properties;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/22 21:27<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class EmailStrategy extends NotifyStrategy {

    @Autowired
    private INotifyServerLogService logService;

    @Override
    public Result sendNotify(JSONObject channelParams, NotifyDTO dto, TemplateEntity templateEntity, String content, TenantIsolation tenantIsolation) {
        JSONObject requestJson = new JSONObject();
        requestJson.put("channelParams", channelParams);
        requestJson.put("notifyDTO", dto);
        requestJson.put("templateEntity", templateEntity);
        requestJson.put("content", content);
        String requestParams = requestJson.toJSONString();

        String receivers = dto.getReceivers();
        if (StringUtils.isBlank(receivers)) {
            log.error("无接收人消息。requestParams:" + requestParams);
            logService.createLog(NotifyServerLogEnum.NONE_RECEIVER_INFO_ERROR, requestParams, tenantIsolation);
            return Result.error(NotifyErrorEnum.NONE_RECEIVER_INFO_ERROR.getCode(), NotifyErrorEnum.NONE_RECEIVER_INFO_ERROR.getMessage());
        }

        String[] receiverArr = receivers.split(",");

        for (String receiver : receiverArr) {
            if (!receiver.matches(NotifyConstant.EMAIL_REGEX)) {
                log.error("请求参数异常，requestParams：" + requestParams);
                logService.createLog(NotifyServerLogEnum.RECEIVER_FORMAT_ERROR, requestParams, tenantIsolation);
                return Result.error(NotifyErrorEnum.RECEIVER_FORMAT_ERROR.getCode(), NotifyErrorEnum.RECEIVER_FORMAT_ERROR.getMessage());
            }
        }


        String userName = channelParams.getString(EmailConstant.USER_NAME);
        String password = channelParams.getString(EmailConstant.PASSWORD);
        try {
            password = RSAUtils.decryptByPrivateKey(password, NotifyConstant.PRIVATE_KEY);
        } catch (Exception e) {
            log.error("密码解密失败导致邮件发送失败。requestParams:" + requestParams, e);
            logService.createErrorLog(NotifyServerLogEnum.SEND_NOTIFY_FAIL, requestParams, e.toString(), tenantIsolation,dto.getSubscriptionId());
            return Result.error(NotifyErrorEnum.SEND_EMAIL_FAIL.getCode(), NotifyErrorEnum.SEND_EMAIL_FAIL.getMessage());
        }

        String host = channelParams.getString(EmailConstant.HOST);
        try {
            Session mailSession = this.getMailSession(host, channelParams.getString(EmailConstant.PROTOCOL));
            this.sendEmail(mailSession, dto.getReceivers(), templateEntity.getTitle(), content, host, userName, password);
        } catch (Exception e) {
            log.error("发送邮件失败。requestParams:" + requestParams, e);
            logService.createErrorLog(NotifyServerLogEnum.SEND_NOTIFY_FAIL, requestParams, e.toString(), tenantIsolation,dto.getSubscriptionId());
            return Result.error(NotifyErrorEnum.SEND_EMAIL_FAIL.getCode(), NotifyErrorEnum.SEND_EMAIL_FAIL.getMessage());
        }
        logService.createLog(NotifyServerLogEnum.SEND_NOTIFY_SUCCESS, requestParams, tenantIsolation);
        return Result.ok();
    }

    private void sendEmail(Session mailSession, String receivers, String title, String content, String host, String userName, String password) throws Exception {
        Message msg = new MimeMessage(mailSession);
        msg.setSubject(title);
        msg.setText(content);
        msg.setFrom(new InternetAddress(userName));

        Transport transport = mailSession.getTransport();
        transport.connect(host, userName, password);
        String[] receiverArr = receivers.split(NotifyConstant.COMMA);

        Address[] addresses = new InternetAddress[receiverArr.length];

        for (int i = 0; i < receiverArr.length; i++) {
            addresses[i] = new InternetAddress(receiverArr[i]);
        }
        transport.sendMessage(msg, addresses);
        transport.close();
    }

    @Override
    public Result validationChannel(NotifyChannelEntity notifyChannelEntity) {
        JSONObject channelParams = JSONObject.parseObject(notifyChannelEntity.getParams());

        String userName = channelParams.getString(EmailConstant.USER_NAME);
        String password = channelParams.getString(EmailConstant.PASSWORD);
        try {
            password = RSAUtils.decryptByPrivateKey(password, NotifyConstant.PRIVATE_KEY);
        } catch (Exception e) {
            log.error("密码解密失败导致验证通道失败", e);
            return Result.error(NotifyErrorEnum.CONNECTION_EMAIL_FAIL.getCode(), NotifyErrorEnum.CONNECTION_EMAIL_FAIL.getMessage());
        }
        String host = channelParams.getString(EmailConstant.HOST);

        try {
            Transport transport = this.getMailSession(host, channelParams.getString(EmailConstant.PROTOCOL)).getTransport();
            transport.connect(host, userName, password);
            transport.close();
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(NotifyErrorEnum.CONNECTION_EMAIL_FAIL.getCode(), NotifyErrorEnum.CONNECTION_EMAIL_FAIL.getMessage());
        }
    }

    private Session getMailSession(String host, String protocol) throws Exception {
        MailSSLSocketFactory sf = new MailSSLSocketFactory();
        sf.setTrustAllHosts(true);
        Properties props = new Properties();
        // 开启debug调试
        props.setProperty("mail.debug", "true");
        // 发送服务器需要身份验证
        props.setProperty("mail.smtp.auth", "true");
        // 设置邮件服务器主机名
        props.setProperty("mail.host", host);
        // 发送邮件协议名称
        props.setProperty("mail.transport.protocol", protocol);
        props.put("mail.smtp.ssl.enable", "true");
        props.put("mail.smtp.ssl.socketFactory", sf);
        return Session.getInstance(props);
    }

    @Override
    public void retrySendFailNotify(JSONObject channelParams, NotifyServerLogEntity notifyServerLogEntity, TemplateEntity templateEntity, JSONObject requestJson, TenantIsolation tenantIsolation) {
        String requestParams = notifyServerLogEntity.getRequestParams();
        Long sourceId = notifyServerLogEntity.getSourceId();

        String userName = channelParams.getString(EmailConstant.USER_NAME);
        String password = channelParams.getString(EmailConstant.PASSWORD);
        try {
            password = RSAUtils.decryptByPrivateKey(password, NotifyConstant.PRIVATE_KEY);
        } catch (Exception e) {
            log.error("密码解密失败导致发送邮件失败。requestParams:" + requestParams, e);
            logService.createErrorLog(sourceId, NotifyServerLogEnum.SEND_NOTIFY_FAIL, requestParams, e.toString(), tenantIsolation, notifyServerLogEntity.getSubscriptionId());
            return;
        }
        String host = channelParams.getString(EmailConstant.HOST);


        try {
            Session mailSession = this.getMailSession(host, channelParams.getString(EmailConstant.PROTOCOL));
            this.sendEmail(mailSession, requestJson.getObject("notifyDTO", NotifyDTO.class).getReceivers()
                    , templateEntity.getTitle(), requestJson.getString("content"), host, userName, password);
        } catch (Exception e) {
            log.error("发送邮件失败。requestParams:" + requestParams, e);
            logService.createErrorLog(sourceId, NotifyServerLogEnum.SEND_NOTIFY_FAIL, requestParams, e.toString(), tenantIsolation, notifyServerLogEntity.getSubscriptionId());
            return;
        }
        logService.createLog(sourceId, NotifyServerLogEnum.SEND_NOTIFY_SUCCESS, requestParams, tenantIsolation);
    }


}
