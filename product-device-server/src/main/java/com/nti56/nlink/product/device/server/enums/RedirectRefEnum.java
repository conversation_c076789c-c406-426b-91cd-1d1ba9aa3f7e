package com.nti56.nlink.product.device.server.enums;

public enum RedirectRefEnum {

    DEVICE(1,"设备"),
    MODEL(2,"模型"),
    CHANGE_NOTICE(3,"变动通知"),

    ;

    RedirectRefEnum(int type,String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    private int type;
    private String typeName;

    public int getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }
}
