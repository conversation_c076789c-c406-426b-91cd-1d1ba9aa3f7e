package com.nti56.nlink.product.device.server.enums;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/12/6 11:12<br/>
 * @since JDK 1.8
 */
public enum ErrorEnum {

    MENU_CODE_UNIQUE_NAME(700,"已存在该菜单编码","已存在该菜单编码"),
    MENU_USER_ROLE_NOT_EXIST(701,"该租户下用户角色不存在","该租户下用户角色不存在"),
    MENU_USER_INFO_NOT_EXIST(702,"查询用户信息失败","查询用户信息失败"),
    MENU_ROLE_NAME_REPEAT(703,"角色名称重复","角色名称重复"),
    ROLE_EDIT_ERROR(704,"租户管理员不能编辑自己","租户管理员不能编辑自己"),
    ROLE_USER_EXIST_ERROR(705,"删除失败，该角色下存在用户","\"删除失败，该角色下存在用户"),
    NO_AUTH(80001,"登录超时,请重新登录！"),
    BACKEND_COMEXCEPTION(40001,"网关与鉴权服务通讯异常"),
    SIGNATURE_ERROR(40002,"签名错误"),
    NO_WHITE_LIST(40003,"远程地址不在白名单中"),
    KEY_EXPIRED(40004,"密钥已过期"),
    ABNORMAL_PERMISSION(49999,"权限异常"),
    UNKNOW_TENANT(49998,"未知租户");
    ;

    private Integer code;
    private String message;
    private String describe;

    private ErrorEnum(Integer code, String message, String describe) {
        this.code = code;
        this.message = message;
        this.describe = describe;
    }
    private ErrorEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
        this.describe = message;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public String getDescribe() {
        return this.describe;
    }
}
