package com.nti56.nlink.product.device.server.openapi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.it.ITResult;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.InstanceRedirectEntity;
import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import com.nti56.nlink.product.device.server.model.changeNotice.dto.QueryChangeNoticeDTO;
import com.nti56.nlink.product.device.server.model.changeNotice.vo.ChangeNoticeVO;
import com.nti56.nlink.product.device.server.openapi.convertor.ITResultConvertor;
import com.nti56.nlink.product.device.server.service.IChangeNoticeService;
import com.nti56.nlink.product.device.server.service.IInstanceRedirectService;
import com.nti56.nlink.product.device.server.service.IThingModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/it/open/")
@Tag(name = "开放能力")
public class ITOpenController {

    @Autowired
    private IChangeNoticeService changeNoticeService;
    @Autowired
    private IInstanceRedirectService instanceRedirectService;


    @PostMapping(path = "list")
    @Operation(summary = "获取变动通知列表")
    public ITResult<List<ThingModelEntity>> listThingModels(@RequestHeader("ot_headers") TenantIsolation tenantIsolation) {

        QueryChangeNoticeDTO queryChangeNoticeDTO = new QueryChangeNoticeDTO();
        Result<List<ChangeNoticeVO>> listResult = changeNoticeService.listChangeNotice(queryChangeNoticeDTO, tenantIsolation);
        return ITResultConvertor.convert(R.result(Result.ok(listResult)));
    }

    @PostMapping(path = "redirect/info")
    @Operation(summary = "获取回调信息")
    public ITResult<InstanceRedirectEntity> listThingModels(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestParam("id") Long id) {
        Result<InstanceRedirectEntity> result = instanceRedirectService.getByIdAndTenantIsolation(id, tenantIsolation);
        return ITResultConvertor.convert(R.result(Result.ok(result)));
    }
}
