package com.nti56.nlink.product.device.server.model.device.dto;

import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 类说明：fault事件触发实例
 *
 * @ClassName FaultEventInstance
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/10/20 13:24
 * @Version 1.0
 */

@Data
public class FaultEventInstance implements Serializable {


    private String eventName;
    private String eventDesc;
    private Long continueMillSecond;
    private Long eventStartTime;
    private Long eventEndTime;
    private String triggerCondition;
    private UpData upData;
    /**
     * 调用物服务的ID
     */
    private Long thingServiceId;

    private Integer faultLevel;

    private Map<String,String> faultLevelMap;
}
