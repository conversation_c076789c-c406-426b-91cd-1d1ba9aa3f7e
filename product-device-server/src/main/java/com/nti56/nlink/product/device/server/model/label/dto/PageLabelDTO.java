package com.nti56.nlink.product.device.server.model.label.dto;

import com.nti56.nlink.common.util.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/7/4 17:05<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "标签分页dto")
public class PageLabelDTO {
    @Schema(description = "搜索词")
    private String searchStr;

    @Schema(description = "数据类型")
    private String dataType;

    @Schema(description = "分组id")
    private Long labelGroupId;

    @Schema(description = "通道id")
    private Long channelId;

    @Schema(description = "分组名称")
    private String labelGroupName;

    @Schema(description = "分页参数")
    @NotNull(message = "分页参数不能为空")
    private PageParam pageParam;

    private List<Long> labelGroupIds;

    @Schema(description = "排序列(name、address、create_time、data_type、is_array、read_only)")
    private String sortColumn = "name";

    @Schema(description = "排序类型:0升序，1降序")
    private String sortType = "0";
}
