package com.nti56.nlink.product.device.server.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 类说明：
 *
 * @ClassName IdHashUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/6/7 11:52
 * @Version 1.0
 */

public class IdHashUtil {

    public static int hashMapping(long userId) {
        // 选择哈希函数（这里使用 MD5 哈希函数）
        MessageDigest md;
        try {
            md = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return -1;
        }

        // 计算哈希值
        byte[] hashBytes = md.digest(String.valueOf(userId).getBytes());

        // 范围映射
        int rangeStart = 1;
        int rangeEnd = 1000000;
        int mappedValue = (Math.abs(bytesToInt(hashBytes)) % (rangeEnd - rangeStart + 1)) + rangeStart;

        return mappedValue;
    }

    private static int bytesToInt(byte[] bytes) {
        int result = 0;
        for (int i = 0; i < bytes.length; i++) {
            result <<= 8;
            result |= (bytes[i] & 0xFF);
        }
        return result;
    }

    public static void main(String[] args) {
        // 测试哈希映射函数
        long userId = 1360394920787968L;
        int mappedValue = hashMapping(userId);
        System.out.println("User ID: " + userId);
        System.out.println("Mapped Value: " + mappedValue);
    }

}
