package com.nti56.nlink.product.device.server.model.label;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/03/24 15:12<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "标签分组复制请求体")
public class CopyLabelGroupReq {

    private List<Long> targetLabelGroupIds;

    private List<Long> targetChannelIds;

    @NotNull(message = "参数异常")
    private Long sourceLabelGroupId;

}
