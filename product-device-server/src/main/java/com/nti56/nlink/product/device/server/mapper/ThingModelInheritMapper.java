package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.ThingModelInheritEntity;
import com.nti56.nlink.product.device.server.model.ThingModelInheritBo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 物模型继承表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13 15:37:55
 */
@Mapper
public interface ThingModelInheritMapper extends CommonMapper<ThingModelInheritEntity> {

    @Select("SELECT * FROM thing_model_inherit WHERE tenant_id = #{tenantId} AND deleted = 0 AND thing_model_id = #{thingModelId}")
    List<ThingModelInheritEntity> listByThingModelId(@Param("tenantId") Long tenantId, @Param("thingModelId") Long thingModelId);

    @Delete("DELETE FROM thing_model_inherit WHERE tenant_id = #{tenantId} AND thing_model_id = #{thingModelId}")
    Integer deleteByThingModelId(@Param("tenantId") Long tenantId,   
                                @Param("thingModelId") Long thingModelId);

    //获取入参物模型id继承了的物模型名称及id
    List<ThingModelInheritBo> listInheritBoByThingModelId(@Param("tenantId") Long tenantId, @Param("thingModelId") Long thingModelId);

    //获取入参物模型id继承了的物模型id
    List<Long> listInheritIdByThingModelId(@Param("tenantId") Long tenantId, @Param("thingModelId") Long thingModelId);

    //获取入参物模型id被哪些模型继承名称及id
    List<ThingModelInheritBo> listBeInheritBoByThingModelId(@Param("tenantId") Long tenantId,   
                                                            @Param("thingModelId") Long thingModelId);

    @Delete("DELETE FROM thing_model_inherit WHERE tenant_id = #{tenantId)  AND id = #{id}")
    int deleteById(@Param("tenantId") Long tenantId,   
                    @Param("id") Long id);

    @Select("SELECT * FROM thing_model_inherit WHERE tenant_id = #{tenantId) AND i.deleted = 0 AND id = #{id}")
    ThingModelInheritEntity getById(@Param("tenantId") Long tenantId,   
                                @Param("id") Long id);

}
