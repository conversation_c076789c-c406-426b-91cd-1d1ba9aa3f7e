package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.ServiceConfigEntity;
import com.nti56.nlink.product.device.server.mapper.ServiceConfigMapper;
import com.nti56.nlink.product.device.server.model.ServiceConfigDTO;
import com.nti56.nlink.product.device.server.service.IServiceConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【service_config(通用物服务配置表)】的数据库操作Service实现
 * @createDate 2023-03-06 14:51:50
 */
@Service
public class ServiceConfigServiceImpl extends BaseServiceImpl<ServiceConfigMapper, ServiceConfigEntity> implements IServiceConfigService {

    @Resource
    private ServiceConfigMapper serviceConfigMapper;

    @Override
    public Result getConfigByDeviceId(TenantIsolation tenantIsolation, Long serviceId, Long deviceId) {
        if (Objects.isNull(serviceId)) {
            return Result.error("服务id为空！");
        }
        return Result.ok(serviceConfigMapper.selectByServiceIdAndDeviceId(tenantIsolation.getTenantId(), serviceId, deviceId));
    }

    @Override
    public Result getConfigByModelId(TenantIsolation tenantIsolation, Long serviceId, Long modelId) {
        if (Objects.isNull(serviceId)) {
            return Result.error("服务id为空！");
        }
        return Result.ok(serviceConfigMapper.selectByServiceIdAndModel(tenantIsolation.getTenantId(), serviceId, modelId));
    }

    @Override
    public Result saveOrUpdateConfig(ServiceConfigDTO serviceConfig, TenantIsolation tenantIsolation) {

        Long id = serviceConfig.getId();
        if (Objects.isNull(id)) {
            //新增
            ServiceConfigEntity serviceConfigEntity = BeanUtilsIntensifier.copyBean(serviceConfig, ServiceConfigEntity.class);
            serviceConfigEntity.setDeleted(0);
//            serviceConfigEntity.setCreateTime(new Date());
            serviceConfigEntity.setTenantId(tenantIsolation.getTenantId());
            serviceConfigEntity.setEngineeringId(tenantIsolation.getEngineeringId());
            serviceConfigEntity.setModuleId(tenantIsolation.getModuleId());
            serviceConfigEntity.setSpaceId(tenantIsolation.getSpaceId());
            serviceConfigEntity.setConfigMap(JSONUtil.toJsonStr(serviceConfig.getConfigMap()));
            this.save(serviceConfigEntity);
            return Result.ok(serviceConfigEntity);
        } else {
            ServiceConfigEntity byId = this.getById(serviceConfig.getId());
            BeanUtil.copyProperties(serviceConfig, byId);
            this.updateById(byId);
            return Result.ok();
        }

    }

    @Override
    public List<ServiceConfigEntity> queryConfigByServiceIdAndDeviceIds(Long serviceId, List<Long> deviceIds, Long tenantId) {
        return serviceConfigMapper.selectByServiceIdAndDeviceIds(serviceId, deviceIds, tenantId);
    }

    @Override
    public ServiceConfigEntity getConfigByServiceIdAndModelId(Long serviceId, Long modelId, Long tenantId) {
        return serviceConfigMapper.selectByServiceIdAndModel(tenantId, serviceId, modelId);
    }
}
