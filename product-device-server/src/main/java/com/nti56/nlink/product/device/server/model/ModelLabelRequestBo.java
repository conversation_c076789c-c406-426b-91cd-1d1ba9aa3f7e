package com.nti56.nlink.product.device.server.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 设备dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:58
 * @since JDK 1.8
 */
@Data
@Schema(description = "标签请求对象")
public class ModelLabelRequestBo {

    @Schema(description = "选中的标签分组id")
    private List<Long> labelGroupIds;

    @Schema(description = "比较模型id")
    private Long modelId;

    @Schema(description = "标签名称")
    private String name;

}
