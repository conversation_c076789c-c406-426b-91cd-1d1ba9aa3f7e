package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;

/**
 * 类说明: spi测试用
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-07-14 13:32:04
 * @since JDK 1.8
 */
public interface ISpiTestService {

    Result connectChannel(TenantIsolation tenantIsolation);

    Result connectLabel(TenantIsolation tenantIsolation);

    Result writeBool(TenantIsolation tenantIsolation);

    Result writeShort(TenantIsolation tenantIsolation);

    Result writeInt(TenantIsolation tenantIsolation);

    Result writeFloat(TenantIsolation tenantIsolation);

    Result writeString(TenantIsolation tenantIsolation);

    Result multiWrite(TenantIsolation tenantIsolation);

    Result writeByte(TenantIsolation tenantIsolation);

    Result writeBoolArray(TenantIsolation tenantIsolation);

    Result writeShortArray(TenantIsolation tenantIsolation);

    Result writeIntArray(TenantIsolation tenantIsolation);

    Result writeFloatArray(TenantIsolation tenantIsolation);

    Result writeStringArray(TenantIsolation tenantIsolation);

    Result writeByteArray(TenantIsolation tenantIsolation);

    Result writeWordArray(TenantIsolation tenantIsolation);

    Result writeWord(TenantIsolation tenantIsolation);

    Result writeDouble(TenantIsolation tenantIsolation);

    Result writeDoubleArray(TenantIsolation tenantIsolation);

    Result writeDWord(TenantIsolation tenantIsolation);

    Result writeDWordArray(TenantIsolation tenantIsolation);
}
