package com.nti56.nlink.product.device.server.model.deviceTemplate;

import com.nti56.nlink.product.device.server.model.ChannelRespondBo;
import com.nti56.nlink.product.device.server.model.DeviceRequestBo;
import com.nti56.nlink.product.device.server.model.DeviceRespondBo;
import com.nti56.nlink.product.device.server.model.channel.dto.EditChannelDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName DeviceTemplateRequstBo
 * @date 2023/3/9 13:47
 * @Version 1.0
 */
@Data
@Schema(description = "设备模板请求对象")
public class DeviceTemplateRespondBo {

    @Schema(description = "设备列表")
    private List<DeviceRespondBo> deviceList;

    @Schema(description = "通道列表")
    private List<ChannelRespondBo> channelList;


}
