//package com.nti56.nlink.product.device.server.mapper;
//
//import com.nti56.nlink.product.device.server.model.neo4j.Neo4jDeviceDTO;
//import org.neo4j.ogm.model.Result;
//import org.springframework.data.neo4j.annotation.Query;
//import org.springframework.data.neo4j.repository.Neo4jRepository;
//import org.springframework.data.repository.query.Param;
//
///**
// * 类说明：
// *
// * @ClassName Neo4jDeviceRepository
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/11/16 17:49
// * @Version 1.0
// */
//
//public interface Neo4jDeviceRepository extends Neo4jRepository<Neo4jDeviceDTO,Long> {
//
//    @Query(value = "MATCH path = (t:Device {deviceId: $deviceId})-[:INHERIT*0..]->(p:THING_MODEL) WITH collect(path) AS paths CALL apoc.convert.toTree(paths) YIELD value RETURN value;")
//    Result queryDeviceInherit(@Param("deviceId") Long deviceId);
//
//    @Query(value = "MATCH path = (t:Device {name: $deviceName,tenantId: $tenantId})-[:INHERIT*0..]->(p:THING_MODEL) WITH collect(path) AS paths CALL apoc.convert.toTree(paths) YIELD value RETURN value;")
//    Result queryDeviceInheritByName(@Param("deviceName") String deviceName, @Param("tenantId") Long tenantId);
//
//    @Query("MATCH (:Device { deviceId: $id })-[:INHERIT*] -> (mx: THING_MODEL) return collect(mx.modelId)")
//    Result collectorInheritModelId(@Param("id") Long id);
//
//    @Query("MATCH (:Device {deviceId: $id })-[r:INHERIT] -> () DELETE r")
//    Result deleteInhertedInRelationById(@Param("id") Long id);
//    @Query(value = "MATCH path = (t:Device {deviceId: $deviceId})<-[:INHERIT*0..]-(p) WITH collect(path) AS paths CALL apoc.convert.toTree(paths) YIELD value RETURN value;")
//    Result queryWhichInheritMeByDeviceId(@Param("deviceId") Long deviceId);
//
//    @Query("MATCH (n:Device {tenantId: $tenantId}) DETACH DELETE n")
//    Result deleteByTenantId(@Param("tenantId") Long tenantId);
//
//    @Query("MATCH (n:Device {deviceId: $deviceId}), (t:THING_MODEL {modelId: $modelId}) create (n)-[r:INHERIT]->(t) return type(r)")
//    Result addDeviceInheritRelation(@Param("deviceId") Long deviceId, @Param("modelId") Long modelId);
//
//    @Query("MATCH (n:Device) -[:INHERIT*] -> (:THING_MODEL{modelId: $modelId}) return n")
//    Result devicesByInheritModelId(@Param("modelId") Long modelId);
//
//
//}
