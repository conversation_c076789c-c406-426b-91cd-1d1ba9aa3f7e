package com.nti56.nlink.product.device.server.excel.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ReadOnlyConverter
 * @date 2022/9/19 10:15
 * @Version 1.0
 */
public class ReadOnlyConverter implements Converter<Boolean> {

    @Override
    public Class supportJavaTypeKey() {
        return Boolean.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.BOOLEAN;
    }

    /**
     * 导入枚举类型转换
     * @param cellData
     * @param excelContentProperty
     * @param globalConfiguration
     * @return
     * @throws Exception
     */
    @Override
    public Boolean convertToJavaData(ReadCellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if("true".equalsIgnoreCase(cellData.getStringValue()) || "RO".equals(cellData.getStringValue())){
            return  true;
        }else if("false".equalsIgnoreCase(cellData.getStringValue()) || "R/W".equals(cellData.getStringValue())){
            return  false;
        }
        return true;
    }

    /**
     * 导出枚举类型转换
     * @param readOnly
     * @param excelContentProperty
     * @param globalConfiguration
     * @return
     * @throws Exception
     */
    @Override
    public WriteCellData<String> convertToExcelData(Boolean readOnly, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (readOnly) {
            return new WriteCellData("RO");
        }
        return new WriteCellData("R/W");
    }
}
