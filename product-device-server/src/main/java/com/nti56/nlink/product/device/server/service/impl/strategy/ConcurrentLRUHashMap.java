package com.nti56.nlink.product.device.server.service.impl.strategy;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 类说明：
 *
 * @ClassName ConcurrentLRUHashMap
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/9/6 14:58
 * @Version 1.0
 */

public class ConcurrentLRUHashMap<K,V> {

    private int capacity;
    private ConcurrentHashMap<K,V> map;
    private ConcurrentLinkedQueue<K> keyQueue;

    private ReadWriteLock readWriteLock = new ReentrantReadWriteLock();
    private Lock writeLock = readWriteLock.writeLock();
    private Lock readLock = readWriteLock.readLock();

    public ConcurrentLRUHashMap(int capacity) {
        this.capacity = capacity;
        map = new ConcurrentHashMap<>(capacity);
        keyQueue = new ConcurrentLinkedQueue<>();
    }


    public V get(K key) {
        readLock.lock();
        try {
            if (map.containsKey(key)) {
                keyQueue.remove(key);
                keyQueue.add(key);
                return map.get(key);
            }
            return null;
        } finally {
            readLock.unlock();
        }
    }

    public V remove(K key){
        writeLock.lock();
        try{
            if(map.containsKey(key)){
                keyQueue.remove(key);
                return map.remove(key);
            }
        }finally {
            writeLock.unlock();
        }

        return null;
    }

    public V put(K key, V value) {
        writeLock.lock();
        try {
            //1. key存在当前缓存中
            if (map.containsKey(key)) {
                keyQueue.remove(key);
                keyQueue.add(key);
                map.put(key, value);
                return value;
            }
            //超出缓存容量
            if (map.size() == capacity) {
                K oldestKey = keyQueue.poll();
                if (oldestKey != null) {
                    V v = map.get(oldestKey);
                    //释放对象
                    v = null;
                    map.remove(oldestKey);
                }
            }
            keyQueue.add(key);
            map.put(key, value);
            return value;
        } finally {
            writeLock.unlock();
        }
    }

    public boolean containsKey(K redirectId) {
        return map.containsKey(redirectId);
    }
}
