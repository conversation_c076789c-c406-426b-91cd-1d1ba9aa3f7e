package com.nti56.nlink.product.device.server.job.callble;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.service.IJobService;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName JobCallable
 * @date 2022/8/15 11:08
 * @Version 1.0
 */
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class JobCallable implements Callable<Result<Void>> {
    @Setter
    private IJobService jobService;

    @Override
    public Result<Void> call() throws Exception {
        log.info("thread {} exec job :{}", Thread.currentThread().getName(),jobService.getClass().getName());
        return jobService.jobHandler();
    }
}
