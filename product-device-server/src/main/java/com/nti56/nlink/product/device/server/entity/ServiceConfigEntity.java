package com.nti56.nlink.product.device.server.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 通用物服务配置表
 * @TableName service_config
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("service_config")
@Schema(description = "ServiceConfigEntity对象")
public class ServiceConfigEntity implements Serializable {
    /**
     * 
     */
    private Long id;

    /**
     * 物服务ID
     */
    @Schema(description = "物服务ID")
    private Long thingServiceId;

    /**
     * 关联的直属的物模型 方便物模型更改时查找影响
     */
    @Schema(description = "关联的直属的物模型 方便物模型更改时查找影响")
    private Long thingModelId;

    /**
     * 所属设备
     */
    @Schema(description = "关联设备Id")
    private Long deviceId;

    /**
     * 服务配置
     */
    @Schema(description = "服务配置")
    private String configMap;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @Schema(description = "创建人名称")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat()
    private LocalDateTime createTime;

    /**
     * 工程ID
     */
    private Long engineeringId;

    /**
     * 模块ID
     */
    private Long moduleId;

    /**
     * 空间ID
     */
    private Long spaceId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 是否删除
     */
    private Integer deleted;

    /**
     * 修改人名称
     */
    @Schema(description = "修改人名称")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    private static final long serialVersionUID = 1L;


}