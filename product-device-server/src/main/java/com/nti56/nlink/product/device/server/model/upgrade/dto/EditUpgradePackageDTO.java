package com.nti56.nlink.product.device.server.model.upgrade.dto;

import com.nti56.nlink.product.device.client.model.dto.json.UpgradePackageRangeField;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/2/17 12:41<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "新增变动通知")
public class EditUpgradePackageDTO {
    
    private Long id;
    
    /**
     * 升级包名称
     */
    @Schema(description = "升级包名称")
    @NotBlank(message = "升级包名称不能为空")
    @Length(max = 255,message = "升级包名称不能超过255个字符")
    private String upgradePackageName;
    
    /**
     * 升级包描述
     */
    @Schema(description = "升级包描述")
    private String descript;
    
    /**
     * 是否期望版本(0 否,1 是)
     */
    @Schema(description = "是否期望版本(0 否,1 是)")
    @NotNull(message = "是否期望版本不能为空")
    private Integer isExpectVersion;
    
    /**
     * 升级包范围
     */
    @Schema(description = "升级包范围")
    @NotNull(message = "至少选择一个范围")
    private UpgradePackageRangeField upgradeRange;
    
}
