package com.nti56.nlink.product.device.server.verticle;

import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwSyncLabelTopic;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayControlProxy;
import com.nti56.nlink.product.device.server.model.HeartbeatInfo;
import com.nti56.nlink.product.device.server.model.datasync.SyncEdgeGatewayDto;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;
import io.netty.handler.codec.mqtt.MqttQoS;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-21 17:19:00
 * @since JDK 1.8
 */
@Component
@Scope(SCOPE_PROTOTYPE)
@Slf4j
public class MqttSyncLabelConfigurationConsumerVerticle extends MqttBaseVerticle {
    
    @Getter
    @Value("${mqtt.host}")
    private String host;

    @Getter
    @Value("${mqtt.port}")
    private Integer port;

    @Getter
    @Value("${mqtt.username}")
    private String username;

    @Getter
    @Value("${mqtt.password}")
    private String password;

    @Getter
    @Value("${mqtt.ssl:true}")
    private Boolean ssl;
    
    @Getter
    private Integer reconnectGapTime;
    
    @Autowired
    IEdgeGatewayService edgeGatewayService;
    
    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        log.info("start-verticle mqttSyncLabelConfiguration");
        super.start(startPromise);
    }

    @Override
    public void subscribe(){
        String topic = GwSyncLabelTopic.createSubscribeTopic(MqttBaseVerticle.shareSubscribeGroup);
        this.client.publishHandler(s1 -> {

            String topicName = s1.topicName();
            String payload = s1.payload().toString();
            log.debug("topic: {}, msg: {}", topicName, payload);
            GwSyncLabelTopic.TopicInfo topicInfo = GwSyncLabelTopic.parseTopic(topicName);
            Long tenantId = topicInfo.getTenantId();
            Long edgeGatewayId = topicInfo.getEdgeGatewayId();
            TenantIsolation tenant = new TenantIsolation();
            tenant.setTenantId(tenantId);
            SyncEdgeGatewayDto info = JSONObject.parseObject(payload, SyncEdgeGatewayDto.class);

            edgeGatewayService.syncLabelConfiguration(edgeGatewayId,info,tenantId);
        });
        client.subscribe(topic, 1);
    }

    @Override
    protected void handleConnectStatusChange(Boolean connected) {
       
    }
}
