package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.device.Device;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceTwin;
import com.nti56.nlink.product.device.server.domain.thing.devicemodel.DeviceModel;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.domain.thing.dpo.PropertyDpo;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Event;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Property;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModel;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModelInherit;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.DeviceModelMapper;
import com.nti56.nlink.product.device.server.mapper.LabelBindRelationMapper;
import com.nti56.nlink.product.device.server.model.LabelBindRelationBo;
import com.nti56.nlink.product.device.server.model.deviceLog.PropertyLogConditionBo;
import com.nti56.nlink.product.device.server.model.product.dto.EditDeviceModelDTO;
import com.nti56.nlink.product.device.server.model.product.vo.DeviceVO;
import com.nti56.nlink.product.device.server.model.thingModel.vo.ThingModelVO;
import com.nti56.nlink.product.device.server.service.IDeviceModelService;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.service.ILabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明: 设备服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:10:33
 * @since JDK 1.8
 */
@Service
@Slf4j
public class DeviceModelServiceImpl implements IDeviceModelService {


    public final static String PRODUCT_IMAGE_DIR = "product/image";

    @Autowired
    private DeviceModelMapper deviceModelMapper;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    CommonFetcherFactory commonFetcherFactory;

    @Autowired
    LabelBindRelationMapper labelBindRelationMapper;

    @Value("${fault.commonModel}")
    private String baseFaultModelJSONString;

    @Value("${device.base.model}")
    private String deviceBaseModelStr;

    @Autowired
    private ILabelService labelService;

    @Autowired @Lazy
    private RedisTemplate redisTemplate;

    private Result<DeviceEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<DeviceEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(DeviceEntity::getTenantId, tenantIsolation.getTenantId()).eq(DeviceEntity::getId, id);
        return Result.ok(deviceModelMapper.selectOne(lqw));
    }

    @Override
    public Result<DeviceVO> getDeviceModel(Long id, TenantIsolation tenantIsolation) {
        DeviceEntity deviceEntity = this.getByIdAndTenantIsolation(id, tenantIsolation).getResult();
        if (deviceEntity == null) {
            throw new BizException(ServiceCodeEnum.CODE_GET_FAIL);
        }

        DeviceVO deviceVO = BeanUtilsIntensifier.copyBean(deviceEntity, DeviceVO.class);

        //构建领域对象
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        Result<Device> deviceResult = Device.checkInfoWithBindRelation(
            deviceEntity, 
            commonFetcher
        );
        if (!deviceResult.getSignal()) {
            return Result.error(deviceResult.getMessage());
        }
        Device device = deviceResult.getResult();
        DeviceModel deviceModel = device.getDeviceModel();

        //完整模型
        deviceVO.setFullModel(deviceModel.toDpo());
        //自己模型
        deviceVO.setModel(deviceModel.toSelfDpo());

        //继承模型
        List<ModelDpo> modelContainsInherits = new ArrayList<>();
        List<ThingModel> thingModelList = deviceModel.getInherit().getThingModelList();
        if (thingModelList != null && thingModelList.size() > 0) {
            for (ThingModel thingModel : thingModelList) {
                ModelDpo dpo = thingModel.toDpo();
                modelContainsInherits.add(dpo);
               /* if(thingModel.getModelType().equals(ModelTypeEnum.FAULT_MODEL.getValue())){
                    containFaultModel = true;
                }*/
            }
        }
        //判断是否包含故障模型
        boolean containFaultModel = checkContainFaultModel(thingModelList);

        deviceVO.setModelContainsInherits(modelContainsInherits);

        //继承模型下拉
        ThingModelInherit inherit = deviceModel.getInherit();
        if(inherit != null && inherit.getThingModelList() != null){
            List<ThingModelVO> thingModelVOS = inherit.getThingModelList().stream().map(item -> {
                ThingModelVO vo = new ThingModelVO();
                vo.setId(item.getId());
                vo.setName(item.getName());
                Set<Property> collect = item.getProperties().stream().filter(property -> Optional.ofNullable(property.getLabelId()).isPresent()).collect(Collectors.toSet());
                vo.setIsBind(collect.size() > 0);
                return vo;   
            }).collect(Collectors.toList());
            deviceVO.setThingModels(thingModelVOS);
        }

        Map<Long, LabelBindRelationBo> labelDetailMap = new HashMap<>();
        for (PropertyDpo property : deviceVO.getModel().getProperties()) {
            if (property.getLabelId() != null){
                LabelBindRelationBo labelBindRelationBo = labelDetailMap.get(property.getLabelId());
                if (labelBindRelationBo == null){
                    labelBindRelationBo = labelService.getLabelDetail(property.getLabelId()).getResult();
                    labelDetailMap.put(property.getLabelId(),labelBindRelationBo);
                }
                property.setEdgeGatewayName(labelBindRelationBo.getEdgeGatewayName());
                property.setChannelName(labelBindRelationBo.getChannelName());
                property.setLabelGroupName(labelBindRelationBo.getLabelGroupName());
            }
        }

        for (ModelDpo modelDpo : deviceVO.getModelContainsInherits()) {
            for (PropertyDpo property : modelDpo.getProperties()){
                if (property.getLabelId() != null){
                    LabelBindRelationBo labelBindRelationBo = labelDetailMap.get(property.getLabelId());
                    if (labelBindRelationBo == null){
                        labelBindRelationBo = labelService.getLabelDetail(property.getLabelId()).getResult();
                        labelDetailMap.put(property.getLabelId(),labelBindRelationBo);
                    }
                    property.setEdgeGatewayName(labelBindRelationBo.getEdgeGatewayName());
                    property.setChannelName(labelBindRelationBo.getChannelName());
                    property.setLabelGroupName(labelBindRelationBo.getLabelGroupName());
                }
            }
        }
        if(containFaultModel){
            //存在故障模型，赋值基础故障模型,拿配置
            ModelDpo baseFaultModel = JSONUtil.toBean(baseFaultModelJSONString,ModelDpo.class);
            deviceVO.setCommonFaultModel(baseFaultModel);
        }
        ModelDpo baseModel = JSONUtil.toBean(deviceBaseModelStr, ModelDpo.class);
        deviceVO.setDeviceBaseModel(baseModel);
        deviceVO.getFullModel().merge(baseModel);
        return Result.ok(deviceVO);
    }

    private boolean checkContainFaultModel(List<ThingModel> thingModelList){
        if(Objects.isNull(thingModelList)) return false;
        return JSONUtil.toJsonStr(thingModelList).contains("\"modelType\":3");
        //退出条件
       /* if(CollectionUtils.isEmpty(thingModelList)){
            return false;
        }
        boolean flag = false;
        for (ThingModel thingModel : thingModelList) {
            if(thingModel.getModelType().equals(ModelTypeEnum.FAULT_MODEL.getValue())){
                flag = true;
                break;
            }
            if(!CollectionUtils.isEmpty(thingModel.getInherit().getThingModelList())){
                return flag || checkContainFaultModel(thingModel.getInherit().getThingModelList());
            }

        }
        return flag;*/
    }

    @Override
    @Transactional
    public Result<Void> editDeviceModel(EditDeviceModelDTO dto, TenantIsolation tenantIsolation) {
        DeviceEntity deviceEntity = this.getByIdAndTenantIsolation(dto.getId(), tenantIsolation).getResult();
        if (deviceEntity == null) {
            throw new BizException("该租户下不存在此设备");
        }
        String oldModel = JSON.toJSONString(deviceEntity.getModel(), SerializerFeature.MapSortField);
        String newModel = JSON.toJSONString(dto.getModel(), SerializerFeature.MapSortField);
        if (newModel.equals(oldModel)) {
            return Result.ok();
        }
        deviceService.setNotSyncById(dto.getId());
        DeviceEntity newDevice = BeanUtilsIntensifier.copyBean(dto, DeviceEntity.class);
        newDevice.setModelUpdateTime(LocalDateTime.now());
        deviceModelMapper.updateById(newDevice);
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        Result<DeviceModel> productModelResult = DeviceModel.checkInfo(
                newDevice,
                commonFetcher
        );
        if (!productModelResult.getSignal()) {
            throw new BizException(productModelResult.getMessage());
        }

        List<Property> properties = DeviceModel.getBaseModel().getProperties();
        //自己的属性
        Result<List<Property>> selfPropertiesResult = Property.batchCheckInfo(
                null, null,
                newDevice.getId(), newDevice.getName(),
                newDevice.getModel()
        );
        if(!selfPropertiesResult.getSignal()){
            return Result.error(selfPropertiesResult.getMessage() + ",  deviceId" + newDevice.getId());
        }
        List<Property> selfProperties = selfPropertiesResult.getResult();

        //检查属性重复
        Result<List<Property>> propertiesResult = Property.checkRepeat(
                properties, selfProperties
        );
        if(!propertiesResult.getSignal()){
            throw new BizException(propertiesResult.getMessage() + ",and deviceId" + newDevice.getId());
        }

        List<Event> selfEvents = productModelResult.getResult().getSelfEvents();
        String faultEventInstanceKey = String.format(RedisConstant.DEVICE_FAULT_EVENT_INSTANCE,tenantIsolation.getTenantId(), deviceEntity.getId());
        if(!Objects.isNull(deviceEntity.getModel()) && CollectionUtil.isNotEmpty(deviceEntity.getModel().getEvents())){
            List<String> oldEventsName= deviceEntity.getModel().getEvents().stream().map(item -> item.getName()).collect(Collectors.toList());
            List<String> newEventsName = selfEvents.stream().map(item -> item.getName()).collect(Collectors.toList());
            List<String> deleteEvents = new ArrayList<>();
            for (String old : oldEventsName) {
                boolean flag = false;
                for (String newEvent :newEventsName){
                    if(old.equals(newEvent)) {
                        flag = true;
                        break;
                    }
                }
                if(!flag){
                    deleteEvents.add(old);
                    String faultEventStatusKey = String.format(RedisConstant.DEVICE_FAULT_STATUS,tenantIsolation.getTenantId(),old,deviceEntity.getId());
                    redisTemplate.delete(faultEventStatusKey);
                }

            }
            if(CollectionUtil.isNotEmpty(deleteEvents)){
                redisTemplate.opsForHash().delete(faultEventInstanceKey,deleteEvents.toArray(new String[deleteEvents.size()]));
                if(redisTemplate.opsForHash().size(faultEventInstanceKey) == 0){
                    String tenantOnFaultDeviceKey = String.format(RedisConstant.TENANT_DEVICE_ON_FAULT,tenantIsolation.getTenantId());
                    String faultInstanceKey = String.format(RedisConstant.DEVICE_FAULT_INSTANCE_CACHE,tenantIsolation.getTenantId(),dto.getId());
                    redisTemplate.delete(faultEventInstanceKey);
                    redisTemplate.opsForSet().remove(tenantOnFaultDeviceKey,dto.getId());
                    redisTemplate.delete(faultInstanceKey);
                }

            }
        }
        return Result.ok();
    }

    @Override
    public Result<ModelDpo> getDeviceModel4Service(Long id, TenantIsolation tenantIsolation) {
        DeviceEntity deviceEntity = this.getByIdAndTenantIsolation(id, tenantIsolation).getResult();
        if (deviceEntity == null) {
            throw new BizException(ServiceCodeEnum.CODE_GET_FAIL);
        }
        //构建领域对象
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        Result<Device> deviceResult = Device.checkInfoToModel(
                deviceEntity,
                commonFetcher);
        if (!deviceResult.getSignal()) {
            throw new BizException(deviceResult.getMessage());
        }
        ModelDpo deviceBaseModel = JSONUtil.toBean(deviceBaseModelStr,ModelDpo.class);
        return Result.ok(deviceResult.getResult().getDeviceModel().toDpo().merge(deviceBaseModel));
    }


    @Override
    public Result<Set<ModelDpo>> listModelByDeviceIds(List<Long> deviceIds, TenantIsolation tenantIsolation) {
        Set<ModelDpo> set = new HashSet<>();
        if (CollectionUtil.isEmpty(deviceIds)) {
            return Result.ok(set);
        }
        Result<List<DeviceEntity>> listResult = deviceService.listDeviceByIds(tenantIsolation.getTenantId(), deviceIds);
        if (listResult.getSignal()) {
            set = buildModelDpoSet(tenantIsolation, listResult.getResult());
        }
        return Result.ok(set);
    }

    @Override
    public Result<Set<ModelDpo>> listModelByDeviceNames(List<String> deviceNames, TenantIsolation tenantIsolation) {
        Set<ModelDpo> set = new HashSet<>();
        if (CollectionUtil.isEmpty(deviceNames)) {
            return Result.ok(set);
        }
        Result<List<DeviceEntity>> listResult = deviceService.listDeviceByNames(tenantIsolation.getTenantId(), deviceNames);
        if (listResult.getSignal()) {
            set = buildModelDpoSet(tenantIsolation, listResult.getResult());
        }
        return Result.ok(set);
    }

    private Set<ModelDpo> buildModelDpoSet(TenantIsolation tenantIsolation, List<DeviceEntity> deviceEntityList) {
        Set<ModelDpo> set = new HashSet<>();
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        Set<Long> idSet = new HashSet<>();
        deviceEntityList.forEach(deviceEntity -> {
            Result<Device> deviceResult = Device.checkInfoToModel(
                    deviceEntity,
                    commonFetcher);
            if (deviceResult.getSignal()) {
                List<ThingModel> thingModelList = deviceResult.getResult().getDeviceModel().getInherit().getThingModelList();
                if (CollectionUtil.isNotEmpty(thingModelList)) {
                    thingModelList.forEach(thingModel -> {
                        if (!idSet.contains(thingModel.getId())) {
                            idSet.add(thingModel.getId());
                            set.add(thingModel.toDpo());
                        }
                    });
                }
                if (CollectionUtil.isNotEmpty(deviceResult.getResult().getDeviceModel().getSelfServices())) {
                    set.add(deviceResult.getResult().getDeviceModel().toSelfDpo());
                }
            }
        });
        return set;
    }

    @Override
    public Result<Set<String>> getPropertiesByDeviceIdsAndType(Long tenantId, PropertyLogConditionBo condition) {
        if (CollectionUtils.isEmpty(condition.getDeviceIds())) {
            return Result.ok(new HashSet<>());
        }
        Result<List<DeviceEntity>> listResult = deviceService.listDeviceByIds(tenantId,condition.getDeviceIds());
        if (!listResult.getSignal()) {
            throw new BizException(listResult.getServiceCode(),listResult.getMessage());
        }
        Set<String> propertySet = new HashSet<>();
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        boolean checkDataType = false;
        ThingDataTypeEnum type = null;
        if (ObjectUtil.isNotEmpty(condition.getDataType())) {
            type = ThingDataTypeEnum.typeOfName(condition.getDataType());
            if (type != null) {
                checkDataType = true;
            }
        }
        boolean finalCheckDataType = checkDataType;
        ThingDataTypeEnum finalType = type;
        listResult.getResult().forEach(device -> {
            Result<Device> deviceResult = Device.checkInfoToModel(device, commonFetcher);
            if (!deviceResult.getSignal()) {
                throw new BizException(deviceResult.getServiceCode(),deviceResult.getMessage());
            }
            List<Property> properties = deviceResult.getResult().getDeviceModel().getProperties();
            properties.forEach(property -> {
                if (finalCheckDataType) {
                    if (property.getDataType().getType().equals(finalType)) {
                        propertySet.add(property.getName());
                    }
                }else {
                    propertySet.add(property.getName());
                }
            });
        });
        return Result.ok(propertySet);
    }

}
