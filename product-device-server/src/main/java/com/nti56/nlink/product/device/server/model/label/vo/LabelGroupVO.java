package com.nti56.nlink.product.device.server.model.label.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 10:47<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "标签分组dto")
public class LabelGroupVO {

    private Long id;

    @Schema(description = "名字")
    private String name;

    @Schema(description = "描述")
    private String descript;

    @Schema(description = "标签列表")
    private List<LabelVO> labelList;

    @Schema(description = "标签列表")
    private List<LabelVO> updateLabelList;


}
