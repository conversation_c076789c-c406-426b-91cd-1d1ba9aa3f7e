package com.nti56.nlink.product.device.server.model.channel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/25 9:04<br/>
 * @since JDK 1.8
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "修改通道配置dto")
public class CheckChannelParamsDTO {

    @Schema(description = "所属驱动类型，1-Snap7，2-Modbus，3-OPC UA，4-ZigBee，5-BLE")
    @NotNull(message = "通道类型不能为空")
    private Integer driver;

    @Schema(description = "是否是服务端通讯")
    private Boolean isServer;

    @Schema(description = "通道参数列表")
    @Valid
    private List<ChannelParamDTO> channelParamList;
}
