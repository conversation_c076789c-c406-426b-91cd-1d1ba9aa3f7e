package com.nti56.nlink.product.device.server.scriptApi;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.DeviceRuntimeMetadataField;
import com.nti56.nlink.product.device.client.model.dto.json.device.PropertyMetadataItem;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceDataResource;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceTwin;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @ClassName Device
 * @date 2022/4/12 13:43
 * @Version 1.0
 */
@Getter
@Slf4j
public class Device {

    private Long tenantId;

    /**
     * 参数的定义及标签地址绑定
     */
    private Map<String, PropertyMetadataItem> propertyMetadata;

    private Map<Long, ChannelRuntimeInfoField> channelRuntimeInfoFieldMap;

    /**
     * 服务的定义
     */
    private Map<String,com.nti56.nlink.product.device.client.model.dto.json.scriptApi.Service> services;

    /**
     * 运行时数据
     */
//    private Map<String,Object> runTimeData;

    private Long id;

    private Long edgeGatewayId;

    private String resourceId;

    private String name;

    /**
     * 设备孪生
     */
    private DeviceTwin deviceTwin;

    public static Result<Device> getInstance(String resourceId, DeviceRuntimeMetadataField runtimeMetadata, Map<Long, ChannelRuntimeInfoField> channelRuntimeInfoFieldMap) {
        if (!Optional.ofNullable(runtimeMetadata).isPresent()) {
            return Result.error(ServiceCodeEnum.DEVICE_NO_ACTIVATION);
        }
        Device device = new Device();
        device.name = runtimeMetadata.getDeviceName();
        device.id = runtimeMetadata.getId();
        device.resourceId = resourceId;
        device.edgeGatewayId = runtimeMetadata.getEdgeGatewayId();
        device.propertyMetadata = runtimeMetadata.getProperties();
        device.services = runtimeMetadata.getServices();
        device.tenantId = runtimeMetadata.getTenantId();
        device.channelRuntimeInfoFieldMap = channelRuntimeInfoFieldMap;
        return Result.ok(device);
    }

    public void initDevice(DeviceDataResource deviceDataResource) {
        deviceTwin = new DeviceTwin(deviceDataResource,id);
    }



}
