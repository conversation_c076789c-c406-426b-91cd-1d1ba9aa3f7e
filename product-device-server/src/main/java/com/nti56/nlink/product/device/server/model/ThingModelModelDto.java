package com.nti56.nlink.product.device.server.model;


import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "物模型定义")
public class ThingModelModelDto {
    
    @Schema(description = "物模型主键ID")
    private Long id;
    
    @Schema(description = "物模型定义")
    private ModelField model;
}
