package com.nti56.nlink.product.device.server.model.deviceTemplate;

import com.nti56.nlink.product.device.server.model.DeviceRequestBo;
import com.nti56.nlink.product.device.server.model.channel.dto.EditChannelDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName DeviceTemplateRequstBo
 * @date 2023/3/9 13:47
 * @Version 1.0
 */
@Data
@Builder
@Schema(description = "设备模板请求对象")
@AllArgsConstructor
@NoArgsConstructor
public class DeviceTemplateBo {

    @Schema(description = "模板ID")
    private Long id;

    @Schema(description = "设备模板名称")
    private String name;

    @Schema(description = "设备列表")
    private List<DeviceRequestBo> deviceList;

    @Schema(description = "通道列表")
    private List<EditChannelDTO> channelList;


}
