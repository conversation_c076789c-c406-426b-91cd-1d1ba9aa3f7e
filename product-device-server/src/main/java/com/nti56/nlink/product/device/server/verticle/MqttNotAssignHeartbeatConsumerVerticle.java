package com.nti56.nlink.product.device.server.verticle;

import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwHeartbeatTopic;
import com.nti56.nlink.product.device.server.model.NotAssignHeartbeatInfo;
import com.nti56.nlink.product.device.server.service.INotAssignGatewayService;
import io.netty.handler.codec.mqtt.MqttQoS;
import io.vertx.core.Promise;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-04-18 17:55:00
 * @since JDK 1.8
 */
@Component
@Scope(SCOPE_PROTOTYPE)
@Slf4j
public class MqttNotAssignHeartbeatConsumerVerticle extends MqttBaseVerticle {
    
    @Getter
    @Value("${mqtt.host}")
    private String host;

    @Getter
    @Value("${mqtt.port}")
    private Integer port;

    @Getter
    @Value("${mqtt.username}")
    private String username;

    @Getter
    @Value("${mqtt.password}")
    private String password;

    @Getter
    @Value("${mqtt.ssl:true}")
    private Boolean ssl;
    
    @Getter
    private Integer reconnectGapTime;
    
    @Autowired
    private INotAssignGatewayService notAssignGatewayService;

    
    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        log.info("start-verticle notAssignHeartbeat");
        super.start(startPromise);
    }

    @Override
    public void subscribe(){
        String topic = GwHeartbeatTopic.createNotAssignHeartbeatTopic();
        this.client.publishHandler(s1 -> {
            String topicName = s1.topicName();
            String payload = s1.payload().toString();
            log.debug("topic: {}, msg: {}", topicName, payload);
            NotAssignHeartbeatInfo info = JSONObject.parseObject(payload, NotAssignHeartbeatInfo.class);
            log.debug("notAssignHeartbeatInfo is {}" , info);
            
            vertx.executeBlocking(promise -> {
                notAssignGatewayService.dealWithNotAssignHeartbeat(info);
                promise.complete();
            }, res -> {

            });
        });
        client.subscribe(topic, MqttQoS.AT_LEAST_ONCE.value());
    }

    @Override
    protected void handleConnectStatusChange(Boolean connected) {
       
    }
}
