package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.product.device.server.entity.DeviceTemplateEntity;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateBo;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateBuildDTO;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateCreateDeviceReq;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateThingPropertyBo;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateThingPropertyReq;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateValidRep;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateRespondBo;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateValidReq;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 设备模板表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2023-03-07 17:33:19
 * @since JDK 1.8
 */
public interface IDeviceTemplateService {

    Result<DeviceTemplateEntity> save(DeviceTemplateEntity entity);

    Result<Page<DeviceTemplateEntity>> getPage(@Nullable DeviceTemplateEntity entity, Page<DeviceTemplateEntity> page);

    Result<List<DeviceTemplateEntity>> list(DeviceTemplateEntity entity);

    Result<Void> update(DeviceTemplateEntity entity);

    Result<Void> deleteById(Long id, TenantIsolation tenantIsolation);

    Result<DeviceTemplateEntity> getById(Long id, TenantIsolation tenantIsolation);

    void downloadDeviceTemplate(HttpServletResponse response, TenantIsolation tenantIsolation, Long id);

    Result<Void> importDeviceTemplate(TenantIsolation tenantIsolation, MultipartFile file);

    Result<Void> buildDeviceTemplate(TenantIsolation tenantIsolation, List<Long> ids, String name, String descript);

    Result<DeviceTemplateBo> getTemplateInfo(TenantIsolation tenantIsolation, Long templateId);

    Result<DeviceTemplateBo> createByTemplate(TenantIsolation tenantIsolation, Long templateId, Long edgeGatewayId, DeviceTemplateBo requestBo);

    DeviceTemplateRespondBo batchUpdate(TenantIsolation tenantIsolation, DeviceTemplateBo requestBo);

    DeviceTemplateRespondBo batchDelete(TenantIsolation tenantIsolation, DeviceTemplateBo requestBo);

    Result<Void> buildTemplate(TenantIsolation tenantIsolation, DeviceTemplateBuildDTO dto, MultipartFile file);

    Result<DeviceTemplateValidRep> valid(TenantIsolation tenantIsolation, DeviceTemplateValidReq validReq);

    Result<Long> createByTemplate2(TenantIsolation tenantIsolation, DeviceTemplateCreateDeviceReq createDeviceReq);

    Result<List<DeviceTemplateThingPropertyBo>> getTemplateThingModel(TenantIsolation tenantIsolation, DeviceTemplateThingPropertyReq req);

    Result<Map<Long,Long>> batchCopyTemplate(TenantIsolation tenantIsolation, DeviceTemplateThingPropertyReq req);
}
