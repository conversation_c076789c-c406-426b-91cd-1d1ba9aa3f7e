package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.InstanceRedirectEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【re_instance_redirect(实例回调操作)】的数据库操作Mapper
* @createDate 2022-06-24 15:42:07
* @Entity com.nti56.nlink.ruleengine.model.entity.ReInstanceRedirect
*/

public interface RedirectMapper extends CommonMapper<InstanceRedirectEntity> {

    @Update("update redirect set redirect_invoke_time = IFNULL(redirect_invoke_time,0) + 1,update_time = SYSDATE() where id = #{id}")
    int updateInvokeTimesById(@Param("id") Long id);

    void deleteAllByTenantId(@Param("tenantId")Long tenantId);

    @Select("SELECT * FROM redirect WHERE deleted = 0 AND tenant_id = #{tenantId}")
    List<InstanceRedirectEntity> listAllRedirect(@Param("tenantId") Long tenantId);
}




