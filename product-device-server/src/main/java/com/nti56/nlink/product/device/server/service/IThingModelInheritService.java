package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.DeviceModelInheritEntity;
import com.nti56.nlink.product.device.server.entity.ThingModelInheritEntity;
import com.nti56.nlink.product.device.server.model.ThingModelInheritBo;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 物模型继承表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 15:37:55
 * @since JDK 1.8
 */
public interface IThingModelInheritService extends IBaseService<ThingModelInheritEntity> {

    Result<ThingModelInheritEntity> save(TenantIsolation tenantIsolation, ThingModelInheritEntity device);

    Result<List<ThingModelInheritEntity>> list(ThingModelInheritEntity device);

    Result<Integer> deleteById(TenantIsolation tenantIsolation, Long id);

    Result<ThingModelInheritEntity> getById(TenantIsolation tenantIsolation, Long id);

    Result<List<ThingModelInheritBo>> listBoByThingModelId(TenantIsolation tenantIsolation, Long thingModelId);

    default List<ThingModelInheritEntity> getListByThingModelId(Long thingModelId){
        ThingModelInheritEntity build = ThingModelInheritEntity.builder().thingModelId(thingModelId).build();
        return list(build).getResult();
    }

}
