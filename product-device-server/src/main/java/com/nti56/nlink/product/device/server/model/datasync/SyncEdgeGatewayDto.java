package com.nti56.nlink.product.device.server.model.datasync;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/********************************************************************************
 *
 * Title: 
 * Description: 
 * <AUTHOR>
 * create date on 2023/5/11
 * @version 1.0.0
 *
 *******************************************************************************/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "网关同步对象")
public class SyncEdgeGatewayDto {
    
    /**
     * 流量卡号
     */
    @Schema(description = "流量卡号")
    private String trafficCard;
    
    /**
     * 运营商
     */
    @Schema(description = "运营商 1-移动，2-电信，3-联通")
    private String operators;
    
    /**
     * 目标版本
     */
    @Schema(description = "目标版本")
    private String upgradeVersion;
    
    /**
     * 下载地址
     */
    @Schema(description = "下载地址")
    private String downloadUrl;
    
    /**
     * md5
     */
    @Schema(description = "md5")
    private String md5Proofread;
    
    /**
     * 5G流量AppKey
     */
    @Schema(description = "5G流量AppKey")
    private String traffic5gAppKey;
    
    /**
     * 5G流量AppScrect
     */
    @Schema(description = "5G流量AppScrect")
    private String traffic5gAppScrect;
    
    private List<SyncChannelDto> channelDtoList;
    
    private List<SyncChannelParamDto> channelParamDtoList;
    
    private List<SyncLabelGroupDto> labelGroupDtoList;
    
    private List<SyncLabelDto> labelDtoList;
    
    private List<SyncCustomContentDto> customContentDtoList;

    /**
     * 内存监控百分比
     */
    @Schema(description = "网关内存监控百分比")
    private Integer memoryMonitor;

    /**
     * 硬件空间监控百分比
     */
    @Schema(description = "网关空间监控百分比")
    private Integer spaceMonitor;
    
}
