package com.nti56.nlink.product.device.server.service.export.handler;

import java.util.List;

import cn.hutool.extra.spring.SpringUtil;

import org.apache.commons.collections.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.nti56.nlink.common.export.handler.AbstractExportSqlHandler;
import com.nti56.nlink.common.util.GeneratorSqlUtil;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;
import com.nti56.nlink.product.device.server.mapper.ChannelMapper;
import com.nti56.nlink.product.device.server.mapper.ChannelParamMapper;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/19 15:34<br/>
 * @version 1.0
 * @since JDK 1.8
 */
public class ChannelExportSqlHandler extends AbstractExportSqlHandler {
  @Override
  public void exportSqlDml(Long tenantId, List<String> sqlList) {
    exportChannels(tenantId, sqlList);
    exportChannelParams(tenantId, sqlList);
    if (getNext() != null) {
      getNext().exportSqlDml(tenantId, sqlList);
    }
  }

  private void exportChannels(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<ChannelEntity> queryWrapper = new LambdaQueryWrapper<ChannelEntity>().eq(ChannelEntity::getTenantId, tenantId);
    List<ChannelEntity> dtoList = SpringUtil.getBean(ChannelMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(ChannelEntity.class, dtoList));
    }
  }

  private void exportChannelParams(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<ChannelParamEntity> queryWrapper = new LambdaQueryWrapper<ChannelParamEntity>().eq(ChannelParamEntity::getTenantId, tenantId);
    List<ChannelParamEntity> dtoList = SpringUtil.getBean(ChannelParamMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(ChannelParamEntity.class, dtoList));
    }
  }

}
