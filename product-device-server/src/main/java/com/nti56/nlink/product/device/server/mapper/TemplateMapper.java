package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.product.device.server.entity.TemplateEntity;
import com.nti56.nlink.product.device.server.model.template.dto.TemplateDTO;
import com.nti56.nlink.product.device.server.model.template.vo.TemplateVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/9 9:35<br/>
 * @since JDK 1.
 */
public interface TemplateMapper extends BaseMapper<TemplateEntity> {

    Page<TemplateVO> pageTemplate(IPage<TemplateVO> templatePage, @Param("dto") TemplateDTO dto, @Param("tenantIsolation") TenantIsolation tenantIsolation);

    List<TemplateVO> listTemplate(@Param("dto") TemplateDTO dto, @Param("tenantIsolation") TenantIsolation tenantIsolation);

    void deleteAllByTenantId(@Param("tenantId")Long tenantId);
}
