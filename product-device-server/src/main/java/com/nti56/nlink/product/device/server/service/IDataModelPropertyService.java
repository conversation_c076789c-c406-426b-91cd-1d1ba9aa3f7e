package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.DataModelPropertyEntity;
import com.nti56.nlink.product.device.server.model.DataModelPropertyBo;

import java.util.List;

/**
 * <p>
 * 数据模型属性表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 13:31:16
 * @since JDK 1.8
 */
public interface IDataModelPropertyService extends IBaseService<DataModelPropertyEntity> {

    Result<DataModelPropertyEntity> save(TenantIsolation tenantIsolation, DataModelPropertyEntity entity);

    Result<Integer> update(TenantIsolation tenantIsolation, DataModelPropertyEntity entity);

    Result<Integer> deleteById(TenantIsolation tenantIsolation, Long id);

    Result<DataModelPropertyEntity> getById(TenantIsolation tenantIsolation, Long id);

    Result<List<DataModelPropertyBo>> listByDataModelId(TenantIsolation tenantIsolation, Long dataModelId);

}
