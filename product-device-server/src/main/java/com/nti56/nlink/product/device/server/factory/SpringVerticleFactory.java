/*
 * Copyright 2017 Red Hat, Inc.
 *
 * Red Hat licenses this file to you under the Apache License, version 2.0
 * (the "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at:
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

package com.nti56.nlink.product.device.server.factory;

import io.vertx.core.Promise;
import io.vertx.core.Verticle;
import io.vertx.core.Vertx;
import io.vertx.core.spi.VerticleFactory;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.concurrent.Callable;

/**
 * 
 * 类说明: 
 * A {@link VerticleFactory} backed by Spring's {@link ApplicationContext}. It allows to implement verticles as Spring
 * beans and thus benefit from dependency injection, ...etc.
 *
 * <AUTHOR> and Thomas Segismont
 * @version 1.0
 * @date 2022-02-21 17:19:00
 * @since JDK 1.8
 */
@Component
@Slf4j
public class SpringVerticleFactory implements VerticleFactory, ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public String prefix() {
        // Just an arbitrary string which must uniquely identify the verticle factory
        return "ntiapp";
    }

    @Override
    public void init(Vertx vertx) {
        log.info("verticle init");
    }

    @Override
    public void createVerticle(String verticleName, ClassLoader classLoader, Promise<Callable<Verticle>> promise) {
        log.info("verticle createVerticle: {}", verticleName);
        // Our convention in this example is to give the class name as verticle name
        String clazz = VerticleFactory.removePrefix(verticleName);
        log.info("verticle createVerticle clazz: {}", clazz);
        promise.complete(() -> {
            log.info("verticle complete begin: {}, {}", clazz, verticleName);
            Verticle v = (Verticle) applicationContext.getBean(Class.forName(clazz));
            log.info("verticle complete end: {}, {}", clazz, verticleName);
            return v;
        });
    }

    @Override
    public void close() {
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
