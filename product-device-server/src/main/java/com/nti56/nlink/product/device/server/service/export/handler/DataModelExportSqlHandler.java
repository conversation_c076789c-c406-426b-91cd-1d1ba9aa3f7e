package com.nti56.nlink.product.device.server.service.export.handler;

import java.util.List;

import cn.hutool.extra.spring.SpringUtil;

import org.apache.commons.collections.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.nti56.nlink.common.export.handler.AbstractExportSqlHandler;
import com.nti56.nlink.common.util.GeneratorSqlUtil;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;
import com.nti56.nlink.product.device.server.entity.DataModelEntity;
import com.nti56.nlink.product.device.server.entity.DataModelPropertyEntity;
import com.nti56.nlink.product.device.server.mapper.ChannelMapper;
import com.nti56.nlink.product.device.server.mapper.ChannelParamMapper;
import com.nti56.nlink.product.device.server.mapper.DataModelMapper;
import com.nti56.nlink.product.device.server.mapper.DataModelPropertyMapper;

/**
 * 类说明: 数据模型导出<br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/19 15:34<br/>
 * @version 1.0
 * @since JDK 1.8
 */
public class DataModelExportSqlHandler extends AbstractExportSqlHandler {
  @Override
  public void exportSqlDml(Long tenantId, List<String> sqlList) {
    exportDataModel(tenantId, sqlList);
    exportDataModelProperty(tenantId, sqlList);
    if (getNext() != null) {
      getNext().exportSqlDml(tenantId, sqlList);
    }
  }

  private void exportDataModel(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<DataModelEntity> queryWrapper = new LambdaQueryWrapper<DataModelEntity>().eq(DataModelEntity::getTenantId, tenantId);
    List<DataModelEntity> dtoList = SpringUtil.getBean(DataModelMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(DataModelEntity.class, dtoList));
    }
  }

  private void exportDataModelProperty(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<DataModelPropertyEntity> queryWrapper = new LambdaQueryWrapper<DataModelPropertyEntity>().eq(DataModelPropertyEntity::getTenantId, tenantId);
    List<DataModelPropertyEntity> dtoList = SpringUtil.getBean(DataModelPropertyMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(DataModelPropertyEntity.class, dtoList));
    }
  }

}
