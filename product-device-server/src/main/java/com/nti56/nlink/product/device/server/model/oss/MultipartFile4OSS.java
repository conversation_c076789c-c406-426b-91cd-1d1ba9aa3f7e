package com.nti56.nlink.product.device.server.model.oss;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

import org.springframework.web.multipart.MultipartFile;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/20 10:14<br/>
 * @version 1.0
 * @since JDK 1.8
 */
public class MultipartFile4OSS implements MultipartFile {
  private final byte[] fileContent;

  private String fileName;

  public MultipartFile4OSS(byte[] fileData, String name) {
    this.fileContent = fileData;
    this.fileName = name;

  }

  @Override
  public void transferTo(File dest) throws IOException, IllegalStateException {
    throw new UnsupportedOperationException("不支持的操作");
  }

  @Override
  public String getName() {
    return fileName;
  }

  @Override
  public String getOriginalFilename() {
    return fileName;
  }

  @Override
  public String getContentType() {
    throw new UnsupportedOperationException("不支持的操作");
  }

  @Override
  public boolean isEmpty() {
    return fileContent == null || fileContent.length == 0;
  }

  @Override
  public long getSize() {
    return fileContent.length;
  }

  @Override
  public byte[] getBytes() {
    return fileContent;
  }

  @Override
  public InputStream getInputStream() {
    return new ByteArrayInputStream(fileContent);
  }
}
