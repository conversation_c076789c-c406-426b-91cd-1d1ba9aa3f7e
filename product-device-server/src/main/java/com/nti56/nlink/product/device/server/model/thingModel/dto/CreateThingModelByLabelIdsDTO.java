package com.nti56.nlink.product.device.server.model.thingModel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/7/4 9:11<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "根据标签创建物模型")
public class CreateThingModelByLabelIdsDTO {

    @Schema(description = "物模型名称")
    @NotBlank(message = "物模型名称不能为空")
    @Length(max = 32,message = "物模型名称不能超过32个字符")
    private String name;

    @Schema(description = "描述")
    @Length(max = 256,message = "物模型描述不能超过256个字符")
    private String descript;

    @Schema(description = "标记id列表")
    private List<Long> tagIds;

    @Schema(description = "标签id列表")
    @NotNull(message = "至少选择一个标签")
    @Size(min = 1,message = "至少选择一个标签")
    private List<Long> labelIds;
}
