package com.nti56.nlink.product.device.server.model.device.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 类说明：
 *
 * @ClassName DeviceFaultInstance
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/10/18 14:40
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceFaultInstance implements Serializable {

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 持续秒数
     */
    private Double continueSecond;

    /**
     * 故障描述
     */
    private String desc;

    /**
     * 设备ID
     */
    private Long deviceId;


    private String deviceName;

    private List<FaultEventInstance> eventInstances;

}
