package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.nti56.nlink.product.device.server.entity.json.InputValueField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 服务日志表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-08-10 10:28:52
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("device_service_log")
@Schema(description = "DeviceServiceLogEntity对象")
public class DeviceServiceLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 所属设备ID
     */
    @Schema(description = "所属设备ID")
    private Long deviceId;

    /**
     * 服务ID
     */
    @Schema(description = "服务ID")
    private Long serviceId;

    /**
     * 服务名称
     */
    @Schema(description = "服务名称")
    private String serviceName;

    /**
     * 设备名称
     */
    @Schema(description = "设备名称")
    private String deviceName;

    /**
     * 调用类型 1-用户调用，2-场景联动调用，3-接口调用，4-服务调用
     */
    @Schema(description = "调用类型 1-用户调用，2-场景联动调用，3-接口调用，4-服务调用")
    private Integer callType;

    /**
     * 调用成功:0-(失败) 1-(成功)
     */
    @Schema(description = "调用成功:0-(失败) 1-(成功)")
    private Boolean callSuccess;

    /**
     * 输入参数
     */
    @Schema(description = "输入参数")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private InputValueField[] inputData;

    /**
     * 结果
     */
    @Schema(description = "结果")
    private String outputData;

    /**
     * 调用者ID
     */
    @Schema(description = "调用者ID")
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 调用者
     */
    @Schema(description = "调用者")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 调用时间
     */
    @Schema(description = "调用时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime createTime;

    @Schema(description = "租户ID")
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    @TableField(exist = false)
    private Boolean isRecordLog = true;

}
