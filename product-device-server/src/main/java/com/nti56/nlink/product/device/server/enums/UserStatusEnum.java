package com.nti56.nlink.product.device.server.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:33:25
 * @since JDK 1.8
 */
public enum UserStatusEnum {
    ENABLE(0, "正常"),
    DISABLE(1, "禁用"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String desc;

    UserStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
    public static List toList(){
        List<Map> result = new ArrayList<>();
        UserStatusEnum[] values = UserStatusEnum.values();
        Map<String,Object> map ;
        for (UserStatusEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.desc);
            map.put("value",v.value);
            result.add(map);
        }
        return result;
    }

}
