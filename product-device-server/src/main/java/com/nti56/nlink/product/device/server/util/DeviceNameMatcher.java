package com.nti56.nlink.product.device.server.util;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DeviceNameMatcher {

    public static List<String> matchStrings(List<String> groupNames, String patternString) {
        List<String> result = new ArrayList<>();

        for (String groupName : groupNames) {
            String[] parts = groupName.split("\\.");
            StringBuilder replacedString = new StringBuilder();

            // 使用正则表达式匹配${group1}、${group2}等占位符
            Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
            Matcher matcher = pattern.matcher(patternString);

            int lastIndex = 0;
            while (matcher.find()) {
                String placeholder = matcher.group(1);
                int groupIndex = Integer.parseInt(placeholder.substring(5)) - 1; // 提取占位符中的数字并减1获取对应的索引
                if (groupIndex >= 0 && groupIndex < parts.length) {
                    replacedString.append(patternString, lastIndex, matcher.start()); // 添加占位符之前的部分
                    replacedString.append(parts[groupIndex]); // 添加匹配的字符串值
                    lastIndex = matcher.end(); // 更新上一次匹配结束的索引
                }
            }

            replacedString.append(patternString, lastIndex, patternString.length()); // 添加最后一个占位符之后的部分
            result.add(replacedString.toString());
        }

        return result;
    }

    public static String matchSingle(String groupName, String patternString) {

        String[] parts = groupName.split("\\.");
        StringBuilder replacedString = new StringBuilder();

        // 使用正则表达式匹配${group1}、${group2}等占位符
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(patternString);

        int lastIndex = 0;
        while (matcher.find()) {
            String placeholder = matcher.group(1);
            int groupIndex = Integer.parseInt(placeholder.substring(5)) - 1; // 提取占位符中的数字并减1获取对应的索引
            if (groupIndex >= 0 && groupIndex < parts.length) {
                replacedString.append(patternString, lastIndex, matcher.start()); // 添加占位符之前的部分
                replacedString.append(parts[groupIndex]); // 添加匹配的字符串值
                lastIndex = matcher.end(); // 更新上一次匹配结束的索引
            }
        }

        replacedString.append(patternString, lastIndex, patternString.length()); // 添加最后一个占位符之后的部分
        return replacedString.toString();
    }


    public static void main(String[] args) {
        List<String> groupNames = new ArrayList<>();
        groupNames.add("g1.1001");
        groupNames.add("g1.1002");

        String patternString = "S7.${group1}.${group2}.${group3}";

        List<String> matchedStrings = matchStrings(groupNames, patternString);

        for (String matchedString : matchedStrings) {
            System.out.println(matchedString);
        }
    }
}
