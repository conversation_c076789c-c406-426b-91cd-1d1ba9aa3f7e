package com.nti56.nlink.product.device.server.model.oss;


import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "打包对象")
public class PackageVersionDTO implements Serializable {

  private static final long serialVersionUID = 5664871617880467838L;

  @Schema(description = "工程")
  private Long engineeringId;
  /**
   *
   */
  @Schema(description = "包版本")
  private String packageVersion;
  @Schema(description = "oss文件地址")
  private String ossFilePath;
  @Schema(description = "基础包文件地址")
  private String baseSysPath;
  @Schema(description = "oss文件id")
  private Long ossFileId;
  @Schema(description = "文件名")
  private String fileName;
  @Schema(description = "租户")
  private Long tenantId;

  @Schema(description = "id")
  private Long id;

  @Schema(description = "创建者")
  private String creator;

  @Schema(description = "创建时间")
  private LocalDateTime createTime;

  @Schema(description = "更新者")
  private String updator;
  @Schema(description = "更新时间")
  private LocalDateTime updateTime;
  private Integer version;
  private Byte deleted;


}