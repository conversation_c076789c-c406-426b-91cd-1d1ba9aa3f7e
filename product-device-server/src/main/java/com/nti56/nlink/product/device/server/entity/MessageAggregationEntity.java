package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName message_aggregation
 */
@TableName(value ="message_aggregation")
@Data
public class MessageAggregationEntity implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 模板标题
     */
    private String title;

    /**
     * 消息聚合id
     */
    private Long aggregationId;

    /**
     * 工程id
     */
    private Long tenantId;

    /**
     * 创建人名称
     */
    private String creator;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人名称
     */
    private String updator;

    /**
     * 更新人id
     */
    private Long updatorId;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}