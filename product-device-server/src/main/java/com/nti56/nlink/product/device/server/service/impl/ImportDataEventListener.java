//package com.nti56.nlink.product.device.server.service.impl;
//
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.json.JSONUtil;
//import com.nti56.nlink.common.util.BeanUtilsIntensifier;
//import com.nti56.nlink.product.device.client.model.dto.DeviceDTO;
//import com.nti56.nlink.product.device.client.model.dto.DeviceModelInheritDTO;
//import com.nti56.nlink.product.device.client.model.dto.ThingModelDTO;
//import com.nti56.nlink.product.device.client.model.dto.ThingModelInheritDTO;
//import com.nti56.nlink.product.device.client.model.dto.engineering.ProductDeviceServerDataDTO;
//import com.nti56.nlink.product.device.server.domain.event.DeleteDataEvent;
//import com.nti56.nlink.product.device.server.domain.event.ImportDataEvent;
//import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
//import com.nti56.nlink.product.device.server.model.DeviceDto;
//import com.nti56.nlink.product.device.server.service.IModelGraphService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.context.ApplicationListener;
//import org.springframework.context.event.EventListener;
//import org.springframework.data.neo4j.transaction.Neo4jTransactionManager;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.PlatformTransactionManager;
//import org.springframework.transaction.TransactionDefinition;
//import org.springframework.transaction.TransactionStatus;
//import org.springframework.transaction.interceptor.TransactionAspectSupport;
//import org.springframework.transaction.support.DefaultTransactionDefinition;
//
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * 类说明：
// *
// * @ClassName ImportDataEventListener
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/11/30 15:21
// * @Version 1.0
// */
//
//@Component
//@Slf4j
//public class ImportDataEventListener {
//
//    //@Autowired
//    //private IModelGraphService modelGraphService;
//    @Autowired
//    private TransactionDefinition transactionDefinition;
//    @Qualifier("neo4jTransactionManager")
//    @Autowired
//    private PlatformTransactionManager platformTransactionManager;
//
//
//    @EventListener(DeleteDataEvent.class)
//    public void handleDeleteDataEvent(DeleteDataEvent event) {
//        log.info("==================delete data event trigger==================");
//        try {
//            TransactionStatus transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);
//            //modelGraphService.deleteModelAndDeviceGraph(event.getTenantId());
//            platformTransactionManager.commit(transactionStatus);
//        } catch (Exception e) {
//            // TODO: handle exception
//            // TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//            log.error("", e);
//        }
//    }
//
//    @EventListener(ImportDataEvent.class)
//    public void handleImportDataEvent(ImportDataEvent event) {
//        log.info("==================import data event trigger==================");
//        ProductDeviceServerDataDTO dto = event.getData();
//
//        List<ThingModelDTO> thingModelList = dto.getThingModelList();
//        List<ThingModelInheritDTO> thingModelInheritList = dto.getThingModelInheritList();
//        List<DeviceDTO> deviceList = dto.getDeviceList();
//        List<DeviceModelInheritDTO> deviceModelInheritList = dto.getDeviceModelInheritList();
////        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);
//        try {
//            if(CollectionUtil.isNotEmpty(thingModelList)){
//                //对继承数据分组
//                Map<Long, List<ThingModelInheritDTO>> thingModelInheritGroup = thingModelInheritList.stream().collect(Collectors.groupingBy(ThingModelInheritDTO::getThingModelId));
//                for (ThingModelDTO thingModelDTO : thingModelList) {
//                    List<ThingModelInheritDTO> inheritsDTOs = thingModelInheritGroup.get(thingModelDTO.getId());
//                    /*if(CollectionUtil.isNotEmpty(inheritsDTOs)){
//                        List<Long> inheritIds = inheritsDTOs.stream().map(ThingModelInheritDTO::getInheritThingModelId).collect(Collectors.toList());
//                        modelGraphService.saveModelNode(BeanUtilsIntensifier.copyBean(thingModelDTO, ThingModelEntity.class),inheritIds);
//                    }else{
//
//                    }*/
//                    //modelGraphService.saveModelNode(BeanUtilsIntensifier.copyBean(thingModelDTO,ThingModelEntity.class),null);
//
//                }
//                //保存关系
//                /*for (ThingModelDTO thingModelDTO : thingModelList) {
//                    List<ThingModelInheritDTO> inheritsDTOs = thingModelInheritGroup.get(thingModelDTO.getId());
//                    if(CollectionUtil.isNotEmpty(inheritsDTOs)){
//                        for (ThingModelInheritDTO inheritsDTO : inheritsDTOs) {
//                            modelGraphService.addModelRelation(thingModelDTO.getId(),inheritsDTO.getInheritThingModelId());
//                        }
//                    }
//                }*/
//    //            platformTransactionManager.commit(transactionStatus);
//
//            }
//           /* if(CollectionUtil.isNotEmpty(deviceList)){
//                Map<Long, List<DeviceModelInheritDTO>> deviceInheritGroup = deviceModelInheritList.stream().collect(Collectors.groupingBy(DeviceModelInheritDTO::getDeviceId));
//                for (DeviceDTO deviceDTO : deviceList) {
//                    List<DeviceModelInheritDTO> inheritsDTOs = deviceInheritGroup.get(deviceDTO.getId());
//                    if(CollectionUtil.isNotEmpty(inheritsDTOs)){
//                        List<Long> collect = inheritsDTOs.stream().map(DeviceModelInheritDTO::getInheritThingModelId).collect(Collectors.toList());
//                        modelGraphService.saveDeviceNode(BeanUtilsIntensifier.copyBean(deviceDTO, DeviceDto.class),collect);
//                    }else{
//                        modelGraphService.saveDeviceNode(BeanUtilsIntensifier.copyBean(deviceDTO, DeviceDto.class),null);
//                    }
//
//                }
//            }*/
//        } catch (Exception e) {
//            log.error("", e);
//            // TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//        }
//
//
//    }
//}
