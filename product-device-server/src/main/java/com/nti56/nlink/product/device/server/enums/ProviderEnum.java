package com.nti56.nlink.product.device.server.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/13 16:56<br/>
 * @since JDK 1.8
 */
public enum ProviderEnum {

    ALI_YUN_SMS(1, "阿里云",
            "[{" +
            "\"fieldName\": \"accessKeyId\"," +
            "\"fieldLabel\": \"accessKeyId\"," +
            "\"pField\": \"params\"," +
            "\"fieldType\": \"text:1:true\"" +
            "}, {" +
            "\"fieldName\": \"accessKeySecret\"," +
            "\"fieldLabel\": \"accessKeySecret\"," +
            "\"pField\": \"params\"," +
            "\"fieldType\": \"password:1:true\"" +
            "}, {" +
            "\"fieldName\": \"signName\"," +
            "\"fieldLabel\": \"短信签名\"," +
            "\"pField\": \"params\"," +
            "\"fieldType\": \"text:1:true\"" +
            "}]")

    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String channelField;

    ProviderEnum(Integer value, String name,String channelField) {
        this.value = value;
        this.name = name;
        this.channelField = channelField;
    }

    public static ProviderEnum typeOfValue(Integer value){
        ProviderEnum[] values = ProviderEnum.values();
        for (ProviderEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ProviderEnum typeOfName(String name){
        ProviderEnum[] values = ProviderEnum.values();
        for (ProviderEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static String getNameByValue(Integer value){
        ProviderEnum[] values = ProviderEnum.values();
        for (ProviderEnum v : values) {
            if (v.value.equals(value)) {
                return v.name;
            }
        }
        return null;

    }


    public static List toList(){
        List<Map> result = new ArrayList<>();
        ProviderEnum[] values = ProviderEnum.values();
        Map<String,Object> map ;
        for (ProviderEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            map.put("channelField",v.channelField);
            result.add(map);
        }
        return result;
    }
}
