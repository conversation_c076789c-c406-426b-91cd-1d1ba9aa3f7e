package com.nti56.nlink.product.device.server.entity.json;

import cn.hutool.core.util.ObjectUtil;
import com.nti56.nlink.common.export.json.AbstractExport;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Map;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName InputValue
 * @date 2022/8/11 10:15
 * @Version 1.0
 */
@Data
@Schema
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InputValueField implements Serializable {

    private String name;

    private Object value;

    public static InputValueField[] valueOf(Map<String, Object> input) {
        InputValueField[] inputValueFields;
        if (ObjectUtil.isNotEmpty(input)) {
            inputValueFields = new InputValueField[input.size()];
            int i = 0;
            for (Map.Entry<String, Object> entry : input.entrySet()) {
                inputValueFields[i] = InputValueField.builder().name(entry.getKey()).value(entry.getValue()).build();
                i ++;
            }
        }else {
            inputValueFields = new InputValueField[0];
        }
        return inputValueFields;
    }
}
