package com.nti56.nlink.product.device.server.service.impl;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.domain.thing.datamodel.DataModel;
import com.nti56.nlink.product.device.server.entity.DataModelEntity;
import com.nti56.nlink.product.device.server.entity.DataModelPropertyEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.DataModelMapper;
import com.nti56.nlink.product.device.server.mapper.DataModelPropertyMapper;
import com.nti56.nlink.product.device.server.model.DataModelPropertyBo;
import com.nti56.nlink.product.device.server.service.IDataModelPropertyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 数据模型属性表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 13:31:16
 * @since JDK 1.8
 */
@Service
public class DataModelPropertyServiceImpl extends BaseServiceImpl<DataModelPropertyMapper, DataModelPropertyEntity> implements IDataModelPropertyService {

    @Autowired
    DataModelPropertyMapper mapper;

    @Autowired
    DataModelMapper dataModelMapper;

    @Autowired
    CommonFetcherFactory commonFetcherFactory;

    private void check(Long tenantId, Long dataModelId){

        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantId);
        DataModelEntity dataModelEntity = dataModelMapper.getById(tenantId, dataModelId);
        Result<DataModel> dataModelResult = DataModel.checkInfo(dataModelEntity, commonFetcher);
        if(!dataModelResult.getSignal()){
            throw new BizException("创建数据模型失败:" + dataModelResult.getMessage());
        }
    }
    @Override
    @Transactional
    public Result<DataModelPropertyEntity> save(TenantIsolation tenantIsolation, DataModelPropertyEntity entity) {
        entity.setTenantId(tenantIsolation.getTenantId());
        mapper.insert(entity);
        
        check(tenantIsolation.getTenantId(), entity.getDataModelId());

        return Result.ok(entity);
    }

    @Override
    public Result<Integer> update(TenantIsolation tenantIsolation, DataModelPropertyEntity entity) {
        DataModelPropertyEntity dataModelPropertyEntity = mapper.getById(tenantIsolation.getTenantId(), entity.getId());
        if(dataModelPropertyEntity == null){
            return Result.error("找不到数据模型属性");
        }
        entity.setUpdateTime(LocalDateTime.now());
        int updateById = mapper.updateById(entity);
        
        check(tenantIsolation.getTenantId(), entity.getDataModelId());

        return Result.ok(updateById);
    }

    @Override
    public Result<Integer> deleteById(TenantIsolation tenantIsolation, Long entityId) {
        DataModelPropertyEntity dataModelPropertyEntity = mapper.getById(tenantIsolation.getTenantId(), entityId);
        if(dataModelPropertyEntity == null){
            return Result.error("找不到数据模型属性");
        }
        if (mapper.deleteById(entityId) == 1) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    @Override
    public Result<DataModelPropertyEntity> getById(TenantIsolation tenantIsolation, Long entityId) {
        DataModelPropertyEntity entity = mapper.getById(tenantIsolation.getTenantId(), entityId);
        return Result.ok(entity);
    }


    @Override
    public Result<List<DataModelPropertyBo>> listByDataModelId(TenantIsolation tenantIsolation, Long dataModelId) {
        List<DataModelPropertyBo> list = mapper.listBoByDataModelId(tenantIsolation.getTenantId(), dataModelId);
        return Result.ok(list);
    }

}
