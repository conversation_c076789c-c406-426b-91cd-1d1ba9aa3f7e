package com.nti56.nlink.product.device.server.listener;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

import com.nti56.nlink.common.thing.ThingDataTypeEnum;
import com.nti56.nlink.common.util.CurrentTimeMillisClock;
import com.nti56.nlink.product.device.client.model.dto.json.compute.ComputeEventItem;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpEventTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import com.nti56.nlink.product.device.server.entity.ComputeTaskEntity;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.mapper.DeviceMapper;
import com.nti56.nlink.product.device.server.verticle.post.processor.event.EventUpData2InfluxDBHandler;
import com.nti56.nlink.product.device.server.verticle.post.processor.event.NoChangeEventHandler;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class NoChangeKeyExpireListener implements MessageListener {

    @Getter
    public static class NoChangeInfo {
        private Long deviceId;
        private String property;

        public NoChangeInfo(Long deviceId, String property){
            this.deviceId = deviceId;
            this.property = property;
        }

        public static Boolean isNoChangeKey(String key){
            if(key == null){
                return false;
            }
            return key.startsWith(RedisConstant.DEVICE_NOCHANGE_PREFIX);
        }

        public static NoChangeInfo parse(String key) {
            String noChangeStr = key.replaceFirst(RedisConstant.DEVICE_NOCHANGE_PREFIX + ":", "");
            String[] split = noChangeStr.split(":");
            Long deviceId = Long.parseLong(split[0]);
            String property = split[1];
            return new NoChangeInfo(deviceId, property);
        }

        public String toKey() {
            return RedisConstant.DEVICE_NOCHANGE_PREFIX + ":" + deviceId + ":" + property;
        }

    }

    @Autowired
    NoChangeEventHandler noChangeEventHandler;
    
    @Autowired
    EventUpData2InfluxDBHandler eventUpData2InfluxDBHandler;

    @Autowired
    DeviceMapper deviceMapper;
    
    @Override
    public void onMessage(Message message, byte[] pattern) {
        String key = new String(message.getBody(), StandardCharsets.UTF_8);
        log.debug("expire message:{}", key);
        if(!NoChangeInfo.isNoChangeKey(key)){
            return;
        }
        NoChangeInfo noChangeInfo = NoChangeInfo.parse(key);
        Long deviceId = noChangeInfo.getDeviceId();
        DeviceEntity deviceEntity = deviceMapper.selectById(deviceId);
        if(deviceEntity == null){
            log.error("找不到设备:{}", deviceId);
            return;
        }
        Long timestamp = CurrentTimeMillisClock.getInstance().now();
        GwUpEventTopic.TopicInfo topicInfo = builTopicInfo(
            deviceEntity.getEdgeGatewayId(), 
            deviceEntity.getTenantId(),
            deviceId,
            noChangeInfo.getProperty()
        );
        UpData build = UpData.builder()
                .timestamp(timestamp)
                .prop(new ArrayList<UpProp>(){{
                    UpProp upProp = new UpProp();
                    upProp.setProperty(noChangeInfo.getProperty());
                    upProp.setIsArray(false);
                    upProp.setDataType(ThingDataTypeEnum.BOOLEAN.getName());
                    upProp.setValue(true);
                    add(upProp);
                }})
                .build();
        //noChange事件入库
        eventUpData2InfluxDBHandler.process(topicInfo, build);
        //noChange事件触发订阅
        noChangeEventHandler.process(topicInfo,build);
    }
    
    private GwUpEventTopic.TopicInfo builTopicInfo(Long edgeGatewayId, Long tenantId, Long deviceId, String property) {
        GwUpEventTopic.TopicInfo topicInfo = GwUpEventTopic.TopicInfo.builder()
                .eventName(property)
                .deviceId(deviceId.toString())
                .edgeGatewayId(edgeGatewayId.toString())
                .tenantId(tenantId.toString())
                .eventType("noChange")
                .build();
        return topicInfo;
    }
}
