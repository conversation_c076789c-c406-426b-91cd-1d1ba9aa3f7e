package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.ConnectorItemEntity;
import com.nti56.nlink.product.device.server.model.connectorItem.dto.QueryConnectorItemDTO;
import org.apache.ibatis.annotations.Param;

/**
 * 类说明:
 * <AUTHOR>
 * @version 1.0
 * @since JDK 1.8
 */
public interface ConnectorItemMapper extends CommonMapper<ConnectorItemEntity> {
    
    Page<ConnectorItemEntity> pageConnectorItem(IPage<ConnectorItemEntity> connectorItemPage, @Param("queryConnectorItemDTO") QueryConnectorItemDTO queryConnectorItemDTO, @Param("tenantId") Long tenantId);
}
