package com.nti56.nlink.product.device.server.service.engineering;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.nti56.nlink.common.exception.BizException;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.engineering.ProductDeviceServerDataDTO;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.service.IEngineeringProductService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/7 15:26<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class ProductDeviceHandle {


    @Autowired
    private IEngineeringProductService engineeringProductService;

    public void handle(Long tenantId, ProductDeviceServerDataDTO productDeviceServerData,Long userId,String userName, boolean tenantIdEqual ,
                       Map<Long,Long> deviceMap, Map<Long,Long> serviceMap, Map<Long,Long> tagMap, Map<Long,Long> redirectMap) {

        if(productDeviceServerData == null){
            return;
        }
        HashMap<Long, Long> edgeGatewayMap = new HashMap<>(productDeviceServerData.getEdgeGatewayList().size(),1);
        HashMap<Long, Long> thingModelMap = new HashMap<>(productDeviceServerData.getThingModelList().size(),1);
        HashMap<Long, Long> channelMap = new HashMap<>(productDeviceServerData.getChannelList().size(),1);
        HashMap<Long, Long> dataModelMap = new HashMap<>(productDeviceServerData.getDataModelList().size(),1);
        HashMap<Long, Long> labelMap = new HashMap<>(productDeviceServerData.getLabelList().size(),1);
        HashMap<Long, Long> labelGroupMap = new HashMap<>(productDeviceServerData.getLabelGroupList().size(),1);
        HashMap<Long, Long> customDriverMap = new HashMap<>(CollectionUtils.isEmpty(productDeviceServerData.getCustomDriverList()) ? 0 : productDeviceServerData.getCustomDriverList().size(),1);
        HashMap<Long, Long> customMessageMap = new HashMap<>(CollectionUtils.isEmpty(productDeviceServerData.getCustomMessageList())? 0 : productDeviceServerData.getCustomMessageList().size(),1);
    
        HashMap<Long, Long> connectorDTOMap = new HashMap<>(CollectionUtils.isEmpty(productDeviceServerData.getConnectorDTOList())? 0 : productDeviceServerData.getConnectorDTOList().size(),1);
        HashMap<Long, Long> connectorItemDTOMap = new HashMap<>(CollectionUtils.isEmpty(productDeviceServerData.getConnectorItemDTOList())? 0 : productDeviceServerData.getConnectorItemDTOList().size(),1);
    
    
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getEdgeGatewayList())) {
            productDeviceServerData.getEdgeGatewayList().forEach(c -> {
                long id = IdGenerator.generateId();
                edgeGatewayMap.put(c.getId(),id);
                c.setId(id);
          
                if(c.getVisitPublicMqtt() == null){
                    c.setVisitPublicMqtt(false);
                }
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getDeviceList())) {
            productDeviceServerData.getDeviceList().forEach(c -> {
                long id = IdGenerator.generateId();
                deviceMap.put(c.getId(),id);
                c.setId(id);
                c.setEdgeGatewayId(edgeGatewayMap.getOrDefault(c.getEdgeGatewayId(),c.getEdgeGatewayId()));
                c.setStatus(0);
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getThingModelList())) {
            productDeviceServerData.getThingModelList().forEach(c -> {
                long id = IdGenerator.generateId();
                thingModelMap.put(c.getId(),id);
                c.setId(id);
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }


        if (CollectionUtils.isNotEmpty(productDeviceServerData.getChannelList())) {
            productDeviceServerData.getChannelList().forEach(c -> {
                long id = IdGenerator.generateId();
                channelMap.put(c.getId(),id);
                c.setId(id);
                c.setEdgeGatewayId(edgeGatewayMap.getOrDefault(c.getEdgeGatewayId(),c.getEdgeGatewayId()));
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getChannelParamList())) {
            productDeviceServerData.getChannelParamList().forEach(c -> {
                c.setId(IdGenerator.generateId());
                c.setChannelId(channelMap.getOrDefault(c.getChannelId(),c.getChannelId()));
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

//        if (CollectionUtils.isNotEmpty(productDeviceServerData.getComputeTaskList())) {
//            productDeviceServerData.getComputeTaskList().forEach(c -> {
//                c.setId(IdGenerator.generateId());
//                c.setDeviceId(deviceMap.getOrDefault(c.getDeviceId(),c.getDeviceId()));
//                c.setEdgeGatewayId(edgeGatewayMap.getOrDefault(c.getEdgeGatewayId(),c.getEdgeGatewayId()));
//
//                if (!tenantIdEqual) {
//                    c.setTenantId(tenantId);
//                }
//            });
//        }



        if (CollectionUtils.isNotEmpty(productDeviceServerData.getDataModelList())) {
            productDeviceServerData.getDataModelList().forEach(c -> {
                long id = IdGenerator.generateId();
                dataModelMap.put(c.getId(),id);
                c.setId(id);
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getDataModelPropertyList())) {
            productDeviceServerData.getDataModelPropertyList().forEach(c -> {
                c.setId(IdGenerator.generateId());
                c.setDataModelId(dataModelMap.getOrDefault(c.getDataModelId(),c.getDataModelId()));
                c.setPropertyDataModelId(dataModelMap.getOrDefault(c.getPropertyDataModelId(),c.getPropertyDataModelId()));
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getDeviceModelInheritList())) {
            productDeviceServerData.getDeviceModelInheritList().forEach(c -> {
                c.setId(IdGenerator.generateId());
                c.setDeviceId(deviceMap.getOrDefault(c.getDeviceId(),c.getDeviceId()));
                c.setInheritThingModelId(thingModelMap.getOrDefault(c.getInheritThingModelId(),c.getInheritThingModelId()));
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getDeviceServiceList())) {
            productDeviceServerData.getDeviceServiceList().forEach(c -> {
                long id = IdGenerator.generateId();
                serviceMap.put(c.getId(),id);
                c.setId(id);
                c.setDeviceId(deviceMap.getOrDefault(c.getDeviceId(),c.getDeviceId()));

                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getLabelGroupList())) {
            productDeviceServerData.getLabelGroupList().forEach(c -> {
                long id = IdGenerator.generateId();
                labelGroupMap.put(c.getId(),id);
                c.setId(id);
                c.setChannelId(channelMap.getOrDefault(c.getChannelId(),c.getChannelId()));
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getLabelList())) {
            productDeviceServerData.getLabelList().forEach(c -> {
                long id = IdGenerator.generateId();
                labelMap.put(c.getId(),id);
                c.setId(id);
                c.setLabelGroupId(labelGroupMap.getOrDefault(c.getLabelGroupId(),c.getLabelGroupId()));
                c.setCreatorId(userId);
                c.setCreator(userName);
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }


        if (CollectionUtils.isNotEmpty(productDeviceServerData.getLabelBindRelationList())) {
            productDeviceServerData.getLabelBindRelationList().forEach(c -> {
                c.setId(IdGenerator.generateId());
                c.setLabelId(labelMap.getOrDefault(c.getLabelId(),c.getLabelId()));
                c.setDeviceId(deviceMap.getOrDefault(c.getDeviceId(),c.getDeviceId()));
                c.setDataModelId(dataModelMap.getOrDefault(c.getDataModelId(),c.getDataModelId()));
                c.setEdgeGatewayId(edgeGatewayMap.getOrDefault(c.getEdgeGatewayId(),c.getEdgeGatewayId()));
                if (Integer.valueOf(1).equals(c.getModelType())){
                    c.setDirectlyModelId(thingModelMap.getOrDefault(c.getDirectlyModelId(),c.getDirectlyModelId()));
                }else if (Integer.valueOf(2).equals(c.getModelType())){
                    c.setDirectlyModelId(deviceMap.getOrDefault(c.getDeviceId(),c.getDeviceId()));
                }
                c.setCreator(userName);
                c.setCreatorId(userId);
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getResourceRelationList())) {
            productDeviceServerData.getResourceRelationList().forEach(c -> {
                c.setId(IdGenerator.generateId());
                c.setDeviceId(deviceMap.getOrDefault(c.getDeviceId(),c.getDeviceId()));
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getSubscriptionList())) {
            productDeviceServerData.getSubscriptionList().forEach(c -> {
                c.setId(IdGenerator.generateId());
                c.setCallbackId(redirectMap.getOrDefault(c.getCallbackId(),c.getCallbackId()));
                if (Integer.valueOf(1).equals(c.getModelType())){
                    c.setDirectlyModelId(thingModelMap.getOrDefault(c.getDirectlyModelId(),c.getDirectlyModelId()));
                }else if (Integer.valueOf(2).equals(c.getModelType())){
                    c.setDirectlyModelId(deviceMap.getOrDefault(c.getDeviceId(),c.getDirectlyModelId()));
                }else if(ModelTypeEnum.CHANNEL_MODEL.getValue().equals(c.getModelType())){
                    if(StrUtil.isNotBlank(c.getFromId())) {
                        List<Long> idList = Arrays.stream(c.getFromId().split(",")).map(Long::parseLong).collect(Collectors.toList());
                        List<Long> newIdList = new ArrayList<>();
                        for (Long id : idList) {
                            newIdList.add(channelMap.getOrDefault(id, id));
                        }
                        c.setFromId(newIdList.stream()
                                .map(Object::toString) // 将 Long 转换为 String
                                .collect(Collectors.joining(","))); // 使用逗号连接
                    }
                    c.setDirectlyModelId(edgeGatewayMap.getOrDefault(c.getDirectlyModelId(),c.getDirectlyModelId()));

                }else if(ModelTypeEnum.GATEWAY_MODEL.getValue().equals(c.getModelType())){
                    if(StrUtil.isNotBlank(c.getFromId())) {
                        c.setFromId(String.valueOf(edgeGatewayMap.getOrDefault(Long.parseLong(c.getFromId()), Long.parseLong(c.getFromId()))));
                    }
                    c.setDirectlyModelId(edgeGatewayMap.getOrDefault(c.getDirectlyModelId(),c.getDirectlyModelId()));
                }
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getTagBindRelationList())) {
            productDeviceServerData.getTagBindRelationList().forEach(c -> {
                c.setId(IdGenerator.generateId());
                c.setTagId(tagMap.getOrDefault(c.getTagId(),c.getTagId()));
                if (Integer.valueOf(2).equals(c.getResourceType())){
                    c.setTargetId(deviceMap.getOrDefault(c.getTargetId(),c.getTargetId()));
                }else if (Integer.valueOf(3).equals(c.getResourceType())) {
                    c.setTargetId(edgeGatewayMap.getOrDefault(c.getTargetId(),c.getTargetId()));
                }else if (Integer.valueOf(4).equals(c.getResourceType())) {
                    c.setTargetId(labelMap.getOrDefault(c.getTargetId(),c.getTargetId()));
                }else if (Integer.valueOf(5).equals(c.getResourceType())) {
                    c.setTargetId(thingModelMap.getOrDefault(c.getTargetId(),c.getTargetId()));
                }
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getThingModelInheritList())) {
            productDeviceServerData.getThingModelInheritList().forEach(c -> {
                c.setId(IdGenerator.generateId());
                c.setThingModelId(thingModelMap.getOrDefault(c.getThingModelId(),c.getThingModelId()));
                c.setInheritThingModelId(thingModelMap.getOrDefault(c.getInheritThingModelId(),c.getInheritThingModelId()));
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getThingServiceList())) {
            productDeviceServerData.getThingServiceList().forEach(c -> {
                long id = IdGenerator.generateId();
                serviceMap.put(c.getId(),id);
                c.setId(id);
                c.setThingModelId(thingModelMap.getOrDefault(c.getThingModelId(),c.getThingModelId()));
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });

        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getCustomDriverList())) {
            productDeviceServerData.getCustomDriverList().forEach(c -> {
                long id = IdGenerator.generateId();
                customDriverMap.put(c.getId(),id);
                c.setId(id);
          
                if(c.getStatus() == null){
                    c.setStatus(1);
                }
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getCustomMessageList())) {
            productDeviceServerData.getCustomMessageList().forEach(c -> {
                long id = IdGenerator.generateId();
                customMessageMap.put(c.getId(),id);
                c.setId(id);
                c.setCustomDriverId(customDriverMap.getOrDefault(c.getCustomDriverId(),c.getCustomDriverId()));
          
                if(c.getDirection() == null){
                    c.setDirection(1);
                }
                if(c.getAutoResponse() == null){
                    c.setAutoResponse(false);
                }
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }
        

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getCustomFieldList())) {
            productDeviceServerData.getCustomFieldList().forEach(c -> {
                c.setId(IdGenerator.generateId());
                if (Integer.valueOf(1).equals(c.getTargetType())){
                    c.setTargetId(customDriverMap.getOrDefault(c.getTargetId(),c.getTargetId()));
                }else if (Integer.valueOf(2).equals(c.getTargetType())) {
                    c.setTargetId(customMessageMap.getOrDefault(c.getTargetId(),c.getTargetId()));
                }
          
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        }
    
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getConnectorDTOList())) {
            productDeviceServerData.getConnectorDTOList().forEach(c -> {
                long id = IdGenerator.generateId();
                connectorDTOMap.put(c.getId(),id);
                if(ObjectUtil.isNotNull(c.getEdgeGatewayId())){
                    c.setEdgeGatewayId(edgeGatewayMap.getOrDefault(c.getEdgeGatewayId(),c.getEdgeGatewayId()));
                }
                c.setId(id);
                c.setStatus(0);
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        
        }
    
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getConnectorItemDTOList())) {
            productDeviceServerData.getConnectorItemDTOList().forEach(c -> {
                long id = IdGenerator.generateId();
                connectorItemDTOMap.put(c.getId(),id);
                c.setId(id);
                c.setConnectorId(connectorDTOMap.get(c.getConnectorId()));
                if (!tenantIdEqual) {
                    c.setTenantId(tenantId);
                }
            });
        
        }


        Result<Void> r = engineeringProductService.initProductData(productDeviceServerData,tenantId);
        log.info("product-device-result"+r.toString());
        if (!r.getSignal()) {
            throw new BizException("服务器异常，请稍后重试6");
        }

        edgeGatewayMap.clear();
        deviceMap.clear();
        thingModelMap.clear();
        channelMap.clear();
        dataModelMap.clear();
        labelMap.clear();
        labelGroupMap.clear();
        connectorDTOMap.clear();
        connectorItemDTOMap.clear();
        log.info("product-device服务导入成功");
    }
}
