package com.nti56.nlink.product.device.server.model.deviceLog;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.InstantDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.InstantSerializer;
import com.influxdb.annotations.Column;
import com.influxdb.annotations.Measurement;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName PropertyLogBo
 * @date 2022/8/8 17:07
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "属性上报记录")
@Measurement(name = "nti")
public class PropertyLogBo {

    @Column(tag = true)
    String deviceId;

    String deviceName;

    @Column(tag = true)
    String property;

    @Column(tag = true)
    String propertyAlias;

    @Column(tag = true)
    String eventName;

    @Column(tag = true)
    String dataType;

    @Column(tag = true)
    String isArray;

    @Column(tag = true)
    String length;

    @Column(tag = true)
    String type;

    @Column
    Object value;

    @Column
    String field;

    @Column(timestamp = true)
    @JsonDeserialize(using = InstantDeserializer.class)
    @JsonSerialize(using = InstantSerializer.class)
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss.SSS")
    Instant time;

    Long start;

    Long stop;


}
