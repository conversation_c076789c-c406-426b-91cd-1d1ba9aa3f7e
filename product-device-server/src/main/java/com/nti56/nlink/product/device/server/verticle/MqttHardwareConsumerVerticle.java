package com.nti56.nlink.product.device.server.verticle;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.StatusEventEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SubscriptionFromEnum;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwHardwareMonitorTopic;
import com.nti56.nlink.product.device.server.model.edgegateway.vo.HardwareAlarm;
import io.vertx.core.Promise;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-10-18 17:12:00
 * @since JDK 1.8
 */
@Component
@Scope(SCOPE_PROTOTYPE)
@Slf4j
public class MqttHardwareConsumerVerticle extends MqttBaseVerticle {

    @Getter
    @Value("${mqtt.host}")
    private String host;

    @Getter
    @Value("${mqtt.port}")
    private Integer port;

    @Getter
    @Value("${mqtt.username}")
    private String username;

    @Getter
    @Value("${mqtt.password}")
    private String password;

    @Getter
    @Value("${mqtt.ssl:true}")
    private Boolean ssl;

    @Getter
    private Integer reconnectGapTime;

    @Autowired
    @Lazy
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        log.info("start-verticle mqttHardware");
        super.start(startPromise);
    }

    @Override
    public void subscribe(){
        String topic = GwHardwareMonitorTopic.createSubscribeTopic(MqttBaseVerticle.shareSubscribeGroup);
        this.client.publishHandler(s1 -> {
            String topicName = s1.topicName();
            String payload = s1.payload().toString();
            log.debug("topic: {}, msg: {}", topicName, payload);
            GwHardwareMonitorTopic.TopicInfo topicInfo = GwHardwareMonitorTopic.parseTopic(topicName);
            Long tenantId = topicInfo.getTenantId();
            Long edgeGatewayId = topicInfo.getEdgeGatewayId();
            TenantIsolation tenant = new TenantIsolation();
            tenant.setTenantId(tenantId);
            HardwareAlarm hardwareAlarm = JSONObject.parseObject(payload, HardwareAlarm.class);
            String key =  hardwareAlarm.getAlarmType() == 0 ? StatusEventEnum.SPACE.getName() : StatusEventEnum.MEMORY.getName();
            String redisKey = String.format(RedisConstant.SUBSCRIPTION_EDGE_GATEWAY_REGISTRY,edgeGatewayId, key);
            Map<Object,Object> subscriptionMap = stringRedisTemplate.opsForHash().entries(redisKey);
            if(CollectionUtil.isNotEmpty(subscriptionMap)){
                for (Map.Entry<Object, Object> entry : subscriptionMap.entrySet()) {
                    Subscription subscription = JSONObject.parseObject(String.valueOf(entry.getValue()),Subscription.class);
                    Map<String, Object> actual = getMessageMap(edgeGatewayId, hardwareAlarm);
                    subscription.edgeGatewaySubscriptionPostProcess(SubscriptionFromEnum.EDGE_GATEWAY_STATUS, edgeGatewayId,tenantId,actual,new Date().getTime(), subscription.getOutType());
                }
                log.info("网关硬件内存监控");
            }

        });
        client.subscribe(topic, 1);
    }

    @NotNull
    private static Map<String, Object> getMessageMap(Long edgeGatewayId, HardwareAlarm hardwareAlarm) {
        Map<String,Object> actual = new HashMap<>();
        actual.put("id", edgeGatewayId);
        actual.put("alarmType", hardwareAlarm.getAlarmType());
        actual.put("totalSpace", hardwareAlarm.getTotalSpace());
        actual.put("usedSpace", hardwareAlarm.getUsedSpace());
        actual.put("usableSpace", hardwareAlarm.getUsableSpace());
        actual.put("usedSpacePercent", hardwareAlarm.getUsedSpacePercent());
        actual.put("totalMemory", hardwareAlarm.getTotalMemory());
        actual.put("usedMemory", hardwareAlarm.getUsedMemory());
        actual.put("usableMemory", hardwareAlarm.getUsableMemory());
        actual.put("usedMemoryPercent", hardwareAlarm.getUsedMemoryPercent());
        return actual;
    }

    @Override
    protected void handleConnectStatusChange(Boolean connected) {
       
    }
}
