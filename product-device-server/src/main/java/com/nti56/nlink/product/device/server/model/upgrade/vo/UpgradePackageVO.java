package com.nti56.nlink.product.device.server.model.upgrade.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.nti56.nlink.product.device.client.model.dto.json.UpgradePackageRangeField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/3/12 15:55<br/>
 * @since JDK 1.8
 */
@Data
public class UpgradePackageVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    
    /**
     * 升级包名称
     */
    @Schema(description = "升级包名称")
    private String upgradePackageName;
    
    /**
     * 升级包版本
     */
    @Schema(description = "升级包版本")
    private String upgradeVersion;
    
    /**
     * 升级包类型(0 普通,1 紧急)
     */
    @Schema(description = "升级包类型(0 普通,1 紧急)")
    private Integer upgradeType;
    
    /**
     * 是否期望版本(0 否,1 是)
     */
    @Schema(description = "是否期望版本(0 否,1 是)")
    private Integer isExpectVersion;
    
    /**
     * 下载链接
     */
    @Schema(description = "下载链接")
    private String downloadLink;
    
    /**
     * 升级包描述
     */
    @Schema(description = "升级包描述")
    private String descript;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    /**
     * 升级包范围
     */
    @Schema(description = "升级包范围")
    private UpgradePackageRangeField upgradeRange;

}
