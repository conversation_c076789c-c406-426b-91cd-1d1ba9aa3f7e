package com.nti56.nlink.product.device.server.model.cloudnest;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ApiRoleMemberAddRequest extends BaseRequest {
    /**
     * 角色id
     */
    @ApiModelProperty(value = "角色id")
    private Long roleId;

    /**
     * 用户
     */
    @ApiModelProperty(value = "用户ids")
    private Set<Long> userIds;
}
