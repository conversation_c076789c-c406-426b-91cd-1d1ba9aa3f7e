package com.nti56.nlink.product.device.server.model;

import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 通道dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:58
 * @since JDK 1.8
 */
@Data
@Schema(description = "通道对象")
public class ChannelDto extends ChannelEntity {

    @Schema(description = "所属网关名称")
    private String edgeGatewayName;

    /**
     * 通道参数列表
     */
    @Schema(description = "通道参数列表")
    private List<ChannelParamDto> paramList;

    /**
     * 标签分组列表
     */
    @Schema(description = "标签分组列表")
    private List<LabelGroupDto> labelGroupDtoList;
    
}
