package com.nti56.nlink.product.device.server.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 分组表
 * 
 * author: shann
 * create time: 2022-03-08 14:05:06
 */ 
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("device_group")
public class DeviceGroupEntity {

    /**
     * id
     */ 
    private Long id;
    /**
     * 分组名称
     */ 
    private String name;
    /**
     * 所属通道id
     */ 
    private Long channelId;
    /**
     * 使用策略id
     */ 
    private Long strategyId;
    /**
     * 创建时间
     */ 
    private LocalDateTime createTime;

}
