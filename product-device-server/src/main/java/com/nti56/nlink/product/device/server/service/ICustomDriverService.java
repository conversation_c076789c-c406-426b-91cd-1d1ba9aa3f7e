package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.client.model.dto.CustomDriverExportDTO;
import com.nti56.nlink.product.device.server.entity.CustomDriverEntity;
import com.nti56.nlink.product.device.server.model.custom.CustomDriverBo;
import com.nti56.nlink.product.device.server.model.custom.CustomDriverDto;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.util.Result;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.nlink.common.mybatis.IBaseService;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Nullable;

import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 自定义协议表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-11-18 11:43:27
 * @since JDK 1.8
 */
public interface ICustomDriverService extends IBaseService<CustomDriverEntity> {

    Result<Long> create(TenantIsolation tenant, CustomDriverDto dto);

    Result<Void> update(TenantIsolation tenant, CustomDriverDto dto);

    Result<Page<CustomDriverBo>> getPage(TenantIsolation tenant, @Nullable CustomDriverEntity entity, Page<CustomDriverEntity> page);

    Result<List<CustomDriverEntity>> list(TenantIsolation tenant, CustomDriverEntity entity);

    Result<Void> deleteById(TenantIsolation tenant, Long id);

    Result<CustomDriverDto> getById(TenantIsolation tenant, Long id);

    Result<CustomDriverExportDTO> exportCustomDriver(TenantIsolation tenant, Long driverId);

    Result<Void> importCustomDriver(TenantIsolation tenantIsolation, MultipartFile file, String driverName);

    Result<Void> enableById(TenantIsolation tenant, Long entityId);

    Result<Void> disableById(TenantIsolation tenant, Long entityId);

}
