package com.nti56.nlink.product.device.server.util;


import com.nti56.nlink.common.util.JwtUserInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/7 9:27<br/>
 * @since JDK 1.8
 */
@Slf4j
public class FileUtil {
    public static void exportFile(HttpServletResponse response, File file, String exportName,String fileType) throws IOException{

        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.addHeader("Content-Length", String.valueOf(file.length()));
        response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(exportName.trim() + "." + fileType, "UTF-8"));
        byte[] buff = new byte[1024];
        BufferedInputStream bis = null;
        OutputStream os;
        try {
            os = response.getOutputStream();
            bis = new BufferedInputStream(new FileInputStream(file));
            int i = bis.read(buff);
            while (i != -1) {
                os.write(buff, 0, buff.length);
                os.flush();
                i = bis.read(buff);
            }
        } finally {
            if (bis != null) {
                bis.close();
            }
        }
    }

//    public static Result uploadFile2minio(MultipartFile file, RestTemplate restTemplate, String url){
//        if (file == null) {
//            return  Result.fail("文件上传失败！");
//        }
//        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
////        FileUtils.writeByteArrayToFile(new File(Objects.requireNonNull(file.getOriginalFilename())), file.getBytes());
////        FileSystemResource fileResource = new FileSystemResource(new File(file.getOriginalFilename()));
//        param.add("file", file.getResource());
//        HttpHeaders headers = new HttpHeaders();
//        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
//        headers.add("Authorization", JwtUserInfoUtils.getAuthorization());
//        headers.setContentType(MediaType.parseMediaType("multipart/form-data; charset=UTF-8"));
//        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<MultiValueMap<String, Object>>(param,
//                headers);
//        try {
//            return restTemplate.postForObject(url, requestEntity,Result.class);
//        } catch (Exception e) {
//            log.error("文件上传失败,file:{},url:{},error:{}", file.getResource(), url,e.getMessage());
//            return Result.fail(String.format("文件上传失败：{0}", e.getMessage()));
//        }
//
//    }
}
