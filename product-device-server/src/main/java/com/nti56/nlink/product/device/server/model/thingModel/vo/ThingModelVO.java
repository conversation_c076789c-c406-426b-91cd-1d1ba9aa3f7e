package com.nti56.nlink.product.device.server.model.thingModel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/27 17:26<br/>
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "物模型vo")
public class ThingModelVO {

    @Schema(description = "物模型主键ID")
    private Long id;

    @Schema(description = "物模型名称")
    private String name;

    @Schema(description = "是否绑定标签")
    private Boolean isBind;
}
