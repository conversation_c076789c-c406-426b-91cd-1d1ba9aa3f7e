package com.nti56.nlink.product.device.server.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.*;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.exception.BizException;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.send.NotifyDTO;
import com.nti56.nlink.product.device.server.constant.AliYunConstant;
import com.nti56.nlink.product.device.server.constant.NotifyConstant;
import com.nti56.nlink.product.device.server.entity.NotifyChannelEntity;
import com.nti56.nlink.product.device.server.entity.NotifyServerLogEntity;
import com.nti56.nlink.product.device.server.entity.TemplateEntity;
import com.nti56.nlink.product.device.server.enums.AuditEnum;
import com.nti56.nlink.product.device.server.enums.NotifyServerLogEnum;
import com.nti56.nlink.product.device.server.enums.error.NotifyErrorEnum;
import com.nti56.nlink.product.device.server.model.template.dto.CreateTemplateDTO;
import com.nti56.nlink.product.device.server.model.template.dto.EditTemplateDTO;
import com.nti56.nlink.product.device.server.service.INotifyServerLogService;
import com.nti56.nlink.product.device.server.service.ITemplateService;
import com.nti56.nlink.product.device.server.service.NotifyStrategy;
import com.nti56.nlink.product.device.server.util.RSAUtils;
import com.nti56.nlink.product.device.server.util.aliyun.AliYunSmsBase;
import com.nti56.nlink.product.device.server.util.aliyun.AliYunSmsSend;
import com.nti56.nlink.product.device.server.util.aliyun.AliYunSmsSign;
import com.nti56.nlink.product.device.server.util.aliyun.AliYunSmsTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/22 22:24<br/>
 * @since JDK 1.8
 */
@Slf4j
@Service
public class AliYunSmsStrategy extends NotifyStrategy {

    @Autowired
    private ITemplateService templateService;

    @Autowired
    private INotifyServerLogService logService;

    @Autowired
    private Mapper dozerMapper;

    @Override
    public Result addTemplate(JSONObject channelParams, CreateTemplateDTO dto, TenantIsolation tenantIsolation) {
        Result<Client> clientResult = this.getClient(channelParams);
        if (!clientResult.getSignal()) {
            return clientResult;
        }
        Client client = clientResult.getResult();

        String templateCode;
        try {
            AddSmsTemplateResponse addSmsTemplateResponse = AliYunSmsTemplate.addSmsTemplate(client, AliYunConstant.DEFAULT_TEMPLATE_TYPE,
                    dto.getTitle(), dto.getContent(), AliYunConstant.DEFAULT_REASON);
            if (!com.aliyun.teautil.Common.equalString(addSmsTemplateResponse.body.code, AliYunConstant.REQUEST_SUCCESS)) {
                log.error("新增模板失败。message：" + addSmsTemplateResponse.body.message);
                return Result.error(NotifyErrorEnum.PROVIDER_ERROR.getCode(), addSmsTemplateResponse.body.message);
            }

            templateCode = addSmsTemplateResponse.body.templateCode;

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(NotifyErrorEnum.PROVIDER_ADD_TEMPLATE_FAIL.getCode(), NotifyErrorEnum.PROVIDER_ADD_TEMPLATE_FAIL.getMessage());
        }

        TemplateEntity templateEntity = dozerMapper.map(dto, TemplateEntity.class);
        templateEntity.setTemplateCode(templateCode);
        templateEntity.setAuditStatus(AuditEnum.AUDITING.getValue());

        try {
            templateService.save(templateEntity);
        } catch (Exception e) {
            JSONObject requestJson = new JSONObject();
            requestJson.put("templateCode", templateCode);
            requestJson.put("channelParams", channelParams);
            requestJson.put("channelId", dto.getNotifyChannelId());
            String requestParams = requestJson.toJSONString();
            //数据库添加失败，应删除阿里云模板
            try {
                DeleteSmsTemplateResponse deleteSmsTemplateResponse = AliYunSmsTemplate.deleteSmsTemplate(client, templateCode);
                if (!com.aliyun.teautil.Common.equalString(deleteSmsTemplateResponse.body.code, AliYunConstant.REQUEST_SUCCESS)) {
                    log.error("数据库新增模板失败后，阿里云删除模板失败。requestParams：" + requestParams, deleteSmsTemplateResponse.body.message);
                    logService.createRequestFailLog(NotifyServerLogEnum.DELETE_TEMPLATE_FAIL,
                            requestParams, deleteSmsTemplateResponse.body.message,
                            tenantIsolation);
                }
            } catch (Exception e1) {
                log.error("数据库新增模板失败后，阿里云删除模板失败。requestParams：" + requestParams, e1);
                logService.createErrorLog(NotifyServerLogEnum.DELETE_TEMPLATE_FAIL, requestParams, e1.toString(), tenantIsolation);
            }
        }

        return Result.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteTemplate(JSONObject channelParams, TemplateEntity template) {
        if (!templateService.removeById(template.getId())) {
            return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
        }
        Result<Client> clientResult = this.getClient(channelParams);
        if (!clientResult.getSignal()) {
            throw new BizException(NotifyErrorEnum.PROVIDER_CREATE_CLIENT_FAIL.getCode(), NotifyErrorEnum.PROVIDER_CREATE_CLIENT_FAIL.getMessage());
        }
        Client client = clientResult.getResult();

        try {
            DeleteSmsTemplateResponse deleteSmsTemplateResponse = AliYunSmsTemplate.deleteSmsTemplate(client, template.getTemplateCode());
            if (!com.aliyun.teautil.Common.equalString(deleteSmsTemplateResponse.body.code, AliYunConstant.REQUEST_SUCCESS)) {
                log.error("删除模板失败。message：" + deleteSmsTemplateResponse.body.message);
                if (com.aliyun.teautil.Common.equalString(deleteSmsTemplateResponse.body.code, AliYunConstant.SMS_TEMPLATE_ILLEGAL)) {
                    return Result.ok();
                }
                throw new BizException(NotifyErrorEnum.PROVIDER_ERROR.getCode(), deleteSmsTemplateResponse.body.message);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BizException(NotifyErrorEnum.PROVIDER_REMOVE_TEMPLATE_FAIL.getCode(), NotifyErrorEnum.PROVIDER_REMOVE_TEMPLATE_FAIL.getMessage());
        }

        return Result.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result editTemplate(JSONObject channelParams, EditTemplateDTO dto, TemplateEntity templateEntity, TenantIsolation tenantIsolation) {

        TemplateEntity template = dozerMapper.map(dto, TemplateEntity.class);
        template.setAuditStatus(AuditEnum.AUDITING.getValue());
        if (!templateService.updateById(template)) {
            return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
        }

        Result<Client> clientResult = this.getClient(channelParams);
        if (!clientResult.getSignal()) {
            throw new BizException(NotifyErrorEnum.PROVIDER_CREATE_CLIENT_FAIL.getCode(), NotifyErrorEnum.PROVIDER_CREATE_CLIENT_FAIL.getMessage());
        }
        Client client = clientResult.getResult();
        try {
            if (AuditEnum.AUDIT_SUCCESS.getValue().compareTo(templateEntity.getAuditStatus()) == 0) {
                AddSmsTemplateResponse addSmsTemplateResponse = AliYunSmsTemplate.addSmsTemplate(client, AliYunConstant.DEFAULT_TEMPLATE_TYPE,
                        StringUtils.isBlank(dto.getTitle()) ? templateEntity.getTitle() : dto.getTitle(),
                        StringUtils.isBlank(dto.getContent()) ? templateEntity.getContent() : dto.getContent(),
                        AliYunConstant.DEFAULT_REASON);

                if (!com.aliyun.teautil.Common.equalString(addSmsTemplateResponse.body.code, AliYunConstant.REQUEST_SUCCESS)) {
                    log.error("新增模板失败。message：" + addSmsTemplateResponse.body.message);
                    throw new BizException(NotifyErrorEnum.PROVIDER_EDIT_TEMPLATE_FAIL.getCode(), addSmsTemplateResponse.body.message);
                }

                LambdaUpdateWrapper<TemplateEntity> luw = new LambdaUpdateWrapper<>();
                luw.set(TemplateEntity::getTemplateCode, addSmsTemplateResponse.body.templateCode)
                        .set(TemplateEntity::getUpdateTime, LocalDateTime.now())
                        .set(TemplateEntity::getAuditPrompt, "")
                        .eq(TemplateEntity::getId, dto.getId());

                try {
                    templateService.update(luw);
                } catch (Exception e) {
                    JSONObject requestJson = new JSONObject();
                    requestJson.put("templateCode", addSmsTemplateResponse.body.templateCode);
                    requestJson.put("channelParams", channelParams);
                    requestJson.put("channelId", dto.getNotifyChannelId());
                    String requestParams = requestJson.toJSONString();
                    //数据库添加失败，应删除阿里云模板
                    try {
                        DeleteSmsTemplateResponse deleteSmsTemplateResponse = AliYunSmsTemplate.deleteSmsTemplate(client, addSmsTemplateResponse.body.templateCode);
                        if (!com.aliyun.teautil.Common.equalString(deleteSmsTemplateResponse.body.code, AliYunConstant.REQUEST_SUCCESS)) {
                            log.error("数据库修改模板失败后，阿里云删除模板失败。requestParams：" + requestParams, deleteSmsTemplateResponse.body.message);
                            logService.createRequestFailLog(NotifyServerLogEnum.DELETE_TEMPLATE_FAIL,
                                    requestParams, deleteSmsTemplateResponse.body.message,
                                    tenantIsolation);
                        }
                    } catch (Exception e1) {
                        log.error("数据库修改模板失败后，阿里云删除模板失败。requestParams：" + requestParams, e1);
                        logService.createErrorLog(NotifyServerLogEnum.DELETE_TEMPLATE_FAIL, requestParams, e1.toString(), tenantIsolation);
                    }
                    log.error("数据库修改模板失败。requestParams：" + requestParams, e);
                    throw new BizException(NotifyErrorEnum.PROVIDER_EDIT_TEMPLATE_FAIL.getCode(), e.getMessage());
                }

                String templateCode = templateEntity.getTemplateCode();
                JSONObject requestJson = new JSONObject();
                requestJson.put("templateCode", templateCode);
                requestJson.put("channelParams", channelParams);
                requestJson.put("channelId", dto.getNotifyChannelId());
                String requestParams = requestJson.toJSONString();
                try {
                    DeleteSmsTemplateResponse deleteSmsTemplateResponse = AliYunSmsTemplate.deleteSmsTemplate(client, templateCode);
                    if (!com.aliyun.teautil.Common.equalString(deleteSmsTemplateResponse.body.code, AliYunConstant.REQUEST_SUCCESS)) {
                        log.error("数据库修改已通过模板，阿里云删除模板失败。requestParams：" + requestParams, deleteSmsTemplateResponse.body.message);
                        logService.createRequestFailLog(NotifyServerLogEnum.DELETE_TEMPLATE_FAIL,
                                requestParams, deleteSmsTemplateResponse.body.message,
                                tenantIsolation);
                    }
                } catch (Exception e) {
                    log.error("数据库修改已通过模板，阿里云删除模板失败。requestParams：" + requestParams, e);
                    logService.createErrorLog(NotifyServerLogEnum.DELETE_TEMPLATE_FAIL, requestParams, e.toString(), tenantIsolation);
                }


            } else if (AuditEnum.AUDIT_FAIL.getValue().compareTo(templateEntity.getAuditStatus()) == 0) {
                ModifySmsTemplateResponse modifySmsTemplateResponse = AliYunSmsTemplate.modifySmsTemplate(client,
                        AliYunConstant.DEFAULT_TEMPLATE_TYPE,
                        StringUtils.isBlank(dto.getTitle()) ? templateEntity.getTitle() : dto.getTitle(),
                        templateEntity.getTemplateCode(),
                        StringUtils.isBlank(dto.getContent()) ? templateEntity.getContent() : dto.getContent(),
                        AliYunConstant.DEFAULT_REASON);
                if (!com.aliyun.teautil.Common.equalString(modifySmsTemplateResponse.body.code, AliYunConstant.REQUEST_SUCCESS)) {
                    log.error("修改模板失败。message：" + modifySmsTemplateResponse.body.message);
                    throw new BizException(NotifyErrorEnum.PROVIDER_ERROR.getCode(), modifySmsTemplateResponse.body.message);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BizException(NotifyErrorEnum.PROVIDER_EDIT_TEMPLATE_FAIL.getCode(), NotifyErrorEnum.PROVIDER_EDIT_TEMPLATE_FAIL.getMessage());
        }

        return Result.ok();
    }

    @Override
    public Result sendNotify(JSONObject channelParams, NotifyDTO dto, TemplateEntity templateEntity, String content, TenantIsolation tenantIsolation) {


        JSONObject requestJson = new JSONObject();
        requestJson.put("channelParams", channelParams);
        requestJson.put("notifyDTO", dto);
        requestJson.put("templateEntity", templateEntity);
        requestJson.put("content", content);
        String requestParams = requestJson.toJSONString();

        String receivers = dto.getReceivers();
        if (StringUtils.isBlank(receivers)) {
            log.error("无接收人消息。requestParams:" + requestParams);
            logService.createLog(NotifyServerLogEnum.NONE_RECEIVER_INFO_ERROR, requestParams, tenantIsolation);
            return Result.error(NotifyErrorEnum.NONE_RECEIVER_INFO_ERROR.getCode(), NotifyErrorEnum.NONE_RECEIVER_INFO_ERROR.getMessage());
        }

        String[] receiverArr = receivers.split(",");

        for (String receiver : receiverArr) {
            if (!receiver.matches(NotifyConstant.PHONE_REGEX)) {
                log.error("请求参数异常，requestParams：" + requestParams);
                logService.createLog(NotifyServerLogEnum.RECEIVER_FORMAT_ERROR, requestParams, tenantIsolation);
                return Result.error(NotifyErrorEnum.RECEIVER_FORMAT_ERROR.getCode(), NotifyErrorEnum.RECEIVER_FORMAT_ERROR.getMessage());
            }
        }


        if (content.length() > NotifyConstant.MAX_SMS_SIZE) {
            log.error("短信长度超过限制，应不超过500字符。requestParams:" + requestParams);
            logService.createLog(NotifyServerLogEnum.SMS_LENGTH_OVERSIZE, requestParams, tenantIsolation);
            return Result.error(NotifyErrorEnum.SMS_LENGTH_OVERSIZE.getCode(), NotifyErrorEnum.SMS_LENGTH_OVERSIZE.getMessage());
        }

        Result<Client> clientResult = this.getClient(channelParams);
        if (!clientResult.getSignal()) {
            log.error("发送通知中，阿里云创建客户端失败。requestParams:" + requestParams);
            logService.createLog(NotifyServerLogEnum.SEND_NOTIFY_FAIL, requestParams, tenantIsolation, dto.getSubscriptionId());
            return clientResult;
        }
        Client client = clientResult.getResult();

        try {
            SendSmsResponse sendSmsResponse = AliYunSmsSend.send(client, dto.getReceivers(), channelParams.getString(AliYunConstant.SIGN_NAME),
                    templateEntity.getTemplateCode(), dto.getParams());
            if (!com.aliyun.teautil.Common.equalString(sendSmsResponse.body.code, AliYunConstant.REQUEST_SUCCESS)) {

                log.error("发送通知中，阿里云批量发送短信失败。requestParams:" + requestParams + ";errMsg:" + sendSmsResponse.body.message);
                logService.createRequestFailLog(NotifyServerLogEnum.SEND_NOTIFY_FAIL, requestParams, sendSmsResponse.body.message
                        , tenantIsolation, dto.getSubscriptionId());
                return Result.error(NotifyErrorEnum.PROVIDER_ERROR.getCode(), sendSmsResponse.body.message);
            }
        } catch (Exception e) {

            log.error("发送通知中，阿里云批量发送短信报错。requestParams:" + requestParams, e);
            logService.createErrorLog(NotifyServerLogEnum.SEND_NOTIFY_FAIL, requestParams, e.toString(), tenantIsolation,dto.getSubscriptionId());
            return Result.error(NotifyErrorEnum.PROVIDER_SEND_SMS_FAIL.getCode(), NotifyErrorEnum.PROVIDER_SEND_SMS_FAIL.getMessage());
        }

        logService.createLog(NotifyServerLogEnum.SEND_NOTIFY_SUCCESS, requestParams, tenantIsolation);
        return Result.ok();
    }

    @Override
    public void setAuditStatus(JSONObject channelParams, TemplateEntity t) {
        Result<Client> client = this.getClient(channelParams);
        if (!client.getSignal()) {
            return;
        }
        try {
            QuerySmsTemplateResponse querySmsTemplateResponse = AliYunSmsTemplate.querySmsTemplate(client.getResult(), t.getTemplateCode());
            if (com.aliyun.teautil.Common.equalString(querySmsTemplateResponse.body.code, AliYunConstant.REQUEST_SUCCESS)) {

                Integer templateStatus = querySmsTemplateResponse.body.templateStatus;
                if (AuditEnum.AUDITING.getValue().compareTo(templateStatus) != 0) {
                    LambdaUpdateWrapper<TemplateEntity> luw = new LambdaUpdateWrapper<>();
                    luw.set(TemplateEntity::getAuditStatus, templateStatus)
                            .set(TemplateEntity::getAuditPrompt, querySmsTemplateResponse.body.reason)
                            .set(TemplateEntity::getUpdateTime, LocalDateTime.now())
                            .eq(TemplateEntity::getId, t.getId());
                    templateService.update(luw);
                }

            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    @Override
    public Result validationChannel(NotifyChannelEntity notifyChannelEntity) {
        JSONObject channelParamsJson = JSON.parseObject(notifyChannelEntity.getParams());
        Result<Client> client = this.getClient(channelParamsJson);
        if (!client.getSignal()) {
            return Result.error(NotifyErrorEnum.PROVIDER_CREATE_CLIENT_FAIL.getCode(), NotifyErrorEnum.PROVIDER_CREATE_CLIENT_FAIL.getMessage());
        }
        try {
            QuerySmsSignResponse querySmsSignResponse = AliYunSmsSign.querySmsSign(client.getResult(), channelParamsJson.getString(AliYunConstant.SIGN_NAME));
            if (!com.aliyun.teautil.Common.equalString(querySmsSignResponse.body.code, AliYunConstant.REQUEST_SUCCESS)) {
                return Result.error(NotifyErrorEnum.PROVIDER_QUERY_SIGN_FAIL.getCode(), NotifyErrorEnum.PROVIDER_QUERY_SIGN_FAIL.getMessage());
            } else {
                if (AuditEnum.AUDIT_SUCCESS.getValue().compareTo(querySmsSignResponse.body.signStatus) != 0) {
                    return Result.error(NotifyErrorEnum.PROVIDER_SIGN_AUDIT_NOT_PASS.getCode(), NotifyErrorEnum.PROVIDER_SIGN_AUDIT_NOT_PASS.getMessage());
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(NotifyErrorEnum.PROVIDER_QUERY_SIGN_FAIL.getCode(), NotifyErrorEnum.PROVIDER_QUERY_SIGN_FAIL.getMessage());
        }
        return Result.ok();
    }

    @Override
    public void retryDeleteTemplate(JSONObject channelParams, NotifyServerLogEntity notifyServerLogEntity, JSONObject requestJson, TenantIsolation tenantIsolation) {

        Result<Client> clientResult = this.getClient(channelParams);
        String requestParams = notifyServerLogEntity.getRequestParams();
        Long sourceId = notifyServerLogEntity.getSourceId();
        if (!clientResult.getSignal()) {
            logService.createLog(sourceId, NotifyServerLogEnum.DELETE_TEMPLATE_FAIL, requestParams, tenantIsolation);
            return;
        }

        try {
            DeleteSmsTemplateResponse deleteSmsTemplateResponse = AliYunSmsTemplate.deleteSmsTemplate(clientResult.getResult(),
                    requestJson.getString("templateCode"));
            if (!com.aliyun.teautil.Common.equalString(deleteSmsTemplateResponse.body.code, AliYunConstant.REQUEST_SUCCESS)) {
                log.error("删除模板失败。message：" + deleteSmsTemplateResponse.body.message);
                if (com.aliyun.teautil.Common.equalString(deleteSmsTemplateResponse.body.code, AliYunConstant.SMS_TEMPLATE_ILLEGAL)) {
                    logService.createLog(sourceId, NotifyServerLogEnum.DELETE_TEMPLATE_SUCCESS, requestParams, tenantIsolation);
                    return;
                }
                logService.createRequestFailLog(sourceId, NotifyServerLogEnum.DELETE_TEMPLATE_FAIL, requestParams
                        , deleteSmsTemplateResponse.body.message, tenantIsolation);
                return;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            logService.createErrorLog(sourceId, NotifyServerLogEnum.DELETE_TEMPLATE_FAIL, requestParams, e.toString(), tenantIsolation);
            return;
        }
        logService.createLog(sourceId, NotifyServerLogEnum.DELETE_TEMPLATE_SUCCESS, requestParams, tenantIsolation);
    }

    @Override
    public void retrySendFailNotify(JSONObject channelParams, NotifyServerLogEntity notifyServerLogEntity, TemplateEntity templateEntity, JSONObject requestJson, TenantIsolation tenantIsolation) {

        String requestParams = notifyServerLogEntity.getRequestParams();
        Long sourceId = notifyServerLogEntity.getSourceId();
        Result<Client> clientResult = this.getClient(channelParams);
        if (!clientResult.getSignal()) {
            log.error("发送通知中，阿里云创建客户端失败。requestParams:" + requestParams);
            logService.createLog(sourceId, NotifyServerLogEnum.SEND_NOTIFY_FAIL, requestParams, tenantIsolation,notifyServerLogEntity.getSubscriptionId());
            return;
        }
        Client client = clientResult.getResult();

        NotifyDTO dto = requestJson.getObject("notifyDTO", NotifyDTO.class);

        try {
            SendSmsResponse sendSmsResponse = AliYunSmsSend.send(client, dto.getReceivers(), channelParams.getString(AliYunConstant.SIGN_NAME),
                    templateEntity.getTemplateCode(), dto.getParams());
            if (!com.aliyun.teautil.Common.equalString(sendSmsResponse.body.code, AliYunConstant.REQUEST_SUCCESS)) {

                log.error("发送通知中，阿里云批量发送短信失败。requestParams:" + requestParams + ";errMsg:" + sendSmsResponse.body.message);
                logService.createRequestFailLog(sourceId, NotifyServerLogEnum.SEND_NOTIFY_FAIL, requestParams, sendSmsResponse.body.message, tenantIsolation,notifyServerLogEntity.getSubscriptionId());
                return;
            }
        } catch (Exception e) {

            log.error("发送通知中，阿里云批量发送短信报错。requestParams:" + requestParams, e);
            logService.createErrorLog(sourceId, NotifyServerLogEnum.SEND_NOTIFY_FAIL, requestParams, e.toString(), tenantIsolation,notifyServerLogEntity.getSubscriptionId());
            return;
        }

        logService.createLog(sourceId, NotifyServerLogEnum.SEND_NOTIFY_SUCCESS, requestParams, tenantIsolation);
    }


    private Result<Client> getClient(JSONObject channelParamsJson) {
        try {
            return Result.ok(AliYunSmsBase.createClient(channelParamsJson.getString(AliYunConstant.ACCESS_KEY_ID),
                    RSAUtils.decryptByPrivateKey(channelParamsJson.getString(AliYunConstant.ACCESS_KEY_SECRET), NotifyConstant.PRIVATE_KEY)));
        } catch (Exception e) {
            log.error("阿里云创建客户端失败", e);
            return Result.error(NotifyErrorEnum.PROVIDER_CREATE_CLIENT_FAIL.getCode(), NotifyErrorEnum.PROVIDER_CREATE_CLIENT_FAIL.getMessage());
        }
    }
}
