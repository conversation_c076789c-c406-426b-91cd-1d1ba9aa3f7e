package com.nti56.nlink.product.device.server.service.export.call;

import java.util.concurrent.Callable;


import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.service.IFeignExportService;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/19 17:54<br/>
 * @version 1.0
 * @since JDK 1.8
 */
public class ExportCallable implements Callable<R> {

  private IFeignExportService feignExportService;

  private TenantIsolation tenantIsolation;

  private Integer type;

  public ExportCallable(IFeignExportService feignExportService, Integer type, TenantIsolation tenantId) {
    this.feignExportService = feignExportService;
    this.type = type;
    this.tenantIsolation = tenantId;
  }

  @Override
  public R call() throws Exception {
    return feignExportService.export(tenantIsolation, type);
  }
}
