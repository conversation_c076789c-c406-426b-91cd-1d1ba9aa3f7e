package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 操作审计日志表
 * @TableName audit_log
 */
@TableName(value ="audit_log")
@Data
public class AuditLogEntity implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 操作
     */
    private String actionInfo;

    /**
     * 操作对象
     */
    private String target;

    /**
     * 操作明细
     */
    private String details;

    /**
     * 操作人ID
     */
    private Long userId;

    /**
     * 操作人
     */
    private String userName;

    /**
     * 操作时间
     */
    private Date actionTimestamp;

    /**
     * 操作状态
     */
    private Integer actionStatus;

    private Long tenantId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}