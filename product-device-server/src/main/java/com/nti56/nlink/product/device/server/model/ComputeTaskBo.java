package com.nti56.nlink.product.device.server.model;

import com.nti56.nlink.product.device.server.domain.thing.enumerate.StatusEnum;
import com.nti56.nlink.product.device.server.entity.ComputeTaskEntity;

import lombok.Data;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:04:42
 * @since JDK 1.8
 */
@Data
public class ComputeTaskBo extends ComputeTaskEntity{
    
    private String edgeGatewayName;

    private String enableStatusStr;
    
    public String getEnableStatusStr() {
        if(this.getEnableStatus() == null){
            return null;
        }
        if(this.enableStatusStr == null){
            this.enableStatusStr = StatusEnum.typeOfValue(this.getEnableStatus()).getNameDesc();
        }
        return this.enableStatusStr;
    }

    public void setEnableStatusStr(String enableStatusStr) {
        this.enableStatusStr = enableStatusStr;
    }
    
}
