package com.nti56.nlink.product.device.server.enums;

import lombok.Getter;

public enum ConnectorEnum {
    
    MQTT((byte) 0, "mqtt"),
    HTTP((byte) 1, "http");
    
    @Getter
    private byte code;
    
    @Getter
    private String typeName;
    
    ConnectorEnum(byte code, String typeName) {
        this.code = code;
        this.typeName = typeName;
    }
    
    public static ConnectorEnum typeOfCode(byte code) {
        ConnectorEnum[] values = ConnectorEnum.values();
        for (ConnectorEnum v : values) {
            if (v.code == code) {
                return v;
            }
        }
        return null;
    }
}
