//package com.nti56.nlink.product.device.server.model.neo4j;
//
//import lombok.*;
//import org.neo4j.ogm.annotation.*;
//
//import java.util.Set;
//
///**
// * 类说明：
// *
// * @ClassName ModelLabel
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/11/18 16:27
// * @Version 1.0
// */
//
//@NodeEntity("THING_MODEL")
//@Data
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
//public class Neo4jModelDTO {
//
//    @Id
//    Long modelId;
//    @Property
//    private String name;
//    @Property
//    private String description;
//    @Property
//    private String updateTime;
//    @Property
//    private Integer modelType;
//    @Property
//    private Long tenantId;
///*    @Property
//    private Object define;*/
//
//    @Relationship(type = "INHERIT", direction = Relationship.OUTGOING)
//    private Set<Neo4jModelDTO> inheritModels;
//
//}
