package com.nti56.nlink.product.device.server.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:04:50
 * @since JDK 1.8
 */
@Data
@Schema(description = "创建计算任务参数")
public class ComputeTaskParam {
    
    @Schema(description = "任务名称")
    private String name;
    
    @Schema(description = "订阅的采集任务id")
    private Long gatherTaskId;

    @Schema(description = "设备ids")
    private List<Long> deviceIds;
}
