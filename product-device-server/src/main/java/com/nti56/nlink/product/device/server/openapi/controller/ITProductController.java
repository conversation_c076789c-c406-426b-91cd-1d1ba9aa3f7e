package com.nti56.nlink.product.device.server.openapi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.model.product.dto.*;
import com.nti56.nlink.product.device.server.openapi.convertor.ITResultConvertor;
import com.nti56.nlink.common.it.ITResult;
import com.nti56.nlink.product.device.server.openapi.domain.request.GetProductRequest;
import com.nti56.nlink.product.device.server.openapi.domain.request.PageProductsRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ITProductController
 * @date 2022/7/13 15:16
 * @Version 1.0
 */
@RestController
@RequestMapping("it/products")
@Tag(name = "产品模块")
@Deprecated
public class ITProductController {

    @Deprecated
    @PostMapping(path = "byId")
    @Operation(summary = "根据id获取产品")
    public ITResult<Void> getProduct(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                     @RequestBody GetProductRequest getProductRequest) {
        return ITResultConvertor.convert(
                R.error("接口已过期"));
    }

    @Deprecated
    @PostMapping(path = "page")
    @Operation(summary = "分页查询产品信息")
    public ITResult<Page<Void>> pageProduct(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                            @RequestBody PageProductsRequest pageProductsRequest) {
        return ITResultConvertor.convert(
                R.error("接口已过期"));
    }

    @Deprecated
    @PostMapping(path = "list")
    @Operation(summary = "查询所有产品信息")
    public ITResult<List<Void>> listProduct(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                            @RequestBody QueryProductDTO dto) {
        return ITResultConvertor.convert(
                R.error("接口已过期"));
    }
}

