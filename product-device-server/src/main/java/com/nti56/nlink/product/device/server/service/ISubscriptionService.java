package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.entity.SubscriptionEntity;
import com.nti56.nlink.product.device.server.model.device.dto.FaultEventInstance;
import com.nti56.nlink.product.device.server.model.device.dto.NoChangeEventInstance;
import com.nti56.nlink.product.device.server.model.device.dto.TriggerEventInstance;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 订阅模块 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-09-22 15:17:38
 * @since JDK 1.8
 */
public interface ISubscriptionService extends IBaseService<SubscriptionEntity> {

    Result<SubscriptionEntity> save(TenantIsolation tenantIsolation, SubscriptionEntity entity);

    Result<Page<SubscriptionEntity>> getPage(SubscriptionEntity entity, Page<SubscriptionEntity> page);
    
    Result<Page<SubscriptionEntity>> subscriptionGatewayPage(Long edgeGatewayId,SubscriptionEntity entity, Page<SubscriptionEntity> page,TenantIsolation tenantIsolation);

    Result<List<SubscriptionEntity>> list(SubscriptionEntity entity);

    Result update(TenantIsolation tenantIsolation, SubscriptionEntity entity);

    Result deleteById(TenantIsolation tenantIsolation, Long id);

    Result<SubscriptionEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation);

    void sendFaultEventSubscription(Long deviceId,Long tenantId, FaultEventInstance instance);
    
    void sendTriggerEventSubscription(Long deviceId,Long tenantId, TriggerEventInstance instance);
    
    void sendNoChangeEventSubscription(Long deviceId, Long tenantId, NoChangeEventInstance instance);

    void execRedirectWithPayload(Long tenant, Long callbackId, String payload);

    void batchDeleteByDirectlyModelIds(Long tenantId, List<Long> ids, Integer modelType);

    void invokeDeviceService(Long tenant, Long targetDevice, String targetService, Map<String,Object> result, DeviceServiceLogEntity logEntity,Boolean isRecordLog);

    void sendNotifies(Long tenant, Long callbackId, String receivers, String jsonString);

    void sendNotifies(Long tenant, Long subscriptionId, Long callbackId, String receivers, String jsonString);

    Result<Void> deleteBatchByIds(TenantIsolation tenantIsolation, List<Long> ids);
    
    Result<Void> delByDirectlyModelId(Long directlyModelId,Long tenantId, ModelTypeEnum type);

    void deleteNotifyIfNeed(SubscriptionEntity entity);

    void deleteNotify(Long entityId);

    Result<List<SubscriptionEntity>> getChannelSubscriptionByEdgeGatewayId(Long edgeGatewayId);

    boolean checkSubscription(SubscriptionEntity entity);

    void deleteByThingModelId(Long tenantId , Long thingModelId);

}
