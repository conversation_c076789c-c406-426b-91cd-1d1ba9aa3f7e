package com.nti56.nlink.product.device.server.loader;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;


import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.ConnectorEntity;
import com.nti56.nlink.product.device.server.entity.ConnectorItemEntity;
import com.nti56.nlink.product.device.server.entity.json.element.connector.MqttConnectorInfo;
import com.nti56.nlink.product.device.server.enums.ConnectorEnum;
import com.nti56.nlink.product.device.server.factory.SpringVerticleFactory;
import com.nti56.nlink.product.device.server.mapper.ConnectorItemMapper;
import com.nti56.nlink.product.device.server.mapper.ConnectorMapper;
import com.nti56.nlink.product.device.server.verticle.MqttConnectorConsumerVerticle;

import io.vertx.core.DeploymentOptions;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ConnectorInstanceLoader implements ApplicationListener<ApplicationReadyEvent>{
    
    @Autowired
    private ConnectorMapper connectorMapper;
    
    @Autowired
    private ConnectorItemMapper connectorItemMapper;

    @Autowired 
    private SpringVerticleFactory springVerticleFactory;

    @Autowired
    private Vertx vertx;

    // processorId -> deploymentId
    private Map<Long, String> deploymentMap;

    // connectorId -> [processorId, processorId]
    private Map<Long, List<Long>> processorMap;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        deploymentMap = new HashMap<>();
        processorMap = new HashMap<>();
        initMqttReceive();
    }

    public Result<Boolean> getConnectStatus(Long connectorId){
        CompletableFuture<Boolean> resultFuture = new CompletableFuture<>();
        vertx.eventBus().<Boolean>request(MqttConnectorConsumerVerticle.EVENTBUS_CONNECTOR_STATUS + "_" + connectorId, "status", t -> {
            if(t.succeeded()){
                resultFuture.complete(t.result().body());
            }else{
                resultFuture.complete(false);
            }
        });
        try {
            Boolean status = resultFuture.get(10, TimeUnit.SECONDS);
            return Result.ok(status);
        } catch (Exception e) {
            log.error("{}", e);
            return Result.error(e.getMessage());
        }
    }

    //关闭连接器
    public Result<Void> closeConnector(Long connectorId){
        List<Long> processorList = processorMap.get(connectorId);
        if(processorList == null || processorList.size() <= 0){
            return Result.ok();
        }
        for(Long processorId:processorList){
            String deplomentId = deploymentMap.get(processorId);
            if(deplomentId == null || "".equals(deplomentId)){
                continue;
            }
            vertx.undeploy(deplomentId);
        }
        processorMap.remove(connectorId);
        return Result.ok();
    }

    //开启连接器
    public Result<Void> startConnector(Long connectorId){
        QueryWrapper<ConnectorEntity> connectorWrapper = new QueryWrapper<ConnectorEntity>()
            .eq("id", connectorId)
            .eq("connector_type", ConnectorEnum.MQTT.getCode()) //mqtt连接器
            .eq("mode_type", 0) //发送连接器
            .eq("deleted", 0) //未删除
            .eq("status", 1); //启用状态
        ConnectorEntity connectorEntity = connectorMapper.selectOne(connectorWrapper);
        if(connectorEntity == null){
            return Result.error("找不到连接器");
        }
        closeConnector(connectorId);
        return startConnector(connectorEntity);
    }

    //开启连接器
    private Result<Void> startConnector(ConnectorEntity connectorEntity){

        Long connectorId = connectorEntity.getId();
        Long tenantId = connectorEntity.getTenantId();
        String connectorInfoStr = connectorEntity.getConnectorInfo();
        Long edgeGatewayId = connectorEntity.getEdgeGatewayId();

        List<Long> processorIdList = new ArrayList<>();
        processorMap.put(connectorId, processorIdList);

        QueryWrapper<ConnectorItemEntity> itemWrapper = new QueryWrapper<ConnectorItemEntity>()
            .eq("connector_id", connectorId)
            .eq("deleted", 0); //未删除
        List<ConnectorItemEntity> connectorItemList = connectorItemMapper.selectList(itemWrapper);
        if(connectorItemList == null || connectorItemList.size() <= 0){
            return Result.ok();
        }
        MqttConnectorInfo connectorInfo = JSONObject.parseObject(connectorInfoStr, MqttConnectorInfo.class);
        for(ConnectorItemEntity item:connectorItemList){
            Long processorId = item.getId();
            processorIdList.add(processorId);

            String topic = item.getTopic();

            JsonObject configObj = new JsonObject();
            configObj.put("host", connectorInfo.getIp());
            configObj.put("port", connectorInfo.getPort());
            configObj.put("username", connectorInfo.getUsername());
            configObj.put("password", connectorInfo.getPassword());
            configObj.put("tenantId", tenantId);
            configObj.put("connectorId", connectorId);
            configObj.put("processorId", processorId);
            configObj.put("topic", topic);
            configObj.put("qos", connectorInfo.getQos());
            configObj.put("reconnectGapTime", connectorInfo.getReconnectGapTime());
            configObj.put("ssl", false); //todo
            configObj.put("edgeGatewayId",edgeGatewayId);
            DeploymentOptions options = new DeploymentOptions()
                .setConfig(configObj)
                .setInstances(1);
            log.info("regist connector begin: tenantId: {}, ip: {}, port: {}", tenantId, connectorInfo.getIp(), connectorInfo.getPort());
            vertx.deployVerticle(springVerticleFactory.prefix() + ":" + MqttConnectorConsumerVerticle.class.getName(), options)
            .onComplete(r -> {
                if(r.succeeded()){
                }
                String deploymentId = r.result();
                log.info("regist connector complete: {}, tenantId: {}, ip: {}, port: {}", r.succeeded(), tenantId, connectorInfo.getIp(), connectorInfo.getPort());
                deploymentMap.put(processorId, deploymentId);
            });
        }
        return Result.ok();
    }

    private void initMqttReceive() {
        
        //mqtt接收器发起连接并监听
        QueryWrapper<ConnectorEntity> connectorWrapper = new QueryWrapper<ConnectorEntity>()
            .eq("connector_type", ConnectorEnum.MQTT.getCode()) //mqtt连接器
            .eq("mode_type", 0) //发送连接器
            .eq("deleted", 0) //未删除
            .eq("status", 1); //启用状态
        List<ConnectorEntity> connectorList = connectorMapper.selectList(connectorWrapper);
        if(connectorList == null || connectorList.size() <= 0){
            return;
        }
        for(ConnectorEntity connectorEntity:connectorList){
            startConnector(connectorEntity);
        }

    }

}
