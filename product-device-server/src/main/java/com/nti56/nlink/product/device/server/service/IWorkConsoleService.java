package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import com.nti56.nlink.product.device.server.model.EdgeGatewayDto;
import com.nti56.nlink.product.device.server.model.IdListDTO;
import com.nti56.nlink.product.device.server.model.edgegateway.dto.CreateEdgeGatewayDTO;
import com.nti56.nlink.product.device.server.model.edgegateway.dto.EditEdgeGatewayDTO;
import com.nti56.nlink.product.device.server.model.edgegateway.vo.CreateEdgeGatewayVO;
import com.nti56.nlink.product.device.server.model.edgegateway.vo.EdgeGatewayCurrentTimeInfoVO;
import com.nti56.nlink.product.device.server.model.edgegateway.vo.EdgeGatewayVO;
import com.nti56.nlink.product.device.server.model.workConsole.WorkConsoleVO;

import java.util.List;
import java.util.Map;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 14:11:44
 * @since JDK 1.8
 */
public interface IWorkConsoleService {

    /**
     * 获取网关工作台数 网关总数 网关在线数 todo 网关今日采集点位总数
     * @param tenantIsolation
     * @return
     */
    Result<WorkConsoleVO> getWorkConsoleData(TenantIsolation tenantIsolation);
}