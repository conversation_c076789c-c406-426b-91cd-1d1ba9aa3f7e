package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.ResourceRelationEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 资源关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11 17:27:49
 */
@Mapper
public interface ResourceRelationMapper extends CommonMapper<ResourceRelationEntity> {
    @Delete("delete from resource_relation where device_id = #{deviceId} AND tenant_id = #{tenantId}")
    int deleteByDeviceId(@Param("tenantId") Long tenantId,@Param("deviceId") Long deviceId);
}
