package com.nti56.nlink.product.device.server.util.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName RedisUtil
 * @date 2022/7/25 16:05
 * @Version 1.0
 */
@Slf4j
public class RedisUtil {
    private static long TIME_OUT = 2592000L;
    private RedisTemplate<String, Object> redisTemplate;
    private String keyPrefix;
    private ScheduledExecutorService scheduled = null;

    public RedisUtil(RedisTemplate<String, Object> redisTemplate, String keyPrefix) {
        this.keyPrefix = keyPrefix;
        this.redisTemplate = redisTemplate;
    }

    private String getFullKey(String key) {
        return this.keyPrefix != null ? this.keyPrefix + ":" + key : key;
    }

    private Set<String> getFullKey(List<String> keys) {
        Set<String> fullKeys = new HashSet();
        String prefix = this.keyPrefix;
        if (prefix != null) {
            Iterator var4 = keys.iterator();

            while(var4.hasNext()) {
                String key = (String)var4.next();
                fullKeys.add(prefix + ":" + key);
            }
        } else {
            fullKeys.addAll(keys);
        }

        return fullKeys;
    }

    public boolean expire(String key, long time) {
        try {
            this.redisTemplate.expire(this.getFullKey(key), time, TimeUnit.SECONDS);
            return true;
        } catch (Exception var5) {
            log.error("expire error", var5);
            return false;
        }
    }

    public long getExpire(String key) {
        return this.redisTemplate.getExpire(this.getFullKey(key), TimeUnit.SECONDS);
    }

    public boolean hasKey(String key) {
        try {
            return this.redisTemplate.hasKey(this.getFullKey(key));
        } catch (Exception var3) {
            log.error("hasKey error", var3);
            return false;
        }
    }

    public Boolean del(String... key) {
        if (key != null && key.length > 0) {
            try {
                if (key.length == 1) {
                    return this.redisTemplate.delete(this.getFullKey(key[0]));
                } else {
                    Long lon = this.redisTemplate.delete(this.getFullKey(CollectionUtils.arrayToList(key)));
                    return lon != null && lon > 0L;
                }
            } catch (Exception var3) {
                log.error("del error", var3);
                return false;
            }
        } else {
            return false;
        }
    }

    public Object get(String key) {
        return key == null ? null : this.redisTemplate.opsForValue().get(this.getFullKey(key));
    }

    public boolean set(String key, Object value) {
        try {
            this.redisTemplate.opsForValue().set(this.getFullKey(key), value);
            return true;
        } catch (Exception var4) {
            log.error("set error", var4);
            return false;
        }
    }

    public boolean setNoExpire(String key, Object value) {
        try {
            this.redisTemplate.opsForValue().set(this.getFullKey(key), value);
            return true;
        } catch (Exception var4) {
            log.error("set error", var4);
            return false;
        }
    }

    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0L) {
                this.redisTemplate.opsForValue().set(this.getFullKey(key), value, time, TimeUnit.SECONDS);
            } else {
                this.set(key, value);
            }

            return true;
        } catch (Exception var6) {
            log.error("set error", var6);
            return false;
        }
    }

    public long incr(String key, long delta) {
        if (delta < 0L) {
            throw new RuntimeException("递增因子必须大于");
        } else {
            return this.redisTemplate.opsForValue().increment(this.getFullKey(key), delta);
        }
    }

    public Long increment(String key, long liveTime) {
        RedisAtomicLong entityIdCounter = new RedisAtomicLong(this.getFullKey(key), this.redisTemplate.getConnectionFactory());
        Long increment = entityIdCounter.incrementAndGet();
        if ((null == increment || increment == 0L) && liveTime > 0L) {
            entityIdCounter.expire(liveTime, TimeUnit.SECONDS);
        }

        return increment;
    }

    public long decr(String key, long delta) {
        if (delta < 0L) {
            throw new RuntimeException("递减因子必须大于");
        } else {
            return this.redisTemplate.opsForValue().increment(this.getFullKey(key), -delta);
        }
    }

    public Object hget(String key, String item) {
        return this.redisTemplate.opsForHash().get(this.getFullKey(key), item);
    }

    public Map<Object, Object> hmget(String key) {
        return this.redisTemplate.opsForHash().entries(this.getFullKey(key));
    }

    public boolean hmset(String key, Map<String, Object> map) {
        try {
            this.redisTemplate.opsForHash().putAll(this.getFullKey(key), map);
            return true;
        } catch (Exception var4) {
            log.error("hmset error", var4);
            return false;
        }
    }

    public boolean hmset(String key, Map<String, Object> map, long time) {
        try {
            this.redisTemplate.opsForHash().putAll(this.getFullKey(key), map);
            if (time > 0L) {
                this.expire(key, time);
            }

            return true;
        } catch (Exception var6) {
            log.error("hmset error", var6);
            return false;
        }
    }

    public boolean hset(String key, String item, Object value) {
        try {
            this.redisTemplate.opsForHash().put(this.getFullKey(key), item, value);
            return true;
        } catch (Exception var5) {
            log.error("hset error", var5);
            return false;
        }
    }

    public boolean hset(String key, String item, Object value, long time) {
        try {
            this.redisTemplate.opsForHash().put(this.getFullKey(key), item, value);
            if (time > 0L) {
                this.expire(key, time);
            }

            return true;
        } catch (Exception var7) {
            log.error("hset error", var7);
            return false;
        }
    }

    public void hdel(String key, Object... item) {
        this.redisTemplate.opsForHash().delete(this.getFullKey(key), item);
    }

    public boolean hHasKey(String key, String item) {
        return this.redisTemplate.opsForHash().hasKey(this.getFullKey(key), item);
    }

    public double hincr(String key, String item, double by) {
        return this.redisTemplate.opsForHash().increment(this.getFullKey(key), item, by);
    }

    public double hdecr(String key, String item, double by) {
        return this.redisTemplate.opsForHash().increment(this.getFullKey(key), item, -by);
    }

    public Set<Object> sGet(String key) {
        try {
            return this.redisTemplate.opsForSet().members(this.getFullKey(key));
        } catch (Exception var3) {
            log.error("sGet error", var3);
            return null;
        }
    }

    public List<Object> sPop(String key,long count) {
        try {
            return this.redisTemplate.opsForSet().pop(this.getFullKey(key),count);
        } catch (Exception var3) {
            log.error("sPop error", var3);
            return null;
        }
    }

    public boolean sHasKey(String key, Object value) {
        try {
            return this.redisTemplate.opsForSet().isMember(this.getFullKey(key), value);
        } catch (Exception var4) {
            log.error("sHasKey error", var4);
            return false;
        }
    }

    public long sSet(String key, Object... values) {
        try {
            return this.redisTemplate.opsForSet().add(this.getFullKey(key), values);
        } catch (Exception var4) {
            log.error("sSet error", var4);
            return 0L;
        }
    }

    public long sSetAndTime(String key, long time, Object... values) {
        try {
            Long count = this.redisTemplate.opsForSet().add(this.getFullKey(key), values);
            if (time > 0L) {
                this.expire(key, time);
            }

            return count;
        } catch (Exception var6) {
            log.error("sSetAndTime error", var6);
            return 0L;
        }
    }

    public long sGetSetSize(String key) {
        try {
            return this.redisTemplate.opsForSet().size(this.getFullKey(key));
        } catch (Exception var3) {
            log.error("sGetSetSize error", var3);
            return 0L;
        }
    }

    public long setRemove(String key, Object... values) {
        try {
            Long count = this.redisTemplate.opsForSet().remove(this.getFullKey(key), values);
            return count;
        } catch (Exception var4) {
            log.error("setRemove error", var4);
            return 0L;
        }
    }

    public List<Object> lGet(String key, long start, long end) {
        try {
            return this.redisTemplate.opsForList().range(this.getFullKey(key), start, end);
        } catch (Exception var7) {
            log.error("lGet error", var7);
            return null;
        }
    }

    public long lGetListSize(String key) {
        try {
            return this.redisTemplate.opsForList().size(this.getFullKey(key));
        } catch (Exception var3) {
            log.error("lGetListSize error", var3);
            return 0L;
        }
    }

    public Object lGetIndex(String key, long index) {
        try {
            return this.redisTemplate.opsForList().index(this.getFullKey(key), index);
        } catch (Exception var5) {
            log.error("lGetIndex error", var5);
            return null;
        }
    }

    public boolean lSet(String key, Object value) {
        try {
            this.redisTemplate.opsForList().rightPush(this.getFullKey(key), value);
            return true;
        } catch (Exception var4) {
            log.error("lSet error", var4);
            return false;
        }
    }

    public boolean lSet(String key, Object value, long time) {
        try {
            this.redisTemplate.opsForList().rightPush(this.getFullKey(key), value);
            if (time > 0L) {
                this.expire(key, time);
            }

            return true;
        } catch (Exception var6) {
            log.error("lSet error", var6);
            return false;
        }
    }

    public boolean lSet(String key, List<Object> value) {
        try {
            this.redisTemplate.opsForList().rightPushAll(this.getFullKey(key), value);
            return true;
        } catch (Exception var4) {
            log.error("lSet error", var4);
            return false;
        }
    }

    public boolean lSet(String key, List<Object> value, long time) {
        try {
            this.redisTemplate.opsForList().rightPushAll(this.getFullKey(key), value);
            if (time > 0L) {
                this.expire(key, time);
            }

            return true;
        } catch (Exception var6) {
            log.error("lSet error", var6);
            return false;
        }
    }

    public boolean lUpdateIndex(String key, long index, Object value) {
        try {
            this.redisTemplate.opsForList().set(this.getFullKey(key), index, value);
            return true;
        } catch (Exception var6) {
            log.error("lUpdateIndex error", var6);
            return false;
        }
    }

    public long lRemove(String key, long count, Object value) {
        try {
            Long remove = this.redisTemplate.opsForList().remove(this.getFullKey(key), count, value);
            return remove;
        } catch (Exception var6) {
            log.error("lRemove error", var6);
            return 0L;
        }
    }

    public void init() {
        this.scheduled = new ScheduledThreadPoolExecutor(1);
        this.scheduled.scheduleAtFixedRate(new Runnable() {
            public void run() {
                try {
                    RedisUtil.this.redisTemplate.hasKey(RedisUtil.this.keyPrefix);
                } catch (Exception var2) {
                }

            }
        }, 0L, 1L, TimeUnit.MINUTES);
    }

    public void destroy() {
        if (this.scheduled != null) {
            this.scheduled.shutdown();
        }

    }

    /**
     * 使用scan遍历key
     * 为什么不使用keys 因为Keys会引发Redis锁，并且增加Redis的CPU占用,特别是数据庞大的情况下。这个命令千万别在生产环境乱用。　　　* 支持redis单节点和集群调用　
     * @return
     */
    public Set<String> scan(String pattern) {
        Set<String> keys = new HashSet<>();
        RedisSerializer serializer = redisTemplate.getKeySerializer();
        ScanOptions scanOptions = ScanOptions.scanOptions().match(pattern).count(Integer.MAX_VALUE).build();
        Cursor<byte[]> cursor = redisTemplate.execute(connection -> connection.scan(scanOptions), true);
        while (cursor.hasNext()) {
            keys.add(String.valueOf(serializer.deserialize(cursor.next())));
        }
        return keys;
    }

}
