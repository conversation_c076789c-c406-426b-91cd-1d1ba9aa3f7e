package com.nti56.nlink.product.device.server.openapi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.it.ITResult;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModel;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import com.nti56.nlink.product.device.server.openapi.convertor.ITResultConvertor;
import com.nti56.nlink.product.device.server.service.IThingModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/it/thingModel/")
@Tag(name = "物模型")
public class ITThingModelController {

    @Autowired
    private IThingModelService thingModelService;


    @PostMapping(path = "list")
    @Operation(summary = "获取模型列表")
    public ITResult<List<ThingModelEntity>> listThingModels(@RequestHeader("ot_headers") TenantIsolation tenantIsolation) {

        QueryWrapper<ThingModelEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id",tenantIsolation.getTenantId());
        queryWrapper.eq("ENGINEERING_ID",tenantIsolation.getEngineeringId());
        queryWrapper.eq("deleted",0);
        List<ThingModelEntity> result = thingModelService.list(queryWrapper);
        return ITResultConvertor.convert(R.result(Result.ok(result)));
    }

    @PostMapping(path = "modelService")
    @Operation(summary = "获取模型下的所有服务")
    public ITResult<List<ThingServiceEntity>> listThingServices(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                                                @RequestParam("modelId") Long modelId) {

        return ITResultConvertor.convert(R.result(thingModelService.listModelServices(modelId,tenantIsolation.getTenantId())));
    }

    @PostMapping(path = "modelProperties")
    @Operation(summary = "获取模型下的所有属性")
    public ITResult<List<ThingServiceEntity>> listProperties(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                                                @RequestParam("modelId") Long modelId) {

        return ITResultConvertor.convert(R.result(thingModelService.listModelProperties(modelId,tenantIsolation.getTenantId())));
    }

}
