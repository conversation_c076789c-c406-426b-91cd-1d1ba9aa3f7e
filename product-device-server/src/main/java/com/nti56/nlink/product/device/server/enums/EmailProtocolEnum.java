package com.nti56.nlink.product.device.server.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/5/5 10:34<br/>
 * @since JDK 1.8
 */
public enum EmailProtocolEnum {

    SMTP("smtp", "SMTP"),
    POP3("pop3","POP3"),
    IMAP4("imap4", "IMAP4")
            ;

    @Getter
    private String value;

    @Getter
    private String name;

    EmailProtocolEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static EmailProtocolEnum typeOfValue(String value){
        EmailProtocolEnum[] values = EmailProtocolEnum.values();
        for (EmailProtocolEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }


    public static List toList(){
        List<Map> result = new ArrayList<>();
        EmailProtocolEnum[] values = EmailProtocolEnum.values();
        Map<String,Object> map ;
        for (EmailProtocolEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            result.add(map);
        }
        return result;
    }
}
