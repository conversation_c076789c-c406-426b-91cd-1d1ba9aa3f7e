package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.ConnectorEntity;
import com.nti56.nlink.product.device.server.model.connector.dto.CreateConnectorDTO;
import com.nti56.nlink.product.device.server.model.connector.dto.EditConnectorDTO;
import com.nti56.nlink.product.device.server.model.connector.dto.QueryConnectorDTO;
import com.nti56.nlink.product.device.server.model.connector.vo.ConnectorVO;

import java.util.List;

/**
 * 类说明:
 * <AUTHOR>
 * @version 1.0
 * @since JDK 1.8
 */
public interface IConnectorService extends IBaseService<ConnectorEntity> {
    
    Result<Page<ConnectorVO>> pageConnector(PageParam pageParam, QueryConnectorDTO queryConnectorDTO,TenantIsolation tenantIsolation);
    
    Result<ConnectorEntity> createConnector(CreateConnectorDTO createConnectorDTO, TenantIsolation tenantIsolation);
    
    Result<Void> editConnector(EditConnectorDTO editConnectorDTO, TenantIsolation tenantIsolation);
    
    Result<Void> deleteConnector(Long id, TenantIsolation tenantIsolation);
    
    Result<Void> changeStatus(Long id, Integer status, TenantIsolation tenantIsolation);

    Result<Void> changeStatusByEdgeGatewayId(Long edgeGatewayId, Integer status, TenantIsolation tenantIsolation);

    Result<ConnectorVO> getConnectorInfo(Long id);

    Result<List<ConnectorEntity>> getConnectorByEdgeGatewayId(Long edgeGatewayId);

    Result<Void> deleteConnectorByEdgeGatewayId(Long id, TenantIsolation tenantIsolation);

    Result<List<ConnectorEntity>> getAllConnectorEntity();


}
