package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.mapper.*;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * 类说明: 设备服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:10:11
 * @since JDK 1.8
 */
@Service
@Slf4j
public class DeviceBindRelationServiceImpl extends BaseServiceImpl<DeviceMapper, DeviceEntity> implements IDeviceBindRelationService {

    @Autowired @Lazy
    DeviceMapper deviceMapper;


    @Override
    public Result<List<DeviceRespondBo>> deviceBindRelationBatchDelete(TenantIsolation tenantIsolation, List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Result.ok();
        }
        boolean update = new LambdaUpdateChainWrapper<>(deviceMapper)
                .set(DeviceEntity::getChannel, null)
                .set(DeviceEntity::getSource, null)
                .set(DeviceEntity::getSyncStatus,0)
                .eq(DeviceEntity::getTenantId, tenantIsolation.getTenantId())
                .in(DeviceEntity::getId, ids)
                .update();
        if (update) {
            return Result.ok();
        }else {
            return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
        }
    }

    @Override
    public Result<Void> createOrEditBindRelation(TenantIsolation tenantIsolation, DeviceRequestBo requestBo) {
        boolean update = new LambdaUpdateChainWrapper<>(deviceMapper)
                .set(DeviceEntity::getChannel, requestBo.getChannel())
                .set(DeviceEntity::getSource, requestBo.getSource())
                .set(DeviceEntity::getSyncStatus, 0)
                .eq(DeviceEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(DeviceEntity::getId, requestBo.getId())
                .update();
        if (update) {
            return Result.ok();
        }else {
            return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
        }
    }

    @Override
    public Result<List<DeviceRespondBo>> deviceBatchChannelBind(TenantIsolation tenantIsolation, DeviceRequestBo requestBo) {
        if (CollectionUtil.isEmpty(requestBo.getIds())) {
            return Result.ok();
        }
        boolean update = new LambdaUpdateChainWrapper<>(deviceMapper)
                .set(DeviceEntity::getChannel, requestBo.getChannel())
                .set(DeviceEntity::getSyncStatus,0)
                .eq(DeviceEntity::getTenantId, tenantIsolation.getTenantId())
                .in(DeviceEntity::getId, requestBo.getIds())
                .update();
        if (update) {
            return Result.ok();
        }else {
            return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
        }
    }

    @Override
    public Result<List<DeviceRespondBo>> deviceBatchLabelGroupBind(TenantIsolation tenantIsolation, DeviceRequestBo requestBo, Integer operate, Integer level) {
        if (ObjectUtils.isEmpty(operate) || ObjectUtils.isEmpty(level) || operate < 0 || operate > 3 || level < 0 || level > 5) {
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        if (CollectionUtil.isEmpty(requestBo.getIds())) {
            return Result.ok();
        }
        List<DeviceEntity> list = new LambdaQueryChainWrapper<>(deviceMapper)
                .select(DeviceEntity::getId,DeviceEntity::getSource,DeviceEntity::getChannel,DeviceEntity::getName)
                .eq(DeviceEntity::getTenantId, tenantIsolation.getTenantId())
                .in(DeviceEntity::getId, requestBo.getIds())
                .list();
        if (CollectionUtil.isEmpty(list)) {
            return Result.ok();
        }
        List<DeviceRespondBo> respondBos = new ArrayList<>();
        Iterator<DeviceEntity> iterator = list.iterator();
        while (iterator.hasNext()){
            DeviceEntity deviceEntity = iterator.next();
            Result<Void> result = batchOperateDeviceBindRelation(deviceEntity, requestBo, operate, level);
            if (!result.getSignal()) {
                DeviceRespondBo build = DeviceRespondBo.builder().id(deviceEntity.getId()).result(result).name(deviceEntity.getName()).source(deviceEntity.getSource()).build();
                respondBos.add(build);
                iterator.remove();
            }
        }
        if (CollectionUtil.isNotEmpty(list)) {
            deviceMapper.updateDeviceSource(tenantIsolation.getTenantId(),list);
        }
        return Result.ok(respondBos);
    }

    @Autowired
    ChannelMapper channelMapper;

    @Override
    public Result<List<DeviceEntity>> getRelationByGroup(TenantIsolation tenantIsolation, Long edgeGatewayId, Long channelId, String labelGroupName) {
        ChannelEntity one = new LambdaQueryChainWrapper<>(channelMapper).eq(ChannelEntity::getId, channelId).select(ChannelEntity::getName).one();
        if (ObjectUtils.isEmpty(one)) {
            return Result.error("通道不存在！");
        }
        List<DeviceEntity> list = new LambdaQueryChainWrapper<>(deviceMapper)
                .select(DeviceEntity::getName, DeviceEntity::getId)
                .eq(DeviceEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(DeviceEntity::getEdgeGatewayId, edgeGatewayId)
                .eq(DeviceEntity::getChannel, one.getName())
                .eq(DeviceEntity::getSource, labelGroupName)
                .list();
        return Result.ok(list);
    }

    private Result<Void> batchOperateDeviceBindRelation(DeviceEntity deviceEntity, DeviceRequestBo requestBo, Integer operate, Integer level) {
        if (ObjectUtils.isEmpty(deviceEntity.getSource())) {
            return Result.error("设备未绑定分组，无法操作！");
        }
        if ((operate == 1 || operate == 2) && ObjectUtils.isEmpty(requestBo.getSource())) {
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        switch (operate){
            case 1:
                return updateLabelGroup(deviceEntity,requestBo.getSource(),level);
            case 2:
                return addLabelGroupLevel(deviceEntity,requestBo.getSource(),level);
            case 3:
                return deleteLabelGroupLevel(deviceEntity,level);
            default:
                return Result.error();
        }

    }

    private Result<Void> deleteLabelGroupLevel(DeviceEntity deviceEntity, Integer level) {
        String[] split = deviceEntity.getSource().split("\\.");
        if(level > split.length){
            log.info("设备：{}，绑定分组编辑，分组删除失败！原分组：{}，删除第{}级！",deviceEntity.getId(),deviceEntity.getSource(),level);
            return Result.error("分组删除失败,不存在第" + level +"级！");
        }
        StringBuilder sb = new StringBuilder();
        AtomicInteger index = new AtomicInteger(1);
        Arrays.stream(split).forEach(s -> {
            if (index.get() == level) {
                index.getAndIncrement();
                return;
            }
            sb.append(s);
            if (index.get() != split.length && !(level == split.length && index.get() == split.length -1)) {
                sb.append(".");
            }
            index.getAndIncrement();
        });
        deviceEntity.setSource(sb.toString());
        return Result.ok();
    }

    private Result<Void> addLabelGroupLevel(DeviceEntity deviceEntity, String newLevelName, Integer level) {
        String[] split = deviceEntity.getSource().split("\\.");
        if (level > split.length) {
            log.info("设备：{}，绑定分组编辑，分组新增失败！原分组：{}，新增第{}级！新分组名称：{}",deviceEntity.getId(),deviceEntity.getSource(),level,newLevelName);
            return Result.error("分组新增失败，原分组不存在" + level + "级！");
        }
        StringBuilder sb = new StringBuilder();
        if (split.length + 1 == level) {
            sb.append(deviceEntity.getSource()).append(".").append(newLevelName);
        }else {
            AtomicInteger index = new AtomicInteger(1);
            Arrays.stream(split).forEach(s -> {
                if (index.get() == level) {
                    sb.append(newLevelName).append(".");
                }
                sb.append(s);
                if (index.get() != split.length) {
                    sb.append(".");
                }
                index.getAndIncrement();
            });
        }
        deviceEntity.setSource(sb.toString());
        return Result.ok();
    }

    private Result<Void> updateLabelGroup(DeviceEntity deviceEntity, String newLevelName, Integer level) {
        String[] split = deviceEntity.getSource().split("\\.");
        if (split.length < level) {
            log.info("设备：{}，绑定分组编辑，分组改名失败！原分组：{}，修改第{}级！新分组名称：{}",deviceEntity.getId(),deviceEntity.getSource(),level,newLevelName);
            return Result.error("分组编辑失败，分组层级越界！");
        }
        split[level - 1] = newLevelName;
        StringBuilder sb = new StringBuilder();
        Arrays.stream(split).forEach(s -> sb.append(s).append("."));
        sb.deleteCharAt(sb.length() -1);
        deviceEntity.setSource(sb.toString());
        return Result.ok();
    }

}
