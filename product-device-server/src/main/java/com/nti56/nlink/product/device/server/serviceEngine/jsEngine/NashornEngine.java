package com.nti56.nlink.product.device.server.serviceEngine.jsEngine;

import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.scriptApi.EngineeringSpi;
import com.nti56.nlink.product.device.server.scriptApi.ThingSpi;
import com.nti56.nlink.product.device.server.serviceEngine.BaseService;
import com.nti56.nlink.product.device.server.serviceEngine.ServiceEngine;
import lombok.Getter;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName NashornEngine
 * @date 2022/4/14 18:14
 * @Version 1.0
 */
public class NashornEngine extends ServiceEngine {

//    @Override
//    public R doService(BaseService service) {
//
//        return null;
//    }


}
