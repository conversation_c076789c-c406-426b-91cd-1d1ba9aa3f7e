package com.nti56.nlink.product.device.server.service.engineering;

import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.model.engineering.dto.CommonServiceDataDTO;
import com.nti56.nlink.product.device.server.service.IEngineeringCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/7 15:26<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class CommonHandle {

    @Autowired
    private IEngineeringCommonService engineeringCommonService;

    public void handle(Long tenantId, CommonServiceDataDTO commonServiceData, boolean tenantIdEqual,
                       Map<Long,Long> tagMap, Map<Long,Long> redirectMap) {

        if(commonServiceData == null){
            return;
        }

        if (CollectionUtils.isNotEmpty( commonServiceData.getRedirectList())){
            commonServiceData.getRedirectList().forEach(c-> {
                long id = IdGenerator.generateId();
                redirectMap.put(c.getId(),id);
                c.setId(id);

                if (!tenantIdEqual){
                    c.setTenantId(tenantId);
                }
            });
        }


        if (CollectionUtils.isNotEmpty( commonServiceData.getTagList())){
            commonServiceData.getTagList().forEach(c-> {
                long id = IdGenerator.generateId();
                tagMap.put(c.getId(),id);
                c.setId(id);
                if (!tenantIdEqual){
                    c.setTenantId(tenantId);
                }
            });
        }

        Result<Void> result = engineeringCommonService.deleteCommonDataByTenantId(tenantId);
        if (!result.getSignal()) {
            throw new BizException("服务器异常，请稍后重试2");
        }

        Result<Void> commonData = engineeringCommonService.createCommonData(commonServiceData);
        if (!commonData.getSignal()) {
            throw new BizException("服务器异常，请稍后重试3");
        }
        log.info("common服务导入成功");
    }
}
