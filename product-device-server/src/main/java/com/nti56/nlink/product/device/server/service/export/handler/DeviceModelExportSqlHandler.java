package com.nti56.nlink.product.device.server.service.export.handler;

import java.util.List;

import cn.hutool.extra.spring.SpringUtil;

import org.apache.commons.collections.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.nti56.nlink.common.export.handler.AbstractExportSqlHandler;
import com.nti56.nlink.common.util.GeneratorSqlUtil;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.mapper.*;

/**
 * 类说明:设备模型<br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/19 15:34<br/>
 * @version 1.0
 * @since JDK 1.8
 */
public class DeviceModelExportSqlHandler extends AbstractExportSqlHandler {
  @Override
  public void exportSqlDml(Long tenantId, List<String> sqlList) {
    exportDeviceModel(tenantId, sqlList);
    exportDeviceService(tenantId, sqlList);
    if (getNext() != null) {
      getNext().exportSqlDml(tenantId, sqlList);
    }
  }


  private void exportDeviceModel(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<DeviceModelInheritEntity> queryWrapper = new LambdaQueryWrapper<DeviceModelInheritEntity>()
            .eq(DeviceModelInheritEntity::getTenantId, tenantId);
    List<DeviceModelInheritEntity> dtoList = SpringUtil.getBean(DeviceModelInheritMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(DeviceModelInheritEntity.class, dtoList));
    }
  }

  private void exportDeviceService(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<DeviceServiceEntity> queryWrapper = new LambdaQueryWrapper<DeviceServiceEntity>()
            .eq(DeviceServiceEntity::getTenantId, tenantId);
    List<DeviceServiceEntity> dtoList = SpringUtil.getBean(DeviceServiceMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(DeviceServiceEntity.class, dtoList));
    }
  }

}
