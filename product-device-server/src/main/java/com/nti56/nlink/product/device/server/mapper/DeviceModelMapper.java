package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 类说明: 设备模型mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:23
 * @since JDK 1.8
 */
public interface DeviceModelMapper extends CommonMapper<DeviceEntity> {

    @Select({"select d.* from device d where d.tenant_id = #{tenantId} and d.id = #{deviceId}"})
    DeviceEntity getByDeviceId(@Param("tenantId")Long tenantId,@Param("deviceId") Long deviceId);
}
