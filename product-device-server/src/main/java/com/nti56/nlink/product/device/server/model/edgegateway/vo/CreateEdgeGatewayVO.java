package com.nti56.nlink.product.device.server.model.edgegateway.vo;

import com.nti56.nlink.product.device.server.entity.ConnectorEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/25 15:11<br/>
 * @since JDK 1.8
 */
@Data
@Builder
@Schema(description = "网关vo")
public class CreateEdgeGatewayVO {


    private Long edgeGatewayId;

    private List<ConnectorEntity> connectorEntityList;

}
