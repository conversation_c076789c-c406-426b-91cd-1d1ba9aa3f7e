package com.nti56.nlink.product.device.server.model.channel.dto;

import com.nti56.nlink.product.device.server.model.label.dto.LabelGroupDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 11:36<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "修改通道配置dto")
public class EditChannelAllDTO {
    private Long id;

    @Schema(description = "通道名字")
    @NotBlank(message = "通道名称不能为空")
    @Length(max = 128,message = "通道名称不能超过32个字符")
    private String name;

    @Schema(description = "描述")
    @Length(max = 256,message = "通道描述不能超过256个字符")
    private String descript;

    @Schema(description = "通道参数列表")
    private List<ChannelParamDTO> channelParamList;

    @Schema(description = "删除的通道参数id列表")
    private List<Long> deleteChannelParamIds;

    @Schema(description = "新增标签分组列表")
    private List<LabelGroupDTO> labelGroupList;

    @Schema(description = "删除的标签分组id列表")
    private List<Long> deleteLabelGroupIds;

    @Schema(description = "删除的标签id列表")
    private List<Long> deleteLabelIds;

}
