package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.DataModelEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 数据模型表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13 11:49:35
 */
@Mapper
public interface DataModelMapper extends CommonMapper<DataModelEntity> {

    Page<DataModelEntity> pageDataModel(@Param("tenantId") Long tenantId, IPage<DataModelEntity> page, @Param("searchStr") String searchStr);

    List<DataModelEntity> listDataModel(@Param("tenantId") Long tenantId);

    DataModelEntity getById(@Param("tenantId") Long tenantId, @Param("id") Long id);

}
