package com.nti56.nlink.product.device.server.model.channel.dto;

import com.nti56.nlink.product.device.server.util.RegexUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/24 9:45<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "新增通道配置dto")
public class CopyChannelDTO {

    @Schema(description = "被复制的通道id")
    @NotNull(message = "被复制的通道不能为空")
    private Long copyChannelId;

    @Schema(description = "通道名字")
    @NotBlank(message = "通道名称不能为空")
    @Length(max = 128,message = "通道名称不能超过32个字符")
    @Pattern(regexp = RegexUtil.channelNamePatternString,message = "通道名称只能包含中文、字母、数字、'_'、'-'这些字符!")
    private String name;

    @Schema(description = "描述")
    @Length(max = 256,message = "通道描述不能超过256个字符")
    private String descript;

    @Schema(description = "通道参数列表")
    @Valid
    private List<ChannelParamDTO> channelParamList;

}
