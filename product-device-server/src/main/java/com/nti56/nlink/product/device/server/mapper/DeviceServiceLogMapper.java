package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 服务日志Mapper
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-08-10 10:38:52
 * @since JDK 1.8
 */
@Mapper
public interface DeviceServiceLogMapper extends CommonMapper<DeviceServiceLogEntity> {

    @Insert({
            "<script>",
            "INSERT INTO device_service_log",
            "(id, device_id, service_id, service_name, device_name, call_type, call_success, input_data, output_data, CREATOR_ID, CREATOR, CREATE_TIME, TENANT_ID) VALUES",
            "<foreach collection='list' item='item'  separator=',' >",
            "(#{item.id}, #{item.deviceId}, #{item.serviceId}, #{item.serviceName}, #{item.deviceName}, #{item.callType}, #{item.callSuccess}, #{item.inputData}, #{item.outputData}, #{item.creatorId}, #{item.creator}, #{item.createTime}, #{item.tenantId})",
            "</foreach>",
            "</script>"
    })
    public void saveLogs(List<DeviceServiceLogEntity> list);

}
