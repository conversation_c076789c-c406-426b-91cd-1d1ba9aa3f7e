package com.nti56.nlink.product.device.server.model.device.vo;

import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/2/20 9:47<br/>
 * @since JDK 1.8
 */
@Data
public class DcmpEdgeGatewayVO {

    @Schema(description = "网关id")
    private Long id;

    @Schema(description = "网关名称")
    private String name;


    @Schema(description = "IMEI")
    private String imei;

    @Schema(description = "流量卡号")
    private String trafficCard;

    @Schema(description = "运营商 1-移动，2-电信，3-联通")
    private Integer operators;

    @Schema(description = "网关类型 虚拟网关/网关设备 ")
    private Integer type;

    @Schema(description = "1在线 0离线")
    private Integer online;

    @Schema(description = "网关绑定的标记集合")
    private List<TagRsp> tags;

}
