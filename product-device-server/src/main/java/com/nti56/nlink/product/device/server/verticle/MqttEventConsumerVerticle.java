package com.nti56.nlink.product.device.server.verticle;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.WriteApiBlocking;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.rule.model.WarningLogParam;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.condition.TriggerConditionElm;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.EventDpo;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.WarningLevelEnum;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.FaultLevelDefineElm;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.*;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.FaultLevelDefine;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpEventTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import com.nti56.nlink.product.device.server.entity.DeviceServiceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import com.nti56.nlink.product.device.server.model.device.dto.DeviceFaultInstance;
import com.nti56.nlink.product.device.server.model.device.dto.FaultEventInstance;
import com.nti56.nlink.product.device.server.model.product.vo.DeviceVO;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.util.GzipUtil;

import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-21 17:19:00
 * @since JDK 1.8
 */
@Deprecated
@Component
@Scope(SCOPE_PROTOTYPE)
@Slf4j
public class MqttEventConsumerVerticle extends MqttBaseVerticle {

    public static final int SOURCE_TYPE_FAULT = 1;
    public static final int TRIGGER_MODEL_TRIGGER = 2;
    @Getter
    @Value("${mqtt.host}")
    private String host;

    @Getter
    @Value("${mqtt.port}")
    private Integer port;

    @Getter
    @Value("${mqtt.username}")
    private String username;

    @Getter
    @Value("${mqtt.password}")
    private String password;

    @Getter
    @Value("${mqtt.ssl:true}")
    private Boolean ssl;
    
    @Getter
    private Integer reconnectGapTime;
    
    @Autowired
    private InfluxDBClient influxDbClient;

    @Autowired @Lazy
    private RedisTemplate redisTemplate;

    @Autowired
    private IDeviceModelService deviceModelService;

    @Autowired
    private IThingServiceService thingServiceService;

    @Autowired
    private IDeviceService deviceService;

//    @Autowired
//    private IFeignAlarmService alarmService;

    @Autowired
    private IDeviceServiceService deviceServiceService;

    @Autowired
    private ISubscriptionService subscriptionService;

    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        log.debug("start-verticle");
        super.start(startPromise);
    }

    @Override
    public void subscribe(){
        String eventTopic = GwUpEventTopic.createSubscribeTopic(MqttBaseVerticle.shareSubscribeGroup);
        this.client.publishHandler(s1 -> {

            String topicName = s1.topicName();
            // Buffer gzip = s1.payload();
            // byte[] bs = GzipUtil.uncompress(gzip);
            // String payload = new String(bs, StandardCharsets.UTF_8);
            String payload = s1.payload().toString();
            
            log.debug("topic: {}, msg: {}", topicName, payload);
            GwUpEventTopic.TopicInfo topicInfo = GwUpEventTopic.parseTopic(topicName);
            String eventType = topicInfo.getEventType();
            EventTypeEnum eventTypeEnum = EventTypeEnum.typeOfName(eventType);
            //故障事件触发
            UpData upData = JSONObject.parseObject(payload, UpData.class);
            vertx.executeBlocking(promise -> {
                WriteApiBlocking writeApi = influxDbClient.getWriteApiBlocking();
                List<Point> pointList = upDataToPoints(topicInfo, upData);
                writeApi.writePoints(pointList);
                switch (eventTypeEnum){
                    case ERROR:
                    case FAULT:
                        handleFaultEvent(topicInfo,payload);
                        break;
                    default:
                        //do nothing
                }
                promise.complete();
            }, res -> {

            });
            

        });
        client.subscribe(eventTopic, 1);
    }

    private void handleFaultEvent(GwUpEventTopic.TopicInfo topicInfo, String payload) {

        if(StringUtils.isEmpty(payload)){
            log.error("fault event payload empty. eventName:{}" , topicInfo.getEventName());
            return;
        }
        log.info("fault event trigger.trigger payload:{},topicInfo:{}",payload,JSONUtil.toJsonStr(topicInfo));
        try {
            UpData faultUpData = JSONObject.parseObject(payload, UpData.class);
            //检查事件状态,分开始 结束处理
            Boolean currentStatus = faultUpData.getFaultStatus();
            FaultEventStatusEnum faultEventStatusEnum = FaultEventStatusEnum.typeOfValue(currentStatus);
            if(Objects.isNull(faultEventStatusEnum)){
                log.error("==================fault status miss=========================");
                return;
            }
            Long deviceId = Long.valueOf(topicInfo.getDeviceId());
            Long tenantId = Long.valueOf(topicInfo.getTenantId());
            String faultInstanceKey = String.format(RedisConstant.DEVICE_FAULT_INSTANCE_CACHE,topicInfo.getTenantId(),deviceId);
            String faultEventInstanceKey = String.format(RedisConstant.DEVICE_FAULT_EVENT_INSTANCE,topicInfo.getTenantId(), deviceId);
            String tenantOnFaultDeviceKey = String.format(RedisConstant.TENANT_DEVICE_ON_FAULT,topicInfo.getTenantId());
//            String tenantFaultDeviceCountKey = String.format(RedisConstant.DEVICE_FAULT_COUNT,topicInfo.getTenantId());
            String faultEventStatusKey = String.format(RedisConstant.DEVICE_FAULT_STATUS,topicInfo.getTenantId(),topicInfo.getEventName(),deviceId);
            Boolean existStatus = redisTemplate.opsForValue().setIfAbsent(faultEventStatusKey, faultUpData.getFaultStatus().toString());
            TenantIsolation tenantIsolation = new TenantIsolation();
            tenantIsolation.setTenantId(tenantId);
            switch (faultEventStatusEnum){
                case START:
                    // 检查当前 redis key
                    if(!existStatus){
                        //状态未改变，不处理
                        Boolean eStatus = Boolean.valueOf(String.valueOf(redisTemplate.opsForValue().get(faultEventStatusKey)));
                        if(faultUpData.getFaultStatus().equals(eStatus)){
                            log.info("========repeat event status,event name:{},status:{}========",topicInfo.getEventName(),
                                    faultUpData.getFaultStatus());
                            return;
                        }
                    }
                    Result<DeviceVO> deviceModelResult = deviceModelService.getDeviceModel(deviceId, tenantIsolation);
                    if(!deviceModelResult.getSignal()){
                        log.warn("cant find any device model.check whether device{} is exist first!",deviceId);
                        return;
                    }
                    DeviceVO deviceInfo = deviceModelResult.getResult();

                    String triggerCondition = buildCondition(faultUpData.getProp(), deviceInfo.getFullModel().getEvents(), topicInfo.getEventName());

                    //缓存当前事件实例
                    FaultEventInstance faultEventInstance = new FaultEventInstance();
                    faultEventInstance.setEventName(topicInfo.getEventName());
                    faultEventInstance.setEventStartTime(faultUpData.getFaultBegin());
                    faultEventInstance.setTriggerCondition(triggerCondition);
                    faultEventInstance.setUpData(faultUpData);
                    List<EventDpo> deviceEvents = deviceInfo.getFullModel().getEvents();
                    if(CollectionUtil.isEmpty(deviceEvents)){
                        log.warn("cant find any thing model event.check device{} event first!",deviceId);
                        return;
                    }
                    //遍历模型，找出事件，找出事件关联的服务
//                    boolean eventCatch = false;
                    for (int i = 0; i < deviceEvents.size(); i++) {
                        EventDpo eventDpo = deviceEvents.get(i);
                        if(topicInfo.getEventName().equals(eventDpo.getName())){
//                            eventCatch = true;
                            //调用服务
                            faultEventInstance.setEventDesc(eventDpo.getDescript());
                            faultEventInstance.setThingServiceId(eventDpo.getThingServiceId());
                            FaultLevelDefineElm faultLevelDefineElm = eventDpo.getEventDefine().getFaultLevelDefine();
                            if(!Objects.isNull(faultLevelDefineElm)) {
                                if (!faultLevelDefineElm.getEnableDefine()) {
                                    faultEventInstance.setFaultLevel(WarningLevelEnum.FATAL_WARNING.getCode());
                                } else {
                                    Map<String, String> levelTriggerMap = new HashMap<>(faultLevelDefineElm.getLevelDefine().size());
                                    faultLevelDefineElm.getLevelDefine().forEach((k, v) -> {
                                        levelTriggerMap.put(k, FaultLevelDefine.constructCondition(v));
                                    });
                                    faultEventInstance.setFaultLevelMap(levelTriggerMap);
                                    faultEventInstance.setFaultLevel(FaultLevelDefine.getFaultLevelByCondition(levelTriggerMap, faultUpData.getProp()));
                                }
                            }else{
                                faultEventInstance.setFaultLevel(WarningLevelEnum.FATAL_WARNING.getCode());
                            }
                            break;
                        }
                    }
                    
                    /*if(!eventCatch){
                        redisTemplate.opsForHash().delete(faultEventInstanceKey, topicInfo.getEventName());
                        redisTemplate.delete(faultEventStatusKey);
                        //检查是不是当前最后一个故障事件
                        if(redisTemplate.opsForHash().size(faultEventInstanceKey) == 0){
                            redisTemplate.delete(faultEventInstanceKey);
                            redisTemplate.opsForSet().remove(tenantOnFaultDeviceKey,deviceId);
                            redisTemplate.delete(faultInstanceKey);
                        }
                        return;
                    }*/
                    //状态改变，获取实例缓存，无实例缓存再新增
                    Object cache = redisTemplate.opsForValue().get(faultInstanceKey);
                    if(Objects.isNull(cache)){
                        DeviceFaultInstance deviceFaultInstance = DeviceFaultInstance.builder().deviceId(deviceId)
                                .startTime(faultUpData.getFaultBegin())
                                .deviceName(deviceInfo.getName())
                                .build();
                        //在发生的故障设备数计数
//                        redisTemplate.opsForValue().increment(tenantFaultDeviceCountKey);
                        //缓存故障实例
                        redisTemplate.opsForValue().set(faultInstanceKey,JSONUtil.toJsonStr(deviceFaultInstance));
                    }
                    //缓存故障设备id
                    redisTemplate.opsForSet().add(tenantOnFaultDeviceKey,deviceId);


                    //触发时的数据
                    // faultEventInstance.setUpData(faultUpData);
                    redisTemplate.opsForHash().put(faultEventInstanceKey,
                            topicInfo.getEventName(), JSONUtil.toJsonStr(faultEventInstance));
                    //先创建一条告警
                    WarningLogParam warningLogParam = WarningLogParam.builder()
                            .instanceId(deviceId)
                            .occurTime(new Date(faultUpData.getFaultBegin()))
                            .sourceName(deviceInfo.getName())
                            .warnName(topicInfo.getEventName())
                            //设备故障类型告警
                            .sourceType(SOURCE_TYPE_FAULT)
                            .warnContent(faultEventInstance.getEventDesc())
                            //告警级别 致命
                            .warnLevel(faultEventInstance.getFaultLevel())
                            .triggerModel(TRIGGER_MODEL_TRIGGER)
                            .triggerType(TRIGGER_MODEL_TRIGGER)
                            .triggerCondition(triggerCondition)
                            .build();
                    try{
//                        alarmService.addWarningLog(JSONUtil.toJsonStr(tenantIsolation),warningLogParam);
                        subscriptionService.sendFaultEventSubscription(deviceId,tenantId,faultEventInstance);
                    }catch (Exception e){
                        log.error(e.getMessage());
                        log.error("==fault send warn and subscribe error====");
                    }

                    break;
                case END:
                    if(!existStatus){
                        //状态改变，调用物服务，完善规则实例
                        //获得实例
                        Boolean eStatus = Boolean.valueOf(String.valueOf(redisTemplate.opsForValue().get(faultEventStatusKey)));
                        if(faultUpData.getFaultStatus().equals(eStatus)){
                            redisTemplate.delete(faultEventStatusKey);
                            log.info("========repeat event status,event name:{},status:{}========",topicInfo.getEventName(),
                                    faultUpData.getFaultStatus());
                            return;
                        }
                        try{
                            Object instanceCache = redisTemplate.opsForValue().get(faultInstanceKey);
                            DeviceFaultInstance existInstance = JSONUtil.toBean(JSONUtil.toJsonStr(instanceCache), DeviceFaultInstance.class);
                            //获取事件缓存实例
                            FaultEventInstance existEvents = JSONUtil.toBean(JSONUtil.toJsonStr(redisTemplate.opsForHash().get(faultEventInstanceKey, topicInfo.getEventName())),
                                    FaultEventInstance.class);
                            existEvents.setEventEndTime(faultUpData.getFaultEnd());
                            //创建告警
                            WarningLogParam warningLog = WarningLogParam.builder()
                                    .instanceId(deviceId)
                                    .occurTime(new Date())
                                    .sourceName(existInstance.getDeviceName())
                                    .warnName(topicInfo.getEventName())
                                    //设备故障类型告警
                                    .sourceType(SOURCE_TYPE_FAULT)
                                    .warnContent(existEvents.getEventDesc())
                                    //告警级别 致命
                                    .warnLevel(existEvents.getFaultLevel())
                                    .triggerModel(TRIGGER_MODEL_TRIGGER)
                                    .triggerType(TRIGGER_MODEL_TRIGGER)
                                    .triggerCondition(existEvents.getTriggerCondition())
                                    .build();

                            //
                            //删除事件缓存
                            redisTemplate.opsForHash().delete(faultEventInstanceKey, topicInfo.getEventName());
                            //检查 当前设备是否有其他故障事件，如果没有了，当前租户故障设备-1 id集合删除对应设备
                            if(redisTemplate.opsForHash().size(faultEventInstanceKey) == 0){
                                redisTemplate.delete(faultEventInstanceKey);
//                                redisTemplate.opsForValue().decrement(tenantFaultDeviceCountKey);
                                redisTemplate.opsForSet().remove(tenantOnFaultDeviceKey,deviceId);

                                redisTemplate.delete(faultInstanceKey);
                            }
                            //当前事件的故障开关
                            redisTemplate.delete(faultEventStatusKey);


                            //执行服务
                            try{
                                invokeService(topicInfo, faultUpData, existInstance, existEvents);
                            }catch (Exception e){
                                e.printStackTrace();
                                log.error("==fault invoke service error====");
                            }

                            try{
//                                alarmService.updateFaultEndTime(JSONUtil.toJsonStr(tenantIsolation),warningLog);
                                subscriptionService.sendFaultEventSubscription(deviceId,tenantId,existEvents);
                            }catch (Exception e){
                                e.printStackTrace();
                                log.error("==fault update alarm error====");
                            }



                        }catch (Exception e){
                            log.error("handle fault event end occur error.error msg:{}",e.getMessage());
                            e.printStackTrace();
                        }
                        return;
                    }
                    break;
                default:
                    log.warn("unSupport event status: {}",faultEventStatusEnum.getName());
                    break;
            }

        }catch (Exception e){
            e.printStackTrace();
            log.error("fault event payload transfer error. eventName:{},payload:{}", topicInfo.getEventName(), payload);
            return;
        }



    }

    private void invokeService(GwUpEventTopic.TopicInfo topicInfo, UpData faultUpData, DeviceFaultInstance existInstance,FaultEventInstance eventInstance) {
        Long tenantId = Long.valueOf(topicInfo.getTenantId());
        Long deviceId = Long.valueOf(topicInfo.getDeviceId());
        TenantIsolation tenantIsolation = new TenantIsolation();
        tenantIsolation.setTenantId(tenantId);
        Result<ThingServiceEntity> service = thingServiceService.getByIdAndTenantIsolation(eventInstance.getThingServiceId(), tenantIsolation);
        ThingServiceEntity serviceEntity = service.getResult();
        if(Objects.isNull(serviceEntity)){
            Result<DeviceServiceEntity> deviceServiceEntityResult = deviceServiceService.getByIdAndTenantIsolation(eventInstance.getThingServiceId(), tenantIsolation);
            if(Objects.isNull(deviceServiceEntityResult.getResult())){
                log.warn("找不到服务");
                return;
            }
            serviceEntity = BeanUtilsIntensifier.copyBean(deviceServiceEntityResult.getResult(),ThingServiceEntity.class);
        }
        DeviceServiceLogEntity logEntity = DeviceServiceLogEntity.builder().createTime(LocalDateTime.now())
                .deviceId(deviceId).serviceName(serviceEntity.getServiceName())
                .creatorId(deviceId)
                .creator(topicInfo.getEventName())
                .deviceName(existInstance.getDeviceName())
                .callType(ThingServiceCallTypeEnum.FAULT_INVOKE.getValue())
                .build();
        //组装参数，调用服务
        Map<String,Object> inputData = Maps.newHashMap();
        if(ServiceTypeEnum.FAULT_SERVICE.getValue().equals(serviceEntity.getServiceType())) {
            if (!Objects.isNull(eventInstance.getEventStartTime())) {
                inputData.put("startTime", eventInstance.getEventStartTime());
            }
            if (!Objects.isNull(eventInstance.getEventEndTime())) {
                inputData.put("endTime", eventInstance.getEventEndTime());
            }
            inputData.put("eventStatus", 0);
        }
        List<UpProp> props = faultUpData.getProp();
        props.forEach(prop->{
            if(!StringUtils.isEmpty(prop.getFaultInput())){
                inputData.put(prop.getFaultInput(),prop.getValue());
            }
        });
        R r = deviceService.doServiceTask(tenantId, deviceId, serviceEntity.getServiceName(), inputData, logEntity,true);
        //执行结果
        if (MapUtil.getBool(r, "ok")) {
            //服务执行成功操作 todo
        }else {
            log.error("service invoke error.input data:{},serviceName:{}",JSONUtil.toJsonStr(inputData),serviceEntity.getServiceName());
        }
    }

    private String buildCondition(List<UpProp> prop, List<EventDpo> events, String occurEventName) {

        EventDpo occurEvent = null;
        for (int i = 0; i < events.size(); i++) {
            EventDpo eventDpo = events.get(i);
            if(occurEventName.equals(eventDpo.getName())){
                occurEvent = eventDpo;
                break;
            }
        }
        if(Objects.isNull(occurEvent)){
            return "";
        }
        try {
            Map<String, Object> collect = Maps.newHashMap();
            if(CollectionUtil.isNotEmpty(prop)){
                collect = prop.stream().collect(Collectors.toMap(UpProp::getProperty, UpProp::getValue, (key1, key2) -> key2));
            }
            List<TriggerConditionElm> triggerConditionElms = occurEvent.getEventDefine().getTrigger();
            if (CollectionUtil.isNotEmpty(triggerConditionElms)) {
                for (TriggerConditionElm triggerConditionElm : triggerConditionElms) {
                    triggerConditionElm.setValue(collect.get(triggerConditionElm.getLeft().getProperty()));
                }
            }
            return JSONUtil.toJsonStr(triggerConditionElms);
        } catch (Exception e) {
            return "";
        }
    }

    private List<Point> upDataToPoints(GwUpEventTopic.TopicInfo topicInfo, UpData upData){
        Long timestamp = upData.getTimestamp();

        List<Point> pointList = new ArrayList<>();
        for(UpProp item:upData.getProp()){
            if(item.getIsArray()){
                String propertyTag;
                for(int i=0;i<item.getLength();i++){
                    Map<String, Object> fields = new HashMap<>();
                    propertyTag = item.getProperty() + "." + i;
                    String fieldKey = item.getDataType() + "." + i;
                    JSONArray value = (JSONArray)item.getValue();
                    Object fieldValue = value.get(i);
                    fields.put(fieldKey, fieldValue);

                    Point point = Point.measurement(topicInfo.getTenantId())
                        .addTag("type", topicInfo.getEventType())
                        .addTag("eventName", topicInfo.getEventName())
                        .addTag("deviceId", topicInfo.getDeviceId())
                        .addTag("property", item.getProperty())
                        .addTag("propertyAlias", propertyTag)
                        .addTag("dataType", item.getDataType())
                        .addTag("isArray", item.getIsArray()?YesNoEnum.YES.getName():YesNoEnum.NO.getName())
                        .addTag("length", item.getLength().toString())
                        .addFields(fields)
                        .time(timestamp, WritePrecision.MS);
        
                    pointList.add(point);
                }
            }else{
                Map<String, Object> fields = new HashMap<>();
                
                String fieldKey = item.getDataType();
                Object fieldValue = item.getValue();
                fields.put(fieldKey, fieldValue);
                
                Point point = Point.measurement(topicInfo.getTenantId())
                    .addTag("type", topicInfo.getEventType())
                    .addTag("eventName", topicInfo.getEventName())
                    .addTag("deviceId", topicInfo.getDeviceId())
                    .addTag("property", item.getProperty())
                    .addTag("propertyAlias", item.getProperty())
                    .addTag("dataType", item.getDataType())
                    .addTag("isArray", item.getIsArray()?YesNoEnum.YES.getName():YesNoEnum.NO.getName())
                    .addTag("length", item.getLength().toString())
                    .addFields(fields)
                    .time(timestamp, WritePrecision.MS);
                pointList.add(point);
            }
        }
        return pointList;
    }

    @Override
    protected void handleConnectStatusChange(Boolean connected) {
       
    }
}
