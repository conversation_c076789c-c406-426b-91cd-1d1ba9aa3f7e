package com.nti56.nlink.product.device.server.domain.thing.up;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 类说明: 网关上传数据
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-22 09:14:39
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FaultUpData {

    /**
     * 网关上传数据 属性值
     */
    private UpData upData;

    /**
     * 故障开始时间戳
     */
    private Long faultBegin;

    /**
     * 故障结束时间戳
     */
    private Long faultEnd;

    /**
     * 故障状态：1-开始、2-停止
     */
    private Integer faultStatus;

}
