package com.nti56.nlink.product.device.server.model;

import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ChannelRespondBo
 * @date 2023/3/9 14:01
 * @Version 1.0
 */
@Data
@Schema(description = "通道响应对象")
@Builder
public class ChannelRespondBo {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "设备名称")
    private String name;

    @Schema(description = "结果")
    private Result<Void> result;

}
