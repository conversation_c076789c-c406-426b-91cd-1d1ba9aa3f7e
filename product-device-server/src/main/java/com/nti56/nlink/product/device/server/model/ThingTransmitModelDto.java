package com.nti56.nlink.product.device.server.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
@Data
@Schema
public class ThingTransmitModelDto {

    @Schema(description = "物模型名称")
    private String name;

    @Schema(description = "自己的物模型定义")
    private ModelFieldDto model;

    @Schema(description = "物模型描述")
    private String descript;

    /**
     * 模型类型 0-普通 1-故障
     */
    @Schema(description = "模型类型")
    private Integer modelType;

}
