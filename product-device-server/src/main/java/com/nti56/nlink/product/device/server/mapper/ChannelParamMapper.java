package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;
import com.nti56.nlink.product.device.server.model.channel.dto.ChannelParamDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.ProofreadChannelParamDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-18 17:43:46
 * @since JDK 1.8
 */
public interface ChannelParamMapper extends CommonMapper<ChannelParamEntity> {

    List<ProofreadChannelParamDTO> listProofreadDataByChannelId(@Param("channelId") Long channelId, @Param("tenantIsolation") TenantIsolation tenantIsolation);
    
    List<ChannelParamDTO> listChannelParamByEdgeGatewayId(@Param("edgeGatewayId")Long edgeGatewayId,@Param("tenantIsolation") TenantIsolation tenantIsolation);
    
    List<ChannelParamDTO> listChannelParamByChannelIds(@Param("channelIds")List<Long> channelIds,@Param("tenantIsolation") TenantIsolation tenantIsolation);

    void physicalDeleteByEdgeGatewayId(@Param("edgeGatewayId")Long edgeGatewayId);
}
