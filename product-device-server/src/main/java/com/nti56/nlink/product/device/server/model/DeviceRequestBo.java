package com.nti56.nlink.product.device.server.model;

import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 设备dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:58
 * @since JDK 1.8
 */
@Data
@Schema(description = "设备请求对象")
public class DeviceRequestBo{

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "设备名称")
    private String name;

    @Schema(description = "设备描述")
    private String descript;

    @Schema(description = "设备绑定的通道")
    private String channel;

    @Schema(description = "设备绑定的标签分组")
    private String source;

    @Schema(description = "设备状态，2-已上线， 1-已离线，0-未激活")
    private Integer status;

    @Schema(description = "标记Id数组")
    private List<String> tagIds;

    @Schema(description = "选中的设备Id")
    private List<Long> ids;

}
