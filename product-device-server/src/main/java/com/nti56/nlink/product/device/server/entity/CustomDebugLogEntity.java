package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.nti56.nlink.product.device.client.model.dto.json.ComputeContentField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("custom_debug_log")
@Schema(description = "自定义协议调试日志对象")
public class CustomDebugLogEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @Schema(description = "id")
    @TableId(value = "id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "网关id")
    private Long edgeGatewayId;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private String type;

    /**
     * 内容
     */
    @Schema(description = "内容")
    private String content;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    
}
