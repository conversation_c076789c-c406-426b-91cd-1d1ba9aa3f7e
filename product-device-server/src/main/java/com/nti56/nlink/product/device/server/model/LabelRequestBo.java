package com.nti56.nlink.product.device.server.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 设备dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:58
 * @since JDK 1.8
 */
@Data
@Schema(description = "标签请求对象")
public class LabelRequestBo {

    @Schema(description = "选中的标签Id")
    private List<Long> ids;

    private List<String> absoluteLabelNames;

    private String channelName;

    private Long edgeGatewayId;

    @Schema(description = "轮询间隔")
    private Long intervalMs;

}
