package com.nti56.nlink.product.device.server.model.workConsole;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明：
 *
 * @ClassName WorkConsoleVO
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/8/5 10:47
 * @Version 1.0
 */
@Data
public class WorkConsoleVO {

    @Schema(description = "网关总数")
    private int gatewayTotal;

    @Schema(description = "网关在线数")
    private int gatewayOnline;

    @Schema(description = "设备在线数")
    private int deviceOnline;
    
    @Schema(description = "设备离线数")
    private int offLineDevice;

    @Schema(description = "当天采集点位数")
    private int collectedPropCount;

    @Schema(description = "物模型总数")
    private int thingModelTotal;

    @Schema(description = "物服务触发数")
    private int thingServiceInvokeCount;

    @Schema(description = "设备总数")
    private int deviceTotal;

    @Schema(description = "当天规则实例触发总数")
    private int ruleTriggerCount;

    @Schema(description = "当天告警数")
    private int warningCount;
    
    @Schema(description = "规则实例总数")
    private Integer ruleInstanceCount;
    
    @Schema(description = "业务场景总数")
    private Integer ruleEngineCount;
    
    @Schema(description = "回调总数")
    private Integer redirectCount;
    
    
}
