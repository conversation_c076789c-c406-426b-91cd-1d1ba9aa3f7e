package com.nti56.nlink.product.device.server.domain.thing.device;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.nti56.nlink.common.base.Field;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.updater.CommonUpdater;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.ComputeContentField;
import com.nti56.nlink.product.device.client.model.dto.json.DeviceRuntimeMetadataField;
import com.nti56.nlink.product.device.client.model.dto.json.compute.ComputeChangeItem;
import com.nti56.nlink.product.device.client.model.dto.json.compute.ComputeEventItem;
import com.nti56.nlink.product.device.client.model.dto.json.compute.PropDataElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.PropertyMetadataItem;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.EventDpo;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.SubscriptionDpo;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.DataTypeElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.FaultLevelDefineElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.SpecElm;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.devicemodel.DeviceModel;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.*;
import com.nti56.nlink.product.device.server.domain.thing.label.Label;
import com.nti56.nlink.product.device.server.domain.thing.label.LabelBindRelation;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Event;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.EventTrigger.CompileInfo;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.FaultLevelDefine;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Property;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpEventTopic;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpPropertyTopic;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.model.EventDebugResult;
import com.nti56.nlink.product.device.server.model.device.dto.DeviceCheckInfoContext;
import com.nti56.nlink.product.device.server.serviceEngine.BaseService;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @ClassName Device
 * @date 2022/4/12 13:43
 * @Version 1.0
 */
@Slf4j
public class Device {


    @Getter
    private Long id;

    @Getter
    private DeviceEntity deviceEntity;

    private ResourceRelationEntity resourceEntity;

    private EdgeGatewayEntity edgeGatewayEntity;

    @Getter
    private DeviceModel deviceModel;

    @Getter
    
    private Map<String, LabelBindRelation> propertyLabelMap; //propertyName -> relation

    @Getter
    private List<LabelBindRelationEntity> relationEntities;

    @Getter
    private ChannelEntity bindChannel;

    @Getter
    private LabelGroupEntity bindGroup;

    @Getter
    private Map<String,Set<String>> groupMap;

    @Getter
    private Map<String,String> nameMap;

    private InfoDegreeEnum infoDegree;

    private static final UniqueConstraint nameUniqueConstraint = new UniqueConstraint("name","edge_gateway_id");

    @Getter
    private static enum InfoDegreeEnum {
        BASE(0),
        MODEL(1),
        BIND_RELATION(2), 
        SINGLE_EVENT(3), 
        EVENT(4), 
        REPORT(5), 
        ;

        private Integer degree;

        InfoDegreeEnum(int degree){
            this.degree = degree;
        }

        private boolean greaterEqualThen(InfoDegreeEnum target){
            return this.degree >= target.degree;
        }
    }
    private void checkDegree(InfoDegreeEnum targetDegree){
        if(this.infoDegree == null || !infoDegree.greaterEqualThen(targetDegree)){
            throw new RuntimeException("degree错误");
        }
    }

    /**
     * 0 检查基础
     */
    public static Result<Device> checkInfoToBase(DeviceEntity entity){
        Device device = new Device();
        if(entity == null){
            return Result.error("设备不能为空");
        }
        device.deviceEntity = entity;
        
        if(entity.getId() == null){
            return Result.error("设备id不能为空");
        }
        device.id = entity.getId();

        device.infoDegree = InfoDegreeEnum.BASE;
        return Result.ok(device);
    }

    /**
     * 1 检查网关、通道、模型
     */
    public static Result<Device> checkInfoToModel(
        DeviceEntity entity, 
        CommonFetcher commonFetcher
    ) {
        Result<Device> result = Device.checkInfoToBase(entity);
        if (!result.getSignal()) {
            return result;
        }
        Device device = result.getResult();

        Long edgeGatewayId = entity.getEdgeGatewayId();
        if(edgeGatewayId == null){
            return Result.error("所属网关不能为空");
        }
        EdgeGatewayEntity edgeGatewayEntity = commonFetcher.get(edgeGatewayId, EdgeGatewayEntity.class);
        if(edgeGatewayEntity == null){
            return Result.error("找不到所属网关");
        }
        device.edgeGatewayEntity = edgeGatewayEntity;
        List<ResourceRelationEntity> relationEntities = commonFetcher.list("device_id",entity.getId(),ResourceRelationEntity.class);

        if (Optional.ofNullable(relationEntities).isPresent() && relationEntities.size() == 1) {
            device.resourceEntity = relationEntities.get(0);
        }

        Result<DeviceModel> deviceModelResult = Device.checkModel(entity, commonFetcher);
        if (!deviceModelResult.getSignal()) {
            return Result.error(deviceModelResult.getMessage());
        }
        device.deviceModel = deviceModelResult.getResult();

        device.infoDegree = InfoDegreeEnum.MODEL;

        return Result.ok(device);
    }

    /**
     * 1 检查网关、通道、模型
     */
    public static Result<Device> checkInfoToModel2(
        DeviceEntity entity, 
        DeviceCheckInfoContext context
    ) {
        Result<Device> result = Device.checkInfoToBase(entity);
        if (!result.getSignal()) {
            return result;
        }
        Device device = result.getResult();

        Long edgeGatewayId = entity.getEdgeGatewayId();
        if(edgeGatewayId == null){
            return Result.error("所属网关不能为空");
        }
        EdgeGatewayEntity edgeGatewayEntity = context.getEedgeGatewayMapById(edgeGatewayId);
        if(edgeGatewayEntity == null){
            return Result.error("找不到所属网关");
        }
        device.edgeGatewayEntity = edgeGatewayEntity;
        List<ResourceRelationEntity> relationEntities = context.getResourceRelationMapByDeviceId(entity.getId());

        if (Optional.ofNullable(relationEntities).isPresent() && relationEntities.size() == 1) {
            device.resourceEntity = relationEntities.get(0);
        }

        Result<DeviceModel> deviceModelResult = Device.checkModel2(entity, context);
        if (!deviceModelResult.getSignal()) {
            return Result.error(deviceModelResult.getMessage());
        }
        device.deviceModel = deviceModelResult.getResult();

        device.infoDegree = InfoDegreeEnum.MODEL;

        return Result.ok(device);
    }

    /**
     * 2 检查网关、通道、模型、绑定类型
     */
    public static Result<Device> checkInfoToBindRelation(
        DeviceEntity entity, 
        CommonFetcher commonFetcher
    ) {
        Result<Device> result = checkInfoToModel(entity, commonFetcher);
        if(!result.getSignal()){
            return result;
        }
        Device device = result.getResult();


        device.propertyLabelMap = new HashMap<>();
        device.nameMap = new HashMap<>();
        List<Property> properties = device.deviceModel.getProperties();
        //捞出绑定关系
        List<LabelBindRelationEntity> relationEntityList = commonFetcher.list("device_id", device.getId(), LabelBindRelationEntity.class);
        device.relationEntities = relationEntityList;
        List<LabelBindRelation> relationList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(relationEntityList)) {
            for(LabelBindRelationEntity relationEntity:relationEntityList){
                Result<LabelBindRelation> labelResult = LabelBindRelation.checkInfoToLabel(relationEntity, commonFetcher);
                if(!labelResult.getSignal()){
                    continue;
                }
                relationList.add(labelResult.getResult());
            }
            Result<Map<String, LabelBindRelation>> propertyLabelMapResult = Device.checkLabelBindRelation(properties, relationList,true);
            if (!propertyLabelMapResult.getSignal()) {
                return Result.error(propertyLabelMapResult.getMessage());
            }
            device.propertyLabelMap.putAll(propertyLabelMapResult.getResult());
        }

        device.groupMap = new HashMap<>();
        List<LabelBindRelation> groupLabelRelation = null;
        if (!ObjectUtils.isEmpty(device.deviceEntity.getChannel()) && !ObjectUtils.isEmpty(device.deviceEntity.getSource())) {
            UniqueConstraint.Unique unique = nameUniqueConstraint.buildUnique(new FieldValue(device.deviceEntity.getChannel()),new FieldValue(device.deviceEntity.getEdgeGatewayId()));
            ChannelEntity channelEntity = commonFetcher.get(unique, ChannelEntity.class);
            if (!ObjectUtils.isEmpty(channelEntity)) {
                device.bindChannel = channelEntity;
                Map<String,FieldValue> groupFields = new HashMap<>();
                groupFields.put("name",new FieldValue(device.deviceEntity.getSource()));
                groupFields.put("channel_id",new FieldValue(channelEntity.getId()));
                UniqueConstraint.Unique groupUnique = new UniqueConstraint("name","channel_id").buildUnique(groupFields);
                LabelGroupEntity labelGroupEntity = commonFetcher.get(groupUnique, LabelGroupEntity.class);
                if (!ObjectUtils.isEmpty(labelGroupEntity)) {
                    device.bindGroup = labelGroupEntity;
                    List<LabelEntity> labelList = commonFetcher.list("label_group_id", labelGroupEntity.getId(), LabelEntity.class);
                    if (CollectionUtil.isNotEmpty(labelList)) {
                        Set<String> propertyNames = new HashSet<>();
                        Set<String> labelNames = new HashSet<>();
                        // Map<String, Property> propertyMap = BeanUtilsIntensifier.collection2Map(device.getDeviceModel().getProperties(), Property::getName);
                        BeanUtilsIntensifier.getSomething2Collection(device.getDeviceModel().getProperties(),Property::getName,propertyNames);
                        BeanUtilsIntensifier.getSomething2Collection(labelList,LabelEntity::getName,labelNames);
                        propertyNames.retainAll(labelNames);
                        propertyNames.removeAll(device.propertyLabelMap.keySet());
                        if (CollectionUtil.isNotEmpty(propertyNames)) {
                            String groupName = device.deviceEntity.getSource();
                            groupLabelRelation = LabelBindRelation.bindByGroup(propertyNames,device.deviceModel.getProperties(),labelList,groupName,channelEntity);
                        }
                    }

                }
            }
        }
        if (CollectionUtil.isNotEmpty(groupLabelRelation)) {
            Result<Map<String, LabelBindRelation>> groupLabelRelationMap = Device.checkLabelBindRelation(properties, groupLabelRelation,false);
            if (!groupLabelRelationMap.getSignal()) {
                return Result.error(groupLabelRelationMap.getMessage());
            }
            device.propertyLabelMap.putAll(groupLabelRelationMap.getResult());
        }

        device.propertyLabelMap.values().forEach(relation -> {
            String k = relation.getChannelName() + "." + relation.getLabelGroupName() + "." + relation.getLabelName();
            if (device.nameMap.containsKey(k)) {
                String propertyNames = device.nameMap.get(k);
                propertyNames = propertyNames + "," + relation.getPropertyName();
                device.nameMap.put(k,propertyNames);
            }else {
                device.nameMap.put(k,relation.getPropertyName());
            }
            String group = relation.getChannelName() + "." + relation.getLabelGroupName();
            String relationStr = relation.getLabelName() + relation.getLabel().getDataType().getName() + relation.getLabel().getIsArray();
            if (device.groupMap.containsKey(group)) {
                device.groupMap.get(group).add(relationStr);
            }else {
                Set<String> labels = CollectionUtil.newHashSet();
                labels.add(relationStr);
                device.groupMap.put(group, labels);
            }
        });

        device.infoDegree = InfoDegreeEnum.BIND_RELATION;
        return Result.ok(device);
    }


    /**
     * 2 检查网关、通道、模型、绑定类型
     */
    public static Result<Device> checkInfoToBindRelation2(
        DeviceEntity entity, 
        DeviceCheckInfoContext context
    ) {
        Result<Device> result = checkInfoToModel2(entity, context);
        if(!result.getSignal()){
            return result;
        }
        Device device = result.getResult();


        device.propertyLabelMap = new HashMap<>();
        device.nameMap = new HashMap<>();
        List<Property> properties = device.deviceModel.getProperties();
        //捞出绑定关系
        List<LabelBindRelationEntity> relationEntityList = context.getLabelBindRelationEntitiesByDeviceId(device.getId());
        device.relationEntities = relationEntityList;
        List<LabelBindRelation> relationList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(relationEntityList)) {
            for(LabelBindRelationEntity relationEntity:relationEntityList){
                Result<LabelBindRelation> labelResult = LabelBindRelation.checkInfoToLabel2(relationEntity, context);
                if(!labelResult.getSignal()){
                    continue;
                }
                relationList.add(labelResult.getResult());
            }
            Result<Map<String, LabelBindRelation>> propertyLabelMapResult = Device.checkLabelBindRelation(properties, relationList,true);
            if (!propertyLabelMapResult.getSignal()) {
                return Result.error(propertyLabelMapResult.getMessage());
            }
            device.propertyLabelMap.putAll(propertyLabelMapResult.getResult());
        }

        device.groupMap = new HashMap<>();
        List<LabelBindRelation> groupLabelRelation = null;
        if (!ObjectUtils.isEmpty(device.deviceEntity.getChannel()) && !ObjectUtils.isEmpty(device.deviceEntity.getSource())) {
            ChannelEntity channelEntity = context.getChannelEntityByGwId(device.deviceEntity.getEdgeGatewayId(), device.deviceEntity.getChannel());
            if (!ObjectUtils.isEmpty(channelEntity)) {
                device.bindChannel = channelEntity;
                LabelGroupEntity labelGroupEntity = context.getLabelGroupEntityByChannelId(channelEntity.getId(), device.deviceEntity.getSource());
                if (!ObjectUtils.isEmpty(labelGroupEntity)) {
                    device.bindGroup = labelGroupEntity;
                    List<LabelEntity> labelList = context.getLabelByLabelGroupId( labelGroupEntity.getId());
                    if (CollectionUtil.isNotEmpty(labelList)) {
                        Set<String> propertyNames = new HashSet<>();
                        Set<String> labelNames = new HashSet<>();
                        BeanUtilsIntensifier.getSomething2Collection(device.getDeviceModel().getProperties(),Property::getName,propertyNames);
                        BeanUtilsIntensifier.getSomething2Collection(labelList,LabelEntity::getName,labelNames);
                        propertyNames.retainAll(labelNames);
                        propertyNames.removeAll(device.propertyLabelMap.keySet());
                        if (CollectionUtil.isNotEmpty(propertyNames)) {
                            String groupName = device.deviceEntity.getSource();
                            groupLabelRelation = LabelBindRelation.bindByGroup(propertyNames,device.deviceModel.getProperties(),labelList,groupName,channelEntity);
                        }
                    }

                }
            }
        }
        if (CollectionUtil.isNotEmpty(groupLabelRelation)) {
            Result<Map<String, LabelBindRelation>> groupLabelRelationMap = Device.checkLabelBindRelation(properties, groupLabelRelation,false);
            if (!groupLabelRelationMap.getSignal()) {
                return Result.error(groupLabelRelationMap.getMessage());
            }
            device.propertyLabelMap.putAll(groupLabelRelationMap.getResult());
        }

        device.propertyLabelMap.values().forEach(relation -> {
            String k = relation.getChannelName() + "." + relation.getLabelGroupName() + "." + relation.getLabelName();
            if (device.nameMap.containsKey(k)) {
                String propertyNames = device.nameMap.get(k);
                propertyNames = propertyNames + "," + relation.getPropertyName();
                device.nameMap.put(k,propertyNames);
            }else {
                device.nameMap.put(k,relation.getPropertyName());
            }
            String group = relation.getChannelName() + "." + relation.getLabelGroupName();
            String relationStr = relation.getLabelName() + relation.getLabel().getDataType().getName() + relation.getLabel().getIsArray();
            if (device.groupMap.containsKey(group)) {
                device.groupMap.get(group).add(relationStr);
            }else {
                Set<String> labels = CollectionUtil.newHashSet();
                labels.add(relationStr);
                device.groupMap.put(group, labels);
            }
        });

        device.infoDegree = InfoDegreeEnum.BIND_RELATION;
        return Result.ok(device);
    }

    /**
     * 2 检查网关、通道、模型、绑定类型(绑定关系异常不报错)
     * @param entity
     * @param commonFetcher
     * @return
     */
    public static Result<Device> checkInfoWithBindRelation(
        DeviceEntity entity, CommonFetcher commonFetcher
    ) {
        Result<Device> result = checkInfoToModel(entity, commonFetcher);
        if(!result.getSignal()){
            return result;
        }
        Device device = result.getResult();

        //捞出绑定关系
        List<LabelBindRelationEntity> relationEntityList = commonFetcher.list("device_id", device.getId(), LabelBindRelationEntity.class);
        List<LabelBindRelation> relationList = new ArrayList<>();
        Iterator<LabelBindRelationEntity> relationIterator = relationEntityList.iterator();
        while (relationIterator.hasNext()){
         LabelBindRelationEntity relationEntity = relationIterator.next();
            Result<LabelBindRelation> labelResult = LabelBindRelation.checkInfoToLabel(relationEntity, commonFetcher);
            if(!labelResult.getSignal()){
                relationIterator.remove();
                continue;
            }
            relationList.add(labelResult.getResult());
        }

        List<Property> properties = device.deviceModel.getProperties();
        Result<Map<String, LabelBindRelation>> propertyLabelMapResult = Device.checkLabelBindRelation(properties, relationList,false);
        if (!propertyLabelMapResult.getSignal()) {
            return Result.error(propertyLabelMapResult.getMessage());
        }
        device.propertyLabelMap = propertyLabelMapResult.getResult();

        device.infoDegree = InfoDegreeEnum.BIND_RELATION;
        return Result.ok(device);
    }


    /**
     * 3 检查网关、通道、模型、绑定类型、触发事件
     */
    public static Result<Device> checkInfoToEvent(
        DeviceEntity entity, 
        CommonFetcher commonFetcher
    ) {
        Result<Device> result = checkInfoToBindRelation(entity, commonFetcher);
        if(!result.getSignal()){
            return result;
        }
        Device device = result.getResult();

        //校验事件属性必须绑定标签 不在需要校验绑定
//        Result<Void> eventResult = null;
//        List<Event> events = device.deviceModel.getEvents();
//        for(Event event:events){
//            eventResult = Device.checkEventUsefullyPropertyBindRelation(event, device.propertyLabelMap,true);
//            if (!eventResult.getSignal()) {
//                return Result.error(eventResult.getMessage());
//            }
//        }
        device.infoDegree = InfoDegreeEnum.EVENT;
        return Result.ok(device);
    }

    /**
     * 3 检查网关、通道、模型、绑定类型、触发事件
     */
    public static Result<Device> checkInfoToEvent2(
        DeviceEntity entity, 
        DeviceCheckInfoContext context
    ) {
        Result<Device> result = checkInfoToBindRelation2(entity, context);
        if(!result.getSignal()){
            return result;
        }
        Device device = result.getResult();

        //校验事件属性必须绑定标签 不在需要校验绑定
//        Result<Void> eventResult = null;
//        List<Event> events = device.deviceModel.getEvents();
//        for(Event event:events){
//            eventResult = Device.checkEventUsefullyPropertyBindRelation(event, device.propertyLabelMap,true);
//            if (!eventResult.getSignal()) {
//                return Result.error(eventResult.getMessage());
//            }
//        }
        device.infoDegree = InfoDegreeEnum.EVENT;
        return Result.ok(device);
    }



    /**
     * 3 检查网关、通道、模型、绑定类型、触发事件
     */
    public static Result<Device> checkInfoToEventNew(
            DeviceEntity entity,
            Map<Long,List<LabelBindRelationEntity>> labelBindRelationEntityMap, //根据设备ID
            Map<Long,EdgeGatewayEntity> edgeGatewayEntityMap,
            Map<Long,List<ResourceRelationEntity>> resourceRelationEntityListMap, //根据设备ID
            Map<Long,ThingModelEntity> thingModelEntityMap,//根据继承modelId获取到的map
            Map<Long,List<ThingModelInheritEntity>> thingModelInheritEntityMap, //根据继承modelId获取到的map
            Map<Long,List<ThingServiceEntity>> thingServiceEntityMap,   //根据继承modelId获取到的map
            Map<Long,List<SubscriptionEntity>> subscriptionEntityMap, //根据继承deviceId,modelId获取到的map
            Map<Long,List<DeviceModelInheritEntity>> deviceModelinheritEntityListMap,  // 根据deviceId
            Map<Long,List<DeviceServiceEntity>> deviceServiceEntityMap, //根据deviceId)
            List<LabelEntity> labelEntityList,
            List<ChannelEntity> channelEntitieList,
            List<LabelGroupEntity> labelGroupEntityList) {

        Result<Device> result = checkInfoToBindRelationNew(entity,
                labelBindRelationEntityMap, //根据设备ID
                edgeGatewayEntityMap,
                resourceRelationEntityListMap, //根据设备ID
                thingModelEntityMap,//根据继承modelId获取到的map
                thingModelInheritEntityMap, //根据继承modelId获取到的map
                thingServiceEntityMap,   //根据继承modelId获取到的map
                subscriptionEntityMap, //根据继承deviceId,modelId获取到的map
                deviceModelinheritEntityListMap,  // 根据deviceId
                deviceServiceEntityMap, //根据deviceId)
                labelEntityList,
                channelEntitieList,
                labelGroupEntityList
        );
        if(!result.getSignal()){
            return result;
        }
        Device device = result.getResult();

        device.infoDegree = InfoDegreeEnum.EVENT;
        return Result.ok(device);
    }
    
    /**
     * 3.1 检查网关、通道、模型、绑定类型、某个触发事件
     */
    public static Result<Device> checkInfoToSingleEvent(
        DeviceEntity entity,
        CommonFetcher commonFetcher,
        String eventName,
        Boolean checkBind
    ){
       
        Result<Device> result = checkInfoToBindRelation(entity, commonFetcher);
        if(!result.getSignal()){
            return result;
        }
        Device device = result.getResult();

        List<Event> events = device.deviceModel.getEvents();
        List<Event> collect = events.stream().filter(event -> event.getName().equals(eventName)).collect(Collectors.toList());
        if (collect.size() != 1) {
            log.error("event:{} is no exist!",eventName);
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }

        //result
//        Result<Void> eventResult = null;
//        for (Event event : events) {
//            if (event.getName().equals(eventName)) {
//                eventResult = Device.checkEventUsefullyPropertyBindRelation(event, device.propertyLabelMap,checkBind);
//                break;
//            }
//        }
//        if (!eventResult.getSignal()) {
//            return Result.error(eventResult.getMessage());
//        }
        device.infoDegree = InfoDegreeEnum.SINGLE_EVENT;
        return Result.ok(device);
    }

    /**
     * 4 检查网关、通道、模型、绑定类型、触发事件、属性上报
     */
    public static Result<Device> checkInfoToReport(
        DeviceEntity entity, 
        CommonFetcher commonFetcher
    ) {
        Result<Device> result = checkInfoToEvent(entity, commonFetcher);
        if(!result.getSignal()){
            return result;
        }
        Device device = result.getResult();

        //有上报的属性必须绑定标签
        List<Property> properties = device.deviceModel.getProperties();
        for(Property property:properties){
            ReportTypeEnum reportType = property.getReportType();
            switch (reportType) {
                case NO_REPORT: {
                    //不上报，不需要绑定标签
                    continue;
                }
                case CHANGE_REPORT:
                case KEEP_REPORT: {

                    LabelBindRelation releation = device.propertyLabelMap.get(property.getName());
                    if(releation == null){
                        return Result.error("上报属性未绑定标签，property:" + property.getName());
                    }
                    
                    break;
                }
                default: {

                    break;
                }
            }
        }

        device.infoDegree = InfoDegreeEnum.REPORT;
        return Result.ok(device);
    }

    private static Result<DeviceModel> checkModel(DeviceEntity entity,CommonFetcher commonFetcher){
        Result<DeviceModel> deviceModelResult = DeviceModel.checkInfo(
            entity,
            commonFetcher
        );
        if(!deviceModelResult.getSignal()){
            return Result.error(deviceModelResult.getMessage());
        }
        DeviceModel deviceModel = deviceModelResult.getResult();
        return Result.ok(deviceModel);
    }

    private static Result<DeviceModel> checkModel2(DeviceEntity entity,DeviceCheckInfoContext context){
        Result<DeviceModel> deviceModelResult = DeviceModel.checkInfo2(
            entity,
            context
        );
        if(!deviceModelResult.getSignal()){
            return Result.error(deviceModelResult.getMessage());
        }
        DeviceModel deviceModel = deviceModelResult.getResult();
        return Result.ok(deviceModel);
    }

    /**
     * 校验绑定关系
     * 如果有找到绑定关系，会把标签名和标签id赋值到property上
     * @return 返回校验结果
     */
    private static Result<Map<String, LabelBindRelation>> checkLabelBindRelation(List<Property> properties, List<LabelBindRelation> relationList,boolean throwError){        //检查属性绑定

        //检查属性绑定类型
        Map<String, LabelBindRelation> propertyLabelMap = new HashMap<>();
        Map<String, LabelBindRelation> bindRelationBoMap = relationList.stream().collect(Collectors.toMap(LabelBindRelation::getPropertyName,Function.identity()));
        for(Property property:properties){
            Optional<LabelBindRelation> findAny = Optional.empty();
            if (bindRelationBoMap.containsKey(property.getName())) {
                findAny = Optional.of(bindRelationBoMap.get(property.getName()));
                bindRelationBoMap.remove(property.getName());
            }
            if(findAny.isPresent()){
                LabelBindRelation relation = findAny.get();
                //检查绑定的标签数据类型和属性数据类型是否匹配
                ThingDataTypeEnum propertyType = property.getDataType().getType();
                Boolean propertyIsArray = property.getDataType().getIsArray();
                Boolean propertyReadOnly = property.getReadOnly();

                ThingDataTypeEnum labelType = relation.getLabel().getDataType();
                Boolean labelIsArray = relation.getLabel().getIsArray();
                Boolean labelReadOnly = relation.getLabel().getReadOnly();

                if (!property.getDirectlyModelId().equals(relation.getDirectlyModelId())) {
                    if (throwError) {
                        return Result.error(
                                "还有标签绑定关系异常未处理：name:" + property.getName()
                        );
                    }else {
                        continue;
                    }
                }

                if(!propertyReadOnly.equals(labelReadOnly)){
                    if (throwError) {
                        return Result.error(
                                "属性绑定标签只读不一致：name:" + property.getName()
                                        + ", labelReadOnly:" + labelReadOnly
                                        + ", propertyReadOnly:" + propertyReadOnly
                        );
                    }else {
                        continue;
                    }
                }
                if(!propertyType.equals(labelType)){
                    if (throwError) {
                        return Result.error(
                                "属性绑定标签类型不一致：name:" + property.getName()
                                        + ", labelType:" + labelType
                                        + ", propertyType:" + propertyType.getName()
                        );
                    }else {
                        continue;
                    }
                }
                if(!propertyIsArray.equals(labelIsArray)){
                    if (throwError) {
                        return Result.error(
                                "属性绑定标签是否数组不一致：name:" + property.getName()
                                        + ", labelIsArray:" + labelIsArray
                                        + ", propertyIsArray:" + propertyIsArray
                        );
                    }else {
                        continue;
                    }
                }
                property.setLabelId(relation.getLabelId());
                property.setChannelName(relation.getChannelName());
                property.setLabelGroupName(relation.getLabelGroupName());
                property.setLabelName(relation.getLabel().getName());
                propertyLabelMap.put(property.getName(), relation);
            }
        }
        if (bindRelationBoMap.size() > 0) {
            if (throwError) {
                return Result.error("存在异常绑定关系未处理，name:" + Arrays.toString(bindRelationBoMap.keySet().toArray()));
            }
        }
        return Result.ok(propertyLabelMap);
    }

    private static Result<Void> checkEventUsefullyPropertyBindRelation(Event event, Map<String, LabelBindRelation> propertyLabelMap,Boolean checkBind){
        Set<String> propertyList = new HashSet<>();
        switch (event.getType()) {
            case TRIGGER :
            case ERROR:
            case FAULT: {
                //获取事件表达式的属性
                List<String> conditionPropertyList = event.getEventDefine().getTrigger().listConditionProperty();
                //获取事件上报属性
                List<String> reportPropertyList = event.getEventDefine().getProperties();
                //检查这些属性所属的分组是否是相同频率和相同网关，如果不是，返回错误
                propertyList.addAll(conditionPropertyList);
                propertyList.addAll(reportPropertyList);
                break;
            }
            default:
                return Result.error("事件类型错误, type:" + event.getType().getName());
        }
        if (checkBind) {
            for(String p:propertyList){
                if (!propertyLabelMap.containsKey(p)) {
                    return Result.error("事件涉及的属性没有绑定标签，property:" + p);
                }
            }
        }
        return Result.ok();
    }

    /**
     * 属性计算任务：
     * 1. 上报类型是改变上报或者持续上报，并且都绑定标签才会创建任务
     * @return
     */
    private Result<List<ComputeChangeItem>> createPropertyChangeList(){
        List<ComputeChangeItem> changeList = new ArrayList<>();

        //创建设备的计算任务
        List<Property> properties = deviceModel.getProperties();
        for(Property property:properties){
            ReportTypeEnum reportType = property.getReportType();
            switch (reportType){
                case NO_REPORT: {
                    break;
                }
                case CHANGE_REPORT: {
                    //获取上报属性
                    String reportProperty = property.getName();
                    
                    //获取属性要订阅的采集任务对应的标签分组
                    LabelBindRelation relation = propertyLabelMap.get(reportProperty);
                    if(relation == null){
                        break;
                    }
                    
                    Label label = relation.getLabel();
                    ComputeChangeItem item = ComputeChangeItem.builder()
                        .channelId(label.getChannelId())
                        .labelId(label.getId())
                        .deviceId(id)
                        .property(reportProperty)
                        .labelName(label.getName())
                        .isArray(label.getIsArray())
                        .dataType(label.getDataType().getName())
                        .length(label.getLength())
                        .stringBytes(label.getStringBytes())
                        .build();
                    changeList.add(item);
                    break;
                }
                default: {

                }
            }
        }

        return Result.ok(changeList);
    }

    private Result<ComputeEventItem> buildComputeEvent(Event event, EventTypeEnum eventTypeEnum){
        String eventType = eventTypeEnum.getName();
//        List<ComputeTaskEntity> taskList = new ArrayList<>();

        //获取事件表达式的属性
//        List<String> conditionPropertyList = event.getEventDefine().getTrigger().listConditionProperty();
        //获取事件上报属性
//        List<String> reportPropertyList = event.getEventDefine().getProperties();
        //检查这些属性所属的分组是否是相同频率和相同网关，如果不是，返回错误
//        List<String> propertyList = new ArrayList<>();
//        propertyList.addAll(conditionPropertyList);
//        propertyList.addAll(reportPropertyList);

//        Map<Long, List<String>> edgeGatewayGroup = propertyList.stream().collect(Collectors.groupingBy(t -> {
//            LabelBindRelation relation = propertyLabelMap.get(t);
//            if(relation == null){
//                return -1L;
//            }
//            return relation.getLabel().getEdgeGatewayId();
//        }));
//        edgeGatewayGroup.remove(-1L);
//        if(edgeGatewayGroup.size() > 1){
//            return Result.error("事件属性绑定的标签从属不同的网关, event:" + event.getName() + ", map:" + edgeGatewayGroup);
//        }
//
//        if(!edgeGatewayGroup.containsKey(edgeGatewayEntity.getId())){
//            Long propertyEdgeGatewayId = null;
//            if(edgeGatewayGroup.keySet().size() > 0){
//                propertyEdgeGatewayId = edgeGatewayGroup.keySet().iterator().next();
//            }
//            return Result.error("事件属性绑定的标签网关跟设备网关不相符, event:"
//                        + event.getName()
//                        + ", deviceEdgeGatewayId:" + edgeGatewayEntity.getId()
//                        + ", propertyEdgeGatewayId:" + propertyEdgeGatewayId
//
//            );
//        }

        //编译触发条件，把属性转成标签
        CompileInfo compileInfo = event.getEventDefine().getTrigger().compile(propertyLabelMap,false);
        String triggerStr = compileInfo.getCompileStr();
        Set<String> conditionProperties = compileInfo.getConditionProperties();

//        ComputeEventItem computeEventItem = new ComputeEventItem();

        List<PropDataElm> taskProperties = new ArrayList<>();
        List<String> eventProperties = event.getEventDefine().getProperties();
        List<Property> allProperty = new ArrayList<>();
        allProperty.addAll(DeviceModel.getBaseModel().getProperties());
        allProperty.addAll(this.deviceModel.getProperties());
        Map<String, Property> propertyMap = BeanUtilsIntensifier.collection2Map(allProperty, Property::getName);
        for(String eventProperty:eventProperties){
            Property property = propertyMap.get(eventProperty);
            if(property == null){
                return Result.error("事件涉及的属性不存在，property:" + eventProperty);
            }
            PropDataElm propData = PropDataElm.builder()
                .property(eventProperty)
                .labelId(property.getLabelId())
                .dataType(property.getDataType().getType().getName())
                .isArray(property.getDataType().getIsArray())
                .build();
            taskProperties.add(propData);
        }
        String topic = GwUpEventTopic.createTopic(deviceEntity.getTenantId(), edgeGatewayEntity.getId(), id, eventType, event.getName());

        ComputeEventItem.ComputeEventItemBuilder itemBuilder = ComputeEventItem.builder();
        itemBuilder
            .type(eventType)
            .eventName(event.getName())
            .trigger(triggerStr)
            .conditionProperties(conditionProperties)
            .properties(taskProperties)
            .deviceId(id)
            .topic(topic);
        if(event.getEventDefine() != null){
            Integer faultBeginThreshold = event.getEventDefine().getFaultBeginThreshold();
            if(faultBeginThreshold != null){
                itemBuilder.faultBeginThreshold(faultBeginThreshold);
            }
            if (event.getEventDefine().getFaultLevelDefine() != null && event.getEventDefine().getFaultLevelDefine().getLevelDefine()!=null){
                itemBuilder.faultLevelDefineElm(event.getEventDefine().getFaultLevelDefine().getLevelDefine());
            }
            Integer faultEndThreshold = event.getEventDefine().getFaultEndThreshold();
            if(faultEndThreshold != null){
                itemBuilder.faultEndThreshold(faultEndThreshold);
            }
            Map<String, String> faultInputMap = event.getEventDefine().getFaultInputMap();
            if(faultInputMap != null){
                itemBuilder.faultInputMap(faultInputMap);
            }
        }
        ComputeEventItem item = itemBuilder.build();

        return Result.ok(item);
    }

    private Result<List<ComputeEventItem>> createEventList(){
        List<ComputeEventItem> eventList = new ArrayList<>();

        //每个事件创建1个
        List<Event> events = deviceModel.getEvents();
        for(Event event:events){

            switch (event.getType()) {
                case ERROR:
                case FAULT: {
                    Result<ComputeEventItem> itemResult = buildComputeEvent(event, EventTypeEnum.FAULT);
                    if(!itemResult.getSignal()){
                        return Result.error(itemResult.getMessage());
                    }
                    eventList.add(itemResult.getResult());
                    break;
                }
                case TRIGGER: {
                    Result<ComputeEventItem> itemResult = buildComputeEvent(event, EventTypeEnum.TRIGGER);
                    if(!itemResult.getSignal()){
                        return Result.error(itemResult.getMessage());
                    }
                    eventList.add(itemResult.getResult());
                    break;
                }
                default: {
                    return Result.error("事件类型错误, type:" + event.getType().getName());
                }
            }
        }
        return Result.ok(eventList);
    }

    /**
     * 创建运行时计算任务
     */
    public Result<ComputeTaskEntity> createComputeTask() {
        checkDegree(InfoDegreeEnum.EVENT);

        String computeType = ComputeTypeEnum.DEVICE.getName();
        String topic = GwUpPropertyTopic.createTopic(deviceEntity.getTenantId(), edgeGatewayEntity.getId(), id);

        //不再需要改变上报
        Result<List<ComputeEventItem>> eventResult = createEventList();
        if(!eventResult.getSignal()){
            return Result.error(eventResult.getMessage());
        }
        List<ComputeEventItem> eventList = eventResult.getResult();

        Map<String, List<ComputeEventItem>> group = eventList.stream().collect(Collectors.groupingBy(ComputeEventItem::getType));
        List<ComputeEventItem> faultList = group.get(EventTypeEnum.FAULT.getName());
        List<ComputeEventItem> triggerList = group.get(EventTypeEnum.TRIGGER.getName());

        ComputeContentField content = ComputeContentField.builder()
            .computeType(computeType)
            .id(id)
            .topic(topic)
//            .changeList(changeList)         //不再需要改变上报
            .faultList(faultList)
            .triggerList(triggerList)
            .build();
            
        String taskName = computeType + "_" + id;
        ComputeTaskEntity computeTask = ComputeTaskEntity.builder()
            .name(taskName)
            .edgeGatewayId(edgeGatewayEntity.getId())
            .deviceId(id)
            .content(content)
            .build();
        
        return Result.ok(computeTask);
    }

    /**
     * 创建运行时元数据
     * @return 返回运行时字段
     */
    public DeviceRuntimeMetadataField createRuntimeMetadata() {
        checkDegree(InfoDegreeEnum.EVENT);
        DeviceRuntimeMetadataField data = new DeviceRuntimeMetadataField();
        data.setDeviceName(deviceEntity.getName());
        data.setEdgeGatewayId(deviceEntity.getEdgeGatewayId());
        data.setId(deviceEntity.getId());
        data.setEdgeGatewayName(edgeGatewayEntity.getName());
//        data.setResourceId(resourceEntity.getId().toString());
        data.setTenantId(deviceEntity.getTenantId());
        Map<String, PropertyMetadataItem> properties = new HashMap<>();
        Map<String, com.nti56.nlink.product.device.client.model.dto.json.scriptApi.Service> services = new HashMap<>();
        Map<String, SubscriptionDpo> subscriptions = new HashMap<>();
        data.setProperties(properties);
        List<EventDpo> events = new ArrayList<>();
        data.setServices(services);
        data.setEvents(events);
        data.setSubscriptions(subscriptions);
        Optional.ofNullable(deviceModel.getEvents()).orElse(new ArrayList<>()).forEach(item -> this.initEvent(item,events));
        Optional.ofNullable(deviceModel.getProperties()).orElse(new ArrayList<>()).forEach(item -> this.initProperty(item,properties));
        Optional.ofNullable(deviceModel.getServices()).orElse(new ArrayList<>()).forEach(item -> this.initService(item,services));
        Optional.ofNullable(deviceModel.getSubscriptions()).orElse(new ArrayList<>()).forEach(item -> this.initSubscription(item,subscriptions));

        return data;
    }

    public  Map<String, List<Subscription>>  getSubscriptionRegistry(){
        List<Subscription> subscriptionList = deviceModel.getSubscriptions();
        Map<String, List<Subscription>> registryMap = new HashMap<>();
        if (!subscriptionList.isEmpty()){
            for (Subscription subscription : subscriptionList) {
                SubscriptionEventTypeEnum eventTypeEnum = SubscriptionEventTypeEnum.typeOfValue(subscription.getEventType());
                if (ObjectUtil.isEmpty(eventTypeEnum)) {
                    continue;
                }
                switch (eventTypeEnum) {
                    case ERROR:
                    case FAULT:{
                        String events = subscription.getEvents();
                        if (StringUtils.isNotBlank(events)){
                            String[] eventArr = events.split(",");
                            for (String eventName : eventArr) {
                                String key = RedisConstant.FAULT_EVENT_PREFIX + eventName;
                                List<Subscription> registrySubscriptions = registryMap.get(key);
                                if (registrySubscriptions == null){
                                    registrySubscriptions = new ArrayList<>();
                                }
                                registrySubscriptions.add(subscription);
                                registryMap.put(key,registrySubscriptions);
                            }
                        }
                        break;
                    }
                    case TRIGGER: {
                        String events = subscription.getEvents();
                        if (StringUtils.isNotBlank(events)){
                            String[] eventArr = events.split(",");
                            for (String eventName : eventArr) {
                                String key = RedisConstant.TRIGGER_EVENT_PREFIX + eventName;
                                List<Subscription> registrySubscriptions = registryMap.get(key);
                                if (registrySubscriptions == null){
                                    registrySubscriptions = new ArrayList<>();
                                }
                                registrySubscriptions.add(subscription);
                                registryMap.put(key,registrySubscriptions);
                            }
                        }
                        break;
                    }
                    case DATA_CHANGE:{
                        String propertiesStr = subscription.getProperties();
                        if (StringUtils.isNotBlank(propertiesStr)){
                            String[] propertyArr = propertiesStr.split(",");
                            for (String property : propertyArr) {
                                List<Subscription> registrySubscriptions = registryMap.get(property);
                                if (registrySubscriptions == null){
                                    registrySubscriptions = new ArrayList<>();
                                }
                                registrySubscriptions.add(subscription);
                                registryMap.put(property,registrySubscriptions);
                            }
                        }
                        break;
                    }
                    case NO_CHANGE: {
                        String propertiesStr = subscription.getProperties();
                        if (StringUtils.isNotBlank(propertiesStr)){
                            String[] propertyArr = propertiesStr.split(",");
                            for (String property : propertyArr) {
                                String key = RedisConstant.NO_CHANGE_EVENT_PREFIX + property;
                                List<Subscription> registrySubscriptions = registryMap.get(key);
                                if (registrySubscriptions == null){
                                    registrySubscriptions = new ArrayList<>();
                                }
                                registrySubscriptions.add(subscription);
                                registryMap.put(key,registrySubscriptions);
                            }
                        }
                        break;
                    }
                    default:
                        continue;
                }
            }
        }
        return registryMap;
    }

    private void initSubscription(Subscription subscription,Map<String, SubscriptionDpo> subscriptions){
        subscriptions.put(subscription.getName(),subscription.toDpo());
    }

    private void initEvent(Event item, List<EventDpo> events) {
        EventDpo eventDpo = item.toDpo();
        eventDpo.getEventDefine().setTrigger(null);
        events.add(eventDpo);
    }

    /**
     * 设备属性初始化
     */
    private void initProperty(Property item, Map<String, PropertyMetadataItem> properties) {
        PropertyMetadataItem propertyMetadataItem = new PropertyMetadataItem();
        propertyMetadataItem.setProperty(item.getName());
        propertyMetadataItem.setIsArray(item.getDataType().getIsArray());
        propertyMetadataItem.setDataType(item.getDataType().getRaw());
        propertyMetadataItem.setReadOnly(item.getReadOnly());
        propertyMetadataItem.setPersist(item.getPersist());
        if (this.propertyLabelMap.containsKey(item.getName())) {
            LabelBindRelation labelBindRelation = this.propertyLabelMap.get(item.getName());
            Label label = labelBindRelation.getLabel();
            propertyMetadataItem.setAddress(label.getAddress());
            propertyMetadataItem.setLabelId(labelBindRelation.getLabelId());
            propertyMetadataItem.setLabelPath(labelBindRelation.getChannelName() + "." + labelBindRelation.getLabelGroupName() + "." +labelBindRelation.getLabelName());
            propertyMetadataItem.setLength(label.getLength());
            propertyMetadataItem.setStringBytes(label.getStringBytes());
            propertyMetadataItem.setChannelId(label.getChannelId());
        }
        properties.put(item.getName(), propertyMetadataItem);
    }

    /**
     * 设备服务初始化
     */
    private void initService(com.nti56.nlink.product.device.server.domain.thing.modelbase.Service item, Map<String, com.nti56.nlink.product.device.client.model.dto.json.scriptApi.Service> services) {
        com.nti56.nlink.product.device.client.model.dto.json.scriptApi.Service s = new com.nti56.nlink.product.device.client.model.dto.json.scriptApi.Service();
        s.setServiceCode(item.getServiceCode());
        s.setServiceName(item.getServiceName());
        s.setIsAsync(item.getAsync());
        s.setInputDataDefined(item.getInputData());
        s.setOutputDataDefined(item.getOutputData());
        services.put(item.getServiceName(),s);
    }


    public List<AccessElm> getEventConditionPropertyAccess(String eventName) {
        checkDegree(InfoDegreeEnum.SINGLE_EVENT);
        List<AccessElm> accessElms = new ArrayList<>();
        List<Event> events = this.deviceModel.getEvents();
        List<Property> propertyList = this.deviceModel.getProperties();
        Map<String, Property> propertyMap = BeanUtilsIntensifier.collection2Map(propertyList, Property::getName);
        events.forEach(event -> {
            if (event.getName().equals(eventName)) {
                List<String> properties = event.getEventDefine().getTrigger().listConditionProperty();
                properties.forEach(property -> property2Access(accessElms,property,propertyMap));
            }
        });
        return accessElms;
    }

    public List<AccessElm> listAccess(){
        List<AccessElm> result = new ArrayList<>();
        List<Property> properties = this.deviceModel.getProperties();
        for (Property property : properties) {
            LabelBindRelation labelBindRelation = this.propertyLabelMap.get(property.getName());
            if (labelBindRelation == null) {
                continue;
            }
            result.add(this.labelBindRelation2Access(labelBindRelation,property));
        }
        return result;
    }

    public List<AccessElm> getTriggerEventReportPropertyAccess(String eventName){
        checkDegree(InfoDegreeEnum.SINGLE_EVENT);
        List<Event> events = this.deviceModel.getEvents();
        for (Event event : events) {
            if (event.getName().equals(eventName)) {
                return getTriggerEventReportPropertyAccess(event);
            }
        }
        return new ArrayList<>();
    }

    private List<AccessElm> getTriggerEventReportPropertyAccess(Event event){
        List<AccessElm> accessElms = new ArrayList<>();
        List<Property> propertyList = this.deviceModel.getProperties();
        Map<String, Property> propertyMap = BeanUtilsIntensifier.collection2Map(propertyList, Property::getName);
        List<String> properties = event.getEventDefine().getProperties();
        properties.forEach(property -> property2Access(accessElms,property,propertyMap));
        return accessElms;
    }

    private AccessElm labelBindRelation2Access(LabelBindRelation labelBindRelation, Property property){
        AccessElm accessElm = BeanUtilsIntensifier.copyBean(labelBindRelation.getLabel(), AccessElm.class);
        accessElm.setLabelId(labelBindRelation.getLabelId());
        accessElm.setLabelName(labelBindRelation.getLabel().getName());
        accessElm.setLabelPath(labelBindRelation.getChannelName() + "." + labelBindRelation.getLabelGroupName() + "." +labelBindRelation.getLabelName());
        accessElm.setDataType(labelBindRelation.getLabel().getDataType().getName());
        accessElm.setProperty(property.getName());
        accessElm.setReadOnly(property.getReadOnly());
        return accessElm;
    }

    public EventDebugResult triggerEventDebug(String eventName, Map<String,Object> input){
        checkDegree(InfoDegreeEnum.SINGLE_EVENT);
        List<Event> events = this.deviceModel.getEvents();
        List<Property> propertyList = this.deviceModel.getProperties();
        Map<String, Property> propertyMap = BeanUtilsIntensifier.collection2Map(propertyList, Property::getName);
        EventDebugResult result = EventDebugResult.builder().ok(false).message("未定义trigger类型事件："+ eventName +"！").build();
        if (Optional.ofNullable(events).isPresent()) {
            for (Event event : events) {
                if (event.getName().equals(eventName) && (event.getType().equals(EventTypeEnum.TRIGGER)||event.getType().equals(EventTypeEnum.FAULT)||event.getType().equals(EventTypeEnum.ERROR))) {
                    result.setOk(true);
                    result.setMessage(null);
                    List<Map<String,String>> checkErrorMsg = new ArrayList<>();
                    input.forEach((key, value) -> {
                        Map<String, String> checkError = new HashMap<>();
                        if (ObjectUtil.isEmpty(value)) {
                            checkError.put(key, "属性值不能为空!");
                            checkErrorMsg.add(checkError);
                            return;
                        }
                        if (!propertyMap.containsKey(key)) {
                            checkError.put(key, "property is undefined!");
                            checkErrorMsg.add(checkError);
                            return;
                        }
                        Property property = propertyMap.get(key);
                        if (!inputCheckType(property, value)) {
                            checkError.put(key, "property value type error! type:" + property.getDataType().getType().getName() + ",value:" + value);
                            checkErrorMsg.add(checkError);
                        }else{
                            ThingDataTypeEnum dataType = property.getDataType().getType();
                            if(dataType!=ThingDataTypeEnum.BOOLEAN
                                    && dataType != ThingDataTypeEnum.BYTE && dataType != ThingDataTypeEnum.STRING){
                                input.put(key,new BigDecimal(String.valueOf(value)));
                            }
                        }
                    });
                    if (checkErrorMsg.size() > 0) {
                        result.setOk(false);
                        result.setMessage("事件触发属性异常！");
                        result.setCheckErrorMsg(checkErrorMsg);
                    }
                    if (result.getOk()) {
                        triggerEventDebug(event,input,result);
                    }
                }
            }
        }
        return result;
    }

    private Boolean inputCheckType(Property property, Object value) {
        return BaseService.checkBaseType(property.getDataType().getType(),value);
    }

    private void triggerEventDebug(Event event,Map<String,Object> input,EventDebugResult result){
        CompileInfo compileInfo = event.getEventDefine().getTrigger().compile(propertyLabelMap,true);
        String triggerStr = compileInfo.getCompileStr();
        result.setLogicResult(event.evalTrigger(triggerStr, input));
    }

    private void property2Access(List<AccessElm> accessElms,String property,Map<String,Property> propertyMap){
        if (this.propertyLabelMap.containsKey(property)) {
            LabelBindRelation labelBindRelation = this.propertyLabelMap.get(property);
            AccessElm accessElm = this.labelBindRelation2Access(labelBindRelation,propertyMap.get(property));
            accessElms.add(accessElm);
        }
    }


    public Property getProperty(String propertyName) {
        checkDegree(InfoDegreeEnum.MODEL);
        return deviceModel.getProperty(propertyName);
    }

    public Boolean saveNeedSync(CommonUpdater commonUpdater) {
        checkDegree(InfoDegreeEnum.BASE);
        Field notSyncField = new Field("sync_status", SyncStatusEnum.NOT_SYNC.getValue());
        return commonUpdater.set(id, notSyncField, DeviceEntity.class);
    }


    public Map<String,Object> getPropertiesDefaultValue(Set<String> oldPropertyNames){
        Map<String, Object> propertyMap = new ConcurrentHashMap<>();
        String source = this.getDeviceEntity().getSource();
        String channel = this.getDeviceEntity().getChannel();
        List<Property> properties = this.getDeviceModel().getProperties();
        for (Property property : properties) {
            PropertyElm raw = property.getRaw();
            if (ObjectUtil.isNull(raw)) {
                continue;
            }
            DataTypeElm dataType = raw.getDataType();
            if (ObjectUtil.isNull(dataType)) {
                continue;
            }
            SpecElm spec = dataType.getSpec();
            if (ObjectUtil.isNull(spec)) {
                continue;
            }
            if (oldPropertyNames.contains(property.getName())) {
                continue;
            }
            String expression = spec.getExpression();
            Object defaultValue = spec.getDefaultValue();
            if (StringUtils.isNotBlank(source)) {
                String[] split = source.split("\\.");
                if (split.length > 0 && StringUtils.isNotBlank(expression)) {
                    Integer index = null ;
                    if ("channelName".equals(expression)) {
                        if (!RegexUtil.checkValueString(channel, property.getDataType().getType())){
                            throw new BizException(String.format("属性【%s】：默认值解析数据【%s】与数据类型【%s】对应不上",
                                    property.getName(),channel,property.getDataType().getType().getName()));
                        }
                        propertyMap.put(property.getName(),channel);
                    }
                    else if (expression.contains("labelGroupLevel")) {
                        String num = expression.substring(15);
                        try {
                            index = Integer.parseInt(num) - 1;
                        } catch (Exception e) {
                        }
                    }
                    if (index != null){
                        try{
                            String checkValue = source;
                            if(index != -1) {
                                checkValue = split[index];
                            }
                            if (!RegexUtil.checkValueString(checkValue, property.getDataType().getType())){
                                throw new BizException(String.format("属性【%s】：默认值解析数据【%s】与数据类型【%s】对应不上",
                                        property.getName(),split[index],property.getDataType().getType().getName()));
                            }
                            if(index != -1){
                                propertyMap.put(property.getName(),split[index]);
                            }else {
                                propertyMap.put(property.getName(),source);
                            }
                        }catch (Exception e){
                        }
                    }
               }
            }else {
                if (!org.springframework.util.StringUtils.isEmpty(defaultValue)){
                    propertyMap.put(property.getName(),defaultValue);
                }
            }
        }
        return propertyMap;
    }

    public static Result<Device> checkInfoToModelNew(
            DeviceEntity entity,
            Map<Long,EdgeGatewayEntity> edgeGatewayEntityMap,
            Map<Long,List<ResourceRelationEntity>> resourceRelationEntityListMap, //根据设备ID
            Map<Long,ThingModelEntity> thingModelEntityMap,//根据继承modelId获取到的map
            Map<Long,List<ThingModelInheritEntity>> thingModelInheritEntityMap, //根据继承modelId获取到的map
            Map<Long,List<ThingServiceEntity>> thingServiceEntityMap,   //根据继承modelId获取到的map
            Map<Long,List<SubscriptionEntity>> subscriptionEntityMap, //根据继承deviceId,modelId获取到的map
            Map<Long,List<DeviceModelInheritEntity>> deviceModelinheritEntityListMap,  // 根据deviceId
            Map<Long,List<DeviceServiceEntity>> deviceServiceEntityMap //根据deviceId)

    ) {
        Result<Device> result = Device.checkInfoToBase(entity);
        if (!result.getSignal()) {
            return result;
        }
        Device device = result.getResult();

        Long edgeGatewayId = entity.getEdgeGatewayId();
        if(edgeGatewayId == null){
            return Result.error("所属网关不能为空");
        }
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayEntityMap.get(edgeGatewayId);
        if(edgeGatewayEntity == null){
            return Result.error("找不到所属网关");
        }
        device.edgeGatewayEntity = edgeGatewayEntity;
        List<ResourceRelationEntity> relationEntities = resourceRelationEntityListMap.get(entity.getId());

        if (Optional.ofNullable(relationEntities).isPresent() && relationEntities.size() == 1) {
            device.resourceEntity = relationEntities.get(0);
        }

        Result<DeviceModel> deviceModelResult = Device.checkModelNew(entity,
                thingModelEntityMap,
                thingModelInheritEntityMap,
                thingServiceEntityMap,
                subscriptionEntityMap,
                deviceModelinheritEntityListMap,
                deviceServiceEntityMap);
        if (!deviceModelResult.getSignal()) {
            return Result.error(deviceModelResult.getMessage());
        }
        device.deviceModel = deviceModelResult.getResult();

        device.infoDegree = InfoDegreeEnum.MODEL;

        return Result.ok(device);
    }


    private static Result<DeviceModel> checkModelNew(DeviceEntity entity,
             Map<Long,ThingModelEntity> thingModelEntityMap,//根据继承modelId获取到的map
             Map<Long,List<ThingModelInheritEntity>> thingModelInheritEntityMap, //根据继承modelId获取到的map
             Map<Long,List<ThingServiceEntity>> thingServiceEntityMap,   //根据继承modelId获取到的map
             Map<Long,List<SubscriptionEntity>> subscriptionEntityMap, //根据继承deviceId,modelId获取到的map
             Map<Long,List<DeviceModelInheritEntity>> deviceModelinheritEntityListMap,  // 根据deviceId
             Map<Long,List<DeviceServiceEntity>> deviceServiceEntityMap) //根据deviceId)
    {
        Result<DeviceModel> deviceModelResult = DeviceModel.checkInfoNew(
                entity,thingModelEntityMap,thingModelInheritEntityMap,thingServiceEntityMap,subscriptionEntityMap,deviceModelinheritEntityListMap,deviceServiceEntityMap
        );
        if(!deviceModelResult.getSignal()){
            return Result.error(deviceModelResult.getMessage());
        }
        DeviceModel deviceModel = deviceModelResult.getResult();
        return Result.ok(deviceModel);
    }


    public static Result<Device> checkInfoToBindRelationNew(
            DeviceEntity entity,
            Map<Long,List<LabelBindRelationEntity>> labelBindRelationEntityMap, //根据设备ID
            Map<Long,EdgeGatewayEntity> edgeGatewayEntityMap,
            Map<Long,List<ResourceRelationEntity>> resourceRelationEntityListMap, //根据设备ID
            Map<Long,ThingModelEntity> thingModelEntityMap,//根据继承modelId获取到的map
            Map<Long,List<ThingModelInheritEntity>> thingModelInheritEntityMap, //根据继承modelId获取到的map
            Map<Long,List<ThingServiceEntity>> thingServiceEntityMap,   //根据继承modelId获取到的map
            Map<Long,List<SubscriptionEntity>> subscriptionEntityMap, //根据继承deviceId,modelId获取到的map
            Map<Long,List<DeviceModelInheritEntity>> deviceModelinheritEntityListMap,  // 根据deviceId
            Map<Long,List<DeviceServiceEntity>> deviceServiceEntityMap, //根据deviceId)
            List<LabelEntity> labelEntityList,
            List<ChannelEntity> channelEntitieList,
            List<LabelGroupEntity> labelGroupEntityList

    ) {

        if(CollectionUtil.isEmpty(labelEntityList)){
            labelEntityList = new ArrayList<>();
        }

        if(CollectionUtil.isEmpty(channelEntitieList)){
            channelEntitieList = new ArrayList<>();
        }

        if(CollectionUtil.isEmpty(labelGroupEntityList)){
            labelGroupEntityList = new ArrayList<>();
        }

        Result<Device> result = checkInfoToModelNew(entity,
                edgeGatewayEntityMap,
                resourceRelationEntityListMap,
                thingModelEntityMap,
                thingModelInheritEntityMap,
                thingServiceEntityMap,
                subscriptionEntityMap,
                deviceModelinheritEntityListMap,
                deviceServiceEntityMap);
        if(!result.getSignal()){
            return result;
        }
        Device device = result.getResult();

        Map<String, ChannelEntity> channelEntityMap = channelEntitieList.stream().collect(Collectors.toMap(
                f -> f.getName() + ":" + f.getEdgeGatewayId(),
                Function.identity(),
                (existing, replacement) -> replacement  // 如果出现重复键，使用后来的值替换已有的值
        ));

        Map<String, LabelGroupEntity> labelGroupEntityMap = labelGroupEntityList.stream().collect(Collectors.toMap(
                f -> f.getName() + ":" + f.getChannelId(),
                Function.identity(),
                (existing, replacement) -> replacement  // 如果出现重复键，使用后来的值替换已有的值
        ));

        device.propertyLabelMap = new HashMap<>();
        device.nameMap = new HashMap<>();
        List<Property> properties = device.deviceModel.getProperties();
        //捞出绑定关系
        List<LabelBindRelationEntity> relationEntityList = labelBindRelationEntityMap.get(entity.getId());
        device.relationEntities = relationEntityList;
        List<LabelBindRelation> relationList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(relationEntityList)) {
            for(LabelBindRelationEntity relationEntity:relationEntityList){
                Result<LabelBindRelation> labelResult = LabelBindRelation.checkInfoToLabelNew(relationEntity, labelEntityList,channelEntitieList,labelGroupEntityList,edgeGatewayEntityMap);
                if(!labelResult.getSignal()){
                    continue;
                }
                relationList.add(labelResult.getResult());
            }
            Result<Map<String, LabelBindRelation>> propertyLabelMapResult = Device.checkLabelBindRelation(properties, relationList,true);
            if (!propertyLabelMapResult.getSignal()) {
                return Result.error(propertyLabelMapResult.getMessage());
            }
            device.propertyLabelMap.putAll(propertyLabelMapResult.getResult());
        }

        device.groupMap = new HashMap<>();
        List<LabelBindRelation> groupLabelRelation = null;
        if (!ObjectUtils.isEmpty(device.deviceEntity.getChannel()) && !ObjectUtils.isEmpty(device.deviceEntity.getSource())) {
//            UniqueConstraint.Unique unique = nameUniqueConstraint.buildUnique(new FieldValue(device.deviceEntity.getChannel()),new FieldValue(device.deviceEntity.getEdgeGatewayId()));
//            ChannelEntity channelEntity = commonFetcher.get(unique, ChannelEntity.class);

            ChannelEntity channelEntity = channelEntityMap.get(device.deviceEntity.getChannel() + ":" + device.deviceEntity.getEdgeGatewayId());
            if (!ObjectUtils.isEmpty(channelEntity)) {
                device.bindChannel = channelEntity;
//                Map<String,FieldValue> groupFields = new HashMap<>();
//                groupFields.put("name",new FieldValue(device.deviceEntity.getSource()));
//                groupFields.put("channel_id",new FieldValue(channelEntity.getId()));
//                UniqueConstraint.Unique groupUnique = new UniqueConstraint("name","channel_id").buildUnique(groupFields);
//                LabelGroupEntity labelGroupEntity = commonFetcher.get(groupUnique, LabelGroupEntity.class);

                LabelGroupEntity labelGroupEntity = labelGroupEntityMap.get(device.deviceEntity.getSource() + ":" + channelEntity.getId());
                if (!ObjectUtils.isEmpty(labelGroupEntity)) {
                    Map<Long, List<LabelEntity>> labelListMap = labelEntityList.stream()
                            .collect(Collectors.groupingBy(LabelEntity::getLabelGroupId));
                    device.bindGroup = labelGroupEntity;
                    List<LabelEntity> labelEntitys = labelListMap.get(labelGroupEntity.getId());
                    if (CollectionUtil.isNotEmpty(labelEntityList)) {
                        Set<String> propertyNames = new HashSet<>();
                        Set<String> labelNames = new HashSet<>();
//                        Map<String, Property> propertyMap = BeanUtilsIntensifier.collection2Map(device.getDeviceModel().getProperties(), Property::getName);
                        BeanUtilsIntensifier.getSomething2Collection(device.getDeviceModel().getProperties(),Property::getName,propertyNames);
                        BeanUtilsIntensifier.getSomething2Collection(labelEntitys,LabelEntity::getName,labelNames);
                        propertyNames.retainAll(labelNames);
                        propertyNames.removeAll(device.propertyLabelMap.keySet());
                        if (CollectionUtil.isNotEmpty(propertyNames)) {
                            String groupName = device.deviceEntity.getSource();
                            groupLabelRelation = LabelBindRelation.bindByGroup(propertyNames,device.deviceModel.getProperties(),labelEntitys,groupName,channelEntity);
                        }
                    }

                }
            }
        }
        if (CollectionUtil.isNotEmpty(groupLabelRelation)) {
            Result<Map<String, LabelBindRelation>> groupLabelRelationMap = Device.checkLabelBindRelation(properties, groupLabelRelation,false);
            if (!groupLabelRelationMap.getSignal()) {
                return Result.error(groupLabelRelationMap.getMessage());
            }
            device.propertyLabelMap.putAll(groupLabelRelationMap.getResult());
        }

        device.propertyLabelMap.values().forEach(relation -> {
            String k = relation.getChannelName() + "." + relation.getLabelGroupName() + "." + relation.getLabelName();
            if (device.nameMap.containsKey(k)) {
                String propertyNames = device.nameMap.get(k);
                propertyNames = propertyNames + "," + relation.getPropertyName();
                device.nameMap.put(k,propertyNames);
            }else {
                device.nameMap.put(k,relation.getPropertyName());
            }
            String group = relation.getChannelName() + "." + relation.getLabelGroupName();
            String relationStr = relation.getLabelName() + relation.getLabel().getDataType().getName() + relation.getLabel().getIsArray();
            if (device.groupMap.containsKey(group)) {
                device.groupMap.get(group).add(relationStr);
            }else {
                Set<String> labels = CollectionUtil.newHashSet();
                labels.add(relationStr);
                device.groupMap.put(group, labels);
            }
        });

        device.infoDegree = InfoDegreeEnum.BIND_RELATION;
        return Result.ok(device);
    }

}
