package com.nti56.nlink.product.device.server.model.datasync;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/********************************************************************************
 *
 * Title: 
 * Description: 
 * <AUTHOR>
 * create date on 2023/5/11
 * @version 1.0.0
 *
 *******************************************************************************/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "标签分组")
public class SyncLabelGroupDto {
    
    /**
     * id
     */
    private Long id;
    
    /**
     * 所属通道id
     */
    @Schema(description = "所属通道id")
    private Long channelId;
    
    /**
     * 分组名称
     */
    @Schema(description = "分组名称")
    private String name;
    
    /**
     * 标签描述
     */
    @Schema(description = "标签描述")
    private String descript;
    
}
