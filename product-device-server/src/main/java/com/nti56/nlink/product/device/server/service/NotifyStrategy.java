package com.nti56.nlink.product.device.server.service;

import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.send.NotifyDTO;
import com.nti56.nlink.product.device.server.entity.NotifyChannelEntity;
import com.nti56.nlink.product.device.server.entity.NotifyServerLogEntity;
import com.nti56.nlink.product.device.server.entity.TemplateEntity;
import com.nti56.nlink.product.device.server.enums.AuditEnum;
import com.nti56.nlink.product.device.server.model.template.dto.CreateTemplateDTO;
import com.nti56.nlink.product.device.server.model.template.dto.EditTemplateDTO;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public abstract class NotifyStrategy {



    @Autowired
    private com.nti56.nlink.product.device.server.service.ITemplateService templateService;
    @Autowired
    private Mapper mapper;


    public Result addTemplate(JSONObject channelParams , CreateTemplateDTO dto, TenantIsolation tenantIsolation) {
        TemplateEntity templateEntity = mapper.map(dto, TemplateEntity.class);
        templateEntity.setAuditStatus(AuditEnum.AUDIT_SUCCESS.getValue());
        if (templateService.save(templateEntity)){
            return Result.ok();
        }

        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }

    public Result deleteTemplate(JSONObject channelParams, TemplateEntity template){
        if (templateService.removeById(template.getId())){
            return Result.ok();
        }

        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    public Result editTemplate(JSONObject channelParams, EditTemplateDTO dto, TemplateEntity templateEntity, TenantIsolation tenantIsolation){
        if (templateService.updateById(mapper.map(dto, TemplateEntity.class))){
            return Result.ok();
        }

        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    public void setAuditStatus(JSONObject channelParams, TemplateEntity template){}

    public void retryDeleteTemplate(JSONObject channelParams, NotifyServerLogEntity notifyServerLogEntity, JSONObject requestJson, TenantIsolation tenantIsolation){}

    public abstract Result validationChannel(NotifyChannelEntity notifyChannelEntity);

    public abstract Result sendNotify(JSONObject channelParams, NotifyDTO dto, TemplateEntity templateEntity, String content, TenantIsolation tenantIsolation);

    public abstract void retrySendFailNotify(JSONObject channelParams, NotifyServerLogEntity notifyServerLogEntity, TemplateEntity templateEntity, JSONObject requestJson, TenantIsolation tenantIsolation);
}