package com.nti56.nlink.product.device.server.model.connectorItem.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 类说明: <br/>
 * <AUTHOR> <br/>
 * @version 1.0
 * @since JDK 1.8
 */

@Data
@Schema(description = "创建连接器")
public class CreateConnectorItemDTO {
    
    /**
     * 连接器名称
     */
    @Schema(description = "连接器详情项名称")
    @NotBlank(message = "连接器详情项名称不能为空")
    @Length(max = 256, message = "连接器详情项名称不能超过255个字符")
    private String name;
    
    /**
     * topic
     */
    @Schema(description = "topic")
    @NotBlank(message = "topic不能为空")
    @Length(max = 512, message = "topic不能超过512个字符")
    private String topic;
    
    /**
     * 连接器描述
     */
    @Schema(description = "连接器描述")
    private String descript;
    
    /**
     * 处理代码
     */
    @Schema(description = "处理代码")
    private String processCode;
    
    /**
     * 连接器id
     */
    @Schema(description = "连接器id")
    @NotNull(message = "连接器id不能为空")
    private Long connectorId;
}
