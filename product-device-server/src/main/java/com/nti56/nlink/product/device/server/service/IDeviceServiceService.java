package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.DeviceServiceEntity;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:11:58
 * @since JDK 1.8
 */
public interface IDeviceServiceService extends IBaseService<DeviceServiceEntity> {

    Result<DeviceServiceEntity> save(TenantIsolation tenantIsolation, DeviceServiceEntity entity);

    Result<Page<DeviceServiceEntity>> getPage(@Nullable DeviceServiceEntity entity, Page<DeviceServiceEntity> page);

    Result<List<DeviceServiceEntity>> list(DeviceServiceEntity entity);

    Result update(DeviceServiceEntity entity, TenantIsolation tenantIsolation);

    Result deleteById(Long id, TenantIsolation tenantIsolation);

    Result<DeviceServiceEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation);

    Result<List<DeviceServiceEntity>> listByDeviceId(TenantIsolation tenantIsolation, Long deviceId);

    void deleteByDeviceId(TenantIsolation tenantIsolation, Long deviceId);

    void batchDeleteByDeviceIds(TenantIsolation tenantIsolation, List<Long> ids);

    Result<Void> deleteBatchByIds(List<Long> ids, TenantIsolation tenantIsolation);
}
