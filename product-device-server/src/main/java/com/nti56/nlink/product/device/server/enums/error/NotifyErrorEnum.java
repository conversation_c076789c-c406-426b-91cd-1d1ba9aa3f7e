package com.nti56.nlink.product.device.server.enums.error;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/15 11:12<br/>
 * @since JDK 1.8
 */
public enum NotifyErrorEnum {

    CHANNEL_UNIQUE_NAME(700,"已经存在该名称的渠道","已经存在该名称的渠道"),
    CHANNEL_USED(701,"该渠道已经被使用","该渠道已经被使用，无法删除"),

    TEMPLATE_UNIQUE_NAME(710,"已经存在该名称的模板","已经存在该名称的模板"),
    AUDITING_TEMPLATE_CANNOT_OPERATION(711,"审核中的模板无法操作","审核中的模板无法操作"),
    NONE_RECEIVER_INFO_ERROR(712,"没有接收人信息","没有接收人信息"),
    RECEIVER_FORMAT_ERROR(713,"接收人格式异常","接收人格式异常"),

    SEND_EMAIL_FAIL(720,"发送邮件失败","发送邮件失败"),
    CONNECTION_EMAIL_FAIL(721,"连接email服务器失败","连接email服务器失败"),

    PROVIDER_CREATE_CLIENT_FAIL(770,"创建服务商客户端失败","创建服务商客户端失败"),

    PROVIDER_ADD_TEMPLATE_FAIL(771,"服务商新增模板失败","服务商新增模板失败"),
    PROVIDER_EDIT_TEMPLATE_FAIL(772,"服务商修改模板失败","服务商修改模板失败"),
    PROVIDER_REMOVE_TEMPLATE_FAIL(773,"服务商删除模板失败","服务商删除模板失败"),
    PROVIDER_SEND_SMS_FAIL(774,"服务商短信发送失败","服务商短信发送失败"),
    SMS_LENGTH_OVERSIZE(775,"短信长度超出限制","短信长度超出限制"),
    PROVIDER_QUERY_SIGN_FAIL(776,"服务商查询签名失败","服务商查询签名失败"),
    PROVIDER_SIGN_AUDIT_NOT_PASS(777,"服务商签名未通过","服务商签名未通过"),
    DING_DING_CREATE_CLIENT_FAIL(780,"创建钉钉客户端失败","创建钉钉客户端失败"),
    DING_DING_SEND_NOTIFY_FAIL(781,"钉钉发送通知失败","钉钉发送通知失败"),



    PROVIDER_ERROR(799,"服务商异常","异常信息直接使用服务商")
    ,
    ;

    private Integer code;
    private String message;
    private String descript;

    private NotifyErrorEnum(Integer code, String message, String descript) {
        this.code = code;
        this.message = message;
        this.descript = descript;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public String getDescript() {
        return this.descript;
    }
}
