package com.nti56.nlink.product.device.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.entity.PackageVersionEntity;
import com.nti56.nlink.product.device.server.mapper.PackageVersionMapper;
import com.nti56.nlink.product.device.server.model.oss.PackageBo;
import com.nti56.nlink.product.device.server.model.oss.PackageVersionDTO;
import com.nti56.nlink.product.device.server.service.IPackageVersionService;
import lombok.extern.slf4j.Slf4j;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/5/20 13:51<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class PackageVersionServiceImpl extends BaseServiceImpl<PackageVersionMapper, PackageVersionEntity> implements IPackageVersionService {
    @Autowired
    PackageVersionMapper packageVersionMapper;
    @Autowired
    Mapper dozerMapper;


    @Override
    @Transactional
    public Result<PackageVersionDTO> save(PackageVersionDTO dto) {
        PackageVersionEntity entity = dozerMapper.map(dto, PackageVersionEntity.class);
        packageVersionMapper.insert(entity);
        dto = dozerMapper.map(entity, PackageVersionDTO.class);
        return Result.ok(dto);
    }

    @Override
    public Result<List<PackageVersionDTO>> download(TenantIsolation tenantIsolation, PackageBo packageBo) {
        PackageVersionEntity packageVersionEntity = packageVersionMapper.selectOne(new LambdaQueryWrapper<PackageVersionEntity>()
                .eq(PackageVersionEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(PackageVersionEntity::getPackageVersion, packageBo.getVersion())
                .eq(PackageVersionEntity::getEngineeringId, packageBo.getEngineeringId())
        );
        if (!Optional.ofNullable(packageVersionEntity).isPresent()) {
            return Result.error("该工程未发布，请先发布OT工程");
        }
        List<PackageVersionDTO> list = new ArrayList<>();
        list.add(BeanUtilsIntensifier.copyBean(packageVersionEntity, PackageVersionDTO.class));
        return Result.ok(list);
    }

    @Override
    public Result<String> delete(TenantIsolation tenantIsolation, Long id, boolean check) {
        if (check) {
            PackageVersionEntity entity = packageVersionMapper.selectById(id);
            if (Objects.isNull(entity)) {
                return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
            }
            if (Objects.equals(entity.getTenantId(), tenantIsolation.getTenantId())) {
                log.warn("数据匹配失败");
                return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
            }
            return Result.ok(entity.getOssFileId().toString());
        }
        packageVersionMapper.deleteById(id);
        return Result.ok();
    }
}
