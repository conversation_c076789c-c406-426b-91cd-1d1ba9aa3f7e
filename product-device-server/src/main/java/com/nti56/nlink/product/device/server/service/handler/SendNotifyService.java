package com.nti56.nlink.product.device.server.service.handler;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.send.BatchSendNotifyDTO;
import com.nti56.nlink.product.device.client.model.dto.send.SendNotifyDTO;
import com.nti56.nlink.product.device.server.entity.NotifyServerLogEntity;
import com.nti56.nlink.product.device.server.enums.NotifyServerLogEnum;
import com.nti56.nlink.product.device.server.service.INotifyServerLogService;
import com.nti56.nlink.product.device.server.service.ISendNotifyService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import java.util.Objects;
import java.util.Set;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/9 10:31<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class SendNotifyService {

    @Autowired
    private ISendNotifyService sendNotifyService;

    @Autowired
    private INotifyServerLogService notifyServerLogService;

    @Operation(summary = "批量发送通知" )
    public R batchSend(@Validated @RequestBody BatchSendNotifyDTO dto){
        TenantIsolation tenantIsolation = dto.getTenantIsolation();
        JSONObject requestJson = new JSONObject();
        requestJson.put("sendNotifyDTO",dto);
        String requestParams = requestJson.toJSONString();
        log.info("批量发送通知开始，请求内容："+dto.toString());
        Set<ConstraintViolation<BatchSendNotifyDTO>> validate = Validation.buildDefaultValidatorFactory().getValidator().validate(dto);
        if (validate != null && validate.size() > 0 ){
            log.error("请求参数异常，requestParams："+requestParams);
            notifyServerLogService.createLog(NotifyServerLogEnum.SEND_PARAMS_ERROR,requestParams,tenantIsolation);
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        return R.result(sendNotifyService.batchSend(dto.getNotifyList(),tenantIsolation,requestParams));
    }


    @Operation(summary = "发送通知" )
    public R send(@Validated @RequestBody SendNotifyDTO dto){

        TenantIsolation tenantIsolation = dto.getTenantIsolation();
        log.info("发送通知开始，请求内容："+dto.toString());
        Set<ConstraintViolation<SendNotifyDTO>> validate = Validation.buildDefaultValidatorFactory().getValidator().validate(dto);
        JSONObject requestJson = new JSONObject();
        requestJson.put("sendNotifyDTO",dto);
        String requestParams = requestJson.toJSONString();
        if (validate != null && validate.size() > 0 ){
            log.error("请求参数异常，requestParams："+requestParams);
            notifyServerLogService.createLog(NotifyServerLogEnum.SEND_PARAMS_ERROR,requestParams ,tenantIsolation);
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        return R.result(sendNotifyService.send(dto.getNotify(),tenantIsolation,requestParams));
    }

    public R deleteTask(Long subscriptionId) {
        if(Objects.isNull(subscriptionId)){
            return R.error("请求参数为空");
        }
        QueryWrapper<NotifyServerLogEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("subscription_id",subscriptionId);
        return R.ok(Result.ok(notifyServerLogService.remove(wrapper)));
    }


    @Operation(summary = "发送验证码" )
    public R sendVerificationCode(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable("type") int type,@PathVariable("phone") String phone){
        return R.result(sendNotifyService.sendVerificationCode(type,phone,tenantIsolation));
    }
}
