package com.nti56.nlink.product.device.server.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@Schema(description = "批量标签创建设备参数")
public class BatchLabelCreateDeviceParam {

    @Schema(description = "设备名称")
    @NotBlank(message = "设备名称不能为空")
    @Length(max = 512,message = "设备名称不能超过512个字符")
    private String name;

    @Schema(description = "继承物模型id列表")
    @NotNull(message = "至少选择一个模型继承")
    @Size(min = 1,message = "至少选择一个模型继承")
    private List<Long> inheritThingModelIds;

    @Schema(description = "标记id")
    private List<Long> tagIds;
    
    @Schema(description = "设备描述")
    private String descript;

    @Schema(description = "所属网关id")
    @NotNull(message = "网关标识不能为空")
    private Long edgeGatewayId;

    @Schema(description = "绑定标签id列表")
    @NotNull(message = "至少选择一个标签")
    @Size(min = 1,message = "至少选择一个标签")
    private List<Long> labelIds;

}
