package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.UpgradePackageEntity;
import com.nti56.nlink.product.device.server.model.upgrade.dto.QueryUpgradePackageDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-18 17:45:46
 * @since JDK 1.8
 */
@Mapper
public interface UpgradePackageMapper extends CommonMapper<UpgradePackageEntity> {
    
    Page<UpgradePackageEntity> pageUpgradePackage(IPage<UpgradePackageEntity> upgradePackagePage, @Param("queryUpgradePackageDTO") QueryUpgradePackageDTO queryUpgradePackageDTO);
    
    @Update("UPDATE upgrade_package SET is_expect_version = 0 where DELETED = 0")
    Integer updateExpectVersion();
}
