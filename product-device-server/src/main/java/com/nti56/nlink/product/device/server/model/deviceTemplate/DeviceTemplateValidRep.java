package com.nti56.nlink.product.device.server.model.deviceTemplate;

import com.nti56.nlink.product.device.client.model.dto.ChannelParamDTO;
import com.nti56.nlink.product.device.server.model.DeviceRequestBo;
import com.nti56.nlink.product.device.server.model.channel.dto.CreateChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.EditChannelDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName DeviceTemplateValidRep
 * @date 2023-12-27 10:33:58
 * @Version 1.0
 */
@Data
@Builder
@Schema(description = "设备模板设备云管反馈对象")
@AllArgsConstructor
@NoArgsConstructor
public class DeviceTemplateValidRep {

    @Schema(description = "模板id")
    private Long templateId;

    @Schema(description = "模板名称")
    private String templateName;

    @Schema(description = "模板描述")
    private String templateDescript;

    /**
     * 所属驱动类型，1-Snap7，2-Modbus，3-OPC UA，4-ZigBee，5-BLE
     */
    @Schema(description = "初始协议参数信息")
    private Integer driver;

    @Schema(description = "自定义协议名")
    private String customDriverName;

    @Schema(description = "初始协议参数信息")
    private String[] initChannelParamInfo;


}
