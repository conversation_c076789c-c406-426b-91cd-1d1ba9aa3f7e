package com.nti56.nlink.product.device.server.model.label.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/7/12 17:28<br/>
 * @since JDK 1.8
 */
@Data
public class PageLabelVO {

    /**
     * id
     */
    private Long id;
    /**
     * 标签分组id
     */
    @Schema(description = "标签分组id")
    private Long labelGroupId;
    /**
     * 标签名称
     */
    private String name;

    @Schema(description = "别名")
    private String alias;

    /**
     * 标签描述
     */
    @Schema(description = "标签描述")
    private String descript;
    /**
     * 地址，如DB50.DBB1
     */
    @Schema(description = "地址，如DB50.DBB1")
    private String address;
    /**
     * 长度
     */
    @Schema(description = "长度")
    private Integer length;

    /**
     * 数据类型，bool/byte/short/int/float/string
     */
    @Schema(description = "数据类型，bool/byte/short/int/float/string")
    private String dataType;
    /**
     * 是否数组
     */
    @Schema(description = "是否数组")
    private Boolean isArray;
    /**
     * type是string类型时，表示string元素的byte长度，其他type类型放空
     */
    @Schema(description = "type是string类型时，表示string元素的byte长度，其他type类型放空")
    private Integer stringBytes;

    @Schema(description = "是否只读")
    private Boolean readOnly;

    @Schema(description = "时间间隔，单位毫秒，当策略类型为1时，不为空")
    private Integer intervalMs;

    /**
     * 标签标识
     */
    @Schema(description = "标签标识")
    private String tag;

    @Schema(description = "已绑定")
    private Integer isBind;

}
