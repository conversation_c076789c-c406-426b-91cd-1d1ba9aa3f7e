package com.nti56.nlink.product.device.server.enums;

import lombok.Getter;

/**
 * 类说明: 状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:33:25
 * @since JDK 1.8
 */
public enum StatusEnum {
    ENABLED(1, "enabled", "启动"),
    DISABLED(2, "disabled", "关闭")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    StatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static StatusEnum typeOfValue(Integer value){
        StatusEnum[] values = StatusEnum.values();
        for (StatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static StatusEnum typeOfName(String name){
        StatusEnum[] values = StatusEnum.values();
        for (StatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static StatusEnum typeOfNameDesc(String nameDesc){
        StatusEnum[] values = StatusEnum.values();
        for (StatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
