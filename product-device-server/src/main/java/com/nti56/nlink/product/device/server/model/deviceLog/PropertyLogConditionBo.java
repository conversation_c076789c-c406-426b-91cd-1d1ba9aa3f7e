package com.nti56.nlink.product.device.server.model.deviceLog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName PropertyLogBo
 * @date 2022/8/8 17:07
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "属性日志查询条件")
public class PropertyLogConditionBo {

    String dataType;

    List<Long> deviceIds;

    List<String> properties;

    String value;

    Long start;

    Long stop;

    Integer numb;

}
