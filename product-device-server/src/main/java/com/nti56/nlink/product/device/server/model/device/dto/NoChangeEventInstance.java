package com.nti56.nlink.product.device.server.model.device.dto;

import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;


/**
 * 类说明: 未改变事件触发实例
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-24 11:26:26
 * @since JDK 1.8
 */

@Data
public class NoChangeEventInstance implements Serializable {

    private String property;
    private UpData upData;
    /**
     * 调用物服务的ID
     */
    private Long thingServiceId;

}
