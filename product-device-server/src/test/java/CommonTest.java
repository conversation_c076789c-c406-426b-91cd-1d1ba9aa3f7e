import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.influxdb.annotations.Column;
import com.influxdb.annotations.Measurement;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.WriteApiBlocking;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.reactive.InfluxDBClientReactive;
import com.influxdb.client.reactive.InfluxDBClientReactiveFactory;
import com.influxdb.client.reactive.WriteReactiveApi;
import com.influxdb.client.write.Point;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.util.EscapeUtil;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.Snap7DataTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.YesNoEnum;
import com.nti56.nlink.product.device.server.domain.thing.topic.OtTopic;
import com.nti56.nlink.product.device.server.model.edgegateway.Traffic5gInfo;
import com.nti56.nlink.product.device.server.scriptApi.EngineeringSpi;
import com.nti56.nlink.product.device.server.service.impl.EdgeGatewayServiceImpl;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import com.nti56.nlink.product.device.server.verticle.MqttSenderVerticle;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

import org.apache.commons.lang.StringEscapeUtils;
import org.junit.Test;
import org.reactivestreams.Publisher;

import io.vertx.core.Context;
//import io.reactivex.Flowable;
//import io.reactivex.disposables.Disposable;
import io.vertx.core.DeploymentOptions;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import io.vertx.core.VertxOptions;
import io.vertx.core.impl.VertxInternal;
import io.vertx.core.impl.future.PromiseInternal;
import lombok.extern.slf4j.Slf4j;


/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-04 11:54:45
 * @since JDK 1.8
 */
@Slf4j
public class CommonTest {

    @Test
    public void testeTE(){
        String addressStr = "['1', '2']";
        // String addressStr = "{'a': 1}";
        List<String> address = JSON.parseObject(addressStr, new TypeReference<List<String>>(){});
        log.info("", address);
    }
    @Test
    public void testTETE(){
        ConcurrentHashMap<Long,String> a = new ConcurrentHashMap<>();
        String s = a.get(null);
        log.info(s);
    }
    private void startTestScript(Map<String, Integer> v) throws ScriptException, NoSuchMethodException{
        Vertx vertx = Vertx.vertx();
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("nashorn");
        {
            String userCode = 
                "var n = v.a;\n" + 
                "n = n + 1;\n" + 
                "v.a = n;\n" + 
                "result = n;\n"
                ;
            String code = "function add(v){ \n" + 
                "var result = null; \n" +
                userCode + "\n" + 
                "  return result;\n" + 
                "}";
            engine.eval(code);
        }
        {
            String userCode = 
                "var n = v.a;\n" + 
                "n = n - 1;\n" + 
                "v.a = n;\n" + 
                "result = n;\n"
                ;
            String code = "function minus(v){ \n" + 
                "var result = null; \n" +
                userCode + "\n" + 
                "  return result;\n" + 
                "}";
            engine.eval(code);
        }
        Invocable invocable = (Invocable) engine;
        Context context = vertx.getOrCreateContext();
        for(int i=0;i<100;i++){
            int t = i;
            new Thread(() -> {
                try {
                    Thread.sleep(RandomUtil.randomInt(0, 200));
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                context.runOnContext((c) -> {
                    try {
                        Object r = invocable.invokeFunction("add",v);
                        log.info("vvv add: {}, {}", t, r);
                    } catch (Exception e) {
                        e.printStackTrace();
                    } 
                });
            }).start();
        }
        for(int i=0;i<100;i++){
            int t = i;
            new Thread(() -> {
                try {
                    Thread.sleep(RandomUtil.randomInt(0, 200));
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                context.runOnContext((c) -> {
                    try {
                        Object r = invocable.invokeFunction("minus",v);
                        log.info("vvv minus: {}, {}", t, r);
                    } catch (Exception e) {
                        e.printStackTrace();
                    } 
                });
            }).start();
        }
        
    }
  
    @Test
    public void testScript() throws ScriptException, NoSuchMethodException{
        
        Map<String, Integer> v = new HashMap<>();
        v.put("a", 0);
        new Thread(() -> {
            try {
                startTestScript(v);
            } catch (NoSuchMethodException | ScriptException e) {
                e.printStackTrace();
            }
        }).start();

        log.info("vvv1: {}", v);

        try {
            Thread.sleep(20_000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        log.info("vvv2: {}", v);
        log.info("vvv3: {}", v);

    }
    @Test
    public void ttt(){
        String deviceBaseModel = "{\"name\":\"设备基础模型\",\"descript\":\"设备基础模型\",\"properties\":[{\"name\":\"id\",\"descript\":\"设备资源ID\",\"dataType\":{\"type\":\"Long\",\"isArray\":false},\"reportType\":0,\"bindLabel\":false,\"readOnly\":true,\"persist\":false,\"required\":false},{\"name\":\"name\",\"descript\":\"设备名称\",\"dataType\":{\"type\":\"String\",\"isArray\":false},\"reportType\":0,\"bindLabel\":false,\"readOnly\":false,\"persist\":false,\"required\":false},{\"name\":\"description\",\"descript\":\"设备描述\",\"dataType\":{\"type\":\"String\",\"isArray\":false},\"reportType\":0,\"bindLabel\":false,\"readOnly\":false,\"persist\":false,\"required\":false},{\"name\":\"status\",\"descript\":\"设备状态\",\"dataType\":{\"type\":\"CHAR\",\"isArray\":false},\"reportType\":0,\"bindLabel\":false,\"readOnly\":false,\"persist\":false,\"required\":false}],\"events\":[],\"services\":[],\"subscriptions\":[],\"modelType\":5}"
                ;
        ModelDpo baseFaultModel = JSONUtil.toBean(deviceBaseModel,ModelDpo.class);
        System.out.println(baseFaultModel);
    }

    @Test
    public void test5G(){
        LocalDateTime time = LocalDateTime.now();
        String iccid = "8986112223304704261";
        // String iccid = "8986112223304704555";
        // String iccid = "88888888";
        String traffic5gAppKey = "**********";
        String traffic5gAppScrect = "96bd40794";
        Result<Traffic5gInfo> result = EdgeGatewayServiceImpl.getTraffic5gInfo(time, iccid, traffic5gAppKey, traffic5gAppScrect);
        Boolean signal = result.getSignal();

        log.info("aa: {}", signal);

    }
    @Test
    public void testReg(){
        Matcher m = RegexUtil.snap7AddressPattern.matcher("DB6.DBD1234567890.1");
        boolean matches = m.matches();
        if(matches){
            String dataTypeStr1 = m.group(0);
            String dataTypeStr2 = m.group(1);
            String dataTypeStr3 = m.group(2);
            String dataTypeStr4 = m.group(3);
            String dataTypeStr5 = m.group(4);
            String dataTypeStr6 = m.group(5);
            String dataTypeStr7 = m.group(6);
            log.debug("");
        }
    }

    @Test
    public void testByte(){
        Byte a = 0x7f;
        Integer b = 0x12;
        char c = 'A';
        String d = "Abc";
        char[] e = new char[1];
        d.getChars(0, 1, e, 0);
        byte f = 65;


        log.debug("a: {}", a);
        log.debug("b: {}", b);
        log.debug("c: {}", c);
        log.debug("a > b: {}", a > b);
        log.debug("a > c: {}", a > c);
        log.debug("c == e[0]: {}", c == e[0]);
        log.debug("c == f: {}", c == f);
        log.debug("g: {}", (int)c);
        log.debug("h: {}", Byte.valueOf("129"));
    }
    @Test
    public void testBcd(){
        if(999_9999 < Integer.MAX_VALUE){
            log.debug("bcd is Integer");
        }else{
            log.debug("bcd is Long");
        }
    }
    @Test
    public void testFieldValue(){
        Integer i = null;
        FieldValue fv = new FieldValue(i);
        log.debug("fv:{}", fv);
    }
    @Test
    public void testAddress(){
        Result<Snap7DataTypeEnum> r = RegexUtil.checkSnap7Address("DB50.DBB100");
        log.debug("{}", r.getSignal());
    }
    @Test
    public void testSql() throws IOException{
        String fileName = "F:/tmp/test.json";
        Path path = Paths.get(fileName);
        byte[] bytes = Files.readAllBytes(path);
        List<String> allLines = Files.readAllLines(path, StandardCharsets.UTF_8);
        String s = allLines.get(0);
        System.out.println(s);

        // String escape = EscapeUtil.escape(s);
        String escape = EscapeUtil.escapeSqlInsert(s);
        
        System.out.println(escape);
    }
        

    @Test
    public void testTopic(){
        String topic = "gw/response/2/1526809581594791938/1354449480351744//writeInt/7";
        OtTopic.TopicInfo topicInfo = OtTopic.parseTopic(topic);
        System.out.println(topicInfo);
    }
    @Test
    public void testList(){
        List<String> s = new ArrayList<>();
        s.addAll(null);
        System.out.println(s);
    }
    @Test
    public void testAddAllNull(){
        List<String> s = new ArrayList<>();
        s.addAll(null);

        System.out.println(s);
    }
    @Test
    public void testShort(){
        Short s = Short.valueOf("s");

        System.out.println(s);
    }
    @Test
    public void testParse(){
        {
            Boolean b = RegexUtil.checkPort("102");
            System.out.println(b);
        }

        Boolean b = RegexUtil.checkIpv4(null);
        System.out.println(b);

        int parseInt = Integer.parseInt("s");
        System.out.println(parseInt);
    }

    @Measurement(name = "temperature")
    private static class Temperature {

        @Column(tag = true)
        String location;

        @Column(name = "int[0].headTemperature")
        Double value;

        @Column(timestamp = true)
        Instant time;
    }
    
    private static Integer getInt() {
        Random r = new Random();
        return -20 + 70 * r.nextInt();
    }
    private static Double getDouble() {
        Random r = new Random();
        return -20 + 70 * r.nextDouble();
    }
    private static String getLocation() {
        return "Prague1";
    }
    
    public static final String COMMON_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final DateTimeFormatter commonFormat = DateTimeFormatter.ofPattern(COMMON_FORMAT_PATTERN);

    private Point createPoint(String deviceId, String time, String pairId, String type, String property){
        Map<String, Object> fields = new HashMap<>();
        Long timestamp = LocalDateTime.parse(time, commonFormat).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        fields.put("Long.value", timestamp);

        Point point = Point.measurement("1527470820947841026")
            .addTag("type", type)
            .addTag("eventName", "changeReport")
            .addTag("pairId", pairId)
            .addTag("deviceId", deviceId)
            .addTag("property", property)
            .addTag("propertyAlias", property)
            .addTag("dataType", "Long")
            .addTag("isArray", "no")
            .addTag("length", "1")
            .addFields(fields)
            .time(timestamp, WritePrecision.MS);
        return point;
    }
    @Test
    public void testInfluxDb() throws InterruptedException{
        String influxUrl = "http://10.1.21.201:20257";
        String token = "faKUqgrhXzh6p_dGZQt9sEEsGheEnPBp_vR506vs7othSI4-lvMyX6AdqrMdeZRqOlWEVg8uHA4zTeT-5-AKvw==";
        String influxOrg = "nti";
        String bucket = "nlink-dev";

        InfluxDBClient influxDBClient = InfluxDBClientFactory.create(
            influxUrl, token.toCharArray(), influxOrg, bucket
        );
        
        WriteApiBlocking writeApi = influxDBClient.getWriteApiBlocking();
        List<Point> pointList = new ArrayList<>();
        
        String deviceId = "1390104320909316";
        pointList.add(createPoint(deviceId, "2023-12-05 18:00:00", "1", "offline", "begin"));
        pointList.add(createPoint(deviceId, "2023-12-06 09:00:00", "1", "offline", "end"));
        pointList.add(createPoint(deviceId, "2023-12-06 09:00:00", "2", "online", "begin"));
        pointList.add(createPoint(deviceId, "2023-12-06 09:00:00", "3", "run", "begin"));
        pointList.add(createPoint(deviceId, "2023-12-06 10:05:00", "3", "run", "end"));
        pointList.add(createPoint(deviceId, "2023-12-06 10:05:00", "4", "alarm", "begin"));
        pointList.add(createPoint(deviceId, "2023-12-06 10:15:00", "4", "alarm", "end"));
        pointList.add(createPoint(deviceId, "2023-12-06 10:15:00", "5", "run", "begin"));
        pointList.add(createPoint(deviceId, "2023-12-06 12:00:00", "5", "run", "end"));
        pointList.add(createPoint(deviceId, "2023-12-06 12:00:00", "6", "alarm", "begin"));
        pointList.add(createPoint(deviceId, "2023-12-06 12:30:00", "6", "alarm", "end"));
        pointList.add(createPoint(deviceId, "2023-12-06 12:30:00", "7", "run", "begin"));
        pointList.add(createPoint(deviceId, "2023-12-06 18:00:00", "7", "run", "end"));
        pointList.add(createPoint(deviceId, "2023-12-06 18:00:00", "2", "online", "end"));
        pointList.add(createPoint(deviceId, "2023-12-06 18:00:00", "8", "offline", "begin"));

        writeApi.writePoints(pointList);

        Thread.sleep(3_000);
        influxDBClient.close();

//        Flowable<Temperature> measurements = Flowable.interval(100, TimeUnit.MILLISECONDS)
//                .map(time -> {
//                    Temperature temperature = new Temperature();
//                    temperature.location = "Prague1";
//                    temperature.value = getDouble();
//                    temperature.time = now;
//                    return temperature;
//                });
//        Publisher<WriteReactiveApi.Success> publisher = writeApi.writeMeasurements(WritePrecision.NS, measurements);
//
//        Flowable<Temperature> measurements2 = Flowable.interval(100, TimeUnit.MILLISECONDS)
//                .map(time -> {
//                    Temperature temperature = new Temperature();
//                    temperature.location = "Prague1";
//                    temperature.value = getDouble();
//                    temperature.time = now;
//                    return temperature;
//                });
//        Publisher<WriteReactiveApi.Success> publisher2 = writeApi.writeMeasurements(WritePrecision.NS, measurements2);
//
//        //
//        // Subscribe to Publisher
//        //
//        Disposable subscriber = Flowable.fromPublisher(publisher)
//                .subscribe(success -> System.out.println("Successfully written temperature"));
//
//        Disposable subscriber2 = Flowable.fromPublisher(publisher2)
//                .subscribe(success -> System.out.println("Successfully written temperature"));
//
//
//        subscriber.dispose();
//        subscriber2.dispose();

    }

    private Point createPoint2(String deviceId, String time, String property, Boolean value){
        Map<String, Object> fields = new HashMap<>();
        Long timestamp = LocalDateTime.parse(time, commonFormat).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        fields.put("Boolean.value", value);

        Point point = Point.measurement("1527470820947841026")
            .addTag("eventName", "changeReport")
            .addTag("deviceId", deviceId)
            .addTag("property", property)
            .addTag("propertyAlias", property)
            .addTag("dataType", "Boolean")
            .addTag("isArray", "no")
            .addTag("length", "1")
            .addFields(fields)
            .time(timestamp, WritePrecision.MS);
        return point;
    }
    @Test
    public void testInfluxDb2() throws InterruptedException{
        String influxUrl = "http://10.1.21.201:20257";
        String token = "faKUqgrhXzh6p_dGZQt9sEEsGheEnPBp_vR506vs7othSI4-lvMyX6AdqrMdeZRqOlWEVg8uHA4zTeT-5-AKvw==";
        String influxOrg = "nti";
        String bucket = "nlink-dev";

        InfluxDBClient influxDBClient = InfluxDBClientFactory.create(
            influxUrl, token.toCharArray(), influxOrg, bucket
        );
        
        WriteApiBlocking writeApi = influxDBClient.getWriteApiBlocking();
        List<Point> pointList = new ArrayList<>();
        
        // String deviceId = "1390104320909317";
        // pointList.add(createPoint2(deviceId, "2023-12-05 18:00:00", "online", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 09:00:00", "online", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 09:00:00", "run", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 10:05:00", "run", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 10:05:00", "alarm", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 10:15:00", "alarm", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 10:15:00", "run", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 12:00:00", "run", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 12:00:00", "alarm", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 12:30:00", "alarm", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 12:30:00", "run", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 18:00:00", "run", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 18:00:00", "online", false));
        String deviceId = "1390104320909317";
        pointList.add(createPoint2(deviceId, "2023-12-06 09:30:00", "run", true));

        // String deviceId = "1390104320909318";
        // pointList.add(createPoint2(deviceId, "2023-12-05 18:00:00", "online", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 09:00:00", "online", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 09:00:00", "run", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 11:05:00", "run", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 11:05:00", "alarm", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 11:45:00", "alarm", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 11:45:00", "run", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 12:00:00", "run", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 13:00:00", "alarm", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 13:30:00", "alarm", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 13:30:00", "run", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 14:20:00", "run", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 14:20:00", "alarm", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 15:00:00", "alarm", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 15:00:00", "run", true));
        // pointList.add(createPoint2(deviceId, "2023-12-06 18:00:00", "run", false));
        // pointList.add(createPoint2(deviceId, "2023-12-06 18:00:00", "online", false));

        writeApi.writePoints(pointList);

        Thread.sleep(3_000);
        influxDBClient.close();

    }

    @Test
    public void testPromise() throws InterruptedException{
        log.debug("test begin");
        {
            Promise<Void> promise = Promise.promise();
            promise.complete();
            promise.future().onComplete(r -> {
                log.debug("onComplete");
            });
        }

        {
            VertxOptions options = new VertxOptions();
            options.setBlockedThreadCheckInterval(1000*60*60);
            Vertx vertx = Vertx.vertx(options);
            VertxInternal vertxInternal = (VertxInternal)vertx;
            PromiseInternal<Object> promise = vertxInternal.promise();
            promise.complete();
            promise.future().onComplete(r -> {
                log.debug("onComplete vertx");
            });
        }
        
        Thread.sleep(60_000 * 10 * 10);
    }
    
    @Test
    public void testSender() throws InterruptedException{
        VertxOptions options = new VertxOptions();
        options.setBlockedThreadCheckInterval(1000*60*60);
        Vertx vertx = Vertx.vertx(options);

        //部署1个
        // String host = "***************";
        // Integer port = 11884;
        // MqttSender sender = new MqttSender(host, port, vertx);
        // MqttSender sender2 = new MqttSender(host, port, vertx);

        //部署n个，均匀分配到eventLoop线程上
        DeploymentOptions opts = new DeploymentOptions()
            .setInstances(2);
        vertx.deployVerticle(MqttSenderVerticle.class.getName(), opts);

        Thread.sleep(60_000 * 10 * 10);
    }

    @Test
    public void testNeo4j(){

        /*Driver driver = GraphDatabase.driver("bolt://localhost:7687", AuthTokens.basic("neo4j","123456"));
        try(Session session = driver.session()){

            String greeting = session.writeTransaction(tx -> {
                org.neo4j.driver.Result result = tx.run(
                        "CREATE (a:Greeting) " + "SET a.message = $message "
                                + "RETURN a.message + ', from node ' + id(a)",
                        parameters("message", "hello neo4j"));
                return result.single().get(0).asString();
            });
            System.out.println(greeting);
        }*/

    }


}
