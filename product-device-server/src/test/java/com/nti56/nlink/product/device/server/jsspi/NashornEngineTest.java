package com.nti56.nlink.product.device.server.jsspi;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.model.DeviceDto;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import jdk.nashorn.api.scripting.ScriptObjectMirror;
import org.junit.Test;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;

// import com.nti56.nlink.product.device.server.scriptApi.Thing;

/**
 * <AUTHOR>
 * @ClassName NashornEngineTest
 * @date 2022/3/29 16:21
 * @Version 1.0
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = ProductDeviceServerApplication.class)
public class NashornEngineTest {

//    @Autowired
    IDeviceService deviceSPI;

//    @Autowired
    // Thing thing;

    //用户
    @Test
    public void jsEnginePropertyTest(){
        ScriptEngine engine = new ScriptEngineManager().getEngineByName("nashorn");
        DeviceEntity build = DeviceEntity.builder().id(1350488767504384L).build();
        Result<DeviceDto> deviceResult = deviceSPI.getDevice(build);
        System.out.println("Java获取设备名：" + deviceResult.getResult().getName());
        Map<String,Object> a = new HashMap<>();
        a.put("a","1350488767504384");
        a.put("b",true);
        a.put("c",100);
        try {
            String testjs = "function start(deviceSPI,input) {\n" +
                    "    var deviceResult = deviceSPI.getDeviceById(input.a);\n" +
                    "    print('js获取设备名：' + deviceResult.result.name);\n" +
                    "    print('c：' + input.b ? input.c : 0);\n" +
                    "    return deviceResult;\n" +
                    "}";
            engine.eval(testjs);
            Invocable invocable = (Invocable) engine;
            Result<DeviceEntity> result = (Result<DeviceEntity>)invocable.invokeFunction("start", deviceSPI,a);
            System.out.println("js返回给Java的设备对象的设备名:" + result.getResult().getName());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    @Test
    public void jsEngineTest(){
        ScriptEngine engine = new ScriptEngineManager().getEngineByName("nashorn");
        try {
            String testjs = "var start = function (thing) {\n" +
                    "    var thingService = thing.thingServiceService;\n" +
                    "    print('js获取设备名：' + thingService);\n" +
                    "    return thingService;\n" +
                    "}";
            engine.eval(testjs);
            Invocable invocable = (Invocable) engine;
            // IThingServiceService result = (IThingServiceService)invocable.invokeFunction("start", thing);
            // System.out.println("js返回给Java的设备对象的设备名:" + result);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    @Test
    public void NashornEngineTest(){
        ScriptEngine engine = new ScriptEngineManager().getEngineByName("nashorn");
        DeviceEntity build = DeviceEntity.builder().id(1350488767504384l).build();
        try {
            Result<DeviceDto> deviceResult = deviceSPI.getDevice(build);
            System.out.println("Java获取设备名：" + deviceResult.getResult().getName());
            String testjs = "var start = function (deviceSPI) {\n" +
                    "    var deviceResult = deviceSPI.deviceService.getDeviceById(\"1350488767504384\");\n" +
                    "    print('js获取设备名：' + deviceResult.result.name);\n" +
                    "    return deviceResult;\n" +
                    "}";
            engine.eval(testjs);
            Invocable invocable = (Invocable) engine;
            Result<DeviceEntity> result = (Result<DeviceEntity>)invocable.invokeFunction("start", deviceSPI);
            System.out.println("js返回给Java的设备对象的设备名:" + result.getResult().getName());
        } catch (Exception e) {
            e.printStackTrace();
        }


    }



    /*@Test
    public void NashornEngineOverrideTest(){
        ScriptEngine engine = new ScriptEngineManager().getEngineByName("nashorn");
        Invocable invocable = (Invocable) engine;
        Class<Thread> threadClass = Thread.class;
        try {
            String testjs = "function setTimeout(func) { func() }\n"+

                    "function start(a) {\n" +
                    "   a.sleep(10000);\n" +
                    "   return a;\n" +
                    "}";
            engine.eval(testjs);

            Object result = invocable.invokeFunction("start", new TimeOut());
            System.out.println("第一次:" + result);
            String testjs2 = "function start() {\n" +
                    "    print('我是固定的');\n" +
                    "    return '已经覆盖了';\n" +
                    "}";
            engine.eval(testjs2);
            Object o = invocable.invokeFunction("start");
            return;
        } catch (Exception e) {
            e.printStackTrace();
        }


    }*/

    /**
     * Nashorn 引擎特性：
     * 1. invocable.invokeFunction 会复制一份当前 engine.eval() 进去的脚本到运行域；
     * 2. 方法里面可以定义方法，invoke只能调用到最外层的方法,无法调用到方法里的方法（只能在方法作用域的调用，且无法返回给Java层调用（）），
     */
   /* @Test
    public void overrideFunctionName() throws InterruptedException {
        ExecutorService executorService = Executors.newFixedThreadPool(5, new testThreadPoolFactory("fixedThreadPool"));
        ScriptEngine engine = new ScriptEngineManager().getEngineByName("nashorn");
        engine.put("timeOut",new TimeOut(10,"第一次"));
        Invocable invocable = (Invocable) engine;
        Class<Thread> threadClass = Thread.class;

        for (int i = 1 ; i < 10 ;i++){
            String testjs =

                    "var start" +i+" = function (a,b) {\n" +
//                    "   a.sleep();\n" +
                            "   timeOut.sleep();\n" +
                            "   print('外层'+b);\n" +
                            "   print('第' +" +i+"+'套函数');\n" +
//                    "   var ax = function (a,b){timeOut.sleep();print('内层'+b+1);};\n" +
//                    "   ax(a,b);\n" +
//                    "   return ax;\n" +
                            "}";
            doSomething(testjs,engine,i,new TimeOut(1000,"第" +i+ "次"),executorService);
        }
        Thread.sleep(100000);
        return;

    }*/


    public Object doSomething(String testjs,ScriptEngine engine,int i,TimeOut timeOut,ExecutorService executorService){
        Invocable invocable = (Invocable) engine;
        try {
            synchronized (NashornEngineTest.class){
                engine.eval(testjs);
                executorService.submit(() -> {
                    try {
//                        synchronized (NashornEngineTest.class){
                        engine.put("timeOut",timeOut);
                        System.out.println("第"+i+"次开始" );
                        Object result = (ScriptObjectMirror) invocable.invokeFunction("start1", new TimeOut(),i);
                        System.out.println("第"+i+"次结束" );

//                        }
                        return;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }

            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
