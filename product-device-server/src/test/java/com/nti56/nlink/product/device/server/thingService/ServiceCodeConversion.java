package com.nti56.nlink.product.device.server.thingService;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.product.device.RunWithSpringBase;
import com.nti56.nlink.product.device.client.model.dto.json.DeviceRuntimeMetadataField;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.StatusEnum;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceEntity;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import com.nti56.nlink.product.device.server.mapper.DeviceMapper;
import com.nti56.nlink.product.device.server.mapper.DeviceServiceMapper;
import com.nti56.nlink.product.device.server.mapper.ThingServiceMapper;
import com.nti56.nlink.product.device.server.service.IDeviceStatusManagementService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ServiceCodeConvery
 * @date 2022/12/12 10:43
 * @Version 1.0
 */
public class ServiceCodeConversion extends RunWithSpringBase {


    @Autowired
    DeviceServiceMapper deviceServiceMapper;

    @Autowired
    ThingServiceMapper thingServiceMapper;

    @Test
    public void doCodeConversion(){
        LambdaQueryWrapper<DeviceServiceEntity> eq = new LambdaQueryWrapper<DeviceServiceEntity>();
        List<DeviceServiceEntity> deviceServiceEntityList = deviceServiceMapper.selectList(eq);
        deviceServiceEntityList.stream().forEach(deviceServiceEntity -> {
            deviceServiceEntity.setServiceCode(codeConversion(deviceServiceEntity.getServiceCode()));
            deviceServiceMapper.updateById(deviceServiceEntity);
        });
        List<ThingServiceEntity> list = new LambdaQueryChainWrapper<>(thingServiceMapper)
                .list();
        list.stream().forEach(thingServiceEntity -> {
            thingServiceEntity.setServiceCode(codeConversion(thingServiceEntity.getServiceCode()));
            thingServiceMapper.updateById(thingServiceEntity);
        });

    }

    private String codeConversion(String serviceCode) {
        if (StringUtils.isEmpty(serviceCode)) {
            return serviceCode;
        }
        serviceCode = serviceCode.replace("thing.", "me.");
        serviceCode = serviceCode.replace("engineering.","things.");
        serviceCode = serviceCode.replace("getPropertyInRunTime","readProperty");
        serviceCode = serviceCode.replace("getPropertiesInRuntime","readProperties");
        serviceCode = serviceCode.replace("setProperty","writeProperty");
        serviceCode = serviceCode.replace("setProperties","writeProperties");
        serviceCode = serviceCode.replace("getThingService","getService");
        serviceCode = serviceCode.replace("getThingServiceByName","getServiceByName");
        serviceCode = serviceCode.replace("getThingServiceName","getServiceName");
        serviceCode = serviceCode.replace("callThingService","callService");
        serviceCode = serviceCode.replace("getThingByName","getByName");
        serviceCode = serviceCode.replace("getThingByDeviceId","getByResourceId");
        return serviceCode;
    }

    @Autowired
    IDeviceStatusManagementService deviceStatusManagementService;

    @Autowired
    DeviceMapper deviceMapper;

//     @Test
//     public void syncDevice(){
//         List<DeviceEntity> list = new LambdaQueryChainWrapper<>(deviceMapper)
//                 .isNotNull(DeviceEntity::getRuntimeMetadata)
//                 .list();
//         list = list.stream().filter(device -> device.getRuntimeMetadata().getId() == null).collect(Collectors.toList());
//         list.stream().forEach(device -> {
//             DeviceRuntimeMetadataField runtimeMetadata = device.getRuntimeMetadata();
//             if (runtimeMetadata.getId() == null) {
//                 runtimeMetadata.setId(device.getId());
//                 device.setRuntimeMetadata(runtimeMetadata);
//                 deviceMapper.updateById(device);
//             }
//         });

//         Map<Long, Long> onLine = list.stream().filter(device -> device.getStatus().equals(StatusEnum.ONLINE.getValue())).collect(Collectors.toMap(DeviceEntity::getId,DeviceEntity::getTenantId));
//         Map<Long, Long> noOnline = list.stream().filter(device -> device.getStatus().equals(StatusEnum.OFFLINE.getValue())).collect(Collectors.toMap(DeviceEntity::getId,DeviceEntity::getTenantId));

//         onLine.forEach((k,v) ->{
//             TenantIsolation tenantIsolation = new TenantIsolation();
//             tenantIsolation.setTenantId(v);
//             try {
// //                deviceStatusManagementService.deviceOffline(tenantIsolation,k);
// //                deviceStatusManagementService.deviceOnline(tenantIsolation,k);
//             } catch (Exception e) {
//                 e.printStackTrace();
//             }
//         });

//         noOnline.forEach((k,v) -> {
//             TenantIsolation tenantIsolation = new TenantIsolation();
//             tenantIsolation.setTenantId(v);
//             try {
// //                deviceStatusManagementService.deviceOffline(tenantIsolation,k);
//             } catch (Exception e) {
//                 e.printStackTrace();
//             }
//         });

//     }

}
