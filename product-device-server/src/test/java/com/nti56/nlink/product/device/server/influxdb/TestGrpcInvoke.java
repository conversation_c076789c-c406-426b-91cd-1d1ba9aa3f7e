/*
package com.nti56.nlink.product.device.server.influxdb;

import cn.hutool.json.JSONUtil;
import com.nti56.nlink.common.client.grpc.InvokeResult;
import com.nti56.nlink.common.client.grpc.MqttCallbackGrpc;
import com.nti56.nlink.common.client.grpc.RedirectRequest;
import io.grpc.ManagedChannel;
import io.grpc.stub.StreamObserver;
import io.vertx.core.Vertx;
import io.vertx.grpc.VertxChannelBuilder;
import jdk.nashorn.internal.ir.IdentNode;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.concurrent.TimeUnit;


*/
/**
 * 类说明：
 *
 * @ClassName TestGrpcInvoke
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/9/26 15:27
 * @Version 1.0
 *//*


@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles("local")
public class TestGrpcInvoke {


    @GrpcClient("common-server")
    private MqttCallbackGrpc.MqttCallbackStub mqttCallbackStub;

    @Test
    public void testGrpcInvoke() throws InterruptedException {

        Vertx vertx = Vertx.vertx();
        ManagedChannel channel = VertxChannelBuilder.forAddress(vertx,"localhost",6666)
                .usePlaintext().useTransportSecurity()
                .build();
        MqttCallbackGrpc.MqttCallbackStub stub = MqttCallbackGrpc.newStub(channel);
        RedirectRequest request = RedirectRequest.newBuilder().setRedirectId(1362998295044096l).setTenantId(1561632766420258818l).
                setPayload("four word real words").build();
        stub.invoke(request, new StreamObserver<InvokeResult>() {
            @Override
            public void onNext(InvokeResult invokeResult) {
                log.info("grpc invoke result:{}", invokeResult.getSuccess());
            }

            @Override
            public void onError(Throwable throwable) {
                log.error("grpc invoke error");
                throwable.printStackTrace();
            }

            @Override
            public void onCompleted() {
                log.info("grpc invoke success");
            }
        });
        TimeUnit.SECONDS.sleep(10);
    }

    @Test
    public void testFromNacos() throws InterruptedException {
        RedirectRequest request = RedirectRequest.newBuilder().setRedirectId(1362998295044096l).setTenantId(1561632766420258818l).
                setPayload("four word real words").build();
        mqttCallbackStub.invoke(request, new StreamObserver<InvokeResult>() {
            @Override
            public void onNext(InvokeResult invokeResult) {
                log.info("grpc invoke result:{}", invokeResult.getSuccess());
            }

            @Override
            public void onError(Throwable throwable) {
                log.error("grpc invoke error");
                throwable.printStackTrace();
            }

            @Override
            public void onCompleted() {
                log.info("grpc invoke success");
            }
        });

        TimeUnit.SECONDS.sleep(100);
    }
}

*/
