package com.nti56.nlink.product.device.server.influxdb;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.QueryApi;
import com.influxdb.query.FluxTable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.Instant;
import java.time.Period;
import java.util.*;


@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
@ActiveProfiles("local")
public class QueryTest {

  @Autowired
  InfluxDBClient influxDBClient;

  @Value("${influx.bucket}")
  private String bucket;

  @Value("${influx.org}")
  private String influxOrg;

  @Test
  public void testExport(){
    QueryApi queryApi = influxDBClient.getQueryApi();
    Map<String, Object> params = new HashMap<>();
    params.put("bucketParam", bucket);
    Instant yesterday = Instant.now().minus(Period.ofDays(1));
    params.put("startParam", yesterday.toString());
    params.put("endParam", yesterday.toString());
    params.put("deviceId", "1359972632666112");
//    String parametrizedQuery = "from(bucket: params.bucketParam) |> range(start: time(v: params.startParam))";
     String QUERY_TEMP = "from(bucket: \"%s\")\n" +
            "  |> range(start: time(v: %s), stop: time(v: %s))\n" +
             " |> filter(fn: (r) => r[\"_measurement\"] == \"1544856476503687169\")\n" +
            "  |> filter(fn: (r) =>  contains(value: r.deviceId, set: %s))\n" +
            "  |> drop(columns:[\"_values\"])\n" +
            "  |> group(columns: [\"property\"])\n" +
            "  |> unique(column: \"property\")\n" +
            "  |> yield()";

     String bucket = "nlink-test";
//     Date date = DateUtil.parse("2022-08-02T00:00:00Z", DatePattern.UTC_PATTERN);
     String start = "2022-08-15T00:00:00Z";
     String end = "2022-08-15T12:00:00Z";
     List<String> deviceIds = new ArrayList<>();
     deviceIds.add("1359972632666112");
    String format = String.format(QUERY_TEMP, bucket, start, end, JSONUtil.toJsonStr(deviceIds));
   /* String p2 = "from(bucket: \"nlink-test\")\n" +
            "  |> range(start: 2022-08-02T00:00:00Z, stop: 2022-08-02T12:00:00Z)\n" +
            "  |> filter(fn: (r) => [\"1358960334381056\"].indexOf(r[\"deviceId\"])>0)\n" +
            "  |> group()\n" +
            "  |> unique(column: \"property\")\n" +
            "  |> count(column: \"property\")\n" +
            "  |> yield()";*/

//    List<FluxTable> query = queryApi.query(parametrizedQuery, influxOrg, params);
      int collectedPropCount = 0;
      List<FluxTable> query = queryApi.query(format, influxOrg);
      System.out.println(query.size());
//    System.out.println(JSONUtil.toJsonStr(query));
  }
}
