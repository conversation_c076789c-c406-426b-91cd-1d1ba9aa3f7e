package com.nti56.nlink.product.device.server;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.InstantDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.InstantSerializer;
import com.influxdb.annotations.Column;
import com.influxdb.annotations.Measurement;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.QueryApi;
import com.influxdb.client.WriteApiBlocking;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.nti56.nlink.common.base.Field;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.updater.CommonUpdater;
import com.nti56.nlink.common.updater.CommonUpdaterFactory;
import com.nti56.nlink.common.util.CurrentTimeMillisClock;
import com.nti56.nlink.common.util.GeneratorSqlUtil;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.InputDataField;
import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.client.model.dto.json.OutputDataField;
import com.nti56.nlink.product.device.client.model.dto.json.condition.LeftElm;
import com.nti56.nlink.product.device.client.model.dto.json.condition.RightElm;
import com.nti56.nlink.product.device.client.model.dto.json.condition.TriggerConditionElm;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.DataTypeElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventDefineElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import com.nti56.nlink.product.device.server.config.XxlJobConfig;
import com.nti56.nlink.product.device.server.domain.thing.datamodel.DataModel;
import com.nti56.nlink.product.device.server.domain.thing.device.Device;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.*;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModel;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.mapper.*;
import com.nti56.nlink.product.device.server.model.ExpandDataModelPropertyBo;
import com.nti56.nlink.product.device.server.model.deviceLog.PropertyLogBo;
import com.nti56.nlink.product.device.server.scriptApi.Engineering;
import com.nti56.nlink.product.device.server.scriptApi.EngineeringFactory;
import com.nti56.nlink.product.device.server.scriptApi.EngineeringSpi;
import com.nti56.nlink.product.device.server.service.IDeviceService;

import cn.hutool.core.date.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-04 11:54:54
 * @since JDK 1.8
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class AppTest {
    
    @Autowired
    DataModelMapper dataModelMapper;

    @Autowired
    DataModelPropertyMapper dataModelPropertyMapper;

    @Autowired
    IDeviceService deviceService;

    @Autowired
    LabelBindRelationMapper labelBindRelationMapper;

    @Autowired
    ResourceRelationMapper resourceRelationMapper;

    @Autowired
    DeviceModelInheritMapper deviceModelInheritMapper;
    
    @Autowired
    DeviceServiceMapper deviceServiceMapper;

    
    @Autowired
    CommonFetcherFactory commonFetcherFactory;

    @Autowired
    CommonUpdaterFactory commonUpdaterFactory;

    private Long tenantId = 1527470820947841026L; //sushangqun

    @Autowired
    ThingServiceMapper thingServiceMapper;

    @Autowired
    private InfluxDBClient influxDbClient;

    public static final String COMMON_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd";
    public static final String MINUTE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm";
    private static final DateTimeFormatter commonFormat = DateTimeFormatter.ofPattern(COMMON_FORMAT_PATTERN);
    private static final DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN);
    private static final DateTimeFormatter minuteFormat = DateTimeFormatter.ofPattern(MINUTE_FORMAT_PATTERN);

    @Value("${influx.bucket}")
    private String bucket;

    private String tenantId1 = "1527470820947841026";
    private Long deviceId1 = 1382697974153216L;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "计数记录")
    @Measurement(name = "nti")
    public static class CountLog {

        @Column(tag = true)
        String deviceId;

        @Column(tag = true)
        String minute;

        @Column(tag = true)
        String date;

        @Column
        Object value;

        @Column
        String field;

        @Column(timestamp = true)
        @JsonDeserialize(using = InstantDeserializer.class)
        @JsonSerialize(using = InstantSerializer.class)
        @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss.SSS")
        Instant time;

        Long start;

        Long stop;
    }

    @MockBean
    private XxlJobConfig xxlJobConfig;
    
    @Autowired
    EngineeringFactory engineeringFactory;
    
    @Test
    public void testScript() throws ScriptException, NoSuchMethodException{
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("nashorn");
        String userCode = 
            "var deviceId = topic;\n" + 
            "var inputData = {v: payload};\n" +
            "var device = things.getByResourceId(deviceId);\n" + 
            "result = device.callService('writeAaa', inputData);\n"
            ;
        String code = "function dodo(topic, payload, things){ \n" + 
            "var result = null; \n" +
            userCode + "\n" + 
            "  return result;\n" + 
            "}";
        engine.eval(code);

        EngineeringSpi engineering = this.engineeringFactory.getEngineering(1527470820947841026L);

        Invocable invocable = (Invocable) engine;
        Object r = invocable.invokeFunction("dodo","1383197948510208", "121", engineering); 
        log.info("r: {}", r);
    }
    @Test
    public void testQueryInfluxdb() throws InterruptedException{

        Long start = LocalDateTime.parse("2023-05-02 10:01:00", commonFormat).toInstant(ZoneOffset.of("+8")).toEpochMilli()/1000;
        Long stop = LocalDateTime.parse("2023-07-02 10:01:00", commonFormat).toInstant(ZoneOffset.of("+8")).toEpochMilli()/1000;
        Flux flux = Flux.from(bucket)
        .range(start, stop)
        .filter(Restrictions.and(
            Restrictions.measurement().equal(tenantId1),
            Restrictions.tag("deviceId").equal(deviceId1.toString())
        ))
        .drop(new String[]{"date", "_measurement", "_start", "_stop"})
        .sum()
        .groupBy(new String[]{"minute"})
        ;
        String fql = flux.toString();
        log.info("fql: {}", fql);
        QueryApi queryApi = influxDbClient.getQueryApi();
        try {
            List<CountLog> list = queryApi.query(fql,CountLog.class);
            log.info("list:{}", list);
        } catch (Exception e) {
            log.error("ERROR:{}",e.getMessage());
        }

        Thread.sleep(60_000 * 10 * 10);
    }

    @Test
    public void testWriteInfluxdb() throws InterruptedException{

        write(LocalDateTime.parse("2023-06-03 10:01:00", commonFormat), 10);
        write(LocalDateTime.parse("2023-06-03 10:01:04", commonFormat), 15);
        write(LocalDateTime.parse("2023-06-03 10:01:12", commonFormat), 5);
        write(LocalDateTime.parse("2023-06-03 10:01:50", commonFormat), 6);

        write(LocalDateTime.parse("2023-06-03 10:02:01", commonFormat), 11);
        write(LocalDateTime.parse("2023-06-03 10:02:34", commonFormat), 13);
        write(LocalDateTime.parse("2023-06-03 10:02:41", commonFormat), 13);

        write(LocalDateTime.parse("2023-06-03 10:04:05", commonFormat), 15);
        write(LocalDateTime.parse("2023-06-03 10:04:25", commonFormat), 5);
        write(LocalDateTime.parse("2023-06-03 10:04:40", commonFormat), 10);
        write(LocalDateTime.parse("2023-06-03 10:04:47", commonFormat), 7);
        write(LocalDateTime.parse("2023-06-03 10:04:58", commonFormat), 12);

        Thread.sleep(60_000 * 10 * 10);
    }

    private void write(LocalDateTime time, Integer value){
        WriteApiBlocking writeApi = influxDbClient.getWriteApiBlocking();
        Map<String, Object> fields = new HashMap<>();
        fields.put("Short", value);
        String minute = minuteFormat.format(time);
        String date = dateFormat.format(time);
        long stamp = time.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        Point point = Point.measurement(tenantId1)
            .addTag("deviceId", deviceId1.toString())
            .addTag("type", "writeTwin")
            .addTag("eventName", "writeTwin")
            .addTag("length", "1")
            // .addTag("property", "operateOneDone")
            .addTag("property", "operateOneDoneDuration")
            .addFields(fields)
            .time(stamp, WritePrecision.MS);
        writeApi.writePoint(point);
    }

    @Test
    public void testCommonUpdater(){
        CommonUpdater commonUpdater = commonUpdaterFactory.buildCommonUpdater(1527470820947841026L);
        boolean success = commonUpdater.set(1356916884332544L, new Field("sync_status", 1), EdgeGatewayEntity.class);
        log.debug("success", success);
    }
    
    // @Test
    // public void testDevice(){
    //     DeviceEntity deviceEntity = deviceMapper.selectById(1354020949458944L);

    //     String s = deviceEntity.getRuntimeMetadata().toSqlString();
    //     System.out.println(s);
    //     List<DeviceEntity> list = new ArrayList<>();
    //     list.add(deviceEntity);
    //     List<String> insertSql = GeneratorSqlUtil.getInsertSql(DeviceEntity.class, list);
    //     String sql = insertSql.get(0);
    //     System.out.println(sql);
    // }

    @Test
    public void testService(){
        List<ThingServiceEntity> list = thingServiceMapper.listByDataModelId(1524201708284735490L, 1354591119753216L);
        System.out.println(list);
    }

    @Test
    public void testThingModel(){

        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);

        List<Long> list = ThingModel.listAllBeInheritThingModelIdById(1354148913750016L, commonFetcher);
        System.out.println(list);

    }

    @Test
    public void testFetcher(){
        // CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(0L);
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(0L);
        List<DeviceModelInheritEntity> deviceEntity = commonFetcher.list("device_id", 1354020926808064L, DeviceModelInheritEntity.class);
        System.out.println(deviceEntity);
        List<DeviceModelInheritEntity> deviceEntity2 = commonFetcher.list("device_id", 1354020926808064L, DeviceModelInheritEntity.class);
        System.out.println(deviceEntity2);
    }

    @Test
    public void testList(){

        DataModelEntity dataModelA = new DataModelEntity();
        dataModelA.setName("A");
        dataModelMapper.insert(dataModelA);

        DataModelPropertyEntity propertySwitch = new DataModelPropertyEntity();
        propertySwitch.setDataModelId(dataModelA.getId());
        propertySwitch.setName("switch");
        propertySwitch.setDataType(1); //bool
        propertySwitch.setIsArray(false);
        propertySwitch.setDefaultValue("false");
        dataModelPropertyMapper.insert(propertySwitch);

        DataModelEntity dataModelB = new DataModelEntity();
        dataModelB.setName("B");
        dataModelMapper.insert(dataModelB);

        DataModelPropertyEntity propertyLightSwitch = new DataModelPropertyEntity();
        propertyLightSwitch.setDataModelId(dataModelB.getId());
        propertyLightSwitch.setName("lightSwitch");
        propertyLightSwitch.setDataType(7); //dataModel
        propertyLightSwitch.setIsArray(true);
        propertyLightSwitch.setPropertyDataModelId(dataModelA.getId());
        dataModelPropertyMapper.insert(propertyLightSwitch);

        DataModelPropertyEntity propertyLevel = new DataModelPropertyEntity();
        propertyLevel.setDataModelId(dataModelB.getId());
        propertyLevel.setName("level");
        propertyLevel.setDataType(3); //short
        propertyLevel.setIsArray(false);
        propertyLevel.setPropertyDataModelId(dataModelA.getId());
        dataModelPropertyMapper.insert(propertyLevel);

        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantId);
        Result<DataModel> dataModelResult = DataModel.checkInfo(
            dataModelB,  commonFetcher
        );
        if(!dataModelResult.getSignal()){
            System.out.println(dataModelResult.getMessage());
        }
        List<ExpandDataModelPropertyBo> expandProperty = dataModelResult.getResult().listExpandProperty();
        log.info("aa111: {}", expandProperty);
    }
    @Test
    public void testA() throws InterruptedException{
        log.debug("myMqttSender begin");
        Thread.sleep(60_000 * 10 * 10);
    }

    @Autowired
    EdgeGatewayMapper edgeGatewayMapper;

    @Autowired
    ChannelMapper channelMapper;

    @Autowired
    ChannelParamMapper channelParamMapper;

    @Autowired
    LabelGroupMapper labelGroupMapper;

    @Autowired
    LabelMapper labelMapper;

    @Autowired
    ThingModelMapper thingModelMapper;

    @Autowired
    DeviceMapper deviceMapper;

    @Autowired
    DeviceModelMapper deviceModelMapper;

    @Autowired
    ThingModelInheritMapper thingModelInheritMapper;


    /**
        
        delete from edge_gateway where name like 'H_%';
        delete from channel_param where channel_id in (select id from channel where name like 'H_%');
        delete from channel where name like 'H_%';
        delete from label where descript like 'H_%';
        delete from label_group where name like 'H_%';
        delete from label_bind_relation where thing_model_id in (select id from thing_model where name like 'H_%');
        delete from thing_model_inherit where thing_model_id in (select id from thing_model where name like 'H_%');

        delete from thing_model where name like 'H_%';
        delete from device where name like 'H_%';
        delete from product where name like 'H_%';
        delete from strategy where name like 'H_%';

        delete from thing_service where service_name like 'H_%';
     */
    @Test
    public void testIntiDate() throws InterruptedException{
//        deleteByTenant(0l);
//        deleteByTenant(1526388533040697345l);
//        testInitData("FF_",0L,0L,0L,0L);
//        testInitData("LT_",0l,0L,0L,0L);
//        testInitData("ZT_",0l,0L,0L,0L);
//        testInitData("ST_",0l,0L,0L,0L);
//        testInitData("PT_",0l,0L,0L,0L);
        testInitData("GT_",0l,0L,0L,0L);
//        testInitData("LYR_",1526388533040697345l,1526388027245383682l,0l,1526388533040697345l);
        Thread.sleep(5000);
    }
    @Autowired
    TagBindRelationMapper tagBindRelationMapper;

    @Autowired
    ComputeTaskMapper computeTaskMapper;

    public void deleteByTenant(Long tenantId){
        dataModelMapper.delete(new UpdateWrapper<DataModelEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        deviceMapper.delete(new UpdateWrapper<DeviceEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        thingModelInheritMapper.delete(new UpdateWrapper<ThingModelInheritEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        thingModelMapper.delete(new UpdateWrapper<ThingModelEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        thingServiceMapper.delete(new UpdateWrapper<ThingServiceEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        deviceModelInheritMapper.delete(new UpdateWrapper<DeviceModelInheritEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        deviceServiceMapper.delete(new UpdateWrapper<DeviceServiceEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        tagBindRelationMapper.delete(new UpdateWrapper<TagBindRelationEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        labelBindRelationMapper.delete(new UpdateWrapper<LabelBindRelationEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        labelGroupMapper.delete(new UpdateWrapper<LabelGroupEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        labelMapper.delete(new UpdateWrapper<LabelEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        edgeGatewayMapper.delete(new UpdateWrapper<EdgeGatewayEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        resourceRelationMapper.delete(new QueryWrapper<ResourceRelationEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        dataModelPropertyMapper.delete(new UpdateWrapper<DataModelPropertyEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        channelMapper.delete(new UpdateWrapper<ChannelEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        channelParamMapper.delete(new UpdateWrapper<ChannelParamEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
        computeTaskMapper.delete(new UpdateWrapper<ComputeTaskEntity>().eq("tenant_id", tenantId).or().isNull("tenant_id"));
    }

    @Transactional
    public void testInitData(String prefix,Long tenantId,Long spaceId,Long moduleId,Long engineeringId){
        //创建网关
        EdgeGatewayEntity edgeGateway = EdgeGatewayEntity.builder()
                .name(prefix + "网关")
                .host("127.0.0.1")
                .port(8088)
                .type(EdgeGatewayTypeEnum.VIRTUAL_GATEWAY.getValue())
                .tenantId(tenantId).engineeringId(engineeringId).spaceId(spaceId).moduleId(moduleId)
                .build();
        edgeGatewayMapper.insert(edgeGateway);

        //创建通道
        ChannelEntity channel = ChannelEntity.builder()
            .name(prefix + "通道").tenantId(tenantId).engineeringId(engineeringId).spaceId(spaceId).moduleId(moduleId)
            .driver(DriverEnum.SNAP7.getValue())
            .edgeGatewayId(edgeGateway.getId())
            .build();
        channelMapper.insert(channel);

        //创建通道参数
        String[][] paramArray = new String[][]{
            new String[]{"ip", "**************"}, 
            new String[]{"port", "102"}, 
            new String[]{"rack", "0"}, 
            new String[]{"slot", "0"}
        };
        for(String[] s:paramArray){
            ChannelParamEntity param = ChannelParamEntity.builder()
                .channelId(channel.getId()).tenantId(tenantId).engineeringId(engineeringId).spaceId(spaceId).moduleId(moduleId)
                .name(s[0])
                .value(s[1])
                .build();
            channelParamMapper.insert(param);
        }


        //创建标签分组
        LabelGroupEntity labelGroup = LabelGroupEntity.builder()
            .channelId(channel.getId()).tenantId(tenantId).engineeringId(engineeringId).spaceId(spaceId).moduleId(moduleId)
            .name(prefix + "标签分组")
            .build();
        labelGroupMapper.insert(labelGroup);
        
        //创建标签
        String[][] labelArray = new String[][]{
            new String[]{prefix + "taskNo", prefix + "任务号", "DB50.INT0", "1", "short", "0", null },
            new String[]{prefix + "boxStatus", prefix + "箱子类型", "DB50.INT2", "1", "short", "0", null },
            new String[]{prefix + "goodsType", prefix + "货物类型", "DB50.INT4", "1", "short", "0", null },
            new String[]{prefix + "fromNode", prefix + "起始地址", "DB50.INT6", "1", "short", "0", null },
            new String[]{prefix + "toNode", prefix + "目标地址", "DB50.INT8", "1", "short", "0", null },
            new String[]{prefix + "barCode", prefix + "条码", "DB50.INT10", "2", "short", "1", null },
            new String[]{prefix + "boxQty", prefix + "烟箱数量", "DB50.INT14", "1", "short", "0", null },
            new String[]{prefix + "mode", prefix + "设备模式", "DB50.INT16", "1", "short", "0", null },
            new String[]{prefix + "rfidFault", prefix + "RFID读写错误", "DB50.BIT18.0", "1", "bool", "0", null },
            new String[]{prefix + "comparisonFault", prefix + "条码比对异常", "DB50.BIT18.1", "1", "bool", "0", null },
            new String[]{prefix + "netWeight", prefix + "重量为0", "DB50.BIT18.2", "1", "bool", "0", null },
            new String[]{prefix + "rfidCheckFault", prefix + "校验RFID信息错误", "DB50.BIT18.3", "1", "bool", "0", null },
            new String[]{prefix + "rfidModifyFault", prefix + "RFID修改错误", "DB50.BIT18.4", "1", "bool", "0", null },
            new String[]{prefix + "boxStatusCheck", prefix + "实空箱检验错误", "DB50.BIT18.5", "1", "bool", "0", null },
            new String[]{prefix + "weightFault", prefix + "重量异常", "DB50.BIT18.6", "1", "bool", "0", null },
            new String[]{prefix + "visionCompareFault", "H_视觉比对错误", "DB50.BIT19.0", "1", "bool", "0", null },
            new String[]{prefix + "visionFault", prefix + "视觉读故障", "DB50.BIT19.1", "1", "bool", "0", null }
        };

        List<LabelEntity> labelList = new ArrayList<>();
        for(String[] s:labelArray){
            LabelEntity label = LabelEntity.builder()
                .labelGroupId(labelGroup.getId()).tenantId(tenantId).engineeringId(engineeringId).spaceId(spaceId).moduleId(moduleId)
                .name(s[0])
                .descript(s[1])
                .address(s[2])
                .length(Integer.parseInt(s[3]))
                .dataType(s[4])
                .isArray(YesNoEnum.YES.getValue().equals(Integer.parseInt(s[5])))
                .build();
            String stringBytes = s[6];
            if(stringBytes != null){
                label.setStringBytes(Integer.parseInt(stringBytes));
            }
            labelMapper.insert(label);
            labelList.add(label);
        }

        //创建基础物模型
        ThingModelEntity thingModel = new ThingModelEntity();
        thingModel.setName(prefix + "香烟输出基础模型");
        thingModel.setTenantId(tenantId);
        thingModel.setEngineeringId(engineeringId);
        thingModel.setSpaceId(spaceId);
        thingModel.setModuleId(moduleId);



        ModelField model = new ModelField();
        thingModel.setModel(model);

        List<PropertyElm> properties = new ArrayList<>();
        model.setProperties(properties);
        for(String[] s:labelArray){
            PropertyElm propertyElm = new PropertyElm();
            propertyElm.setName(s[0]);
            propertyElm.setDescript(s[1]);
            propertyElm.setBindLabel(true);
            DataTypeElm dataTypeElm = new DataTypeElm();
            dataTypeElm.setType(s[4]);
            dataTypeElm.setIsArray(YesNoEnum.YES.getValue().equals(Integer.parseInt(s[5])));
            propertyElm.setDataType(dataTypeElm);
            propertyElm.setReportType(0);
            properties.add(propertyElm);
        }
        List<EventElm> events = new ArrayList<>();
        model.setEvents(events);
        {
            EventElm eventElm = new EventElm();
            eventElm.setType(EventTypeEnum.TRIGGER.getName());
            eventElm.setName(prefix + "weightFaultEvent");
            eventElm.setDescript("重量错误事件");
            EventDefineElm eventDefineElm = new EventDefineElm();
            eventElm.setEventDefine(eventDefineElm);
            eventDefineElm.setProperties(new ArrayList<String>(){{
                add(prefix + "taskNo");
                add(prefix + "netWeight");
                add(prefix + "weightFault");
            }});
            List<TriggerConditionElm> trigger = new ArrayList<>();
            {
                TriggerConditionElm elm = new TriggerConditionElm();
                elm.setType("compare");
                elm.setLevel(0);
                LeftElm left = new LeftElm();
                left.setProperty(prefix + "boxQty");
                elm.setLeft(left);
                elm.setOperator(">");
                RightElm right = new RightElm();
                right.setValue("10");
                elm.setRight(right);
                trigger.add(elm);
            }
            eventDefineElm.setTrigger(trigger);
            eventDefineElm.setTopic("");
            events.add(eventElm);
        }

        thingModelMapper.insert(thingModel);

        //创建物模型服务
        ThingServiceEntity thingService = new ThingServiceEntity();
        thingService.setThingModelId(thingModel.getId());
        thingService.setServiceName(prefix + "changeFromNode");
        thingService.setDescript(prefix + "改变起始位置");
        thingService.setOverride(false);
        InputDataField inputData = new InputDataField();
        inputData.setName("node");
        inputData.setDataType(ThingDataTypeEnum.LONG.getValue());
        inputData.setIsArray(false);
        inputData.setDescript("起始位置");
        thingService.setInputData(new InputDataField[]{inputData});
        OutputDataField outputData = new OutputDataField();
        outputData.setDataType(ThingDataTypeEnum.VOID.getValue());
        outputData.setIsArray(false);
        outputData.setDescript("无返回数据");
        thingService.setOutputData(outputData);
        thingService.setAsync(false);
        thingService.setServiceCode("thing.setProperty('node',input.node); result = input.node");
        thingService.setTenantId(tenantId);
        thingService.setEngineeringId(engineeringId);
        thingService.setSpaceId(spaceId);
        thingService.setModuleId(moduleId);

        thingServiceMapper.insert(thingService);

        //创建香烟输出机器物模型
        ThingModelEntity thingModel1 = new ThingModelEntity();
        thingModel1.setName(prefix + "香烟输出机模型");
        ModelField model1 = new ModelField();
        thingModel1.setModel(model1);
        thingModel1.setTenantId(tenantId);
        thingModel1.setEngineeringId(engineeringId);
        thingModel1.setSpaceId(spaceId);
        thingModel1.setModuleId(moduleId);



        String[][] labelArray1 = new String[][]{
            new String[]{prefix + "temperature", prefix + "温度", "DB50.INT30", "1", "short", "0", null },
        };

        //创建标签
        List<LabelEntity> labelList1 = new ArrayList<>();
        for(String[] s:labelArray1){
            LabelEntity label = LabelEntity.builder().tenantId(tenantId).engineeringId(engineeringId).spaceId(spaceId).moduleId(moduleId)
                .labelGroupId(labelGroup.getId())
                .name(s[0])
                .descript(s[1])
                .address(s[2])
                .length(Integer.parseInt(s[3]))
                .dataType(s[4])
                .isArray(YesNoEnum.YES.getValue().equals(Integer.parseInt(s[5])))
                .build();
            String stringBytes = s[6];
            if(stringBytes != null){
                label.setStringBytes(Integer.parseInt(stringBytes));
            }
            labelMapper.insert(label);
            labelList1.add(label);
        }

        //创建属性
        List<PropertyElm> properties1 = new ArrayList<>();
        model1.setProperties(properties1);
        for(String[] s:labelArray1){
            PropertyElm propertyElm = new PropertyElm();
            propertyElm.setName(s[0]);
            propertyElm.setDescript(s[1]);
            propertyElm.setBindLabel(true);
            DataTypeElm dataTypeElm = new DataTypeElm();
            dataTypeElm.setType(s[4]);
            dataTypeElm.setIsArray(YesNoEnum.YES.getValue().equals(Integer.parseInt(s[5])));
            propertyElm.setDataType(dataTypeElm);
            properties1.add(propertyElm);
        }

        //创建物模型
        thingModelMapper.insert(thingModel1);

        //创建继承关系
        ThingModelInheritEntity thingModelInherit = new ThingModelInheritEntity();
        thingModelInherit.setTenantId(tenantId);
        thingModelInherit.setEngineeringId(engineeringId);
        thingModelInherit.setSpaceId(spaceId);
        thingModelInherit.setModuleId(moduleId);
        thingModelInherit.setThingModelId(thingModel1.getId());
        thingModelInherit.setInheritThingModelId(thingModel.getId());
        thingModelInherit.setSortNo(1);
        thingModelInheritMapper.insert(thingModelInherit);

        //创建设备
        DeviceEntity device = DeviceEntity.builder()
                .name(prefix + "产线1号香烟输出机")
                .edgeGatewayId(edgeGateway.getId())
                .status(StatusEnum.INACTIVATED.getValue())
                .build();
        device.setTenantId(tenantId);
        device.setEngineeringId(engineeringId);
        device.setSpaceId(spaceId);
        device.setModuleId(moduleId);
        deviceMapper.insert(device);

        //TODO:设备模型关系
        DeviceModelInheritEntity deviceModelInheritEntity = new DeviceModelInheritEntity();
        deviceModelInheritEntity.setDeviceId(device.getId());
        deviceModelInheritEntity.setInheritThingModelId(thingModel.getId());
        deviceModelInheritEntity.setSortNo(1);
        deviceModelInheritEntity.setTenantId(tenantId);
        deviceModelInheritEntity.setEngineeringId(engineeringId);
        deviceModelInheritEntity.setSpaceId(spaceId);
        deviceModelInheritEntity.setModuleId(moduleId);
        deviceModelInheritMapper.insert(deviceModelInheritEntity);



        ResourceRelationEntity resource = new ResourceRelationEntity();
        resource.setDeviceId(device.getId());
        resource.setTenantId(tenantId);
        resource.setEngineeringId(engineeringId);
        resource.setSpaceId(spaceId);
        resource.setModuleId(moduleId);
        resourceRelationMapper.insert(resource);

        //创建标签绑定关系
        {
            int i = 0;
            for(String[] s:labelArray){
                LabelBindRelationEntity bindRelation = new LabelBindRelationEntity();
                bindRelation.setDirectlyModelId(thingModel1.getId());
                bindRelation.setLabelId(labelList.get(i).getId());
                bindRelation.setDeviceId(device.getId());
                bindRelation.setPropertyName(s[0]);
                bindRelation.setTenantId(tenantId);
                bindRelation.setEngineeringId(engineeringId);
                bindRelation.setSpaceId(spaceId);
                bindRelation.setModuleId(moduleId);
                labelBindRelationMapper.insert(bindRelation);
                i++;
            }
        }
        {
            
            int i = 0;
            for(String[] s:labelArray1){
                LabelBindRelationEntity bindRelation = new LabelBindRelationEntity();
                bindRelation.setDirectlyModelId(thingModel1.getId());
                bindRelation.setLabelId(labelList1.get(i).getId());
                bindRelation.setDeviceId(device.getId());
                bindRelation.setPropertyName(s[0]);
                bindRelation.setTenantId(tenantId);
                bindRelation.setEngineeringId(engineeringId);
                bindRelation.setSpaceId(spaceId);
                bindRelation.setModuleId(moduleId);
                labelBindRelationMapper.insert(bindRelation);
                i++;
            }
        }
        
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        Result<Device> deviceResult = Device.checkInfoToEvent(
            device, 
            commonFetcher
        );
        if(!deviceResult.getSignal()){
            throw new RuntimeException(deviceResult.getMessage());
        }


    }

}
