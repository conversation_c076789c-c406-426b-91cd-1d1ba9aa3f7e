//package com.nti56.nlink.product.device.server.neo4j;
//
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.json.JSONUtil;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.google.common.collect.Sets;
//import com.nti56.nlink.common.dto.TenantIsolation;
//import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
//import com.nti56.nlink.product.device.server.entity.DeviceEntity;
//import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
//import com.nti56.nlink.product.device.server.mapper.Neo4jModelRepository;
//import com.nti56.nlink.product.device.server.mapper.MovieRepository;
//import com.nti56.nlink.product.device.server.mapper.Neo4jDeviceRepository;
//import com.nti56.nlink.product.device.server.mapper.PersonRepository;
//import com.nti56.nlink.product.device.server.model.ThingModelDto;
//import com.nti56.nlink.product.device.server.model.neo4j.Neo4jDeviceDTO;
//import com.nti56.nlink.product.device.server.model.neo4j.Neo4jModelDTO;
//import com.nti56.nlink.product.device.server.model.neo4j.MovieDTO;
//import com.nti56.nlink.product.device.server.model.neo4j.PersonDTO;
//import com.nti56.nlink.product.device.server.model.product.vo.DeviceVO;
//import com.nti56.nlink.product.device.server.service.IDeviceModelService;
//import com.nti56.nlink.product.device.server.service.IDeviceService;
//import com.nti56.nlink.product.device.server.service.IModelGraphService;
//import com.nti56.nlink.product.device.server.service.IThingModelService;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.neo4j.driver.internal.value.MapValue;
//import org.neo4j.ogm.model.Result;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageRequest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.util.*;
//import java.util.stream.Stream;
//
///**
// * 类说明：
// *
// * @ClassName TestCRUD
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/11/16 17:10
// * @Version 1.0
// */
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@Slf4j
//@ActiveProfiles("local")
//public class TestCRUD {
//
//    /* @Autowired
//     private INeo4jTemplate neo4jTemplate;*/
//    @Resource
//    private MovieRepository movieRepository;
//
//    @Resource
//    private PersonRepository personRepository;
//
//    @Autowired
//    private IThingModelService thingModelService;
//
//    @Autowired
//    private IDeviceModelService deviceModelService;
//
//    @Autowired
//    private IDeviceService deviceService;
//
//    @Resource
//    private Neo4jModelRepository neo4jModelRepository;
//
//    @Resource
//    private Neo4jDeviceRepository neo4jDeviceRepository;
//
//    @Autowired
//    private IModelGraphService modelGraphService;
//
//   /* @Autowired
//    private Driver driver;*/
//
//    @Test
//    public void testQueryModelInherit(){
//
//        System.out.println(JSONUtil.toJsonStr(thingModelService.listModelServices(1386349939646464l,null)));
//
//    }
//
//
//
//    @Test
//    public void testQueryDeviceByModelId(){
//        com.nti56.nlink.common.util.Result<List<DeviceEntity>> listResult = deviceService.listByInheritModelId(1369024145719365l,null);
//        System.out.println(JSONUtil.toJsonStr(listResult));
//    }
//
//    @Test
//    public void testQuery() {
//       /* List list = neo4jTemplate.queryListBySql("MATCH (m:Movie) RETURN m ORDER BY m.name ASC");
//        System.out.println(JSONUtil.toJsonStr(list));*/
//        Optional<MovieDTO> the_matrix = movieRepository.findById("The Matrix");
//        if (the_matrix.isPresent()) {
//            System.out.println(JSONUtil.toJsonStr(the_matrix.get()));
//        }
//    }
//
//    @Test
//    public void testAdd() {
//        Neo4jModelDTO neo4jModelDTO = new Neo4jModelDTO();
//        neo4jModelDTO.setDescription("test3");
//        neo4jModelDTO.setModelId(1222323323L);
//        neo4jModelDTO.setUpdateTime("2022-11-21 22:00:00");
//        neo4jModelDTO.setModelType(1);
//        neo4jModelDTO.setName("测试模型3");
//        neo4jModelRepository.save(neo4jModelDTO);
//
//       /* MovieDTO movieDTO = new MovieDTO();
//        movieDTO.setReleased(2022);
//        movieDTO.setTitle("The Avanter 4");
//        movieDTO.setTagline("3D Grete Movie");
//        movieRepository.save(movieDTO);*/
//
//    }
//
//    @Test
//    public void testQueryPage() {
//
//        PageRequest pageRequest = PageRequest.of(1, 20);
//        Page<MovieDTO> all = movieRepository.findAll(pageRequest);
//        System.out.println(JSONUtil.toJsonStr(all));
//    }
//
//    @Test
//    public void testQueryRelation() {
//        PageRequest pageRequest = PageRequest.of(1, 5);
//        Page<MovieDTO> all = movieRepository.findAll(pageRequest);
//        System.out.println(JSONUtil.toJsonStr(all));
//        Optional<PersonDTO> keanu_reeves = personRepository.findById("Keanu Reeves");
//        if (keanu_reeves.isPresent()) {
//            System.out.println(JSONUtil.toJsonStr(keanu_reeves.get()));
//        }
//    }
//
//    @Test
//    public void addRelation() {
//        PersonDTO personDTO = new PersonDTO();
//        personDTO.setName("levi");
//        personDTO.setBorn(1922);
//        Optional<MovieDTO> the_avanter_two = movieRepository.findById("The Avanter Two");
//        personDTO.setActedIns(Sets.newHashSet(the_avanter_two.get()));
//        personRepository.deleteById("levi");
//        personRepository.save(personDTO);
//    }
//
//    @Test
//    public void whoDirectCloudAtlas() {
//        /*List<PersonDTO> personDTOS= personRepository.getActorsThatActInAmoviesFromTitle("levi");
//        System.out.println(JSONUtil.toJsonStr(personDTOS));*/
//        Page<PersonDTO> page = personRepository.getActorsThatActInAmoviesFromTitlePage("The Matrix", PageRequest.of(0, 5));
//        System.out.println(JSONUtil.toJsonStr(page));
//    }
//
//    @Test
//    public void getPersonActedInTree() {
//        Result keanu_reeves = personRepository.getPersonActedInTree("Keanu Reeves");
//        System.out.println(JSONUtil.toJsonStr(keanu_reeves));
//    }
//
//    @Test
//    public void testQueryInherit() {
//        Result meInherit = neo4jModelRepository.queryModelInherit(1367311633899613l);
//        System.out.println(JSONUtil.toJsonStr(meInherit));
//
//       /* Result inheritMe = neo4jModelRepository.queryWhichInheritMe(1367311633899605l);
//        System.out.println(JSONUtil.toJsonStr(inheritMe));*/
//    }
//
//    @Test
//    public void addData4OldData() {
//        QueryWrapper<ThingModelEntity> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("deleted", 0);
////        queryWrapper.eq("tenant_id",)
//        List<ThingModelEntity> list = thingModelService.list(queryWrapper);
//        if (CollectionUtil.isNotEmpty(list)) {
//            for (ThingModelEntity thingModelEntity : list) {
//                TenantIsolation tenantIsolation = new TenantIsolation();
//                tenantIsolation.setTenantId(thingModelEntity.getTenantId());
//                com.nti56.nlink.common.util.Result<ThingModelDto> model = thingModelService.getThingModelById(tenantIsolation, thingModelEntity.getId());
//                if (model.getSignal()) {
//                    ThingModelDto result = model.getResult();
//                    Neo4jModelDTO modelLabel = Neo4jModelDTO.builder()
//                            .description(result.getDescript())
//                            .modelId(result.getId())
//                            .updateTime(result.getUpdateTime().toString())
//                            .modelType(result.getFullModel() == null ? 1 : result.getFullModel().getModelType())
//                            .name(result.getName())
//                            .tenantId(thingModelEntity.getTenantId())
//                            .build();
//
//                    if (CollectionUtil.isNotEmpty(result.getInheritThingModelIds())) {
//                        Set<Neo4jModelDTO> inheritModel = new HashSet<>(result.getInheritThingModelIds().size());
//                        result.getInheritThingModelIds().forEach(id -> {
//                            ThingModelEntity byId = thingModelService.getById(id);
//                            inheritModel.add(Neo4jModelDTO.builder()
//                                    .description(byId.getDescript())
//                                    .modelId(byId.getId())
//                                    .updateTime(byId.getUpdateTime().toString())
//                                    .modelType(byId.getModelType())
//                                    .name(byId.getName())
//                                    .tenantId(byId.getTenantId())
//                                    .build());
//
//                        });
//                        modelLabel.setInheritModels(inheritModel);
//                    }
//                    neo4jModelRepository.save(modelLabel);
//                }
//
//            }
//        }
//
//    }
//
//    @Test
//    public void addDeviceOldData() {
//        QueryWrapper<DeviceEntity> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("deleted", 0);
//        List<DeviceEntity> list = deviceService.list(queryWrapper);
//        if (CollectionUtil.isNotEmpty(list)) {
//            for (DeviceEntity deviceEntity : list) {
//                TenantIsolation tenantIsolation = new TenantIsolation();
//                tenantIsolation.setTenantId(deviceEntity.getTenantId());
//                Neo4jDeviceDTO neo4jDeviceDTO = Neo4jDeviceDTO.builder()
//                        .deviceId(deviceEntity.getId())
//                        .description(deviceEntity.getDescript())
//                        .status(deviceEntity.getStatus())
//                        .name(deviceEntity.getName())
//                        .updateTime(deviceEntity.getUpdateTime().toString())
//                        .tenantId(deviceEntity.getTenantId())
//                        .build();
//                try {
//                    com.nti56.nlink.common.util.Result<DeviceVO> deviceModel = deviceModelService.getDeviceModel(deviceEntity.getId(), tenantIsolation);
//                    DeviceVO result = deviceModel.getResult();
//                    if (deviceModel.getSignal()) {
//                        Set<Neo4jModelDTO> inherits = new HashSet<>(result.getModelContainsInherits().size());
//                        if (CollectionUtil.isNotEmpty(result.getModelContainsInherits())) {
//                            for (ModelDpo modelContainsInherit : result.getModelContainsInherits()) {
//                                Neo4jModelDTO neo4jModelDTO = Neo4jModelDTO.builder()
//                                        .modelId(modelContainsInherit.getId())
//                                        .modelType(modelContainsInherit.getModelType())
//                                        .name(modelContainsInherit.getName())
//                                        .description(modelContainsInherit.getDescript())
//                                        .tenantId(deviceEntity.getTenantId())
//                                        .build();
//                                inherits.add(neo4jModelDTO);
//
//                            }
//                            neo4jDeviceDTO.setInheritModels(inherits);
//                        }
//                    }
//                }catch (Exception e){
//                    log.error(e.getMessage());
//                }
//                neo4jDeviceRepository.save(neo4jDeviceDTO);
//            }
//        }
//    }
//
//    @Test
//    public void testCollectorId(){
//        Result maps = neo4jModelRepository.collectorInheritModelId(1367311633899613l);
//        System.out.println(JSONUtil.toJsonStr(maps));
//    }
//
//    @Test
//    public void test4UpdateBoot3(){
//
//        Result stream = neo4jDeviceRepository.queryDeviceInherit(1373478267568128l);
//        com.nti56.nlink.common.util.Result<Object> test = getResult(stream, "test");
//        log.info("result:{}",JSONUtil.toJsonStr(test.getResult()));
//    }
//
//    private com.nti56.nlink.common.util.Result<Object> getResult(Result stream, String msg) {
//
//        Iterator<Map<String, Object>> iterator = stream.iterator();
//        Map<String, Object> next = iterator.next();
//        if(!Objects.isNull(next) && !next.isEmpty()){
////            log.info("============{}",next.toString());
//            return com.nti56.nlink.common.util.Result.ok(JSONUtil.parseObj(next.get("value")));
//        }
//        return com.nti56.nlink.common.util.Result.error(msg);
//    }
//}
