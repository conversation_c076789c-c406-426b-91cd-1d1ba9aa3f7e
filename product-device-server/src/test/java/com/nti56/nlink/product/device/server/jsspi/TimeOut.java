package com.nti56.nlink.product.device.server.jsspi;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName TimeOut
 * @date 2022/4/20 14:28
 * @Version 1.0
 */
public class TimeOut {

    @Getter
    private Integer time;

    private String tag;

    TimeOut(int time,String tag){
        this.time = time;
        this.tag = tag;
    }
    TimeOut(){
        this.time = 133;
        tag = "默认";
    }

    public void sleep(){
//        try {
//            Thread.sleep(time != null ? time: 133 );
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//        System.out.println(tag+"延时了" + time);
    }

    public Object checkClass(String name) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("a",1);
        dataMap.put("b",new int[]{1,2});
        if (dataMap.contains<PERSON><PERSON>(name)) {
            Object data = dataMap.get(name);
            boolean array = data.getClass().isArray();


            return data;
        }
        return null;
    }
}
