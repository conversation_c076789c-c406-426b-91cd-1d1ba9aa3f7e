package com.nti56.nlink.product.device.server.export;

import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;

import com.nti56.nlink.product.device.server.service.export.handler.ThingExportSqlHandler;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/23 13:28<br/>
 * @version 1.0
 * @since JDK 1.8
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class ExportThingTest {

  @Test
  public void testExport(){
    ThingExportSqlHandler thing =new ThingExportSqlHandler();
    List<String> ls=new ArrayList<>();
    thing.exportSqlDml(0L,ls);
    System.out.println(JSON.toJSONString(ls));

  }
}
