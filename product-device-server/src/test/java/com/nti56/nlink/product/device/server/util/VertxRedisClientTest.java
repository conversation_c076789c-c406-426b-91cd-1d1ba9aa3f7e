package com.nti56.nlink.product.device.server.util;


import com.google.common.collect.Maps;
import io.vertx.core.Future;
import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.buffer.impl.BufferImpl;
import io.vertx.redis.client.Redis;
import io.vertx.redis.client.RedisAPI;
import io.vertx.redis.client.RedisOptions;
import io.vertx.redis.client.Response;
import io.vertx.redis.client.impl.types.BulkType;
import io.vertx.redis.client.impl.types.MultiType;
import org.junit.Test;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.util.*;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;


public class VertxRedisClientTest {


    @Test
    public void testRedisQuery() {
        Vertx vertx = Vertx.vertx();
        RedisOptions options = new RedisOptions()
                .setConnectionString("redis://:nti56@10.1.21.201:20111/0")
                .setMaxPoolSize(5) // 每个连接池的最大连接数
                .setMaxPoolWaiting(3) // 每个连接池最大等待连接数
                .setPoolRecycleTimeout(1000) // 连接回收超时时间（毫秒）
                .setPoolCleanerInterval(10000); // 连接池清理间隔（毫秒）

        Redis client = Redis.createClient(vertx, options);
        RedisAPI redis = RedisAPI.api(client);
//        Future<Response> future = redis.hmget(Arrays.asList("pd:device:twin:changeTime:1381827208912916", "status", "description")).onSuccess(
//                result -> {
//                    System.out.println(result);
//                }
//        );
//        Future<Response> future = redis.hmget(Arrays.asList("pd:groupMapping:1396569005998080:AGV-DEMO4_CVC600_01:121_AGV.001","1402070932439040"));

//        CompletionStage<Object> completionStage = future.compose(result -> {
//            result.forEach(item -> {
//                System.out.println(item);
//            });
//            return null;
//        }).toCompletionStage();


  //hmget
      /* Future<Response> groupDeviceMappingFuture = redis.hmget(Arrays.asList("pd:groupMapping:1396569005998080:AGV-DEMO4_CVC600_01:121_AGV.001", "1402070932439040"));

        CompletionStage<Response> completionStage =  groupDeviceMappingFuture.onSuccess(groupDeviceMappingResp -> {
            HashSet<String> restoredSet = new HashSet<String>();
            Iterator<Response> iterator = groupDeviceMappingResp.iterator();
            while (iterator.hasNext()) {
                Response item = iterator.next();
                BulkType bufferImpl = (BulkType) item;
                try (ByteArrayInputStream bis = new ByteArrayInputStream(bufferImpl.toBytes());
                     ObjectInputStream ois = new ObjectInputStream(bis)) {
                    restoredSet.addAll((HashSet<String>) ois.readObject());
                    System.out.println("Restored HashSet: " + restoredSet);
                } catch (IOException | ClassNotFoundException e) {
                    e.printStackTrace();
                }
            }

        }).toCompletionStage();*/


   /*     Future<Response> hgetall = redis.hgetall("pd:groupMapping:1396569005998080:AGV-DEMO4_CVC600_01:121_AGV.001");
        CompletionStage<Response> completionStage = hgetall.onSuccess(groupDeviceMappingResp -> {
            Map<String, Set<String>> resultMap = Maps.newHashMap();
           for(String key : groupDeviceMappingResp.getKeys()) {
               Response response = groupDeviceMappingResp.get(key);
               BulkType bufferImpl =(BulkType)response;
               try (ByteArrayInputStream bis = new ByteArrayInputStream(bufferImpl.toBytes());
                    ObjectInputStream ois = new ObjectInputStream(bis)) {
                    resultMap.put(key,(HashSet<String>) ois.readObject());
               } catch (IOException | ClassNotFoundException e) {
                   e.printStackTrace();
               }
           }
            System.out.println(resultMap);
        }).toCompletionStage();*/


//        Future<Response> hgetall = redis.get("pd:label:property:1426345918660608");
//        CompletionStage<Response> completionStage = hgetall.onSuccess(groupDeviceMappingResp -> {
//            Map<String, String> nameMap = Maps.newHashMap();
//            BulkType bufferImpl = (BulkType) groupDeviceMappingResp;
//            try (ByteArrayInputStream bis = new ByteArrayInputStream(bufferImpl.toBytes()); ObjectInputStream ois = new ObjectInputStream(bis)) {
//                nameMap = (HashMap) ois.readObject();
//            } catch (IOException | ClassNotFoundException e) {
//                e.printStackTrace();
//            }
//            System.out.println(nameMap);
//        }).toCompletionStage();


        Future<Response> hgetall = redis.get("pd:device:enable:1428037552234496");
        CompletionStage<Response> completionStage = hgetall.onSuccess(res -> {

            BulkType bufferImpl = (BulkType) res;
            try (ByteArrayInputStream bis = new ByteArrayInputStream(bufferImpl.toBytes());
                 ObjectInputStream ois = new ObjectInputStream(bis)) {
                Boolean result = (Boolean) ois.readObject();
                System.out.println(result);
            } catch (IOException | ClassNotFoundException e) {
                e.printStackTrace();
            }

            if (ObjectUtils.isEmpty(res) || !res.toBoolean()) {
                System.out.println(res);
            }
        }).toCompletionStage();

        // 处理结果
        completionStage.whenComplete((response, throwable) -> {
            if (throwable != null) {
                System.err.println("Redis 操作失败，原因: " + throwable.getMessage());
            } else {
                System.out.println("Redis 操作成功，结果: " + response.toString());
            }
            // 关闭 Redis 客户端
            redis.close();
        });

        try {
            // 等待结果（可选）
            completionStage.toCompletableFuture().get(5, TimeUnit.SECONDS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            e.printStackTrace();
        }

    }
}
