```sql

alter table product add column thing_model_id BIGINT(11) COMMENT '物模型id';

drop table product_module;

CREATE TABLE edge_gateway (
  id BIGINT(11) AUTO_INCREMENT COMMENT 'id',
  name VARCHAR(128) NOT NULL COMMENT '边缘网关名称',
  
  host VARCHAR(32) COMMENT 'host',
  port INT COMMENT 'port',
  descript VARCHAR(512) COMMENT '描述',
  
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 AUTO_INCREMENT = 2 COMMENT='边缘网关表';

alter table channel drop column ip;
alter table channel drop column port;
alter table channel drop column rack;
alter table channel drop column slot;
alter table channel add column edge_gateway_id BIGINT(11) NOT NULL COMMENT '所属边缘网关id';

CREATE TABLE channel_param (
  id BIGINT(11) AUTO_INCREMENT COMMENT 'id',
  channel_id BIGINT(11) NOT NULL COMMENT '所属通道id',
  name VARCHAR(64) NOT NULL COMMENT '通道参数名称，如ip/port/rack/slot等',
  value VARCHAR(128) COMMENT '通道参数值',

  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 AUTO_INCREMENT = 1 COMMENT='通道参数表';

drop table device_group;
drop table label_category;

alter table label drop column label_category_id;
alter table label add column data_type VARCHAR(16) NOT NULL COMMENT '数据类型，bool/byte/short/int/float/string';
alter table label add column is_array TINYINT(1) NOT NULL COMMENT '是否数组';

alter table label add column label_group_id BIGINT(11) COMMENT '所属标签分组id';
alter table label add column string_bytes INT COMMENT 'type是string类型时，表示string元素的byte长度，其他type类型放空';

CREATE TABLE label_group (
  id BIGINT(11) AUTO_INCREMENT COMMENT 'id',
  channel_id BIGINT(11) COMMENT '所属通道id',
  
  name VARCHAR(128) NOT NULL COMMENT '分组名称',
  tag VARCHAR(128) COMMENT '分组标识,逗号分隔',
  strategy_id BIGINT(11) COMMENT '使用策略id',

  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 AUTO_INCREMENT = 10 COMMENT='标签分组表';

alter table device drop column device_group_id;
alter table device drop column ip;
alter table device drop column port;
alter table device drop column gateway_device_id;
alter table device drop column thing_model_id;
alter table device add column edge_gateway_id BIGINT(11) NOT NULL COMMENT '所属边缘网关id';

CREATE TABLE gather_task (
  id BIGINT(11) AUTO_INCREMENT COMMENT 'id',
  edge_gateway_id BIGINT(11) NOT NULL COMMENT '所属网关id',
  name VARCHAR(128) NOT NULL COMMENT '任务名称',

  content JSON COMMENT '任务内容',

  -- 网关定时上报任务状态
  enable_status TINYINT(1) NOT NULL DEFAULT 0 COMMENT '启用状态，0-关闭，1-启用',

  version INT NOT NULL DEFAULT 1 COMMENT '版本号',

  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 AUTO_INCREMENT = 9 COMMENT='采集任务表';

CREATE TABLE compute_task (
  id BIGINT(11) AUTO_INCREMENT COMMENT 'id',
  edge_gateway_id BIGINT(11) NOT NULL COMMENT '所属网关id',
  gather_task_id BIGINT(11) NOT NULL COMMENT '订阅采集任务id',
  name VARCHAR(128) NOT NULL COMMENT '任务名称',
  content JSON COMMENT '任务内容',

  -- 网关定时上报任务状态
  enable_status TINYINT(1) NOT NULL DEFAULT 0 COMMENT '启用状态，0-关闭，1-启用',

  version INT NOT NULL DEFAULT 1 COMMENT '版本号',

  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 AUTO_INCREMENT = 9 COMMENT='计算任务表';

```