
## 规范

#### controller规范

分页查询 
    GET /device-group/page
    pageDeviceGroup
    当前页 current
    页大小 size
    返回总数 total
    返回记录 records

列表查询 
    GET /device-group/list
    listDeviceGroup

单个新增 
    POST /device-group
    createDeviceGroup

单个删除
    DELETE /device-group/{id}
    deleteDeviceGroup

单个改动 
    PUT /device-group/{id}
    editDeviceGroup

单个查询 
    GET /device-group/{id}
    listDeviceGroup

controller层只处理http协议相关的东西，
如何接收参数，一些简单的入参的校验，
如何暴露接口，
如何返回json的response，
如何解析header参数，
如何生成swagger文档，
如何将参数转换成service入参
允许调用多个service方法

#### service层规范

service层做业务处理，对http协议无感知，无状态，比如service层对当前用户无感知

不使用mybatis-plus的ServiceImpl，因为这个类把wrapper相关方法
暴露出来，相当于允许sql代码出现在service层外部的调用方，比如controller，
mvc分层失去意义，更重要的问题是，相关的业务功能没有聚合到service层，
业务代码分散在调用方和服务提供方。

#### dao层规范

数据库查询都通过注入的mapper

#### 异常/错误规范

业务错误

    尽量使用Result表达错误和错误信息

框架、sdk等错误异常

    直接抛出给全局异常处理器处理，
    目的是不使用try catch做流程控制

区别业务错误和非业务错误的一个方法是：

    业务错误，经常是返回200给前端，并返回业务错误码和错误信息，方便前端展示响应的业务错误信息

    非业务错误，经常是返回500给前端，可能会返回相关系统级错误码，方便开发人员调试错误

异常和事务回滚

    当需要利用事务做异常回滚，但又需要返回200以及错误码和错误信息给前端的时候，
    可以抛出BizException业务异常，触发回滚，然后全局捕捉器会捕捉BizException，
    并返回200，即避免写tryCatch和写手动回滚、也可以返回相应的业务错误信息

#### 包规范

entity
  数据库表对应实体，类名加Entity后缀，表字段跟Entity属性一一对应，不能多也不能少
  如：DeviceEntity.java

entity.json
  数据库json字段的对象，类名加Field后缀
  如：ModelField.java

entity.json.element
  json字段内部的对象，类名加Elm后缀
  如：PropertyElm.java

domain
  领域对象，类名不加后缀
  如：Device.java

domain.enumerate
  领域枚举对象，类名加Enum后缀
  如：DriverEnum.java

model
  Dto、Bo、Vo、Param等对象都放在该包下，且类名加相应后缀，区别于领域对象，
  Dto数据传输对象，用于Controller接收参数，
  Param数据参数对象，同Dto，
  Bo数据业务对象，用于Service查询出复杂对象，
  Vo数据展示对象，同Bo，

#### 审计字段规范

```sql
id bigint  unsigned NOT NULL COMMENT 'id',
ENGINEERING_ID bigint unsigned NOT NULL COMMENT '工程id',
SPACE_ID bigint unsigned NOT NULL COMMENT '空间id',
MODULE_ID bigint unsigned NOT NULL COMMENT '模块id',
CREATOR varchar(90) DEFAULT NULL COMMENT '创建人名称',
CREATOR_ID bigint unsigned DEFAULT NULL COMMENT '创建人id',
CREATE_TIME datetime  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
UPDATOR_ID bigint unsigned DEFAULT NULL COMMENT '修改人id',
UPDATOR varchar(90) DEFAULT NULL COMMENT '修改人名称',
UPDATE_TIME datetime DEFAULT NULL COMMENT '修改时间',
VERSION int DEFAULT '1' COMMENT '版本号',
DELETED int DEFAULT '0' COMMENT '是否删除',
```

#### 分支版本规范

| 分支         | 环境后缀   | jar包名                           |
| ------------ | --------- | ---------                         |
| develop      | dev       | {main}.{sub}.{minor}-SNAPSHOT.jar |
| release      | test      | {main}.{sub}.{minor}-RELEASE.jar  |
| main         | prod      | {main}.{sub}.{minor}.jar          |

develop合并到release后，提交测试，
在`提测-修复bug-确认bug修复-版本上线`这个过程中，
如果有新功能开发，不想提测，需要从develop分出一个新的分支。
修复bug的代码依旧在develop分支提交并合并到release给测试。
版本上线后，再把新功能的分支合并到develop。

不允许在release提交代码，只能是在develop提交代码，然后合并到release，
否则会把release的版本信息带到develop分支。

团队开发，使用merge，不使用rebase

#### 日志规约

1. 日志框架

使用门面模式SLF4J框架，在类上增加`@Slf4j`注解，import lombok.extern.slf4j.Slf4j;

使用logback

2. 配置中心
   
在使用配置中心的时候（如nacos），为了保证logback配置文件加载到spring变量，
需要使用如下配置，来保证加载顺序：

```yml
logging:
  config: classpath:logback-delay.xml
```

```xml
<!-- src/main/resources/logback-delay.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty name="APP_NAME" scope="context" source="spring.application.name"/>
    ...
</configuration>

```

3. 性能考量

使用占位符输出

```java
log.debug("Processing trade with id: {} and symbol : {} ", id, symbol);
```

4. 磁盘考量

生产环境禁止输出 debug 日志


#### 过滤器规约

1. 实现方式

实现Filter接口，并用FilterRegistrationBean注册到spring

```java
@Component
public class MyFilter implements Filter {}

@Configuration
public class MyFilterConfig {
  @Autowired
  MyFilter myFilter;

  @Bean
  public FilterRegistrationBean<MyFilter> thirdFilter() {
    FilterRegistrationBean<MyFilter> filterRegistrationBean = new FilterRegistrationBean<>();
    // 注册自定义的 Filter
    filterRegistrationBean.setFilter(myFilter);
    // 设置拦截 Url映射
    filterRegistrationBean.setUrlPatterns(new ArrayList<>(Arrays.asList("/api/*")));
    return filterRegistrationBean;
  }
}
```

注解@WebFilter，并用@ServletComponentScan注册到spring

```java
@WebFilter(filterName = "MyFilter", urlPatterns = "/api/*")
public class MyFilter implements Filter {}

//启动类加：
@ServletComponentScan
```

2. 目录要求

MyFilter放在src/java/xx/xx/config/filter目录下

MyFilterConfig放在src/java/xx/xx/config目录下

3. 使用场景

过滤器是依赖于Servlet容器，属于Servlet规范的一部分，可以修改request，主要的应用场景包括：

过滤敏感词汇（防止sql注入）

设置字符编码

URL级别的权限访问控制

压缩响应信息

#### 拦截器规约

1. 实现方式

实现HandlerInterceptor接口，并配置WebMvcConfigurer

```java
public class MyInterceptor implements HandlerInterceptor {}

@Configuration
public class MyMvcConfig implements WebMvcConfigurer {

   @Override
   public void addInterceptors(InterceptorRegistry registry) {
      registry.addInterceptor(new MyInterceptor());
   }
}
```

2. 目录要求

MyInterceptor放在src/java/xx/xx/config/interceptor目录下

MyMvcConfig放在src/java/xx/xx/config目录下

3. 使用场景

拦截器本质上是面向切面编程（AOP），符合横切关注点的功能都可以放在拦截器中来实现，主要的应用场景包括：

登录验证，判断用户是否登录。

权限验证，判断用户是否有权限访问资源，如校验token

租户id提取，读取请求种的租户id信息

日志记录，记录请求操作日志（用户ip，访问时间等），以便统计请求访问量。

处理cookie、本地化、国际化、主题等。

性能监控，监控请求处理时长等。

通用行为：读取cookie得到用户信息并将用户对象放入请求，从而方便后续流程使用，还有如提取Locale、Theme信息等，只要是多个处理器都需要的即可使用拦截器实现）

## sql模板

```sql
DROP TABLE IF EXISTS tmp;
CREATE TABLE tmp (
  id BIGINT(11) AUTO_INCREMENT COMMENT 'id',
  tmp_name VARCHAR(128) NOT NULL COMMENT '名称',

  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_0900_as_cs AUTO_INCREMENT = 2825686 COMMENT='表';
```

## 上报数据格式

#### trigger

```json5
{
  timestamp: 1234567890, //ms
  prop: [{
    property: "temperature",
    dataType: "short",
    isArray: false,
    length: 0,
    value: 60
  }, {
    property: "barCode",
    dataType: "string",
    isArray: true,
    length: 3,
    value: ["aef323", "55yhh", "65u67j"]
  }]
}

// measurement: nti
// tag: type:trigger, eventName, deviceId, 
//      property, dataType, isArray, length
// field: {dataType.n}
```


#### change

```json5
{
  timestamp: 1234567890, //ms
  prop: [{
    property: "",
    labelId: 123,
    dataType: "int",
    isArray: false,
    length: 0,
    preValue: value1,
    value: value2
  }]
}

// measurement: nti
// tag: type:eventChange, eventName, deviceId, 
//     property, dataType, isArray, length
// field: {dataType.n}.preValue, {dataType.n}.value 
```

#### report

```json5
{
  timestamp: 1234567890, //ms
  prop: [{
    property: "",
    labelId: 123,
    dataType: "int",
    isArray: true,
    length: 0,
    value: value2
  }]
}
// measurement nti
// tag: type:eventReport, eventName, deviceId, 
//       property, dataType, isArray, length
// field: {dataType.n}
```

#### labelChange

```json5
{
  timestamp: 1234567890, //ms
  prop: [{
    labelId: 123,
    labelName: "",
    dataType: "int",
    isArray: true,
    length: 0,
    preValue: value1,
    value: value2
  }]
}

// measurement nti
// tag: type:labelChange, labelId, 
//       labelName, dataType, isArray, length
// field: {dataType.n}.preValue, {dataType.n}.value 
```

#### labelReport:

```json5
{
  timestamp: 1234567890, //ms
  prop: [{
    labelId: 123,
    labelName: "",
    dataType: "int",
    isArray: true,
    length: 0,
    value: value2
  }]
}
// measurement nti
// tag: type:labelReport, labelId, 
//       labelName, dataType, isArray, length
// field: {dataType.n}
```

## v1.2版本

#### 规则引擎实现方案

属性规则

(deviceA.propertyA > 10)

redisHash = redisTemplate.get("ruleId")
Map<Long labelId, Object value> propObj = ()redisHash
spel.exec( "(#prop.get(${labelId}) > 10)" , propObj)
{fullPropertyName, labelId, value, ttl}

启动某个规则，这里假设是规则A，则查出规则A的所有相关属性，
把这些属性按照网关分组，每个组生成一个规则计算任务，
下发给网关，网关计算到改变后发送mqtt消息给gw/up/rulePropertyChange/{ruleId}。
为规则A生成一个规则对象a，用于保存相关属性的值，
属性有效时长，属性上报时间，
规则对象含有1个令牌桶，最快每m秒能释放1个令牌。
ot启动n个mqtt客户端，订阅gw/up/rulePropertyChange/{ruleId}，
当接收消息后，解出ruleId，并拿到对应的规则对象，
把属性值及属性值过期时间保存到对象里，
运行计算规则，如果计算结果如果是触发规则，
从令牌桶拿令牌，拿到令牌就可以执行相应的操作，
拿不到令牌则说明当前在防抖区间

事件规则

启动某个规则，这里假设是规则B，
为规则B生成一个规则对象b，用于保存相关事件的bool值，
事件有效时长，事件上报时间，
规则对象含有1个令牌桶，最快每m秒能释放1个令牌。
查出规则B的所有相关事件，
把规则注册到事件上，规则注册表：
{
  "{deviceId}-{eventName}": [规则B, 规则C, ...]
}
ot启动n个mqtt客户端，订阅
gw/up/{deviceId}/thing/events/trigger/{eventName}，
注意，这个订阅跟入库订阅是不同的订阅分组，他们之间不会共享订阅，
当接收到消息后，从规则注册表查出注册的规则，并遍历，
把事件的bool值=true，及过期时间保存到规则对象里，
计算结果如果是触发规则，从令牌桶拿令牌，拿到令牌就可以执行相应的操作
拿不到令牌则说明当前在防抖区间


初始化/同步

获取规则的所有属性，下放1个只会执行1次(setTimer)的采集+计算任务

计算任务改变上报，在第一次采集的时候，上一次是空的，当成改变，上报。
实现持久化功能

#### 运行时数据

  采集任务
    label.gather_param
  通道信息
    channel.runtime_info
  设备计算计算任务、规则计算任务
    compute_task
  物服务
    device.runtime_metadata 

#### 修改影响

产品、物模型变化，相关设备受影响需同步
  查询受影响
标签分组、通道、网关变化，相关网关通道信息受影响需同步
标签、标签绑定关系变化，相关设备受影响需同步

#### 租户隔离todo

ChannelParamMapper
List<ProofreadChannelParamDTO> listProofreadDataByChannelId(Long channelId);

LabelGroupMapper
List<ProofreadLabelGroupDTO> listProofreadDataByChannelId(Long channelId);

LabelMapper
List<LabelBo> listLabelByIds(@Param("labelIds") List<Long> labelIds);
List<ProofreadLabelDTO> listProofreadDataByLabelGroupId(Long labelGroupId);


## v1.3版本

触发了多少次事件

触发了多少次规则

运行了多少次物服务

采集了多少次、多少量直接上报数据

采集了多少次、多少量改变上报数据

时序数据库数据的挖掘

在系统上直接控制设备

运行日志
  物服务、规则引擎

密钥

bool
  =/!= true/false

byte
  =/!= a/b/c 跟夏总确认是要输入字符还是16进制数

short
  =/!=/>/</>=/<= 1/2/3

int
  =/!=/>/</>=/<= 1/2/3

float
  =/!=/>/</>=/<= 1/2/3/1.0

string
  =/!= ab/cd/efg
  研究下包含关系如何实现

系统物模型模板
  用户使用的时候区分租户
  系统模板放在资源市场
  未发布、发布、下架
  模板发布后不能修改，只能下架、上架