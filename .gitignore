/.idea/
target/
.vscode/
bootstrap-shann.yml
bootstrap-localshann.yml
bootstrap-runtimeshann.yml
logs/
*.iml
.DS_Store
.history/
product-device-server/engineeringDoc/
.cursor/
draft/
.run

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.clinerules
.github
.promptx
.taskmaster
.trae
.roo
.windsurf
.roomodes
.env.example
AGENTS.md
CLAUDE.md
VERTX_CACHE_OPTIMIZATION.md
.gemini
# OS specific

# Task files
# tasks.json
# tasks/ 